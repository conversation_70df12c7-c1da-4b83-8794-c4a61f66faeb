// pages/supply_chain_services/channel_service.js
import config from '../../config'; // 导入配置文件
import api from '../../utils/api'; // 导入API模块
import util from '../../utils/util'; // 导入util工具类

Page({

  /**
   * 页面的初始数据
   */
  data: {
    navBarHeight: 0, // 导航栏高度
    statusBarHeight: 0, // 状态栏高度
    COS_CONFIG: config.COS_CONFIG, // 添加腾讯云配置
    serviceList: [], // 服务列表数据
    pageSize: 10, // 每页数据量
    currentPage: 1, // 当前页码
    totalData: [], // 总数据
    hasMoreData: true, // 是否还有更多数据
    searchValue: '', // 搜索值
    loading: false, // 加载状态
    pageLoading: true, // 页面加载状态
    totalCount: 0 // 总数据量
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setNavBarInfo();
    this.fetchChannelServiceList();
  },

  /**
   * 获取通道服务列表
   */
  fetchChannelServiceList(page = 1, isPullDownRefresh = false) {
    // 如果是下拉刷新，不显示loading状态
    if (!isPullDownRefresh) {
      this.setData({ loading: true });
    }

    const params = {
      page: page,
      limit: this.data.pageSize
    };

    // 如果有搜索关键词，添加到请求参数
    if (this.data.searchValue) {
      params.company_name = this.data.searchValue;
    }

    // 设置请求选项，禁用全局loading
    const options = {
      loading: false // 禁用request.js中的全局loading
    };

    api.channelService.getList(params, options).then(res => {
      // responseInterceptor已经提取了data.data，所以这里直接使用res
      const list = res.list || [];
      const total = res.total || 0;

      if (page === 1) {
        // 首次加载或刷新
        this.setData({
          serviceList: list,
          totalCount: total,
          hasMoreData: list.length < total,
          currentPage: page,
          pageLoading: false
        });
      } else {
        // 加载更多
        this.setData({
          serviceList: [...this.data.serviceList, ...list],
          hasMoreData: this.data.serviceList.length + list.length < total,
          currentPage: page
        });
      }
    }).catch(err => {
      console.error('获取通道服务列表失败', err);
      wx.showToast({
        title: '获取数据失败',
        icon: 'none'
      });
    }).finally(() => {
      this.setData({
        loading: false,
        pageLoading: false
      });

      // 如果是下拉刷新，停止下拉刷新动画
      if (isPullDownRefresh) {
        wx.stopPullDownRefresh();
      }
    });
  },

  /**
   * 加载初始数据
   */
  loadInitialData() {
    this.fetchChannelServiceList(1);
  },

  /**
   * 加载更多数据
   */
  loadMoreData() {
    if (!this.data.hasMoreData || this.data.loading) return;

    const nextPage = this.data.currentPage + 1;
    this.fetchChannelServiceList(nextPage);
  },

  /**
   * 搜索输入事件
   */
  onSearchInput(e) {
    this.setData({
      searchValue: e.detail.value
    });
  },

  /**
   * 执行搜索
   */
  onSearch() {
    // 重置页码并重新获取数据
    this.fetchChannelServiceList(1);
  },

  /**
   * 卡片点击事件
   */
  onCardTap(e) {
    const id = e.currentTarget.dataset.id;

    // 获取用户信息
    const userInfo = util.getUserInfo();

    // 检查登录状态
    if (userInfo && userInfo.app_id) {
      // 用户已登录，直接跳转到详情页
      wx.navigateTo({
        url: `/pages/supply_chain_services/channel_service_detail?id=${id}`
      });
    } else {
      // 用户未登录，直接跳转到登录页面
      wx.navigateTo({
        url: '/pages/login/index'
      });
    }
  },

  /**
   * 设置导航栏信息
   */
  setNavBarInfo() {
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    // 获取状态栏高度
    const statusBarHeight = systemInfo.statusBarHeight;
    // 导航栏高度 = 状态栏高度 + 44(导航内容高度)
    const navBarHeight = statusBarHeight + 44;

    this.setData({
      statusBarHeight: statusBarHeight,
      navBarHeight: navBarHeight,
      searchBarHeight: 90, // 更新搜索栏高度(rpx转px)，用于计算内容区域的margin-top
      windowWidth: systemInfo.windowWidth,
      windowHeight: systemInfo.windowHeight
    });
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 下拉刷新重新加载数据，并传入isPullDownRefresh=true
    this.fetchChannelServiceList(1, true);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    // 触底加载更多数据
    this.loadMoreData();
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})