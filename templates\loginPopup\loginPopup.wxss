/* 登录弹窗样式 */
.login-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.login-popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.login-popup-content {
  width: 390rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  z-index: 2;
}

.login-popup-close {
  position: absolute;
  top: -20rpx;
  right: -20rpx;
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background-color: #FFFFFF;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  z-index: 10;
}

.login-popup-close::before,
.login-popup-close::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20rpx;
  height: 2rpx;
  background-color: #999;
  transform: translate(-50%, -50%) rotate(45deg);
}

.login-popup-close::after {
  transform: translate(-50%, -50%) rotate(-45deg);
}

.login-popup-image {
  width: 108rpx;
  height: 112rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
}

.login-popup-image image {
  width: 100%;
  height: 100%;
}

.login-popup-text {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

.login-popup-button {
  width: 322rpx;
  height: 72rpx;
  background-color: #4080ff;
  color: #fff;
  font-size: 32rpx;
  border-radius: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 500;
  margin: 0;
  box-shadow: 0 4rpx 12rpx rgba(64, 128, 255, 0.3);
}