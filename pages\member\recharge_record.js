// pages/member/recharge_record.js
// 导入 API 模块
import api from '../../utils/api';
import util from '../../utils/util';

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 导航栏相关数据
    statusBarHeight: wx.getSystemInfoSync()['statusBarHeight'],
    menuButtonHeight: wx.getMenuButtonBoundingClientRect().height,
    navBarHeight: wx.getSystemInfoSync()['statusBarHeight'] + wx.getMenuButtonBoundingClientRect().height + (wx.getMenuButtonBoundingClientRect().top - wx.getSystemInfoSync()['statusBarHeight']) * 2,

    userInfo: util.getUserInfo(),

    memberLevels: [
      {
        name: '普通会员',
        image: 'https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/wechat/assets/images/member_badge.png',
        url: '/icons/member/ordinary.png',
        progress: 0,
        total: 5
      },
      {
        name: '白银会员',
        image: 'https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/wechat/assets/images/member_silver.png',
        url: '/icons/member/silver.png',
        progress: 2,
        total: 8
      },
      {
        name: '黄金会员',
        image: 'https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/wechat/assets/images/member_gold.png',
        url: '/icons/member/gold.png',
        progress: 5,
        total: 10
      }
    ],


    // 当前显示的充值记录
    rechargeRecords: [],

    // 分页相关
    page: 1,
    pageSize: 5,
    hasMore: true,

    // 倒计时相关
    countdownTimers: {}
  },

  /**
   * 返回上一页
   */
  navigateBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 计算导航栏高度
    const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
    const systemInfo = wx.getSystemInfoSync();

    this.setData({
      statusBarHeight: systemInfo.statusBarHeight,
      menuButtonHeight: menuButtonInfo.height,
      navBarHeight: systemInfo.statusBarHeight + menuButtonInfo.height + (menuButtonInfo.top - systemInfo.statusBarHeight) * 2
    });

    // 初始加载数据 - 第一页
    this.loadInitialData();
  },

  /**
   * 加载初始数据
   */
  loadInitialData() {
    // 显示加载提示
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    // 重置分页参数
    const page = 1;
    const pageSize = this.data.pageSize;

    // 使用API接口获取充值记录
    api.user.rechargeRecord({
      page: page,
      pageSize: pageSize,
      app_id: util.getAppId()
    }).then(res => {
      console.log(res)
      // 检查API返回结果
      if (res && res.data) {
        // 更新数据
        this.setData({
          page: page,
          rechargeRecords: res.data,
          hasMore: res.data.hasMore || res.data.length >= pageSize
        });

        // 启动倒计时
        this.startCountdowns();
      } else {
        // API返回数据格式不对，使用空数组
        this.setData({
          page: page,
          rechargeRecords: [],
          hasMore: false
        });
        console.error('充值记录API返回数据格式不正确', res);
      }
      // 隐藏加载提示
      wx.hideLoading();
    }).catch(err => {
      console.error('获取充值记录失败', err);
      // 加载失败时显示错误提示
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none',
        duration: 2000
      });
      // 更新为空数据
      this.setData({
        page: page,
        rechargeRecords: [],
        hasMore: false
      });
      // 隐藏加载提示
      wx.hideLoading();
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 获取最新的用户信息并更新数据
    this.setData({
      userInfo: util.getUserInfo()
    });

    // 每次页面显示时刷新数据，确保使用最新缓存
    this.refreshData();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    // 页面隐藏时清除所有定时器
    this.clearAllCountdowns();
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 页面卸载时清除所有定时器
    this.clearAllCountdowns();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('下拉刷新触发');
    // 显示导航栏加载动画
    wx.showNavigationBarLoading();

    // 调用刷新数据函数
    this.refreshData();
  },

  /**
   * 刷新数据
   */
  refreshData() {
    // 显示导航栏加载动画
    wx.showNavigationBarLoading();

    // 重置页码，重新获取第一页数据
    const page = 1;
    const pageSize = this.data.pageSize;

    // 使用API接口获取充值记录
    api.user.rechargeRecord({
      page: page,
      pageSize: pageSize,
      app_id: util.getAppId()
    }).then(res => {
      console.log(res);
      // 检查API返回结果
      if (res && res.data) {
        // 更新页面数据
        this.setData({
          page: page,
          rechargeRecords: res.data,
          hasMore: res.data.hasMore || res.data.length >= pageSize
        });

        // 刷新数据后重新启动倒计时
        this.clearAllCountdowns();
        this.startCountdowns();

        // 显示刷新成功提示
        wx.showToast({
          title: '刷新成功',
          icon: 'success',
          duration: 1500
        });
      } else {
        // API返回数据格式不对，使用空数组
        this.setData({
          page: page,
          rechargeRecords: [],
          hasMore: false
        });
        console.error('充值记录API返回数据格式不正确', res);

        wx.showToast({
          title: '数据格式错误',
          icon: 'none',
          duration: 2000
        });
      }
    }).catch(err => {
      console.error('刷新充值记录失败', err);

      // 显示刷新失败提示
      wx.showToast({
        title: '刷新失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }).finally(() => {
      // 隐藏导航栏加载动画
      wx.hideNavigationBarLoading();

      // 停止下拉刷新动画
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    // 如果没有更多数据，则不执行加载
    if (!this.data.hasMore) {
      wx.showToast({
        title: '没有更多数据了',
        icon: 'none',
        duration: 1500
      });
      return;
    }

    // 显示加载提示
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    // 获取下一页数据
    const nextPage = this.data.page + 1;
    const pageSize = this.data.pageSize;

    // 使用API接口获取更多充值记录
    api.user.rechargeRecord({
      page: nextPage,
      pageSize: pageSize,
      app_id: util.getAppId()
    }).then(res => {
      console.log(res);
      // 检查API返回结果
      if (res && res.data) {
        // 如果返回的数据为空，则表示没有更多数据了
        if (res.data.length === 0) {
          this.setData({
            hasMore: false
          });
          wx.showToast({
            title: '没有更多数据了',
            icon: 'none',
            duration: 1500
          });
          return;
        }

        // 合并新数据
        const newRecords = [...this.data.rechargeRecords, ...res.data];

        // 更新数据
        this.setData({
          page: nextPage,
          rechargeRecords: newRecords,
          hasMore: res.data.hasMore || res.data.length >= pageSize
        });

        // 加载更多数据后，为新的待支付订单启动倒计时
        this.clearAllCountdowns();
        this.startCountdowns();
      } else {
        console.error('加载更多充值记录API返回数据格式不正确', res);
        wx.showToast({
          title: '数据格式错误',
          icon: 'none',
          duration: 1500
        });
      }
    }).catch(err => {
      console.error('加载更多充值记录失败', err);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none',
        duration: 1500
      });
    }).finally(() => {
      // 隐藏加载提示
      wx.hideLoading();
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 处理重新支付
   */
  handleRepay(e) {
    console.log(e.currentTarget.dataset)
    // 获取订单号
    const orderNo = e.currentTarget.dataset.orderNo;
    if (!orderNo) {
      wx.showToast({
        title: '订单号为空',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 显示加载提示
    wx.showLoading({
      title: '请求支付中...',
      mask: true
    });

    // 调用重新支付API
    api.wxPay.rePay({
      out_trade_no: orderNo,
      app_id: util.getAppId()
    }).then(res => {
      console.log('重新支付API返回：', res);
      wx.hideLoading();

      // 接口直接返回支付参数对象，不是{code: 0, data: {...}}格式
      if (res && res.timeStamp && res.nonceStr && res.package) {
        // 直接使用res作为支付参数
        console.log('使用返回数据作为支付参数');
        this.callWxPayment(res);
      } else {
        console.error('支付参数不完整', res);
        wx.showToast({
          title: '支付参数不完整',
          icon: 'none',
          duration: 2000
        });
      }
    }).catch(err => {
      console.error('重新支付失败', err);
      wx.hideLoading();
      wx.showToast({
        title: '支付请求失败，请重试',
        icon: 'none',
        duration: 2000
      });
    });
  },

  /**
   * 调用微信支付
   */
  callWxPayment(payParams) {
    wx.requestPayment({
      timeStamp: payParams.timeStamp,
      nonceStr: payParams.nonceStr,
      package: payParams.package,
      signType: payParams.signType,
      paySign: payParams.paySign,
      success: (result) => {
        console.log('支付成功', result);
        wx.showToast({
          title: '支付成功',
          icon: 'success',
          duration: 1500
        });
        const userInfo = util.getCacheWithExpiry('userInfo');
        //替换缓存中的level值
        // 确保level值为数字类型
        const numericLevel = parseInt(payParams.level) || 1; // 如果转换失败则默认为普通会员(1)

        userInfo.level = numericLevel;
        //直接使用接口返回的time_expire作为会员到期时间
        userInfo.level_time_expire = payParams.time_expire;
        //更新缓存
        util.setCacheWithExpiry('userInfo', userInfo);

        // 立即更新页面上的用户信息和过期时间显示
        let expirationText = '';
        if (userInfo.level_time_expire) {
          if (userInfo.level == 2 || numericLevel === 2) {
            expirationText = `白银会员于${userInfo.level_time_expire}日过期`;
          } else if (userInfo.level == 3 || numericLevel === 3) {
            expirationText = `黄金会员于${userInfo.level_time_expire}日过期`;
          } else if (userInfo.level == 1 || numericLevel === 1) {
            expirationText = `普通会员于${userInfo.level_time_expire}日过期`;
          } else {
            expirationText = `会员于${userInfo.level_time_expire}日过期`;
          }
        }

        this.setData({
          userInfo: userInfo,
          expirationText: expirationText
        });

        // 支付成功后立即刷新数据，无需等待
        this.refreshData();
      },
      fail: (err) => {
        console.log('支付取消或失败', err);
        if (err.errMsg === 'requestPayment:fail cancel') {
          wx.showToast({
            title: '支付已取消',
            icon: 'none',
            duration: 1500
          });
        } else {
          wx.showToast({
            title: '支付失败，请重试',
            icon: 'none',
            duration: 1500
          });
        }
      }
    });
  },

  /**
   * 格式化倒计时显示
   * @param {number} remainingSeconds - 剩余秒数
   * @returns {string} 格式化的倒计时字符串，如 "01:30:45"
   */
  formatCountdown(remainingSeconds) {
    if (remainingSeconds <= 0) {
      return "00:00:00";
    }

    const hours = Math.floor(remainingSeconds / 3600);
    const minutes = Math.floor((remainingSeconds % 3600) / 60);
    const seconds = remainingSeconds % 60;

    return [hours, minutes, seconds]
      .map(unit => unit.toString().padStart(2, '0'))
      .join(':');
  },

  /**
   * 启动所有待支付订单的倒计时
   */
  startCountdowns() {
    // 清除所有现有定时器
    this.clearAllCountdowns();

    // 创建倒计时定时器对象
    const countdownTimers = {};

    // 处理每个充值记录
    this.data.rechargeRecords.forEach((record, index) => {
      // 仅为待支付的订单启动倒计时
      if (record.status === '待支付' && record.trade_state === 1 && record.time_expire) {
        // 计算剩余时间（秒）
        const expireTime = new Date(record.time_expire).getTime();
        const now = new Date().getTime();
        let remainingSeconds = Math.floor((expireTime - now) / 1000);

        // 如果已过期，设置为0
        if (remainingSeconds < 0) {
          remainingSeconds = 0;
        }

        // 更新初始倒计时显示
        const updatedRecords = [...this.data.rechargeRecords];
        updatedRecords[index].countdownDisplay = this.formatCountdown(remainingSeconds);
        updatedRecords[index].countdownSeconds = remainingSeconds;

        this.setData({
          rechargeRecords: updatedRecords
        });

        // 如果还有时间，设置定时器
        if (remainingSeconds > 0) {
          // 创建定时器，每秒更新一次倒计时
          const timerId = setInterval(() => {
            this.updateCountdown(record.out_trade_no);
          }, 1000);

          // 存储定时器ID，以订单号为键
          countdownTimers[record.out_trade_no] = timerId;
        }
      }
    });

    // 更新定时器集合
    this.setData({
      countdownTimers
    });
  },

  /**
   * 更新指定订单的倒计时
   * @param {string} orderNo - 订单号
   */
  updateCountdown(orderNo) {
    const records = [...this.data.rechargeRecords];
    const recordIndex = records.findIndex(r => r.out_trade_no === orderNo);

    if (recordIndex === -1) return;

    const record = records[recordIndex];
    let remainingSeconds = record.countdownSeconds - 1;

    // 更新倒计时显示
    records[recordIndex].countdownDisplay = this.formatCountdown(remainingSeconds);
    records[recordIndex].countdownSeconds = remainingSeconds;

    this.setData({
      rechargeRecords: records
    });

    // 如果倒计时结束
    if (remainingSeconds <= 0) {
      // 清除定时器
      clearInterval(this.data.countdownTimers[orderNo]);

      // 从定时器集合中移除
      const updatedTimers = { ...this.data.countdownTimers };
      delete updatedTimers[orderNo];

      this.setData({
        countdownTimers: updatedTimers
      });

      // 显示订单超时弹窗
      wx.showModal({
        title: '订单提示',
        content: '订单已超时',
        showCancel: false,
        success: (res) => {
          if (res.confirm) {
            // 用户点击确定后刷新数据
            this.refreshData();
          }
        }
      });
    }
  },

  /**
   * 清除所有倒计时定时器
   */
  clearAllCountdowns() {
    const timers = this.data.countdownTimers;

    // 清除所有定时器
    Object.values(timers).forEach(timerId => {
      clearInterval(timerId);
    });

    // 重置定时器集合
    this.setData({
      countdownTimers: {}
    });
  },

  /**
   * 处理取消订单
   */
  handleCancelOrder(e) {
    // 获取订单号
    const orderNo = e.currentTarget.dataset.orderNo;
    if (!orderNo) {
      wx.showToast({
        title: '订单号为空',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 显示确认弹窗
    wx.showModal({
      title: '确认取消',
      content: '确定要取消此订单吗？',
      success: (res) => {
        if (res.confirm) {
          // 用户确认取消
          this.cancelOrder(orderNo);
        }
      }
    });
  },

  /**
   * 执行取消订单
   */
  cancelOrder(orderNo) {
    // 显示加载提示
    wx.showLoading({
      title: '取消中...',
      mask: true
    });

    // 调用取消订单API
    api.user.cancelOrder({
      out_trade_no: orderNo,
      app_id: util.getAppId()
    }).then(res => {
      console.log('取消订单API返回：', res);
      wx.hideLoading();

      // 检查API返回结果
      if (res && res.code === 0) {
        wx.showToast({
          title: '订单已取消',
          icon: 'success',
          duration: 1500
        });
        // 刷新数据
        this.refreshData();
      } else {
        wx.showToast({
          title: res.msg || '取消失败',
          icon: 'none',
          duration: 2000
        });
      }
    }).catch(err => {
      console.error('取消订单失败', err);
      wx.hideLoading();
      wx.showToast({
        title: '取消失败，请重试',
        icon: 'none',
        duration: 2000
      });
    });
  }
})