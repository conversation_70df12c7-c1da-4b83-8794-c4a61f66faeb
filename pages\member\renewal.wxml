<!--pages/member/renewal.wxml-->
<view class="container">
    <!-- 渐变背景 -->
    <view class="gradient-bg"></view>

    <!-- 自定义导航栏 -->
    <view
        class="custom-navbar"
        style="height: {{navBarHeight}}px;"
    >
        <view
            class="status-bar"
            style="height: {{statusBarHeight}}px;"
        ></view>
        <view class="navbar-content">
            <view
                class="nav-back"
                bindtap="goBack"
            >
                <van-icon
                    name="arrow-left"
                    size="20px"
                    color="#333"
                />
            </view>
            <view class="nav-title">续费管理</view>
            <view class="nav-placeholder"></view>
        </view>
    </view>

    <!-- 内容区域 -->
    <view
        class="content"
        style="margin-top: {{navBarHeight}}px;"
    >
        <!-- 商家信息 -->
        <view class="merchant-info">
            <image
                class="merchant-logo"
                src="{{merchantLogo}}"
                mode="aspectFill"
            ></image>
            <view class="merchant-name">{{merchantName}}</view>
        </view>

        <!-- 自动续费服务 -->
        <view class="section-title">已开通的自动续费服务</view>

        <!-- 续费管理卡片 - 整体白色卡片 -->
        <view class="renewal-card">
            <!-- 会员卡信息 -->
            <view class="member-card">
                <view class="member-card-header">
                    <image
                        class="member-icon"
                        src="{{memberIcon}}"
                        mode="aspectFill"
                    ></image>
                    <view class="member-type">{{memberType}}</view>
                    <view class="member-status">已开启</view>
                </view>
                <view class="member-card-content">
                    <view class="member-info-item">
                        <text>有效期至：{{validUntil}}</text>
                    </view>
                    <view class="member-info-item">
                        <text>下次自动续费时间：{{nextRenewalDate}}</text>
                    </view>
                    <view class="member-info-desc">
                        <text>若要终止续费服务合同期限，请至少在下次扣款的24小时前关闭。</text>
                    </view>
                </view>
            </view>

            <!-- 支付方式 -->
            <view class="payment-method">
                <view class="payment-method-title">
                    <view class="wechat-pay-icon">
                        <van-icon
                            name="wechat-pay"
                            size="20px"
                            color="#09BB07"
                        />
                    </view>
                    <text>微信支付</text>
                </view>
                <view
                    class="payment-switch"
                    bindtap="closeAutoRenewal"
                >
                    <text class="switch-text">关闭</text>
                    <van-icon
                        name="arrow"
                        size="16px"
                        color="#999"
                    />
                </view>
            </view>

            <!-- 支付说明 -->
            <view class="payment-description">
                <text>微信支付会在扣款时，提醒您注意账户余额，请确保有足够的余额。</text>
            </view>
        </view>
    </view>
</view>