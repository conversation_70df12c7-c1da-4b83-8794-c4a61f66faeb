<view class="staff-select-container">
  <!-- 顶部新增按钮 -->
  <view class="header">
    <view class="add-btn" bindtap="onAdd">+新增</view>
  </view>

  <!-- 报价人列表 -->
  <view class="staff-list">
    <view class="staff-item {{selectedIndex === index ? 'selected' : ''}}" wx:for="{{staffList}}" wx:key="index" bindtap="onSelect" data-index="{{index}}">
      <view class="select-circle {{selectedIndex === index ? 'selected' : ''}}"></view>
      <view class="staff-info">
        <view class="name">{{item.name}}</view>
        <view class="phone">{{item.phone}}</view>
        <view class="email">{{item.email}}</view>
      </view>
      <view class="action-buttons">
        <view class="delete-btn" catchtap="onDelete" data-index="{{index}}">删除</view>
        <view class="edit-btn" catchtap="onEdit" data-index="{{index}}">编辑</view>
      </view>
    </view>
  </view>

  <!-- 底部选择按钮 -->
  <view class="bottom-btn" bindtap="onConfirm">选择</view>
</view> 