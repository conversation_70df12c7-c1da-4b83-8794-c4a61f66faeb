/* 引入登录弹窗样式 */
@import "/templates/loginPopup/loginPopup.wxss";

/* 全局溢出控制 - 防止横向滚动 */
page {  
  overflow-x: hidden;  
  width: 100%;  
  box-sizing: border-box;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
}
.container {  
  padding: 0;  
  background-color: transparent;  
  width: 100%;  
  box-sizing: border-box;  
  overflow-x: hidden; /* 防止横向滚动 */  
  position: relative; /* 添加相对定位 */
}
/* 新的搜索栏样式 */
.search-bar {  
  padding: 20rpx 0;  
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  width: 100%;  
  box-sizing: border-box;
}
.search-icon {  
  margin-left: 20rpx;  
  color: #999;  
  display: flex;  
  align-items: center;  
  justify-content: center;
}
.search-input {  
  flex: 1;  
  height: 100%;  
  padding: 0 110rpx 0 15rpx;  
  font-size: 28rpx;  
  background-color: transparent;  
  border: none;  
  color: #333;
}
.search-btn {  
  height: 56rpx;  
  line-height: 56rpx;  
  background: linear-gradient(to right, #1C82F5, #27AFF5);  
  color: #fff;  
  font-size: 28rpx;  
  text-align: center;  
  padding: 0 30rpx;  
  border-radius: 16rpx;  
  position: absolute;  
  right: 7rpx;  
  top: 50%;  
  transform: translateY(-50%);  
  box-shadow: 0 2rpx 8rpx rgba(56, 136, 255, 0.3);  
  transition: all 0.2s ease-in-out;
}
.search-btn:active {  
  background: linear-gradient(to right, #1973dd, #24a0e5);  
  box-shadow: 0 1rpx 3rpx rgba(56, 136, 255, 0.2);  
  transform: translateY(-50%) scale(0.98);
}
/* 筛选条件栏 */.filter-tabs {  width: 100%;}/* 自定义 van-tabs 样式 */.filter-tabs .van-tabs__nav {  display: flex !important;}.filter-tabs .van-tab {  -webkit-box-flex: 1 !important;  flex: 1 !important;  min-width: 0 !important;}.filter-tabs .van-tabs__line {  width: 40rpx !important;  background-color: #ff0000 !important;}.filter-nav {  width: 100%;}.filter-tab {  flex: 1 !important;  min-width: 100% !important;  font-size: 28rpx;}.filter-tab-active {  font-weight: bold;  color: #ff0000 !important;}/* 车辆列表 */.car-list {  padding: 0;  background-color: transparent;  width: 100%;  padding-bottom: 120rpx; /* 保留原有的底部内边距，考虑 TabBar 的高度 */  overflow-x: hidden;  margin-top: 200rpx;}.car-item {  background-color: #fff;  margin-bottom: 20rpx;  overflow: hidden;  border-radius: 0;  box-shadow: none;  padding: 20rpx 30rpx;  width: 100%;  box-sizing: border-box;}.car-item-content {  display: flex;  padding: 20rpx 30rpx; /* 增加左右内边距 */  width: 100%;  box-sizing: border-box;  max-width: 100vw; /* 最大宽度不超过视窗宽度 */  overflow: hidden; /* 防止内容溢出 */}/* 左侧图片容器 */.car-image-container {  flex-shrink: 0;  margin-left: 20rpx; /* 添加左边距 */  margin-right: 20rpx;  width: 240rpx; /* 固定宽度 */  height: 220rpx; /* 增加高度，原来是180rpx */  box-sizing: border-box;  overflow: hidden; /* 防止内容溢出 */}.car-image {  border-radius: 8rpx;  overflow: hidden;  width: 100%;  height: 100%;  object-fit: cover; /* 确保图片填满容器且不变形 */}/* 右侧内容 */.car-info {  flex: 1;  display: flex;  flex-direction: column;  justify-content: space-between; /* 改为在容器中均匀分布 */  min-width: 0; /* 确保文本可以正确截断 */  padding-right: 20rpx; /* 添加右边距 */  overflow: hidden; /* 防止内容溢出 */  width: calc(100% - 240rpx - 40rpx); /* 计算实际可用宽度 */  box-sizing: border-box;  height: 220rpx; /* 与图片高度保持一致 */}.car-title {  font-size: 28rpx;  font-weight: bold;  margin-bottom: 8rpx; /* 减小底部间距 */  overflow: hidden;  display: -webkit-box;  -webkit-line-clamp: 2;  -webkit-box-orient: vertical;  max-width: 100%; /* 限制最大宽度 */}.car-desc {  font-size: 24rpx;  color: #666;  margin-bottom: 8rpx; /* 减小底部间距 */}.car-price {  display: flex;  align-items: center;  margin-bottom: 8rpx; /* 减小底部间距 */}/* 新车指导价 */.sale-price {  font-size: 30rpx;  font-weight: bold;  color: #ff0000;  margin-left: 10rpx; /* 从右边距改为左边距 */}/* 新车价格 */.new-price {  font-size: 22rpx;  color: #999;  text-decoration: line-through; /* 添加横线效果 */}/* 车辆标签 */.car-tags {  display: flex;  flex-wrap: wrap;  max-width: 100%; /* 限制宽度 */  overflow: hidden; /* 防止标签溢出 */  padding-top: 10rpx;}.action-tag {  margin-right: 10rpx;  margin-bottom: 6rpx;  font-size: 20rpx !important;  padding: 2rpx 8rpx !important;}/* 底部区域样式 */.car-bottom {  display: flex;  justify-content: space-between;  align-items: center;  font-size: 22rpx;  color: #999;  margin-top: auto; /* 将底部信息推到底部 */  width: 100%;  box-sizing: border-box;  overflow: hidden; /* 防止内容溢出 */}.car-location {  max-width: 50%;  white-space: nowrap;  overflow: hidden;  text-overflow: ellipsis;}.car-time {  font-size: 22rpx;  color: #999;  padding-left: 10rpx;  text-align: right;  white-space: nowrap;  overflow: hidden;  text-overflow: ellipsis;  max-width: 40%; /* 限制最大宽度 */}.car-actions {  padding: 20rpx;  border-top: 1rpx solid #eee;  display: flex;  flex-wrap: wrap;  align-items: center;  width: 100%;  box-sizing: border-box;}.action-buttons {  display: flex;  margin-left: auto;}.action-button {  margin-left: 10rpx !important;  border: none !important;}.custom-filter-bar {  width: 100%;  background-color: #fff;  padding: 20rpx 0;  border-bottom: 1rpx solid #eee;  box-sizing: border-box;}.custom-filter-item {  width: 100%;  text-align: center;  font-size: 28rpx;  color: #333;  position: relative;  padding-bottom: 10rpx;}.custom-filter-item.active {  color: #ff0000;  font-weight: bold;}.custom-filter-line {  position: absolute;  bottom: -10rpx;  left: 50%;  transform: translateX(-50%);  width: 40rpx;  height: 4rpx;  background-color: #ff0000;}.custom-tabs {  width: 100%;  background-color: #fff;  border-bottom: 1rpx solid #eee;}.custom-tab {  flex: 1;  text-align: center;  font-size: 28rpx;  color: #333;  padding: 20rpx 0;  position: relative;}.custom-tab.active {  color: #ff0000;  font-weight: bold;}.custom-tab.active::after {  content: '';  position: absolute;  bottom: 0;  left: 50%;  transform: translateX(-50%);  width: 40rpx;  height: 4rpx;  background-color: #ff0000;}.tab-content {  width: 100%;  background-color: #fff;}.van-search__content {  padding-left: 10rpx !important;  padding-right: 10rpx !important;}.van-field__input {  min-height: 28px !important;}/* 确保下拉菜单横向铺满 */.custom-tabs {  width: 100%;  background-color: #fff;  border-bottom: 1rpx solid #eee;}.filter-dropdown-menu {  width: 100% !important;}/* 强制覆盖 Vant 组件样式 */.van-dropdown-menu {  width: 100% !important;  display: flex !important;  height: 48px !important;  line-height: 48px !important;  transition: all 0.3s ease-in-out !important;}.van-dropdown-menu__item {  flex: 1 !important;  width: 25% !important; /* 强制宽度为 25%，确保四个菜单项平均分配 */  text-align: center !important;}/* 调整菜单标题样式 */.van-dropdown-menu__title {  padding: 0 !important;  max-width: none !important;  font-size: 28rpx !important;  display: inline-block !important;  position: relative !important;}/* 修复下拉箭头位置 */.van-dropdown-menu__title::after {  position: relative !important;  display: inline-block !important;  margin-left: 4px !important;  border-color: #999 transparent transparent !important;  border-style: solid !important;  border-width: 4px 4px 0 !important;  content: "" !important;  transition: transform 0.3s !important;  transform: translateY(-50%) !important;  transform-origin: center !important;}/* 修复激活状态下的箭头 */.van-dropdown-menu__item--active .van-dropdown-menu__title::after {  transform: rotate(180deg) !important;  margin-top: -3px !important;}/* 自定义下拉菜单标题样式 */.custom-title {  display: flex;  align-items: center;  justify-content: space-between;  width: 100%;  padding: 0 10px;}/* 下拉箭头样式 */.dropdown-arrow {  flex-shrink: 0;  transition: transform 0.3s ease-in-out !important;}/* 隐藏原有的下拉箭头 */.van-dropdown-menu__title::after {  display: none !important;}/* 激活状态下箭头旋转 */.van-dropdown-item--active .dropdown-arrow {  transform: rotate(180deg);  transition: transform 0.3s ease-in-out !important;}/* 使用文本作为下拉箭头 */.dropdown-arrow-text {  margin-left: 8px;  font-size: 12px;  color: #333;}/* 激活状态下箭头旋转 */.van-dropdown-item--active .dropdown-arrow-text {  transform: rotate(180deg);  display: inline-block;  transition: transform 0.3s ease-in-out !important;}/* 使用 CSS 绘制三角形 */.css-arrow {  width: 0;  height: 0;  margin-left: 8px;  border-left: 5px solid transparent;  border-right: 5px solid transparent;  border-top: 5px solid #333;  transition: transform 0.3s ease-in-out !important;}/* 激活状态下箭头旋转 */.van-dropdown-item--active .css-arrow {  transform: rotate(180deg);}/* 页面遮罩层 */.page-mask {  position: fixed;  top: 0;  left: 0;  width: 100%;  height: 100%;  background-color: transparent;  z-index: 900;  opacity: 0;  transition: opacity 0.3s ease-in-out !important;  pointer-events: none;}.page-mask.show {  opacity: 1;  pointer-events: auto;}/* 优化下拉菜单过渡动画 */.van-dropdown-item {  transition: all 0.3s ease-in-out !important;}.van-dropdown-item__content {  transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out !important;  transform-origin: top center !important;}/* 遮罩层动画 */.van-overlay {  transition: opacity 0.3s ease-in-out !important;}/* 下拉选项动画 */.van-cell {  transition: background-color 0.2s ease-in-out !important;}/* 新的筛选菜单样式 */.filter-menu {  display: flex;  width: 100%;  height: 80rpx;  background-color: #fff;  border-top: 1rpx solid #f0f0f0;  border-bottom: 1rpx solid #f0f0f0;}.filter-item {  flex: 1;  display: flex;  justify-content: center;  align-items: center;  font-size: 26rpx;  color: #333;}.filter-item text {  margin-right: 4rpx;}.filter-arrow {  color: #999;}/* 筛选区域容器 */.filter-container {  position: relative;  z-index: 100;  width: 100%;  background-color: #fff;  margin-left: -20rpx; /* 如果容器有左内边距 */  margin-right: -20rpx; /* 如果容器有右内边距 */  width: calc(100% + 40rpx); /* 调整宽度以补偿负边距 */}/* 筛选内容 */.filter-content {  position: absolute;  top: 100%; /* 定位在筛选菜单下方 */  left: 0;  width: 100%;  background: #fff;  z-index: 99;  max-height: 0;  overflow: hidden;  transition: max-height 0.3s ease;  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);}.filter-content.show {  max-height: 400px; /* 或其他适当的高度 */}/* 为了更好的滚动体验，添加滚动条样式 */.filter-content::-webkit-scrollbar {  width: 4px;}.filter-content::-webkit-scrollbar-thumb {  background-color: #ddd;  border-radius: 2px;}/* 筛选选项容器 */.filter-options {  padding: 10rpx 20rpx;  max-height: 600rpx;  overflow-y: auto;  overflow-x: hidden; /* 防止水平滚动 */  box-sizing: border-box;}.filter-options::before,.filter-options::after {  content: '';  position: absolute;  left: 0;  right: 0;  height: 20rpx;  pointer-events: none;  z-index: 1;}.filter-options::before {  top: 0;  background: linear-gradient(to bottom, rgba(255,255,255,1) 0%, rgba(255,255,255,0) 100%);}.filter-options::after {  bottom: 0;  background: linear-gradient(to top, rgba(255,255,255,1) 0%, rgba(255,255,255,0) 100%);}/* 筛选选项 */.filter-option {  padding: 20rpx 30rpx;  font-size: 28rpx;  color: #333;  border-bottom: 1rpx solid #eee;}.filter-option.active {  color: #ff0000;  background-color: #f7f7f7;}/* 遮罩层 */.filter-mask {  position: fixed;  top: 0;  left: 0;  right: 0;  bottom: 0;  background-color: rgba(0, 0, 0, 0.5);  z-index: 99;  display: none;  width: 100%;  height: 100%;  box-sizing: border-box;}.filter-mask.show {  display: block;}/* 车辆列表中的最后一个卡片 */.car-list .car-item:last-child {  margin-bottom: 30rpx; /* 为最后一张卡片添加底部外边距 */}/* 确保地区选择器和筛选栏的样式互不影响 */.global-mask {    position: fixed;    top: 0;    left: 0;    right: 0;    bottom: 0;    background-color: rgba(0, 0, 0, 0.5);    z-index: 1001; /* 提高 z-index，确保在筛选栏之上 */    opacity: 0;    visibility: hidden;    transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;}.global-mask.show {    opacity: 1;    visibility: visible;}/* 确保地区选择弹出层在全局遮罩层之上 */.van-popup {    z-index: 1002 !important; /* 提高 z-index，确保在全局遮罩层之上 */}/* 顶部弹出的城市选择器样式 */.city-popup {  width: 100%;  max-height: 70vh; /* 限制最大高度为视口高度的70% */  overflow-y: auto;  border-bottom-left-radius: 16rpx;  border-bottom-right-radius: 16rpx;  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);}/* 自定义 van-area 组件样式 */.custom-area .van-picker__toolbar {  height: 88rpx;}.custom-area .van-picker__title {  font-size: 32rpx;  font-weight: bold;}.custom-area .van-picker__cancel,.custom-area .van-picker__confirm {  font-size: 28rpx;  padding: 0 30rpx;}/* 使用 !important 提高优先级 */.filter-content,.filter-options,.filter-option {  transform: translateX(0) !important;  left: 0 !important;  right: 0 !important;  width: 100% !important;}.area-container {  background: #fff;}.all-city {  padding: 20rpx 30rpx;  font-size: 28rpx;  color: #333;  border-bottom: 1px solid #eee;}.all-city:active {  background-color: #f7f7f7;}/* 筛选标题样式 */.filter-title {  opacity: 0;  transform: translateY(-100%);  pointer-events: none;  display: flex;  justify-content: center;  align-items: center;  height: 100%;  background: #fff;  z-index: 1002; /* 确保筛选标题在最上层 */  visibility: hidden; /* 添加隐藏属性 */  transition: opacity 0.3s ease, transform 0.3s ease, visibility 0.3s ease;}.filter-title.visible {  opacity: 1;  transform: translateY(0);  pointer-events: auto;  visibility: visible; /* 显示时恢复可见性 */}

.filter-close {  position: absolute;  right: 30rpx;  top: 50%;  transform: translateY(-50%);  font-size: 24rpx;  color: #999;  padding: 10rpx;  z-index: 1003;}
.top-container {  
  position: relative;  
  width: 100%;  
  box-sizing: border-box;  
  z-index: 1001;  
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);  
  overflow: hidden;  
  padding: 0; /* 移除可能的内边距 */
}
/* 搜索区域 */
.search-bar, .filter-title {  
  position: absolute;  
  top: 0;  
  left: 0;  
  width: 100%;  
  padding: 0; /* 移除可能的内边距 */  
  transition: opacity 0.2s ease, transform 0.2s ease;
}
.search-bar {  
  padding: 20rpx 0; /* 只保留上下内边距，移除左右内边距 */  
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  width: 100%;  
  box-sizing: border-box;
}
.search-bar.hidden {  
  opacity: 0;  
  transform: translateY(-100%);  
  pointer-events: none;  
  z-index: -1; /* 添加负z-index确保它完全隐藏 */
}
.search-input-wrapper {  
  display: flex;  
  height: 70rpx;  
  width: calc(100% - 40rpx); /* 减去左右边距的总和 */  
  box-sizing: border-box;  
  border-radius: 16rpx;  
  overflow: hidden;  
  margin: 0 20rpx; /* 如果需要与边缘保持一定距离，可以使用外边距代替内边距 */ 
  background-color: #fff;
  position: relative;
  border: 1rpx solid #1C82F5;
}
.search-icon {  
  margin-left: 20rpx;  
  color: #999;  
  display: flex;  
  align-items: center;  
  justify-content: center;
}
.search-input {  
  flex: 1;  
  height: 100%;  
  padding: 0 110rpx 0 15rpx;  
  font-size: 28rpx;  
  background-color: transparent;  
  border: none;  
  color: #333;
}
.search-btn {  
  height: 56rpx;  
  line-height: 56rpx;  
  background: linear-gradient(to right, #1C82F5, #27AFF5);  
  color: #fff;  
  font-size: 28rpx;  
  text-align: center;  
  padding: 0 30rpx;  
  border-radius: 16rpx;  
  position: absolute;  
  right: 7rpx;  
  top: 50%;  
  transform: translateY(-50%);  
  box-shadow: 0 2rpx 8rpx rgba(56, 136, 255, 0.3);  
  transition: all 0.2s ease-in-out;
}
.search-btn:active {  
  background: linear-gradient(to right, #1973dd, #24a0e5);  
  box-shadow: 0 1rpx 3rpx rgba(56, 136, 255, 0.2);  
  transform: translateY(-50%) scale(0.98);
}
/* 筛选弹出层样式 */
.filter-popup {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
}

.custom-nav {
  width: 100%;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  position: relative;
}

.status-bar {
  width: 100%;
}

.nav-title {
  height: 44px;
  line-height: 44px;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  position: relative;
}

.filter-popup-header {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 88rpx;
  position: relative;
  border-bottom: 1px solid #eee;
}

.filter-popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.filter-popup-close {
  position: absolute;
  right: 30rpx;
  font-size: 28rpx;
  color: #999;
  padding: 20rpx;
}

.filter-popup-content {
  flex: 1;
  overflow-y: auto;
}

.filter-section {
  padding: 30rpx;
  border-bottom: 1px solid #eee;
}

.section-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: bold;
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
  padding: 0 15rpx;
}

.slider-labels text {
  font-size: 24rpx;
  color: #666;
  transform: translateX(-50%);
}

/* 自定义滑块样式 */
.van-slider {
  margin: 30rpx 0;
}

.van-slider__button {
  width: 40rpx !important;
  height: 40rpx !important;
  background-color: #fff !important;
  border: 2rpx solid #ffc62c !important;
}

.van-slider__bar {
  background-color: #ffc62c !important;
}

.filter-popup-footer {
  display: flex;
  padding: 20rpx 30rpx;
  border-top: 1px solid #eee;
}

.reset-btn, .confirm-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 10rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.reset-btn {
  background: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background: #ffc62c;
  color: #fff;
}

/* van-area 组件的自定义样式 */
.van-area {
  height: 400rpx;
}

.van-area__title {
  font-size: 28rpx;
  color: #333;
  padding: 20rpx;
}

.van-picker-column__item {
  font-size: 28rpx;
}

.van-picker-column__item--selected {
  color: #ffc62c;
  font-weight: bold;
}

/* 地区选择器样式优化 */
.filter-section .van-area {
  height: 400rpx;
}

.filter-section .van-picker-column {
  height: 400rpx !important;
}

.filter-section .van-picker-column__item {
  font-size: 28rpx;
  line-height: 80rpx;
}

.filter-section .van-picker-column__item--selected {
  color: #ffc62c;
  font-weight: bold;
}

/* 确保选择器内容居中显示 */
.filter-section .van-picker-column__wrapper {
  height: 400rpx !important;
}

/* 固定头部样式 */
.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  background-color: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 头部占位符，防止内容被固定头部遮挡 */
.header-placeholder {
  width: 100%;
  height: calc(var(--top-container-height, 20px));
}

/* 确保筛选内容在固定头部下方显示 */
.filter-content.show {
  position: fixed;
  top: calc(var(--top-container-height, 50px) + var(--filter-menu-height, 44px));
  left: 0;
  width: 100%;
  z-index: 99;
}

/* 新的车辆列表样式 - 与上传图片匹配 */
.car-list {
  padding: 0;
  background-color: transparent;
  width: 100%;
  padding-bottom: 120rpx; /* 保留原有的底部内边距，考虑 TabBar 的高度 */
  overflow-x: hidden;
  /* margin-top: 200rpx; */
}

.car-item {
  background-color: #fff;
  margin-bottom: 20rpx;
  overflow: hidden;
  border-radius: 0;
  box-shadow: none;
  padding: 20rpx 30rpx;
  width: 100%;
  box-sizing: border-box;
}

/* 用户信息区域 */
.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
  position: relative; /* 添加相对定位，用于倒计时标签的绝对定位 */
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  background-color: #eee; /* 默认背景色 */
}

.user-meta {
  flex: 1;
}

.user-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
}

.post-time {
  font-size: 24rpx;
  color: #999;
}

/* 内容区域 */
.post-content {
  margin-bottom: 20rpx;
  position: relative;
}

.post-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.info-grid {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  margin-bottom: 15rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
}

.info-item {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  width: 300rpx;
}

.subtitle {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.post-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 15rpx;
}

/* 收起状态的描述文本 */
.post-desc.collapsed {
  display: -webkit-box;
  -webkit-line-clamp: 2; /* 最多显示2行 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 28rpx;
}

/* 展开状态的描述文本 */
.post-desc.expanded {
  display: block;
}

/* 全文按钮样式 */
.show-more {
  font-size: 28rpx;
  color: #ffc62c;
  font-weight: bold;
  margin-bottom: 15rpx;
  display: inline-block;
}

/* 图片区域 */
.post-images {
  display: flex;
  margin-bottom: 20rpx;
}

.image-item {
  flex: 1;
  height: 200rpx;
  margin-right: 10rpx;
  border: 1rpx solid #eee;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

.image-item:last-child {
  margin-right: 0;
}

.image-item image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 底部操作栏 */
.post-actions {
  padding: 16rpx 20rpx;
  /* border-top: 1px solid #f0f0f0; */
}

.post-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.views-item {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999;
}

.views-item image {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

.right-actions {
  display: flex;
  align-items: center;
}

.stat-item {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;
  margin:0 20rpx;
}

.stat-item image {
  width: 36rpx;
  height: 36rpx;
  /* margin-right: 8rpx; */
  display: block;
  align-self: center;
  transform: translateY(-4rpx);
}

.stat-item:first-child image {
  transform: translateY(-8rpx);
}

.stat-item text {
  align-self: center;
  margin-left: 8rpx;
}

/* .stat-item.liked {
  color: #ff4757;
} */

.stat-item.liked image {
  opacity: 1;
  filter: none;
}

.share-button {
  background: transparent;
  padding: 0;
  margin: 0;
  line-height: 1;
  border: none;
  font-size: 24rpx;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36rpx;
  height: 36rpx;
}

.share-button::after {
  border: none;
}

.share-button image {
  width: 36rpx;
  height: 36rpx;
  margin: 0;
}

/* 标签容器 - 新增 */
.car-tags-container {
  padding: 0 20rpx 15rpx 50rpx; /* 内边距为左侧30rpx(car-item-content) + 20rpx(car-image-container) = 50rpx */
  /* 移除顶部边框 */
  margin-top: -20rpx; /* 增加负边距，让标签向上移动更多，使其与图片更接近 */
  width: 100%;
  box-sizing: border-box;
}

/* 悬浮发帖按钮 */
.post-button {
  position: fixed;
  right: 30rpx;
  bottom: 100rpx;
  background: #1C82F5;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(25, 137, 250, 0.4);
  z-index: 999;
  width: 100rpx;
  height: 100rpx;
}

.post-icon {
  width: 50rpx;
  height: 50rpx;
  margin-right: 0;
}

.post-button text {
  font-size: 28rpx;
  font-weight: bold;
}

.post-stats .share-button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  line-height: normal;
  border-radius: 0;
  outline: none;
  box-sizing: content-box;
  font-size: inherit;
  color: inherit;
  font-weight: normal;
  display: flex;
  align-items: center;
  height: 36rpx;
}

.post-stats .share-button::after {
  display: none;
}

/* 倒计时标签 */
.countdown-badge {
  position: absolute;
  right: 0;
  top: 0;
  background: linear-gradient(135deg, #ff9500, #ff5e3a);
  color: #fff;
  font-size: 22rpx;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 8rpx rgba(255, 94, 58, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  transform: translateY(-4rpx);
}

/* 倒计时图标 */
.countdown-icon {
  margin-right: 6rpx;
  font-size: 24rpx;
}

.plus-icon {
  font-size: 60rpx;
  font-weight: bold;
  color: white;
  line-height: 1;
}

/* 骨架屏样式 */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes skeleton-pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

.skeleton-screen {
  position: fixed; /* 改为fixed定位，确保覆盖整个视口 */
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh; /* 使用视口高度 */
  min-height: 100vh; /* 添加最小高度 */
  z-index: 999;
  background-color: #fff;
  padding: 0;
  box-sizing: border-box;
  overflow-y: auto;
  animation: skeleton-pulse 2s infinite ease-in-out;
  padding-bottom: 100rpx; /* 确保底部有足够空间 */
}

/* 骨架导航栏样式 */
.skeleton-nav {
  width: 100%;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  position: relative;
  z-index: 1000;
}

.skeleton-status-bar {
  width: 100%;
}

.skeleton-nav-title {
  height: 44px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.skeleton-nav-title::after {
  content: "";
  width: 100rpx;
  height: 40rpx;
  border-radius: 4rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* 骨架搜索栏 */
.skeleton-search-bar {
  padding: 20rpx;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  margin-bottom: 20rpx;
}

.skeleton-search-input {
  height: 70rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 16rpx;
}

/* 骨架内容列表 */
.skeleton-content {
  padding: 0 20rpx;
  margin-top: 20rpx;
  padding-bottom: 50rpx; /* 确保列表底部有足够的空间 */
}

/* 骨架卡片 */
.skeleton-card {
  background-color: #fff;
  margin-bottom: 20rpx;
  padding: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  height: auto; /* 改为auto高度，适应内容 */
  min-height: 200rpx; /* 设置最小高度 */
}

/* 骨架卡片加载效果 */
.skeleton-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -150%;
  width: 150%;
  height: 100%;
  background: linear-gradient(to right, transparent 0%, rgba(255,255,255,0.6) 50%, transparent 100%);
  animation: skeleton-card-loading 1.8s infinite linear;
  z-index: 1;
}

@keyframes skeleton-card-loading {
  0% {
    left: -150%;
  }
  100% {
    left: 150%;
  }
}

/* 骨架用户信息 */
.skeleton-user-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  position: relative;
  z-index: 2;
}

.skeleton-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  margin-right: 20rpx;
}

.skeleton-user-meta {
  flex: 1;
}

.skeleton-username {
  width: 150rpx;
  height: 32rpx;
  border-radius: 4rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  margin-bottom: 10rpx;
}

.skeleton-time {
  width: 100rpx;
  height: 24rpx;
  border-radius: 4rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* 骨架帖子内容 */
.skeleton-post-content {
  margin-bottom: 20rpx;
  position: relative;
  z-index: 2;
}

.skeleton-post-title {
  width: 80%;
  height: 36rpx;
  border-radius: 4rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  margin-bottom: 20rpx;
}

.skeleton-info-grid {
  margin-bottom: 20rpx;
}

.skeleton-info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.skeleton-info-item {
  width: 45%;
  height: 30rpx;
  border-radius: 4rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.skeleton-post-desc {
  width: 100%;
  height: 60rpx;
  border-radius: 4rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* 骨架图片区域 */
.skeleton-post-images {
  display: flex;
  margin-bottom: 20rpx;
  position: relative;
  z-index: 2;
  overflow-x: auto; /* 允许横向滚动 */
  flex-wrap: nowrap; /* 不换行 */
}

.skeleton-image {
  flex: 0 0 auto; /* 不拉伸，不压缩 */
  width: 30%; /* 宽度为容器的30% */
  max-width: 200rpx;
  height: 180rpx; /* 降低高度 */
  border-radius: 8rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  margin-right: 10rpx;
}

/* 骨架底部操作栏 */
.skeleton-post-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
  position: relative;
  z-index: 2;
}

.skeleton-views {
  width: 120rpx;
  height: 28rpx;
  border-radius: 4rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.skeleton-actions-right {
  display: flex;
  align-items: center;
}

.skeleton-action {
  width: 60rpx;
  height: 28rpx;
  border-radius: 4rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  margin-left: 20rpx;
}

/* 骨架加载提示 */
.skeleton-loading-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 30rpx 0;
  padding: 20rpx;
}

.skeleton-loading-bar {
  width: 80%;
  height: 6rpx;
  border-radius: 3rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  margin-bottom: 15rpx;
}

.skeleton-loading-text {
  width: 240rpx;
  height: 30rpx;
  border-radius: 4rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* 修复头部占位符高度 */
.header-placeholder {
  margin-top: 0;
  z-index: 100;
}

/* 登录提示弹窗 */
.login-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.login-popup-content {
  width: 390rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.login-popup-close {
  position: absolute;
  top: -20rpx;
  right: -20rpx;
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background-color: #FFFFFF;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  z-index: 10;
}

.login-popup-close::before,
.login-popup-close::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20rpx;
  height: 2rpx;
  background-color: #999;
  transform: translate(-50%, -50%) rotate(45deg);
}

.login-popup-close::after {
  transform: translate(-50%, -50%) rotate(-45deg);
}

.login-popup-image {
  width: 108rpx;
  height: 112rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
}

.login-popup-image image {
  width: 100%;
  height: 100%;
}

.login-popup-text {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

.login-popup-button {
  width: 322rpx;
  height: 72rpx;
  background-color: #4080ff;
  color: #fff;
  font-size: 32rpx;
  border-radius: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 500;
  margin: 0;
  box-shadow: 0 4rpx 12rpx rgba(64, 128, 255, 0.3);
} 