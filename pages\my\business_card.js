// pages/my/business_card.js
import config from '../../config';  // 导入配置文件
Page({

  /**
   * 页面的初始数据
   */
  data: {
    COS_CONFIG:config.COS_CONFIG.url,
    formData:{
      company_name:'',
      person_name:'',
      position:'',
      phone:'',
      email:'',
      address:'',
      wechat:''
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  
  //获取公司名称输入事件
  onInputChange(e){
    this.setData({
      'formData.company_name': e.detail.value
    });
  },
  
  //获取姓名输入事件
  onPersonNameChange(e){
    this.setData({
      'formData.person_name': e.detail.value
    });
  },
  
  //获取职位输入事件
  onPositionChange(e){
    this.setData({
      'formData.position': e.detail.value
    });
  },
  
  //获取电话输入事件
  onPhoneChange(e){
    this.setData({
      'formData.phone': e.detail.value
    });
  },
  //获取邮箱输入事件
  onEmailChange(e){
    this.setData({
      'formData.email': e.detail.value
    });
  },
  //获取地址输入事件
  onAddressChange(e){
    this.setData({
      'formData.address': e.detail.value
    });
  },
  //获取微信输入事件
  onWecharChange(e){
    this.setData({
      'formData.wechat': e.detail.value
    });
  },
})