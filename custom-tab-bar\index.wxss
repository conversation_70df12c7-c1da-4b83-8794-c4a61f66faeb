.tab-bar {
  position: fixed;
  bottom: 38px;
  left: 0;
  right: 0;
  height: 128rpx;
  background: white;
  display: flex;
  padding-bottom: env(safe-area-inset-bottom);
}

.tab-bar-border {
  background-color: rgba(0, 0, 0, 0.1);
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 1px;
  transform: scaleY(0.5);
}

.tab-bar-item {
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  padding: 0 29rpx;
}

.tab-bar-item cover-image {
  width: 76rpx;
  height: 76rpx;
}

.tab-bar-item cover-view {
  font-size: 32rpx;
  margin-top: 8rpx;
}

.tab-bar-item image {
  width: 38px;
  height: 38px;
}

.tab-bar-item view {
  font-size: 16px;
}

.tab-bar-icon {
  width: 76rpx;
  height: 76rpx;
  margin-bottom: 8rpx;
}

.tab-bar-text {
  font-size: 32rpx;
  line-height: 1;
} 