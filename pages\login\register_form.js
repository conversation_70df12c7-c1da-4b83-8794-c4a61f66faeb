// pages/login/register_form.js
const api = require('../../utils/api').default;
const util = require('../../utils/util');
const langData = require('../../utils/lang-function.js');

// 添加调试信息
console.log('register_form.js 开始加载');

// 直接声明一个测试函数在全局
function testFunction() {
  console.log('全局测试函数被调用');
}

// 调用测试函数以验证脚本是否正确执行
testFunction();

Page({

  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: 0,
    phone: '',
    password: '',
    verifyCode: '',
    showPassword: false,
    isAgree: false,
    loading: false,
    codeBtnText: langData.t('get_phone_code'),
    isCountDown: false,
    showSuccessModal: false,
    openid: '',
    errorInfo: {
      show: false,
      type: 'info',
      icon: 'info-o',
      msg: ''
    },
    text: {}
  },
  
  //语言刷新
  refreshLanguage(newLang){
    console.log('页面语言已切换到:', newLang);
    this.setData({
      codeBtnText:langData.t('enter_phone_number'),
   })
  },
  // 更新页面文本
  updateText() {
    this.setData({
      text:{
        sign_up:langData.t('sign_up'),
        i_agree_to_abide_by:langData.t('i_agree_to_abide_by'),
        user_service_agreement:langData.t('user_service_agreement'),
        privacy_policy:langData.t('privacy_policy'),
        please_enter_the_verification_code:langData.t('please_enter_the_verification_code'),
        enter_phone_number:langData.t('enter_phone_number'),
        pass_chars:langData.t('pass_chars'),
      }
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('onLoad 执行');
    this.updateText();
    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });

    // 检查API是否正确加载
    console.log('API模块检查:', api);
    if (api && api.user && api.user.codeLogin) {
      console.log('codeLogin方法存在');
    } else {
      console.error('codeLogin方法不存在，请检查API引入路径');
    }

    // 测试点击事件处理函数是否存在
    console.log('getVerifyCode方法类型:', typeof this.getVerifyCode);
    console.log('方法列表:', Object.keys(this).filter(key => typeof this[key] === 'function'));

    // 尝试获取缓存中的openid
    const cachedOpenid = util.getCacheWithExpiry('openid');
    if (cachedOpenid) {
      console.log('从缓存获取到openid:', cachedOpenid);
      this.setData({
        openid: cachedOpenid
      });
    } else {
      // 如果缓存中没有，则调用登录接口获取
      this.getWechatOpenid();
    }
  },

  /**
   * 获取微信openid
   */
  getWechatOpenid() {
    console.log('开始获取微信openid');
    wx.login({
      success: res => {
        if (res.code) {
          console.log('获取微信授权码成功:', res.code);
          api.user.getWechatInfo({
            code: res.code
          }).then(result => {
            console.log('获取openid成功:', result.openid);
            // 缓存openid
            util.setCacheWithExpiry('openid', result.openid, 1, "hours");
            util.setCacheWithExpiry('session_key', result.session_key, 1, "hours");
            // 更新页面数据
            this.setData({
              openid: result.openid
            });
          }).catch(err => {
            console.error('获取openid失败:', err);
          });
        } else {
          console.error('获取微信授权码失败:', res);
        }
      },
      fail: err => {
        console.error('调用wx.login失败:', err);
      }
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    console.log('页面初次渲染完成 onReady');
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('页面显示 onShow');

    // 检查openid是否存在以及是否已过期
    const cachedOpenid = util.getCacheWithExpiry('openid');

    if (!cachedOpenid) {
      console.log('openid不存在或已过期，重新获取');
      this.getWechatOpenid();
    } else if (cachedOpenid !== this.data.openid) {
      // 如果缓存中的openid与页面数据中的不一致，则更新
      console.log('更新页面openid数据');
      this.setData({
        openid: cachedOpenid
      });
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 返回上一页
   */
  navigateBack() {
    wx.navigateBack();
  },

  /**
   * 切换密码可见性
   */
  togglePasswordVisibility() {
    this.setData({
      showPassword: !this.data.showPassword
    });
  },

  /**
   * 切换协议同意状态
   */
  toggleAgree() {
    this.setData({
      isAgree: !this.data.isAgree
    });
  },

  /**
   * 查看协议
   */
  viewAgreement(e) {
    const type = e.currentTarget.dataset.type;
    let url = '';

    if (type === 'service') {
      url = '/pages/agreement/service';
    } else if (type === 'privacy') {
      url = '/pages/agreement/privacy';
    }

    wx.navigateTo({
      url: url
    });
  },

  /**
   * 获取验证码
   */
  getVerifyCode: function (e) {
    console.log('getVerifyCode被调用', e);

    // 如果正在倒计时，不处理点击
    if (this.data.isCountDown) {
      console.log('正在倒计时中，忽略点击');
      return;
    }

    // 隐藏之前的错误信息
    this.setData({
      errorInfo: {
        show: false,
        type: 'info',
        icon: 'info-o',
        msg: ''
      }
    });

    const phone = this.data.phone;
    if (!phone) {
      console.log('手机号为空');
      this.setData({
        errorInfo: {
          show: true,
          type: 'warning',
          icon: 'warning-o',
          msg: langData.t('enter_phone_number')
        }
      });
      return;
    }

    // 验证手机号格式
    if (!/^1[3-9]\d{9}$/.test(phone)) {
      console.log('手机号格式不正确');
      this.setData({
        errorInfo: {
          show: true,
          type: 'warning',
          icon: 'warning-o',
          msg: langData.t('invalid_number')
        }
      });
      return;
    }

    // 检查openid是否存在
    if (!this.data.openid) {
      console.log('openid不存在，正在重新获取');
      wx.showLoading({
        title: '请求中...',
      });

      // 先获取openid再发送验证码
      this.getWechatOpenid();
      setTimeout(() => {
        wx.hideLoading();
        if (this.data.openid) {
          // 获取到openid后再次调用获取验证码
          this.getVerifyCode(e);
        } else {
          this.setData({
            errorInfo: {
              show: true,
              type: 'error',
              icon: 'close',
              msg: '获取授权信息失败，请重试'
            }
          });
          // 失败时添加短暂倒计时
          this.startFailedCountdown(2.5);
        }
      }, 1500);
      return;
    }

    console.log('手机号验证通过，准备调用API');

    // 调用发送验证码API
    const params = {
      mobile: phone,
      event: 'register',
      type: 'admin',
      openid: this.data.openid,  // 添加openid参数
      lang:langData.getCurrentLang()
    };

    console.log('API参数:', params);

    wx.showLoading({
      title: '发送中...',
    });
    
    try {
      console.log('开始调用验证码API');
      // 修正API调用路径
      api.user.codeLogin(params)
        .then(res => {
          console.log('API调用成功:', res);
          wx.hideLoading();

          // 处理不同的响应状态
          if (res.code === 0) {
            // 成功发送验证码，开始倒计时
            this.setData({
              isCountDown: true,
              errorInfo: {
                show: true,
                type: 'success',
                icon: 'checked',
                msg: langData.t('code_sent')
              }
            });

            let countdown = 60;
            this.setData({
              codeBtnText: `${countdown}`+langData.t('s_to_retry')
            });

            const timer = setInterval(() => {
              countdown--;

              if (countdown <= 0) {
                clearInterval(timer);
                this.setData({
                  codeBtnText: langData.t('get_phone_code'),
                  isCountDown: false
                });
              } else {
                this.setData({
                  codeBtnText: `${countdown}`+langData.t('s_to_retry')
                });
              }
            }, 1000);
          } else {
            // 错误处理
            let errorMsg = res.msg || langData.t('send_failed');
            let errorType = 'error';
            let errorIcon = 'close';

            // 根据错误码显示不同提示
            switch (res.code) {
              case -4:
                errorMsg = langData.t('phone_already_used');
                break;
              case -5:
                errorMsg = langData.t('phone_already_used');
                break;
              case -6:
              case -7:
                errorMsg =  langData.t('not_registered');
                errorType = 'warning';
                errorIcon = 'warning-o';
                break;
              case -8:
                errorMsg = langData.t('install_sms_plugin');
                errorType = 'info';
                errorIcon = 'info-o';
                break;
              case -9:
                errorMsg = langData.t('send_failed_check_config');
                break;
              default:
                errorMsg = res.msg || langData.t('send_failed');
            }
            
            this.setData({
              errorInfo: {
                show: true,
                type: errorType,
                icon: errorIcon,
                msg: errorMsg
              }
            });

            // 失败时添加短暂倒计时
            this.startFailedCountdown(2.5);
          }
        })
        .catch(err => {
          console.error('API调用失败:', err);
          wx.hideLoading();
          // 错误处理
          let errorMsg = err.msg || langData.t('send_failed');
          let errorType = 'error';
          let errorIcon = 'close';
          // 如果是对象，检查是否有code和msg
          if (typeof err === 'object' && err !== null) {
            // 根据错误码显示不同提示
            switch (err.code) {
              case -4:
                errorMsg = langData.t('phone_already_used');
                break;
              case -5:
                errorMsg = langData.t('phone_already_used');
                break;
              case -6:
              case -7:
                errorMsg = langData.t('not_registered');
                errorType = 'warning';
                errorIcon = 'warning-o';
                break;
              case -8:
                errorMsg = langData.t('install_sms_plugin');
                errorType = 'info';
                errorIcon = 'info-o';
                break;
              case -9:
                errorMsg = langData.t('send_failed_check_config');
                break;
              default:
                errorMsg = err.msg || langData.t('send_failed');
            }
          }

          this.setData({
            errorInfo: {
              show: true,
              type: errorType,
              icon: errorIcon,
              msg: errorMsg
            }
          });

          // 失败时添加短暂倒计时
          this.startFailedCountdown(2.5);
        });
    } catch (error) {
      console.error('API调用异常:', error);
      wx.hideLoading();
      this.setData({
        errorInfo: {
          show: true,
          type: 'error',
          icon: 'close',
          msg: '系统异常，请稍后重试'
        }
      });

      // 失败时添加短暂倒计时
      this.startFailedCountdown(2.5);
    }
  },

  /**
   * 失败后的短暂倒计时
   */
  startFailedCountdown(seconds) {
    // 设置倒计时状态
    this.setData({
      isCountDown: true,
      codeBtnText: `${seconds}`+langData.t('s_to_retry')
    });

    // 转换为毫秒
    const totalMs = seconds * 1000;
    const interval = 100; // 更新频率为100毫秒
    const steps = totalMs / interval;
    let currentStep = 0;

    const timer = setInterval(() => {
      currentStep++;
      const remaining = seconds - (currentStep * interval / 1000);

      // 保留一位小数
      const displayTime = Math.max(0, remaining).toFixed(1);

      if (currentStep >= steps) {
        clearInterval(timer);
        this.setData({
          codeBtnText: langData.t('get_phone_code'),
          isCountDown: false
        });
      } else {
        this.setData({
          codeBtnText: `${displayTime}`+langData.t('s_to_retry')
        });
      }
    }, interval);
  },

  /**
   * 提交注册
   */
  handleRegister() {
    console.log('handleRegister 被调用');

    if (!this.data.isAgree) {
      wx.showToast({
        title: '请先同意用户协议',
        icon: 'none'
      });
      return;
    }

    const { phone, password, verifyCode, openid } = this.data;

    // 验证手机号
    if (!phone) {
      wx.showToast({
        title: langData.t('enter_phone_number'),
        icon: 'none'
      });
      return;
    }

    // 验证手机号格式
    if (!/^1[3-9]\d{9}$/.test(phone)) {
      wx.showToast({
        title: langData.t('invalid_number'),
        icon: 'none'
      });
      return;
    }

    // 验证密码
    if (!password) {
      wx.showToast({
        title: langData.t('enter_password'),
        icon: 'none'
      });
      return;
    }

    // 验证密码长度
    if (password.length < 8 || password.length > 16) {
      wx.showToast({
        title: langData.t('pass_chars'),
        icon: 'none'
      });
      return;
    }

    // 验证码
    if (!verifyCode) {
      wx.showToast({
        title: langData.t('please_enter_the_verification_code'),
        icon: 'none'
      });
      return;
    }

    // 检查openid是否存在
    if (!openid) {
      console.log('openid不存在，重新获取');
      // 如果没有openid，先获取再继续
      wx.showLoading({
        title: '请求中...',
      });

      this.getWechatOpenid();
      setTimeout(() => {
        wx.hideLoading();
        // 重新检查获取结果
        if (this.data.openid) {
          // 重新调用注册方法
          this.handleRegister();
        } else {
          wx.showToast({
            title: '获取授权信息失败，请重试',
            icon: 'none'
          });
        }
      }, 1500);
      return;
    }

    // 显示加载状态
    this.setData({
      loading: true
    });

    // 调用注册接口
    const params = {
      mobile: phone,
      password: password,
      code: verifyCode,
      openid: openid  // 添加openid参数
    };

    console.log('调用注册接口，参数:', params);

    api.user.codeLoginVerify(params)
      .then(res => {
        console.log('注册接口返回:', res);
        this.setData({
          loading: false
        });

        if (res) {
          // 显示注册成功的模态窗口，不再自动跳转
          this.setData({
            showSuccessModal: true
          });
        } else {
          wx.showToast({
            title: res.msg || '注册失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('注册失败:', err);
        this.setData({
          loading: false
        });

        wx.showToast({
          title: err.msg || '注册失败，请重试',
          icon: 'none'
        });
      });
  },

  /**
   * 前往完善个人信息页面
   */
  goToInfoPage() {
    const { phone, password } = this.data;
    wx.navigateTo({
      url: `/pages/info/index?phone=${phone}&password=${password}`
    });
  }
})