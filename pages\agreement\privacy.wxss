.container {
  padding: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30rpx;
}

.content {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
}

.header {
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
  position: relative;
}

.section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}

.section-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.section-content text {
  display: block;
  margin-bottom: 16rpx;
} 