/* pages/video/detail.wxss */
.video-container {
  display: flex;
    align-items: center;    /* 这行是关键：实现垂直居中 */
    justify-content: center; /* 这行是附赠的：实现水平居中 */
    height: 100vh;          /* 让容器占满整个屏幕的高度 */
    background-color: #000; /* 视频背景通常是黑色 */
    position: relative;     /* 确保返回按钮、商家信息等绝对定位的元素能正确显示 */
}

/* 返回按钮 */
.back-btn {
  position: absolute;
  top: 80rpx;
  left: 30rpx;
  z-index: 10;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

/* 禁用状态的返回按钮 */
.back-btn.disabled {
  opacity: 0.6;
}

/* 顶部品牌区域 */
.top-branding {
  position: absolute;
  top: 80rpx;
  right: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  z-index: 10;
}

.brand-name {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 8rpx;
}

.brand-title {
  font-size: 28rpx;
  font-weight: 500;
  color: white;
  margin-bottom: 6rpx;
}

.brand-subtitle {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 视频标题 */
.video-title {
  position: absolute;
  top: 240rpx;
  left: 30rpx;
  display: flex;
  flex-direction: column;
  z-index: 10;
}

.video-title text {
  font-size: 56rpx;
  font-weight: bold;
  color: white;
  line-height: 1.3;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 直播时间 */
.live-time {
  position: absolute;
  bottom: 300rpx;
  left: 30rpx;
  display: flex;
  flex-direction: column;
  z-index: 10;
}

.time-cn {
  font-size: 32rpx;
  font-weight: 500;
  color: white;
  margin-bottom: 8rpx;
}

.time-en {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 底部说明 */
.bottom-info {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 210rpx;
  z-index: 15;
  padding-bottom: 10rpx;
  background-color: transparent;
}

/* 视频描述样式 */
.video-description {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5rpx 30rpx;
  padding-bottom: 0;
  background: transparent;
}

.desc-text {
  font-size: 28rpx;
  color: white;
  max-width: 90%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
  transition: all 0.3s;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
}

.video-description.expanded .desc-text {
  white-space: normal;
  display: block;
  text-overflow: clip;
}

.expand-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toggle-icon {
  width: 16rpx;
  height: 16rpx;
  border-right: 2rpx solid #fff;
  border-bottom: 2rpx solid #fff;
}

.toggle-icon.down {
  transform: rotate(45deg);
}

.toggle-icon.up {
  transform: rotate(-135deg);
}

/* 自定义进度条 */
.custom-progress {
  position: relative;
  height: 16rpx;
  padding: 0;
  display: flex;
  align-items: center;
  margin: 0rpx 0;
  margin-bottom: 16rpx;
  z-index: 15;
  background-color: transparent;
  padding: 0rpx 0;
}

.progress-bar {
  width: 100%;
  height: 10rpx;
  background-color: rgba(255, 255, 255, 0.5);
  overflow: hidden;
}

.progress-inner {
  height: 100%;
  background-color: #e14242;
  transition: width 0.1s;
}

.progress-dot {
  position: absolute;
  top: 50%;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #e14242;
  transform: translate(-50%, -50%);
  transition: width 0.2s, height 0.2s;
  box-shadow: 0 0 5rpx rgba(255, 255, 255, 0.8);
}

.progress-dot.dragging {
  width: 28rpx;
  height: 28rpx;
  box-shadow: 0 0 10rpx rgba(225, 66, 66, 0.8);
}

.progress-time {
  position: absolute;
  top: -25rpx;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  white-space: nowrap;
}

/* 视频描述样式 */
.video-description {
  background-color: transparent;
  padding: 20rpx 30rpx;
  position: relative;
}

.desc-content {
  max-height: 80rpx;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.video-description.expanded .desc-content {
  max-height: 1000rpx;
  /* 足够大以容纳所有内容 */
}

.desc-content text {
  font-size: 26rpx;
  color: #fff;
  line-height: 1.6;
}

.desc-gradient {
  position: absolute;
  bottom: 60rpx;
  left: 0;
  right: 0;
  height: 40rpx;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.7));
}

.desc-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40rpx;
  margin-top: 10rpx;
}

.desc-toggle text {
  font-size: 24rpx;
  color: #ccc;
}

/* 商家信息容器 - 独立显示 */
.merchant-info-container {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 20;
}

/* 底部商家信息 */
.merchant-info {
  display: flex;
  align-items: center;
  background-color: #333;
  padding: 36rpx 30rpx;
  position: relative;
  margin-top: 0;
}

.merchant-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 16rpx;
  border: 2rpx solid #e14242;
  background-color: #e14242;
}

.merchant-avatar image {
  width: 100%;
  height: 100%;
}

.merchant-name {
  font-size: 26rpx;
  font-weight: 500;
  color: white;
  flex: 1;
}

/* 互动区域 */
.interaction {
  display: flex;
  align-items: center;
}

.like,
.comment,
.share {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: 40rpx;
}

.like image,
.comment image,
.share image {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 4rpx;
}

.like text,
.comment text,
.share text {
  font-size: 20rpx;
  color: #ccc;
}

/* 加载状态 */
.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: calc(100vh - 132rpx);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #000;
  z-index: 100;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  margin-top: 20px;
  color: #fff;
  font-size: 16px;
}

/* 错误状态 */
.error-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: calc(100vh - 132rpx);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #000;
  z-index: 100;
}

.error-text {
  color: #fff;
  font-size: 16px;
  margin-bottom: 20px;
}

.retry-btn {
  padding: 10px 20px;
  background-color: #444b8f;
  color: #fff;
  border-radius: 4px;
}

/* 视频控制层 */
.video-control-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: calc(100vh - 132rpx);
  z-index: 5;
}

/* 错误状态更多详情 */
.error-detail {
  color: #aaa;
  font-size: 14px;
  margin-bottom: 30px;
}

/* 调试信息样式 */
.debug-info {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 10px;
  border-radius: 5px;
  z-index: 1000;
  font-size: 12px;
  display: flex;
  flex-direction: column;
}

.debug-info text {
  margin-bottom: 5px;
}

/* 占位容器样式 - 替代原来的点赞转发评论按钮 */
.action-buttons-placeholder {
  height: 50rpx;
  width: 100%;
  background-color: transparent;
}

/* 重播按钮样式 */
.replay-button-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: calc(100vh - 132rpx);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.replay-button {
  width: 200rpx;
  height: 200rpx;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.replay-icon {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #fff;
  border-radius: 50%;
  position: relative;
}

.replay-icon:after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-35%, -50%);
  width: 0;
  height: 0;
  border-top: 16rpx solid transparent;
  border-bottom: 16rpx solid transparent;
  border-left: 28rpx solid #fff;
}

.replay-text {
  font-size: 28rpx;
  color: #fff;
  margin-top: 20rpx;
}

/* 登录弹窗样式 */
.login-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.login-popup-content {
  width: 390rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.login-popup-close {
  position: absolute;
  top: -20rpx;
  right: -20rpx;
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background-color: #FFFFFF;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  z-index: 10;
}

.login-popup-close::before,
.login-popup-close::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20rpx;
  height: 2rpx;
  background-color: #999;
  transform: translate(-50%, -50%) rotate(45deg);
}

.login-popup-close::after {
  transform: translate(-50%, -50%) rotate(-45deg);
}

.login-popup-image {
  width: 108rpx;
  height: 112rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
}

.login-popup-image image {
  width: 100%;
  height: 100%;
}

.login-popup-text {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

.login-popup-button {
  width: 322rpx;
  height: 72rpx;
  background-color: #4080ff;
  color: #fff;
  font-size: 32rpx;
  border-radius: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 500;
  margin: 0;
  box-shadow: 0 4rpx 12rpx rgba(64, 128, 255, 0.3);
}

/* 视频模糊遮罩 */
.blur-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: rgba(0, 0, 0, 0.3);
  background: linear-gradient(to bottom,
      rgba(0, 0, 0, 0.3) 0%,
      rgba(0, 0, 0, 0.5) 50%,
      rgba(0, 0, 0, 0.7) 100%);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 15;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.blur-mask.show {
  opacity: 1;
}

.blur-mask-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
  background-color: rgba(0, 0, 0, 0.4);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);
  max-width: 80%;
}

.lock-icon {
  width: 120rpx;
  height: 120rpx;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  margin-bottom: 30rpx;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.lock-icon:before {
  content: '';
  position: absolute;
  width: 60rpx;
  height: 60rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M0 0h24v24H0z' fill='none'/%3E%3Cpath d='M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-size: contain;
}

.mask-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 16rpx;
  text-align: center;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
}

.mask-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 40rpx;
  text-align: center;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
}

.mask-button {
  padding: 20rpx 60rpx;
  background-color: #4080ff;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(64, 128, 255, 0.3);
}

.full-screen {
  width: 100vw; /* 在横屏时设为全屏 */
  height: 100vh;
}

  /* .wxss 文件 */
  .custom-back-btn {
    position: absolute;
    top: 80rpx;
    left: 30rpx;
    z-index: 10;
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
  }
  .back-icon {
    width: 40rpx;
    height: 40rpx;
  }

  