import api from '../../utils/api';
import util from '../../utils/util';
import share from '../../utils/share';  // 确保导入 分享

Page({
  behaviors: [share], //分享设置
  data: {
    //处理分享页面 统一写
    shareData: {},
    price: '',
    publishDate: '',
    carTitle: '',
    firstRegister: '',
    mileage: '',
    emission: '',
    transferCount: '',
    location: '',
    color: '',
    transmission: '',
    engine: '',
    carId: '',
    carImages: [],
    loading: true,  // 添加loading状态
    id: '',
    currentSwiperIndex: 0,
    recommendCars: [], // 添加推荐车辆数组
    isCollected: false, // 是否已收藏
    statusBarHeight: 0, // 状态栏高度
    merchantInfo: {}, // 商家信息
  },

  onLoad(options) {
    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });

    // 检查用户是否登录
    const isLoggedIn = util.getUserInfo() !== null;
    if (!isLoggedIn) {
      console.log('用户未登录，部分功能可能受限');
    }

    // 获取路由参数中的车辆ID
    const { id } = options;
    this.setData({ id })
    if (id) {
      this.getCarDetail(id);
      this.getRecommendCars(id); // 获取推荐车辆
      this.checkCollectStatus(id); // 检查收藏状态
    }
  },

  // 返回上一页
  navigateBack() {
    wx.navigateBack({
      delta: 1,
      fail: () => {
        wx.switchTab({
          url: '/pages/buy/index'
        });
      }
    });
  },

  // 检查收藏状态
  async checkCollectStatus(carId) {
    try {
      const app_id = util.getAppId();
      if (!app_id) {
        console.warn('未登录，无法获取收藏状态');
        return;
      }

      // 获取收藏列表
      const result = await api.car.favoriteList({
        app_id: app_id
      });

      // console.log('获取收藏列表:', result);

      if (result && result.data) {
        // 检查当前车辆是否在收藏列表中
        const vehicleList = Array.isArray(result.data) ? result.data : [];
        // console.log('收藏车辆列表:', vehicleList);

        const isCollected = vehicleList.some(item => {
          const itemId = item.id || item.vehicle_id;
          // console.log('比较ID:', itemId, carId, itemId == carId);
          return itemId == carId;
        });

        // console.log('是否已收藏:', isCollected);

        // 更新本地缓存
        const collectList = vehicleList.map(item => item.id || item.vehicle_id);
        wx.setStorageSync('collectList', collectList);

        // 更新UI状态
        this.setData({ isCollected });
      } else {
        // 如果API请求失败，尝试从本地缓存获取状态
        const collectList = wx.getStorageSync('collectList') || [];
        const isCollected = collectList.includes(carId);
        this.setData({ isCollected });
      }
    } catch (error) {
      console.error('检查收藏状态失败', error);
      // 出错时也尝试从本地缓存获取
      const collectList = wx.getStorageSync('collectList') || [];
      const isCollected = collectList.includes(carId);
      this.setData({ isCollected });
    }
  },

  // 强制刷新收藏状态
  async refreshCollectStatus() {
    await this.checkCollectStatus(this.data.id);
  },

  // 切换收藏状态
  async toggleCollect() {
    // 获取用户信息
    const userInfo = util.getUserInfo();

    // 检查登录状态
    if (userInfo && userInfo.app_id) {
      // 用户已登录，执行收藏/取消收藏操作
      const { id, isCollected } = this.data;

      // 调用收藏API
      const result = await api.car.favorite({
        app_id: userInfo.app_id,
        vehicle_id: id,
        is_favorite: isCollected ? 0 : 1 // 已收藏则取消，未收藏则添加
      });

      if (result) {
        wx.showToast({
          title: isCollected ? '已取消收藏' : '收藏成功',
          icon: 'success'
        });

        // 操作成功后，直接从服务器获取最新的收藏状态
        setTimeout(() => {
          this.refreshCollectStatus();
        }, 300);
      } else {
        wx.showToast({
          title: '操作失败',
          icon: 'none'
        });
      }
    } else {
      // 用户未登录，直接跳转到登录页面
      wx.navigateTo({
        url: '/pages/login/index'
      });
    }
  },

  // 下载车辆手册
  downloadManual() {
    wx.showLoading({
      title: '正在准备下载...',
    });

    setTimeout(() => {
      wx.hideLoading();
      wx.showModal({
        title: '提示',
        content: '该车型手册暂未开放下载，是否联系客服获取?',
        confirmText: '联系客服',
        success(res) {
          if (res.confirm) {
            // 联系客服逻辑
            wx.makePhoneCall({
              phoneNumber: '************', // 替换为实际客服电话
              fail() {
                wx.showToast({
                  title: '拨打电话失败',
                  icon: 'none'
                });
              }
            });
          }
        }
      });
    }, 1500);
  },

  // 联系商家
  contactSeller() {
    wx.openCustomerServiceChat({
        extInfo: {url: 'https://work.weixin.qq.com/kfid/kfc4fa17a0f88a6e7c8'},
        corpId: 'ww3f7ad32ebbae2192', // 企业微信ID
        success(res) {
            console.log('打开客服聊天窗口成功', res);
        },
        fail(err) {
            console.error('打开客服聊天窗口失败', err);
            wx.showToast({
                title: '打开客服聊天失败',
                icon: 'none'
            });
        }
    });
  },

  // 获取推荐车辆
  async getRecommendCars(currentCarId) {
    try {
      const result = await api.car.getCarList({
        page: 1,
        list_rows: 10  // 获取10条数据
      });

      if (result && result.data) {
        const carListData = Array.isArray(result.data) ? result.data : [];

        // 排除当前正在查看的车辆
        const filteredCars = carListData.filter(car => car.id != currentCarId);

        // 随机打乱数组顺序
        const shuffledCars = this.shuffleArray(filteredCars);

        // 只取前4条数据
        const recommendCars = shuffledCars.slice(0, 4).map(car => ({
          id: car.id,
          title: car.ui_vehicle_name || car.title_desc,
          year: car.first_registration_time ? car.first_registration_time.substring(0, 4) : '',
          mileage: (car.mileage || 0).toString(),
          price: car.sell_price || '0',
          originalPrice: car.official_price || '0',
          image: car.main_url || (car.image_urls ? car.image_urls.split(',')[0] : '')
        }));

        this.setData({ recommendCars });
      }
    } catch (error) {
      console.error('获取推荐车辆失败:', error);
    }
  },

  // 数组随机打乱方法
  shuffleArray(array) {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  },

  // 车辆点击事件处理方法
  onCarTap(e) {
    const { id } = e.currentTarget.dataset;
    wx.redirectTo({
      url: `/pages/buy/detail?id=${id}`
    });
  },

  // 点击"查看更多"
  onMoreCarsTap() {
    wx.switchTab({
      url: '/pages/buy/index'  // 跳转到车辆列表页面
    });
  },

  // 数字去重
  removeDuplicates(numbers) {
    return numbers.filter((value, index, self) => self.indexOf(value) === index);
  },

  // 获取车辆详情
  async getCarDetail(id) {
    try {
      wx.showLoading({
        title: '加载中...',
      });

      const carInfo = await api.car.getCarDetail(id);

      // 尝试正常获取商家信息，但已有备用数据
      this.getMerchantInfo(carInfo.app_id);

      // 检查并处理图片数组
      const imageUrls = carInfo.image_urls || [];
      if (!Array.isArray(imageUrls) || imageUrls.length === 0) {
        console.warn('No images found for this car');
      }

      // 处理车架号/VIN码显示格式
      let frameNumber = carInfo.frame_number || '-';
      if (frameNumber && frameNumber !== '-' && frameNumber.length > 8) {
        // 只显示前4位和后4位，中间用*代替
        const firstPart = frameNumber.substring(0, 4);
        const lastPart = frameNumber.substring(frameNumber.length - 4);
        const middlePart = '*'.repeat(frameNumber.length - 8);
        frameNumber = firstPart + middlePart + lastPart;
      }

      const shareData = {
        title: carInfo.ui_vehicle_name || carInfo.title_desc || '暂无标题',
        path: '/pages/buy/detail?id=' + id, // 分享路径，默认为当前页面路径
        imageUrl: this.removeDuplicates(imageUrls)[0] || "", // 分享图片（可选）
        isDetailPage: true, // 标记是否详情页
      }
      this.setData({ shareData })

      // 设置页面数据
      this.setData({
        official_price: carInfo.official_price || '-',
        price: carInfo.sell_price || '-',
        publishDate: util.formatDate(carInfo.create_time),
        carTitle: carInfo.ui_vehicle_name || carInfo.title_desc || '暂无标题',
        firstRegister: util.formatDate(carInfo.first_registration_time),
        mileage: carInfo.mileage || '-',
        emission: carInfo.emission || "-",
        transferCount: carInfo.transfer_number || '0',
        location: carInfo.vehicle_source_location || '-',
        color: carInfo.color || '-',
        transmission: carInfo.transmission || "-",
        engine: carInfo.engine || "-",
        carId: frameNumber,
        carImages: this.removeDuplicates(imageUrls),
        loading: false
      }, () => {
        wx.hideLoading();
      });
    } catch (error) {
      console.error('获取车辆详情失败', error);
      this.setData({
        loading: false,
        carImages: []
      });
      wx.hideLoading();
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    }
  },

  // 提交购车意向
  async handleContact() {
    try {
      const result = await api.car.submitBuyIntent({
        carId: this.data.carId,
        // 可以添加其他必要信息
      });

      if (result) {
        wx.showToast({
          title: '提交成功，客服将尽快联系您',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('提交购车意向失败', error);
    }
  },

  onShow() {
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 1  // 购车是第二个 tab
      });
    }
  },

  // 处理图片加载错误
  handleImageError(e) {
    const { index } = e.currentTarget.dataset;
    const carImages = [...this.data.carImages];
    carImages[index] = '/assets/images/default-car.png'; // 替换为默认图片
    this.setData({ carImages });
    console.warn(`Image load failed at index ${index}`);
  },

  // 预览图片
  previewImage(e) {
    const { url, index } = e.currentTarget.dataset;
    wx.previewImage({
      current: url, // 当前显示图片的链接
      urls: this.data.carImages // 需要预览的图片链接列表
    });
  },

  // 查看维保记录
  viewMaintenanceRecord() {
    wx.showToast({
      title: '正在加载维保记录',
      icon: 'loading',
      duration: 2000
    });
    // 这里可以添加跳转到维保记录页面的逻辑
  },

  // 查看出险记录
  viewInsuranceRecord() {
    wx.showToast({
      title: '正在加载出险记录',
      icon: 'loading',
      duration: 2000
    });
    // 这里可以添加跳转到出险记录页面的逻辑
  },

  // 查看第三方检测报告
  viewThirdPartyReport() {
    wx.showToast({
      title: '正在加载检测报告',
      icon: 'loading',
      duration: 2000
    });
    // 这里可以添加跳转到第三方检测报告页面的逻辑
  },

  // 处理轮播图切换事件
  swiperChange: function (e) {
    this.setData({
      currentSwiperIndex: e.detail.current
    });
  },

  // 查看商家详情
  viewMerchantDetail() {
    const { merchantInfo } = this.data;
    if (!merchantInfo || !merchantInfo.app_id) {
      wx.showToast({
        title: '商家信息不完整',
        icon: 'none'
      });
      return;
    }

    // 跳转到商家详情页面，可以根据实际页面路径调整
    wx.navigateTo({
      url: `/pages/information/index?id=${merchantInfo.app_id}`
    });
  },

  // 获取商家信息方法
  async getMerchantInfo(app_id) {
    try {
      if (!app_id) {
        console.warn('未获取到app_id，无法获取商家信息');
        return;
      }

      const result = await api.user.getCompanyInfo({
        app_id: app_id
      });
      console.log(result)
      if (result) {
        // 将接口返回的数据适配到页面使用的字段
        const merchantInfo = {
          app_id: result.app_id,
          company_name: result.company_name,
          address: result.address,
          car_count: result.vehicle_count || 0,
          logo: result.logo || '',
          score: result.score || '4.5',
          is_auth: result.is_auth || false,
          phone: result.mobile || ''
        };

        this.setData({
          merchantInfo
        });
      }
    } catch (error) {
      console.error('获取商家信息失败:', error);
    }
  },
}); 