/* pages/moments/release.wxss */

.release-container {
  min-height: 100vh;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* 自定义导航栏样式 */
.custom-nav {
  width: 100%;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}

.status-bar {
  width: 100%;
}

.nav-content {
  height: 44px;
  display: flex;
  align-items: center;
  position: relative;
}

.back-icon {
  position: absolute;
  left: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx;
}

.back-icon image {
  width: 40rpx;
  height: 40rpx;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}


.more {
  font-size: 40rpx;
  margin-right: 15rpx;
}

.circle {
  font-size: 30rpx;
  color: #999;
}

.form-container {
  margin-top: 140rpx;
  flex: 1;
  padding: 30rpx;
  overflow-y: auto;
  padding-bottom: 130rpx;
  /* 为底部按钮留出空间 */
  position: relative;
  /* 添加相对定位 */
  z-index: 10;
  /* 设置堆叠上下文，但低于自定义导航栏 */
}

.form-item {
  background-color: #fff;
  padding: 24rpx 30rpx;
  display: flex;
  align-items: center;
  position: relative;
  /* border-radius: 12rpx; */
  z-index: 10;
  /* 增加z-index确保表单项在下拉时不被覆盖 */
}

.label {
  width: 180rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: normal;
}

.required::before {
  content: '*';
  color: #f00;
  margin-right: 4rpx;
}

.custom-picker {
  flex: 1;
  position: relative;
  z-index: 1100;
  /* 添加z-index，确保picker的下拉菜单能正常显示 */
}

.picker-value {
  font-size: 28rpx;
  color: #333;
  text-align: left;
  position: relative;
  padding: 20rpx;
  background-color: #f2f2f2;
  border-radius: 8rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.placeholder-text {
  color: #999;
}

.arrow {
  position: absolute;
  right: 20rpx;
  width: 16rpx;
  height: 16rpx;
  border-bottom: 2rpx solid #999;
  border-right: 2rpx solid #999;
  transform: rotate(45deg);
}

.picker-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  max-height: 400rpx;
  overflow-y: auto;
  background-color: #fff;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
  border-radius: 12rpx;
  z-index: 1500;
  margin-top: 10rpx;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}

.picker-title {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.picker-cancel {
  font-size: 28rpx;
  color: #4080ff;
}

.picker-option {
  padding: 24rpx 30rpx;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1rpx solid #f5f5f5;
}

.picker-option:active {
  background-color: #f8f8f8;
}

.picker-option.active {
  background-color: #f0f7ff;
  color: #4080ff;
}

input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  padding: 20rpx;
  background-color: #f2f2f2;
  border-radius: 16rpx;
}

input::placeholder {
  color: #999;
}

.price-unit {
  font-size: 28rpx;
  color: #999;
  margin-left: 20rpx;
}

.radio-group {
  flex: 1;
  display: flex;
}

.radio-item {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  padding: 15rpx 30rpx;
  border-radius: 8rpx;
}


radio {
  transform: scale(0.7);
  margin-right: 5rpx;
}

.radio-item text {
  font-size: 28rpx;
  color: #333;
}

.new-used-type .radio-group,
.insurance .radio-group {
  justify-content: flex-start;
}

.description-item {
  flex-direction: row;
  align-items: flex-start;
}

.description-container {
  flex: 1;
  margin-left: 20rpx;
}

.description-header {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  margin-top: 20rpx;
  margin-bottom: 10rpx;
}

.description-image {
  width: 48rpx;
  height: 48rpx;
  margin-right: 10rpx;
}

textarea {
  width: 460rpx;
  height: 276rpx;
  padding: 20rpx;
  font-size: 28rpx;
  border-radius: 16rpx;
  box-sizing: border-box;
  background-color: #f2f2f2;
}

textarea::placeholder {
  color: #999;
}

.word-count {
  font-size: 24rpx;
  color: #999;
  text-align: right;
  margin-top: 10rpx;
  width: 100%;
}

.border-radius {
  border-top-left-radius: 16rpx;
  border-top-right-radius: 16rpx;
}

/* 图片上传区域整体容器 */
.upload-section {
  background-color: #fff;
  border-bottom-right-radius: 16rpx;
  border-bottom-left-radius: 16rpx;
  margin-bottom: 20rpx;
  /* 添加20rpx（约10px）的底部间距 */
  overflow: hidden;
}

/* 分隔线 */
.upload-divider {
  height: 1rpx;
  background-color: #f2f2f2;
  margin: 0 30rpx;
}

/* 修改上传区域样式 */
.upload-section .upload-item {
  border-radius: 0;
  margin-bottom: 0;
}

.upload-section .proof-upload {
  padding-bottom: 20rpx;
}

.upload-section .multi-upload-item {
  padding-top: 20rpx;
  margin-bottom: 0;
}

.upload-item {
  flex-direction: row;
  align-items: flex-start;
  border-bottom-left-radius: 16rpx;
  border-bottom-right-radius: 16rpx;
  margin-bottom: 100rpx;
}

.upload-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.upload-container {
  display: flex;
  justify-content: flex-start;
}

.upload-btn {
  width: 180rpx;
  height: 180rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f2f2f2;
  border-radius: 12rpx;
}

.camera-icon {
  width: 50rpx;
  height: 50rpx;
  opacity: 0.4;
}

.image-preview {
  width: 180rpx;
  height: 180rpx;
  position: relative;
  border-radius: 12rpx;
  overflow: hidden;
}

.image-preview image {
  width: 100%;
  height: 100%;
  display: block;
}

.image-preview .reupload {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 24rpx;
  text-align: center;
  padding: 8rpx 0;
}

.upload-hint {
  font-size: 24rpx;
  color: #888;
  margin-top: 16rpx;
  width: 100%;
}

/* 多图上传样式 */
.multi-upload-item {
  flex-direction: row;
  align-items: flex-start;
  border-bottom-left-radius: 16rpx;
  border-bottom-right-radius: 16rpx;
  margin-bottom: 100rpx;
}

.multi-upload-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-top: 10rpx;
}

.multi-upload-container .multi-upload-item {
  width: 160rpx;
  height: 160rpx;
  position: relative;
  border-radius: 12rpx;
  overflow: hidden;
}

.multi-upload-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.multi-delete-icon {
  position: absolute;
  top: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  z-index: 2;
}

.multi-upload-btn {
  width: 160rpx;
  height: 160rpx;
  background-color: #f2f2f2;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.multi-upload-icon {
  width: 60rpx;
  height: 60rpx;
  opacity: 0.5;
}

.multi-upload-hint {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.bottom-actions {
  display: flex;
  padding: 30rpx;
  background-color: #fff;
  border-top: 1rpx solid #eee;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  padding-bottom: calc(30rpx + constant(safe-area-inset-bottom));
  /* 兼容 iOS < 11.2 */
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  /* 兼容 iOS >= 11.2 */
}

.save-draft {
  flex: 1;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f2f2f2;
  color: #666;
  border-radius: 44rpx;
  margin-right: 20rpx;
  font-size: 30rpx;
}

.publish {
  flex: 1;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #4080ff;
  color: #fff;
  border-radius: 44rpx;
  font-size: 30rpx;
}

.empty-tip {
  padding: 20rpx;
  color: #999;
  text-align: center;
  font-size: 28rpx;
}

.brands-count-debug {
  background-color: #f0f8ff;
  padding: 10rpx 20rpx;
  color: #0066cc;
  text-align: center;
  font-size: 24rpx;
  border-bottom: 1rpx solid #eee;
}

/* 打回原因容器 */
.reject-reason-section {
  background-color: #fff;
  padding-top: 0;
  margin-top: 0;
}

/* 驳回原因卡片样式 */
.reject-reason-card {
  margin: 0;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx 30rpx 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.reject-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #f44;
  margin-bottom: 30rpx;
  border-left: 8rpx solid #f44;
  padding-left: 20rpx;
}

.reject-content {
  background-color: transparent;
}

.reason-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 15rpx;
}

.reason-item:last-child {
  margin-bottom: 0;
}

.reason-label {
  font-size: 28rpx;
  color: #999;
  width: 140rpx;
  flex-shrink: 0;
}

.reason-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  text-align: left;
  background-color: #f5f5f5;
  padding: 15rpx 20rpx;
  border-radius: 6rpx;
  width: calc(100% - 140rpx);
  box-sizing: border-box;
}

/* 为详细原因调整样式 */
.detail-value {
  line-height: 1.5;
  min-height: 80rpx;
  width: calc(100% - 140rpx);
  box-sizing: border-box;
  display: flex;
  align-items: center;
  word-break: break-all;
  white-space: pre-line;
}

/* 底部占位元素 */
.bottom-placeholder {
  height: 120rpx;
  /* 增加高度，确保足够空间 */
  width: 100%;
}

/* 审核中状态提示样式 */
.reviewing-status {
  color: #f5a623;
  /* 金色文字 */
  font-size: 30rpx;
  margin: 20rpx 30rpx;
  font-weight: 500;
}

/* 审核中消息样式 */
.reviewing-message {
  color: #f5a623;
  /* 金色文字 */
  font-size: 30rpx;
  padding: 20rpx;
  font-weight: 500;
  text-align: left;
}

/* 禁用状态样式 */
.disabled-picker {
  background-color: #f5f5f5 !important;
  color: #999 !important;
  cursor: not-allowed;
  opacity: 0.8;
}

.disabled-upload {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.disabled-btn {
  background-color: #ccc !important;
  color: #666 !important;
  cursor: not-allowed;
}

/* 审核中状态的样式 */
.reviewing-value {
  background-color: #f5f5f5;
  padding: 15rpx 20rpx;
  border-radius: 6rpx;
  text-align: center;
  line-height: 1.5;
  min-height: 80rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #f5a623;
  font-weight: 500;
  width: 100%;
}

.publish.disabled-btn {
  background-color: #cccccc;
  color: #666666;
}

.publish.review-btn {
  background-color: #1989fa;
  color: white;
}

.picker-dropdown.global-dropdown {
  position: fixed;
  z-index: 2000;
  /* 确保在最顶层 */
  max-width: 92%;
  /* 限制最大宽度 */
  min-width: 300rpx;
  /* 确保最小宽度 */
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);
  animation: dropdown-appear 0.2s ease-out;
  /* 添加过渡动画 */
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
}

/* 下拉菜单动画效果 */
@keyframes dropdown-appear {
  from {
    opacity: 0;
    transform: translateY(-10rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}