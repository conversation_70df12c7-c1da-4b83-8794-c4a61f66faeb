/* pages/my/business_card.wxss */

.business_card{
  width: 702rpx;
  height: 394rpx;
  background-image: url('https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/wechat/assets/images/business_card3.png');
  /* background-image: url('https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/uploads/admin/20250730151306_6889c60216d35.jpg'); */
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  border-radius: 8rpx;
  position: relative;
  margin: 20rpx 24rpx;
}

.card-content {
  position: relative;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 32rpx;
}

.company-name {
  position: absolute;
  font-size: 30rpx;
  font-weight: 100;
  color: white;
  margin-bottom: 20rpx;
  /* text-shadow: 1rpx 1rpx 2rpx rgba(255,255,255,0.8); */
  left: 43rpx;
}

.row{
  display: flex;
  align-items: center;
  margin-top: 92rpx;
  margin-left: 40rpx;
}

.person-name {
  font-size: 36rpx;
  font-weight: 100;
  color: white;
}

.position {
  font-size: 22rpx;
  color: white;
  font-weight: 100;
  margin-left: 20rpx;
}

.phone {
  position: absolute;
  top: 205rpx;
  left: 100rpx;
  font-size: 24rpx;
  color: white;
}

.email{
  position: absolute;
  top: 250rpx;
  left: 100rpx;
  font-size: 24rpx;
  color: white;
}

.address{
  position: absolute;
  top: 295rpx;
  left: 100rpx;
  color: white;
  font-size: 24rpx;
  width: 300rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
}

.info{
  width: 702rpx;
  background-color: white;
  margin: 20rpx 24rpx;
  border-radius: 8rpx;
}

.info .info-item{
  position: relative;
  padding: 24rpx 24rpx;
  display: flex;
  align-items: center;
}

.info .info-item::after{
  content: '';
  position: absolute;
  left: 24rpx;
  right: 24rpx;
  bottom: 0;
  height: 1rpx;
  background: #f0f0f0;
}

.info-item .title{
  font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
  font-size: 30rpx;
  color: #6b7280;
  margin-right: 20rpx;
}

.info-item .value{
  font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
  font-size: 30rpx;
  color: #3d3d3d;
  flex: 1;
}

.info-item .value input {
  font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
  font-size: 30rpx;
  color: #3d3d3d;
  border: none;
  background: transparent;
  width: 100%;
}