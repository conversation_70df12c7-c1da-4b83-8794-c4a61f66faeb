// pages/set/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: 20, // 默认值，会在onLoad中获取实际高度
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取系统信息，设置状态栏高度
    this.getSystemInfo();
  },

  /**
   * 获取系统信息
   */
  getSystemInfo() {
    wx.getSystemInfo({
      success: (res) => {
        this.setData({
          statusBarHeight: res.statusBarHeight,
        });
      }
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack({
      delta: 1,
      fail: () => {
        wx.switchTab({
          url: '/pages/my/index'
        });
      }
    });
  },

  /**
   * 跳转到个人信息修改页面
   */
  goToPersonalInfo() {
    wx.navigateTo({
      url: '/pages/set/revise'
    });
  },

  /**
   * 跳转到简易设置页面
   */
  goToSimpleSettings() {
    // 暂时只提示，后续可以实现对应页面
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  /**
   * 跳转到注销账号页面
   */
  goToCancelAccount() {
    // 暂时只提示，后续可以实现对应页面
    wx.showModal({
      title: '提示',
      content: '确定要注销账号吗？该操作不可恢复！',
      confirmText: '暂不注销',
      cancelText: '考虑一下',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '功能开发中',
            icon: 'none'
          });
        }
      }
    });
  }
})