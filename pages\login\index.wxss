/* 页面样式 */
page {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: fixed;
  -webkit-overflow-scrolling: touch;
  background-color: #F5F5F5;
}

/* 容器样式 */
.container {
  min-height: 100vh;
  height: 100%;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  overflow: hidden;
}

/* 自定义导航栏样式 */
.custom-nav {
  width: 100%;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.status-bar {
  width: 100%;
}

.nav-content {
  height: 44px;
  display: flex;
  align-items: center;
  position: relative;
}

.back-icon {
  position: absolute;
  left: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

/* 主内容区域 */
.main-content {
  position: relative;
  z-index: 10;
  margin-top: 0;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 20rpx 40rpx;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* 头像区域 */
.avatar-container {
  margin-top: 0;
  margin-bottom: 40rpx;
}

.avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 80rpx;
  background-color: #f0f0f0;
}

/* 提示文字 */
.login-tip {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 80rpx;
}

/* 登录按钮 */
.login-btn {
  width: 327px;
  height: 40px;
  line-height: 40px;
  background-color: #3B82F6;
  color: #fff;
  font-size: 28rpx;
  border-radius: 8px;
  margin-bottom: 30rpx;
  font-weight: normal;
  box-sizing: border-box;
  padding: 0;
}

.login-btn::after {
  border: none;
}

/* 禁用状态的登录按钮 */
.login-btn-disabled {
  background-color: #cccccc !important;
  color: #ffffff !important;
  opacity: 0.7;
}

/* 账号密码登录按钮 */
.account-login-btn {
  width: 327px;
  height: 40px;
  line-height: 40px;
  border-radius: 8px;
  background: #FFFFFF;
  border: 1px solid #3B82F6;
  color: #3B82F6;
  font-size: 28rpx;
  margin-bottom: 30rpx;
  font-weight: normal;
  box-sizing: border-box;
  padding: 0;
}

.account-login-btn::after {
  border: none;
}

/* 协议区域 */
.agreement-container {
  display: flex;
  align-items: center;
  margin-top: 20rpx;
  width: 327px;
  height: 34px;
}

.checkbox {
  transform: scale(0.7);
  margin-right: 8rpx;
}

.agreement-text {
  font-family: "Source Han Sans", sans-serif;
  font-size: 12px;
  font-weight: normal;
  line-height: normal;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  letter-spacing: normal;
  color: #6B7280;
}

.link {
  color: #3B82F6;
}

/* iOS底部安全区域适配 */
@supports (padding-bottom: constant(safe-area-inset-bottom)) {
  .container {
    padding-bottom: constant(safe-area-inset-bottom);
  }
}

@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .container {
    padding-bottom: env(safe-area-inset-bottom);
  }
}