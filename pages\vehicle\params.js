// pages/vehicle/params.js
import api from '../../utils/api';
import util from '../../utils/util';

Page({

  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: wx.getSystemInfoSync().statusBarHeight,
    headerPlaceholderHeight: 100, // 默认占位高度，减小默认值
    currentTab: 0, // 当前主标签页：0-车辆档案，1-参数配置
    paramTab: 0, // 当前参数配置子标签页：0-发动机，1-电动机...
    id: null, // 车辆ID
    type: 'new', // 车辆类型
    vehicleInfo: {}, // 车辆详情数据

    // 参数配置数据
    paramConfigs: {
      // 车身尺寸 (用于车辆档案页面)
      bodySize: [
        { label: '长度(mm)', value: '4995' },
        { label: '宽度(mm)', value: '1910' },
        { label: '高度(mm)', value: '1495' },
        { label: '轴距(mm)', value: '2920' },
        { label: '前轮距(mm)', value: '1640' },
        { label: '后轮距(mm)', value: '1650' },
        { label: '车身结构', value: '4门5座三厢车' },
        { label: '车门数', value: '4' },
        { label: '车门开启方式', value: '平开门' },
        { label: '座位数', value: '5' },
        { label: '整备质量(kg)', value: '1910' },
        { label: '满载质量(kg)', value: '2385' },
        { label: '油箱容量(L)', value: '50.0' },
        { label: '行李厢容积(L)', value: '521' },
        { label: '最小转弯半径', value: '6.15m' }
      ],

      // 车辆信息 (用于车辆档案页面)  
      vehicleInfo: [
        { label: '品牌', value: '比亚迪' },
        { label: '车系', value: '汉DM' },
        { label: '厂商', value: '比亚迪' },
        { label: '车身类型', value: 'sedan' }
      ],

      // 发动机
      0: [
        { label: '发动机型号', value: 'BYD472ZQB' },
        { label: '排量(ml)', value: '1498' },
        { label: '排量(L)', value: '1.5' },
        { label: '进气形式', value: '涡轮增压' },
        { label: '发动机布局', value: '前置' },
        { label: '气缸排列形式', value: '直列' },
        { label: '气缸数(个)', value: '4' },
        { label: '每缸气门数', value: '4' },
        { label: '配气结构', value: 'DOHC' },
        { label: '最大马力(Ps)', value: '156' },
        { label: '最大功率(kW)', value: '115' },
        { label: '最大功率转速(rpm)', value: '--' },
        { label: '最大扭矩(N.m)', value: '225' },
        { label: '最大扭矩转速(rpm)', value: '--' },
        { label: '燃料形式', value: '插电式混合动力' },
        { label: '燃油标号', value: '92#' },
        { label: '供油方式', value: '缸内直喷' },
        { label: '缸盖材料', value: '铝合金' },
        { label: '缸体材料', value: '铝合金' },
        { label: '环保标准', value: '国VI' }
      ],

      // 电动机
      1: [
        { label: '电机描述', value: '插电式混动 272马力' },
        { label: '电机类型', value: '永磁/同步' },
        { label: '电机总功率(kW)', value: '200' },
        { label: '前电机最大马力(Ps)', value: '272' },
        { label: '电机总扭矩(N.m)', value: '315' },
        { label: '前电机最大扭矩(N.m)', value: '315' },
        { label: '后电机最大扭矩(N.m)', value: '--' },
        { label: '电机系统扭矩(N.m)', value: '--' },
        { label: '前电机最大功率(kW)', value: '200' },
        { label: '后电机最大功率(kW)', value: '--' },
        { label: '电机系统功率(kW)', value: '--' },
        { label: '电机数量', value: '单电机' },
        { label: '电机布局', value: '前置' },
        { label: '最大扭矩转速(rpm)', value: '--' }
      ],

      // 电池/充电
      2: [
        { label: '电池类型', value: '磷酸铁锂电池' },
        { label: '电池容量(kWh)', value: '15.2' },
        { label: '电池能量密度(Wh/kg)', value: '143.9' },
        { label: '电池包质保', value: '8年或15万公里' },
        { label: '充电方式', value: '直流快充+交流慢充' },
        { label: '快充时间', value: '30%-80%(0.5小时)' },
        { label: '慢充时间', value: '0%-100%(3小时)' },
        { label: '快充功率(kW)', value: '40' },
        { label: '慢充功率(kW)', value: '3.3' },
        { label: '充电口类型', value: '国标' },
        { label: '电池组质量(kg)', value: '111' },
        { label: '电池组体积(L)', value: '80' },
        { label: '电池热管理', value: '液冷' }
      ],

      // 变速箱
      3: [
        { label: '简称', value: 'E-CVT' },
        { label: '变速箱类型', value: 'CVT无级变速' },
        { label: '挡位个数', value: '无级变速' },
        { label: '变速箱描述', value: '电子无级变速箱' },
        { label: '换挡方式', value: '手自一体' },
        { label: '传动比范围', value: '2.6-0.4' },
        { label: '变速箱品牌', value: '比亚迪' }
      ],

      // 底盘转向
      4: [
        { label: '驱动方式', value: '前置前驱' },
        { label: '前悬架类型', value: '麦弗逊式独立悬架' },
        { label: '后悬架类型', value: '多连杆式独立悬架' },
        { label: '助力类型', value: '电动助力' },
        { label: '承载式车身', value: '是' },
        { label: '中央差速器锁止功能', value: '--' },
        { label: '限滑差速器/差速锁', value: '--' },
        { label: '空气悬架', value: '--' },
        { label: '主动悬架', value: '--' },
        { label: '可变悬架', value: '--' }
      ],

      // 车轮制动
      5: [
        { label: '前制动器类型', value: '通风盘式' },
        { label: '后制动器类型', value: '盘式' },
        { label: '驻车制动类型', value: '电子驻车' },
        { label: '前轮胎规格', value: '235/50 R18' },
        { label: '后轮胎规格', value: '235/50 R18' },
        { label: '备胎规格', value: '非全尺寸' },
        { label: '前轮毂规格', value: '18英寸' },
        { label: '后轮毂规格', value: '18英寸' },
        { label: '轮毂材质', value: '铝合金' },
        { label: '轮胎品牌', value: '韩泰' },
        { label: '胎压监测', value: '有' }
      ],

      // 主动安全
      6: [
        { label: 'ABS防抱死', value: '●' },
        { label: '制动力分配(EBD/CBC等)', value: '●' },
        { label: '刹车辅助(EBA/BAS/BA等)', value: '●' },
        { label: '牵引力控制(ASR/TCS/TRC等)', value: '●' },
        { label: '车身稳定控制(ESC/ESP/DSC等)', value: '●' },
        { label: '上坡辅助', value: '●' },
        { label: '陡坡缓降', value: '--' },
        { label: '自动驻车', value: '●' },
        { label: '疲劳提醒', value: '●' },
        { label: '前碰撞预警', value: '●' },
        { label: '后碰撞预警', value: '●' },
        { label: '倒车车侧预警', value: '●' }
      ],

      // 被动安全
      7: [
        { label: '主驾驶安全气囊', value: '●' },
        { label: '副驾驶安全气囊', value: '●' },
        { label: '前排侧气囊', value: '●' },
        { label: '后排侧气囊', value: '--' },
        { label: '前排头部气囊(气帘)', value: '●' },
        { label: '后排头部气囊(气帘)', value: '●' },
        { label: '膝部气囊', value: '--' },
        { label: '安全带未系提示', value: '●' },
        { label: 'ISOFIX儿童座椅接口', value: '●' },
        { label: '发动机电子防盗', value: '●' },
        { label: '车内中控锁', value: '●' },
        { label: '遥控钥匙', value: '●' }
      ],

      // 辅助操控
      8: [
        { label: '前雷达', value: '●' },
        { label: '后雷达', value: '●' },
        { label: '倒车影像', value: '●' },
        { label: '360度全景影像', value: '●' },
        { label: '透明底盘', value: '--' },
        { label: '巡航控制', value: '●' },
        { label: '自适应巡航', value: '●' },
        { label: '自动泊车入位', value: '●' },
        { label: '发动机启停技术', value: '●' },
        { label: '自动驻车', value: '●' },
        { label: '上坡辅助', value: '●' },
        { label: '陡坡缓降', value: '--' }
      ],

      // 外部配置
      9: [
        { label: '天窗类型', value: '全景天窗' },
        { label: '运动外观套件', value: '●' },
        { label: '铝合金轮毂', value: '●' },
        { label: '电动吸合门', value: '--' },
        { label: '侧滑门', value: '--' },
        { label: '电动后备厢', value: '●' },
        { label: '感应后备厢', value: '●' },
        { label: '车顶行李架', value: '--' },
        { label: '车身包围', value: '●' },
        { label: '隐私玻璃', value: '●' }
      ],

      // 内部配置
      10: [
        { label: '真皮方向盘', value: '●' },
        { label: '方向盘调节', value: '手动上下+前后调节' },
        { label: '方向盘电动调节', value: '--' },
        { label: '多功能方向盘', value: '●' },
        { label: '方向盘换挡', value: '--' },
        { label: '方向盘加热', value: '--' },
        { label: '方向盘记忆', value: '--' },
        { label: '全液晶仪表盘', value: '●' },
        { label: '液晶仪表尺寸', value: '12.3英寸' },
        { label: 'HUD抬头显示', value: '--' }
      ],

      // 舒适/防盗配置
      11: [
        { label: '无钥匙进入', value: '●' },
        { label: '无钥匙启动', value: '●' },
        { label: '远程启动', value: '●' },
        { label: '电动车窗', value: '●' },
        { label: '车窗一键升/降', value: '●' },
        { label: '车窗防夹手功能', value: '●' },
        { label: '防紫外线/隔热玻璃', value: '●' },
        { label: '多层隔音玻璃', value: '●' },
        { label: '遮阳板化妆镜', value: '●' },
        { label: '后视镜电动调节', value: '●' },
        { label: '后视镜加热', value: '●' },
        { label: '内/外后视镜自动防眩目', value: '●' }
      ],

      // 座椅配置
      12: [
        { label: '座椅材质', value: '皮革' },
        { label: '运动风格座椅', value: '●' },
        { label: '主驾驶座电动调节', value: '●' },
        { label: '副驾驶座电动调节', value: '●' },
        { label: '主驾驶座椅记忆', value: '●' },
        { label: '副驾驶座椅记忆', value: '--' },
        { label: '前排座椅加热', value: '●' },
        { label: '前排座椅通风', value: '●' },
        { label: '前排座椅按摩', value: '--' },
        { label: '后排座椅加热', value: '--' },
        { label: '后排座椅通风', value: '--' },
        { label: '后排座椅按摩', value: '--' }
      ],

      // 智能互联
      13: [
        { label: '中控屏幕', value: '●' },
        { label: '中控屏尺寸', value: '15.6英寸' },
        { label: 'GPS导航', value: '●' },
        { label: '导航路况信息显示', value: '●' },
        { label: '道路救援呼叫', value: '●' },
        { label: '车联网', value: '●' },
        { label: 'OTA升级', value: '●' },
        { label: 'WiFi热点', value: '●' },
        { label: '蓝牙/车载电话', value: '●' },
        { label: '手机互联/映射', value: '●' },
        { label: '语音识别控制', value: '●' },
        { label: '手势控制', value: '--' }
      ],

      // 影音娱乐
      14: [
        { label: '扬声器品牌', value: 'Infinity' },
        { label: '扬声器数量', value: '10-11喇叭' },
        { label: 'CD/DVD', value: '--' },
        { label: 'USB/Type-C', value: '●' },
        { label: 'AUX外接音源', value: '●' },
        { label: '后排液晶屏', value: '--' },
        { label: '中控液晶屏分屏显示', value: '●' },
        { label: '后排控制多媒体', value: '--' },
        { label: '220V/230V电源', value: '●' },
        { label: '行车记录仪', value: '●' }
      ],

      // 灯光配置
      15: [
        { label: 'LED近光灯', value: '●' },
        { label: 'LED远光灯', value: '●' },
        { label: 'LED日间行车灯', value: '●' },
        { label: '自适应远近光', value: '●' },
        { label: '自动头灯', value: '●' },
        { label: '转向辅助灯', value: '●' },
        { label: '转向头灯', value: '--' },
        { label: '前雾灯', value: '●' },
        { label: 'LED前雾灯', value: '●' },
        { label: '大灯高度可调', value: '●' },
        { label: '大灯清洗装置', value: '--' },
        { label: '车内氛围灯', value: '●' }
      ],

      // 玻璃/后视镜
      16: [
        { label: '前电动车窗', value: '●' },
        { label: '后电动车窗', value: '●' },
        { label: '车窗一键升降', value: '●' },
        { label: '车窗防夹手功能', value: '●' },
        { label: '防紫外线/隔热玻璃', value: '●' },
        { label: '后视镜电动调节', value: '●' },
        { label: '后视镜加热', value: '●' },
        { label: '后视镜电动折叠', value: '●' },
        { label: '后视镜记忆', value: '●' },
        { label: '后视镜倒车自动下翻', value: '--' },
        { label: '内后视镜自动防眩目', value: '●' },
        { label: '外后视镜自动防眩目', value: '●' }
      ],

      // 空调冰箱
      17: [
        { label: '空调控制方式', value: '自动空调' },
        { label: '后排独立空调', value: '--' },
        { label: '后座出风口', value: '●' },
        { label: '温度分区控制', value: '●' },
        { label: '车内空气调节/花粉过滤', value: '●' },
        { label: '车载空气净化器', value: '●' },
        { label: '负离子发生器', value: '●' },
        { label: '车内PM2.5过滤装置', value: '●' },
        { label: '车载冰箱', value: '--' }
      ],

      // 智能化配置
      18: [
        { label: '自适应巡航', value: '●' },
        { label: '全速自适应巡航', value: '●' },
        { label: '车道偏离预警', value: '●' },
        { label: '车道保持辅助', value: '●' },
        { label: '车道居中保持', value: '●' },
        { label: '道路交通标识识别', value: '●' },
        { label: '主动刹车/主动安全系统', value: '●' },
        { label: '疲劳驾驶提示', value: '●' },
        { label: '自动泊车', value: '●' },
        { label: '遥控泊车', value: '●' },
        { label: '驾驶辅助影像', value: '●' },
        { label: '驾驶模式切换', value: '●' }
      ],

      // 选装包
      19: [
        { label: '智能驾驶选装包', value: '¥8,000' },
        { label: '舒适选装包', value: '¥12,000' },
        { label: '豪华内饰选装包', value: '¥15,000' },
        { label: '运动外观选装包', value: '¥6,000' },
        { label: '音响升级包', value: '¥4,000' },
        { label: '座椅升级包', value: '¥10,000' },
        { label: '安全防护包', value: '¥3,000' },
        { label: '冬季选装包', value: '¥5,000' }
      ]
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 计算头部占位符高度，减少额外高度
    this.setData({
      headerPlaceholderHeight: wx.getSystemInfoSync().statusBarHeight + 64, // 从88减少到64
      id: options.id,
      type: options.type || 'new'
    });

    // 获取车辆详情数据
    if (options.id) {
      this.getVehicleDetail(options.id, options.type || 'new');
    }
  },

  // 获取车辆详情数据
  async getVehicleDetail(id, type) {
    wx.showLoading({
      title: '加载中...',
    });

    // 构建查询参数
    const queryParams = {
      id,
      type
    };

    try {
      const result = await api.car.getDetail(queryParams);
      if (result) {
        // 处理基本信息
        const vehicleInfo = {
          title: result.ui_vehicle_name || '--',
          publishTime: util.formatDate(result.create_time) || '--',
          location: result.vehicle_location || '--',
          guidePrice: result.official_price || '--',
          salePrice: result.msrp || '--',
          brand: result.brand_name || '--',
          series: result.series_name || '--',
          manufacturer: result.make || '--',
          bodyType: result.body_type || '--',
          level: result.level_type || '--',
          stock: result.inventory_qty || '--'
        };

        // 创建一个新的paramConfigs对象，复制原始数据
        const paramConfigs = JSON.parse(JSON.stringify(this.data.paramConfigs));

        // 处理规格数据
        if (result.specs && result.specs.length > 0) {
          const spec = result.specs[0];

          // 解析车身尺寸
          if (spec.body_cs) {
            try {
              const csData = JSON.parse(spec.body_cs.replace(/'/g, '"'));
              vehicleInfo.length = csData.length || '--';
              vehicleInfo.width = csData.width || '--';
              vehicleInfo.height = csData.height || '--';
              vehicleInfo.wheelbase = csData.wheelbase || '--';
              vehicleInfo.bodyStruct = csData.body_struct || '--';
              vehicleInfo.seatCount = csData.seat_count || '--';
              vehicleInfo.curbWeight = csData.curb_weight || '--';
              vehicleInfo.fullLoadWeight = csData.full_load_weight || '--';
              vehicleInfo.oilTankVolume = csData.oil_tank_volume || '--';
              vehicleInfo.baggageVolume = csData.baggage_volume || '--';
              vehicleInfo.frontTrack = csData.front_track || '--';
              vehicleInfo.rearTrack = csData.rear_track || '--';
              vehicleInfo.doorNums = csData.door_nums || '--';
              vehicleInfo.doorOpenWay = csData.door_open_way || '--';
              vehicleInfo.minTurningRadius = csData.min_turning_radius || '--';

              // 使用API数据更新paramConfigs.bodySize数组
              paramConfigs.bodySize = [
                { label: '长度(mm)', value: csData.length || '--' },
                { label: '宽度(mm)', value: csData.width || '--' },
                { label: '高度(mm)', value: csData.height || '--' },
                { label: '轴距(mm)', value: csData.wheelbase || '--' },
                { label: '前轮距(mm)', value: csData.front_track || '--' },
                { label: '后轮距(mm)', value: csData.rear_track || '--' },
                { label: '车身结构', value: csData.body_struct || '--' },
                { label: '车门数', value: csData.door_nums || '--' },
                { label: '车门开启方式', value: csData.door_open_way || '--' },
                { label: '座位数', value: csData.seat_count || '--' },
                { label: '整备质量(kg)', value: csData.curb_weight || '--' },
                { label: '满载质量(kg)', value: csData.full_load_weight || '--' },
                { label: '油箱容量(L)', value: csData.oil_tank_volume || '--' },
                { label: '行李厢容积(L)', value: csData.baggage_volume || '--' },
                { label: '最小转弯半径', value: csData.min_turning_radius || '--' }
              ];
            } catch (e) {
              console.error('解析车身尺寸数据失败', e);
            }
          }

          // 更新车辆基本信息
          if (vehicleInfo.brand && vehicleInfo.series && vehicleInfo.manufacturer && vehicleInfo.bodyType) {
            paramConfigs.vehicleInfo = [
              { label: '品牌', value: vehicleInfo.brand },
              { label: '车系', value: vehicleInfo.series },
              { label: '厂商', value: vehicleInfo.manufacturer },
              { label: '车身类型', value: vehicleInfo.bodyType }
            ];
          }

          // 解析发动机参数
          if (spec.body_fdj) {
            try {
              const fdjData = JSON.parse(spec.body_fdj.replace(/'/g, '"'));
              vehicleInfo.engineModel = fdjData.engine_model || '--';
              vehicleInfo.cylinderVolume = fdjData.cylinder_volume_ml || '--';
              vehicleInfo.capacityL = fdjData.capacity_l || '--';
              vehicleInfo.gasForm = fdjData.gas_form || '--';
              vehicleInfo.engineLayoutForm = fdjData.engine_layout_form || '--';
              vehicleInfo.cylinderArrangement = fdjData.cylinder_arrangement || '--';
              vehicleInfo.cylinderNums = fdjData.cylinder_nums || '--';
              vehicleInfo.valvesPerCylinder = fdjData.valves_per_cylinder_nums || '--';
              vehicleInfo.airSupply = fdjData.air_supply || '--';
              vehicleInfo.engineMaxHorsepower = fdjData.engine_max_horsepower || '--';
              vehicleInfo.engineMaxPower = fdjData.engine_max_power || '--';
              vehicleInfo.maxPowerRevolution = fdjData.max_power_revolution || '--';
              vehicleInfo.engineMaxTorque = fdjData.engine_max_torque || '--';
              vehicleInfo.engineMaxTorqueRevolution = fdjData.engine_max_torque_revolution || '--';
              vehicleInfo.fuelForm = fdjData.fuel_form || '--';
              vehicleInfo.fuelLabel = fdjData.fuel_label || '--';
              vehicleInfo.oilSupply = fdjData.oil_supply || '--';
              vehicleInfo.cylinderHeadMaterial = fdjData.cylinder_head_material || '--';
              vehicleInfo.cylinderMaterial = fdjData.cylinder_material || '--';
              vehicleInfo.environmentalStandards = fdjData.environmental_standards || '--';

              // 更新发动机参数配置
              paramConfigs[0] = [
                { label: '发动机型号', value: fdjData.engine_model || '--' },
                { label: '排量(ml)', value: fdjData.cylinder_volume_ml || '--' },
                { label: '排量(L)', value: fdjData.capacity_l || '--' },
                { label: '进气形式', value: fdjData.gas_form || '--' },
                { label: '发动机布局', value: fdjData.engine_layout_form || '--' },
                { label: '气缸排列形式', value: fdjData.cylinder_arrangement || '--' },
                { label: '气缸数(个)', value: fdjData.cylinder_nums || '--' },
                { label: '每缸气门数', value: fdjData.valves_per_cylinder_nums || '--' },
                { label: '配气结构', value: fdjData.compression_ratio_s || '--' },
                { label: '最大马力(Ps)', value: fdjData.engine_max_horsepower || '--' },
                { label: '最大功率(kW)', value: fdjData.engine_max_power || '--' },
                { label: '最大功率转速(rpm)', value: fdjData.max_power_revolution || '--' },
                { label: '最大扭矩(N.m)', value: fdjData.engine_max_torque || '--' },
                { label: '最大扭矩转速(rpm)', value: fdjData.engine_max_torque_revolution || '--' },
                { label: '燃料形式', value: fdjData.fuel_form || '--' },
                { label: '燃油标号', value: fdjData.fuel_label || '--' },
                { label: '供油方式', value: fdjData.oil_supply || '--' },
                { label: '缸盖材料', value: fdjData.cylinder_head_material || '--' },
                { label: '缸体材料', value: fdjData.cylinder_material || '--' },
                { label: '环保标准', value: fdjData.environmental_standards || '--' }
              ];
            } catch (e) {
              console.error('解析发动机参数数据失败', e);
            }
          }

          // 解析电动机
          if (spec.body_ddj) {
            try {
              const ddjData = JSON.parse(spec.body_ddj.replace(/'/g, '"'));
              vehicleInfo.electricDescription = ddjData.electric_description || '--';
              vehicleInfo.electricType = ddjData.electric_type || '--';
              vehicleInfo.totalElectricPower = ddjData.total_electric_power || '--';
              vehicleInfo.frontElectricMaxHorsepower = ddjData.front_electric_max_horsepower || '--';
              vehicleInfo.totalElectricTorque = ddjData.total_electric_torque || '--';
              vehicleInfo.frontElectricMaxPower = ddjData.front_electric_max_power || '--';
              vehicleInfo.frontElectricMaxTorque = ddjData.front_electric_max_torque || '--';
              vehicleInfo.rearElectricMaxPower = ddjData.rear_electric_max_power || '--';
              vehicleInfo.rearElectricMaxTorque = ddjData.rear_electric_max_torque || '--';
              vehicleInfo.electricSystemPower = ddjData.electric_system_power || '--';
              vehicleInfo.electricSystemTorque = ddjData.electric_system_torque || '--';
              vehicleInfo.electricDriveNumber = ddjData.electric_drive_number || '--';
              vehicleInfo.electricLayout = ddjData.electric_layout || '--';

              // 更新电动机参数配置
              paramConfigs[1] = [
                { label: '电机描述', value: ddjData.electric_description || '--' },
                { label: '电机类型', value: ddjData.electric_type || '--' },
                { label: '电机总功率(kW)', value: ddjData.total_electric_power || '--' },
                { label: '前电机最大马力(Ps)', value: ddjData.front_electric_max_horsepower || '--' },
                { label: '电机总扭矩(N.m)', value: ddjData.total_electric_torque || '--' },
                { label: '前电机最大扭矩(N.m)', value: ddjData.front_electric_max_torque || '--' },
                { label: '后电机最大扭矩(N.m)', value: ddjData.rear_electric_max_torque || '--' },
                { label: '电机系统扭矩(N.m)', value: ddjData.electric_system_torque || '--' },
                { label: '前电机最大功率(kW)', value: ddjData.front_electric_max_power || '--' },
                { label: '后电机最大功率(kW)', value: ddjData.rear_electric_max_power || '--' },
                { label: '电机系统功率(kW)', value: ddjData.electric_system_power || '--' },
                { label: '电机数量', value: ddjData.electric_drive_number || '--' },
                { label: '电机布局', value: ddjData.electric_layout || '--' }
              ];
            } catch (e) {
              console.error('解析电动机数据失败', e);
            }
          }

          // 解析电池/充电数据
          if (spec.body_dccd) {
            try {
              const dccdData = JSON.parse(spec.body_dccd.replace(/'/g, '"'));

              // 更新电池/充电参数配置
              paramConfigs[2] = [
                { label: '电池类型', value: dccdData.battery_type || '--' },
                { label: '电池容量(kWh)', value: dccdData.battery_capacity || '--' },
                { label: '电池能量密度(Wh/kg)', value: dccdData.battery_energy_density || '--' },
                { label: '电池包质保', value: dccdData.battery_warranty || '--' },
                { label: '充电方式', value: dccdData.battery_charge || '--' },
                { label: '快充功率(kW)', value: dccdData.max_fast_charge_power || '--' },
                { label: '充电口类型', value: dccdData.quick_charge_position_v5 || '--' },
                { label: '电池热管理', value: dccdData.battery_temperature_management_system || '--' },
                { label: '电池品牌', value: dccdData.battery_brand || '--' }
              ];
            } catch (e) {
              console.error('解析电池/充电数据失败', e);
            }
          }

          // 解析变速箱数据
          if (spec.body_bsx) {
            try {
              const bsxData = JSON.parse(spec.body_bsx.replace(/'/g, '"'));

              // 更新变速箱参数配置
              paramConfigs[3] = [
                { label: '变速箱描述', value: bsxData.gearbox_description || '--' },
                { label: '挡位个数', value: bsxData.stalls || '--' },
                { label: '变速箱类型', value: bsxData.gearbox_type || '--' }
              ];
            } catch (e) {
              console.error('解析变速箱数据失败', e);
            }
          }

          // 解析底盘转向数据
          if (spec.body_dpzx) {
            try {
              const dpzxData = JSON.parse(spec.body_dpzx.replace(/'/g, '"'));

              // 更新底盘转向参数配置
              paramConfigs[4] = [
                { label: '驱动方式', value: dpzxData.driver_form || '--' },
                { label: '前悬架类型', value: dpzxData.front_suspension_form || '--' },
                { label: '后悬架类型', value: dpzxData.rear_suspension_form || '--' },
                { label: '助力类型', value: dpzxData.power_steering_type || '--' },
                { label: '车身结构', value: dpzxData.car_body_structure || '--' },
                { label: '四驱类型', value: dpzxData.fourwheel_drive_type || '--' }
              ];
            } catch (e) {
              console.error('解析底盘转向数据失败', e);
            }
          }

          // 解析车轮制动数据
          if (spec.body_clzd) {
            try {
              const clzdData = JSON.parse(spec.body_clzd.replace(/'/g, '"'));

              // 更新车轮制动参数配置
              paramConfigs[5] = [
                { label: '前制动器类型', value: clzdData.front_brake_type || '--' },
                { label: '后制动器类型', value: clzdData.rear_brake_type || '--' },
                { label: '驻车制动类型', value: clzdData.park_brake_type || '--' },
                { label: '前轮胎规格', value: clzdData.front_tire_size || '--' },
                { label: '后轮胎规格', value: clzdData.rear_tire_size || '--' },
                { label: '备胎规格', value: clzdData.spare_tire_specification || '--' },
                { label: '备胎位置', value: clzdData.spare_tire_placement || '--' }
              ];
            } catch (e) {
              console.error('解析车轮制动数据失败', e);
            }
          }

          // 解析主动安全数据
          if (spec.body_zdaq) {
            try {
              const zdaqData = JSON.parse(spec.body_zdaq.replace(/'/g, '"'));

              // 更新主动安全参数配置
              paramConfigs[6] = [
                { label: 'ABS防抱死', value: zdaqData.abs_anti_lock || '--' },
                { label: '制动力分配(EBD/CBC等)', value: zdaqData.brake_force || '--' },
                { label: '刹车辅助(EBA/BAS/BA等)', value: zdaqData.brake_assist || '--' },
                { label: '牵引力控制(ASR/TCS/TRC等)', value: zdaqData.traction_control || '--' },
                { label: '车身稳定控制(ESC/ESP/DSC等)', value: zdaqData.body_stability_system || '--' },
                { label: '车辆预警系统', value: zdaqData.car_warning_system || '--' },
                { label: '主动刹车', value: zdaqData.active_brake || '--' },
                { label: '车道辅助', value: zdaqData.line_support || '--' },
                { label: '车道保持辅助', value: zdaqData.lane_keeping_assist || '--' },
                { label: '车道居中', value: zdaqData.lane_center || '--' },
                { label: '疲劳驾驶预警', value: zdaqData.fatigue_driving_warning || '--' },
                { label: '主动疲劳检测', value: zdaqData.active_dms_fatigue_detection || '--' },
                { label: '道路交通标识识别', value: zdaqData.road_traffic_sign_recognition || '--' },
                { label: '夜视系统', value: zdaqData.night_vision_system || '--' }
              ];
            } catch (e) {
              console.error('解析主动安全数据失败', e);
            }
          }

          // 解析被动安全数据
          if (spec.body_bdaq) {
            try {
              const bdaqData = JSON.parse(spec.body_bdaq.replace(/'/g, '"'));

              // 更新被动安全参数配置
              paramConfigs[7] = [
                { label: '主/副驾驶安全气囊', value: bdaqData.main_vice_airbag || '--' },
                { label: '前/后排侧气囊', value: bdaqData.front_rear_airbag || '--' },
                { label: '侧气帘', value: bdaqData.side_air_curtain || '--' },
                { label: '膝部气囊', value: bdaqData.main_vice_knee_airbag || '--' },
                { label: '前排中央气囊', value: bdaqData.front_near_center_airbag || '--' },
                { label: '安全带未系提示', value: bdaqData.seat_belt_prompted || '--' },
                { label: '胎压监测系统', value: bdaqData.tire_pressure_system || '--' },
                { label: '儿童座椅接口', value: bdaqData.child_seat_interface || '--' },
                { label: '被动行人保护', value: bdaqData.passive_pedestrian_protection || '--' },
                { label: '爆胎应急', value: bdaqData.explosion_tire || '--' }
              ];
            } catch (e) {
              console.error('解析被动安全数据失败', e);
            }
          }

          // 解析辅助操控数据
          if (spec.body_fzck) {
            try {
              const fzckData = JSON.parse(spec.body_fzck.replace(/'/g, '"'));

              // 更新辅助操控参数配置
              paramConfigs[8] = [
                { label: '前/后驻车雷达', value: fzckData.parking_radar || '--' },
                { label: '前车起步提醒', value: fzckData.forward_car_departure_reminder || '--' },
                { label: '窄路辅助', value: fzckData.narrow_road_assistance || '--' },
                { label: '驾驶辅助影像', value: fzckData.driving_assistant_camera || '--' },
                { label: '巡航系统', value: fzckData.cruise_system || '--' },
                { label: '自动变道', value: fzckData.auto_road_change || '--' },
                { label: '自动上下高速', value: fzckData.auto_road_out_in || '--' },
                { label: '导航辅助驾驶', value: fzckData.navigation_assisted_driving || '--' },
                { label: '自动驾驶级别', value: fzckData.automatic_drive_level || '--' },
                { label: '自动泊车入位', value: fzckData.auto_park_entry || '--' },
                { label: '遥控泊车', value: fzckData.track_reverse || '--' },
                { label: '记忆泊车', value: fzckData.memory_parking || '--' },
                { label: '自主泊车', value: fzckData.automated_valet_parking || '--' },
                { label: '自动驻车', value: fzckData.auto_park || '--' },
                { label: '上坡辅助', value: fzckData.uphill_support || '--' },
                { label: '陡坡缓降', value: fzckData.steep_slope || '--' },
                { label: '发动机启停技术', value: fzckData.engine_sas_tech || '--' },
                { label: '可变悬架', value: fzckData.variable_suspension || '--' },
                { label: '空气悬架', value: fzckData.air_suspension || '--' },
                { label: '电磁感应悬架', value: fzckData.electromagnetic_induct_suspension || '--' },
                { label: '车身姿态控制', value: fzckData.magic_body_control || '--' },
                { label: '可变转向系统', value: fzckData.variable_steer_system || '--' },
                { label: '前桥限滑差速', value: fzckData.front_slip_method || '--' },
                { label: '后桥限滑差速', value: fzckData.rear_slip_method || '--' },
                { label: '中央差速锁', value: fzckData.central_differential_lock || '--' },
                { label: '低速四驱', value: fzckData.four_wd_low || '--' },
                { label: '整体转向系统', value: fzckData.overall_turn || '--' },
                { label: '驾驶模式', value: fzckData.drive_mode || '--' },
                { label: '制动能量回收', value: fzckData.brake_energy_regeneration || '--' },
                { label: '低速行车提示音', value: fzckData.low_speed_driving_warning || '--' }
              ];
            } catch (e) {
              console.error('解析辅助操控数据失败', e);
            }
          }

          // 解析外部配置数据
          if (spec.body_wbpz) {
            try {
              const wbpzData = JSON.parse(spec.body_wbpz.replace(/'/g, '"'));

              // 更新外部配置参数配置
              paramConfigs[9] = [
                { label: '天窗类型', value: wbpzData.skylight_type || '--' },
                { label: '感应天幕', value: wbpzData.light_sensing_canopy || '--' },
                { label: '车顶行李架', value: wbpzData.roof_racks || '--' },
                { label: '运动外观套件', value: wbpzData.sports_appearance_kit || '--' },
                { label: '电动扰流板', value: wbpzData.electric_spoiler || '--' },
                { label: '主动进气格栅', value: wbpzData.active_closed_inlet_grid || '--' },
                { label: '铝合金轮毂', value: wbpzData.alloy_wheel || '--' },
                { label: '侧踏板', value: wbpzData.side_footrest || '--' },
                { label: '无框车门', value: wbpzData.frameless_design_door || '--' },
                { label: '隐藏式门把手', value: wbpzData.hidden_door_handle || '--' },
                { label: '迎宾功能', value: wbpzData.welcome_function || '--' },
                { label: '拖车钩', value: wbpzData.drag_hook || '--' }
              ];
            } catch (e) {
              console.error('解析外部配置数据失败', e);
            }
          }

          // 解析内部配置数据
          if (spec.body_nbpz) {
            try {
              const nbpzData = JSON.parse(spec.body_nbpz.replace(/'/g, '"'));

              // 更新内部配置参数配置
              paramConfigs[10] = [
                { label: '真皮方向盘', value: nbpzData.steer_wheel_material || '--' },
                { label: '方向盘调节', value: nbpzData.steer_wheel_adjustment || '--' },
                { label: '方向盘电动调节', value: nbpzData.elec_steer_wheel_adjustment || '--' },
                { label: '多功能方向盘', value: nbpzData.steer_wheel_functional || '--' },
                { label: '方向盘换挡', value: nbpzData.gear_shift_mode || '--' },
                { label: '行车电脑显示屏', value: nbpzData.driving_computer_display_screen || '--' },
                { label: '液晶仪表类型', value: nbpzData.lcd_dashboard_type || '--' },
                { label: '液晶仪表尺寸', value: nbpzData.lcd_dashboard_size || '--' }
              ];
            } catch (e) {
              console.error('解析内部配置数据失败', e);
            }
          }

          // 解析舒适防盗配置数据
          if (spec.body_ssfd) {
            try {
              const ssfdData = JSON.parse(spec.body_ssfd.replace(/'/g, '"'));

              // 更新舒适防盗配置参数配置
              paramConfigs[11] = [
                { label: '电动车门', value: ssfdData.electric_door || '--' },
                { label: '电动后备厢', value: ssfdData.electric_back_door || '--' },
                { label: '感应后备厢', value: ssfdData.inductive_back_door || '--' },
                { label: '电动后备厢记忆', value: ssfdData.electric_back_door_memory || '--' },
                { label: '发动机电子防盗', value: ssfdData.engine_anti_theft || '--' },
                { label: '车内中控锁', value: ssfdData.central_locking_car || '--' },
                { label: '遥控钥匙', value: ssfdData.remote_key || '--' },
                { label: '无钥匙进入', value: ssfdData.keyless_entry || '--' },
                { label: '无钥匙启动', value: ssfdData.keyless_start || '--' },
                { label: '远程启动发动机', value: ssfdData.engine_remote_start || '--' },
                { label: '车载呼叫', value: ssfdData.car_call || '--' },
                { label: '抬头显示系统', value: ssfdData.header_display_system || '--' },
                { label: 'HUD尺寸', value: ssfdData.hud_size || '--' },
                { label: '内置行车记录仪', value: ssfdData.built_in_tachograph || '--' },
                { label: '主动降噪', value: ssfdData.active_noise_reduction || '--' },
                { label: '手机无线充电', value: ssfdData.mobile_wireless_charging || '--' },
                { label: '无线充电最大功率', value: ssfdData.wireless_charging_max_power || '--' },
                { label: '车载电源', value: ssfdData.power_outlet || '--' },
                { label: '后备厢12V电源', value: ssfdData.baggage_12v_power_outlet || '--' }
              ];
            } catch (e) {
              console.error('解析舒适防盗配置数据失败', e);
            }
          }

          // 解析座椅配置数据
          if (spec.body_zypz) {
            try {
              const zypzData = JSON.parse(spec.body_zypz.replace(/'/g, '"'));

              // 更新座椅配置参数配置
              paramConfigs[12] = [
                { label: '座椅材质', value: zypzData.seat_material || '--' },
                { label: '座椅皮质风格', value: zypzData.seat_cork_style || '--' },
                { label: '运动风格座椅', value: zypzData.sport_style_seat || '--' },
                { label: '座椅布局', value: zypzData.layout_seat || '--' },
                { label: '第二排独立座椅', value: zypzData.second_independent_seat || '--' },
                { label: '第三排座椅数', value: zypzData.third_row_seat_count || '--' },
                { label: '行政座椅', value: zypzData.queen_seat || '--' },
                { label: '座椅电动调节', value: zypzData.seat_electrical_adjustment || '--' },
                { label: '主驾整体调节', value: zypzData.main_drive_whole_adjustment || '--' },
                { label: '主驾局部调节', value: zypzData.main_drive_part_adjustment || '--' },
                { label: '副驾整体调节', value: zypzData.vice_drive_whole_adjustment || '--' },
                { label: '副驾局部调节', value: zypzData.vice_drive_part_adjustment || '--' },
                { label: '第二排座椅调节', value: zypzData.second_seat_control_functional || '--' },
                { label: '第二排小桌板', value: zypzData.second_row_small_desktop || '--' },
                { label: '前排座椅功能', value: zypzData.front_seat_functional || '--' },
                { label: '后排座椅功能', value: zypzData.rear_seat_functional || '--' },
                { label: '第三排座椅功能', value: zypzData.third_seat_functional || '--' },
                { label: '副驾后调节按钮', value: zypzData.co_pilot_rear_adjustable_button || '--' },
                { label: '中央扶手', value: zypzData.centre_armrest || '--' },
                { label: '后排杯架', value: zypzData.rear_cup_holder || '--' },
                { label: '冷热杯架', value: zypzData.hot_cold_cup_holder || '--' },
                { label: '前排座椅放倒', value: zypzData.front_seats_flattened || '--' },
                { label: '第二排座椅比例放倒', value: zypzData.second_row_seat_down_ratio || '--' },
                { label: '后排座椅电动放倒', value: zypzData.rear_seat_electric_down || '--' }
              ];
            } catch (e) {
              console.error('解析座椅配置数据失败', e);
            }
          }

          // 解析智能互联数据
          if (spec.body_znhl) {
            try {
              const znhlData = JSON.parse(spec.body_znhl.replace(/'/g, '"'));

              // 更新智能互联参数配置
              paramConfigs[13] = [
                { label: '中控屏幕', value: znhlData.center_screen_size ? '●' : '--' },
                { label: '中控屏尺寸', value: znhlData.center_screen_size || '--' },
                { label: '中控屏材质', value: znhlData.center_console_screen_material || '--' },
                { label: '中控屏分辨率', value: znhlData.center_screen_resolution || '--' },
                { label: '中控屏PPI', value: znhlData.center_screen_ppi || '--' },
                { label: '副屏尺寸', value: znhlData.vice_screen_size || '--' },
                { label: '副屏分辨率', value: znhlData.vice_screen_resolution || '--' },
                { label: '副屏PPI', value: znhlData.vice_screen_ppi || '--' },
                { label: '振动反馈', value: znhlData.vibrate_feedback || '--' },
                { label: 'GPS导航', value: znhlData.gps || '--' },
                { label: 'AR实景导航', value: znhlData.ar_reality_navigation || '--' },
                { label: '导航系统', value: znhlData.navigation_system || '--' },
                { label: '定位服务', value: znhlData.position_service || '--' },
                { label: '蓝牙/车载电话', value: znhlData.bluetooth_and_car_phone || '--' },
                { label: '手机互联/映射', value: znhlData.mobile_system || '--' },
                { label: '车联网', value: znhlData.car_networking || '--' },
                { label: '数据网络', value: znhlData.data_network || '--' },
                { label: 'OTA升级', value: znhlData.ota_upgrade || '--' },
                { label: '面部识别', value: znhlData.facial_recognition || '--' },
                { label: '指纹识别', value: znhlData.fingerprint_recognition || '--' },
                { label: '声纹识别', value: znhlData.voiceprint_recognition || '--' },
                { label: '情感识别', value: znhlData.emotion_recognition || '--' },
                { label: '语音识别控制', value: znhlData.speech_recognition_system || '--' },
                { label: '语音免唤醒', value: znhlData.voice_wake_up_free || '--' },
                { label: '语音唤醒识别', value: znhlData.voice_wake_up_recognition || '--' },
                { label: '语音识别', value: znhlData.voice_recognition || '--' },
                { label: '可视即可说', value: znhlData.visible_to_say || '--' },
                { label: '语音唤醒词', value: znhlData.voice_wake_up_word || '--' },
                { label: '手势控制', value: znhlData.gesture_control_system || '--' },
                { label: 'WiFi热点', value: znhlData.wifi || '--' }
              ];
            } catch (e) {
              console.error('解析智能互联数据失败', e);
            }
          }

          // 解析影音娱乐数据
          if (spec.body_yyyl) {
            try {
              const yyylData = JSON.parse(spec.body_yyyl.replace(/'/g, '"'));

              // 更新影音娱乐参数配置
              paramConfigs[14] = [
                { label: '多指屏幕控制', value: yyylData.multi_finger_screen_control || '--' },
                { label: '应用商店', value: yyylData.app_store || '--' },
                { label: '多媒体接口', value: yyylData.multimedia_interface || '--' },
                { label: 'USB/Type-C接口数量', value: yyylData.usb_typec_interface_count || '--' },
                { label: 'USB/Type-C最大充电功率', value: yyylData.usb_typec_interface_max_charging_power || '--' },
                { label: '车载电视', value: yyylData.car_tv || '--' },
                { label: '后排液晶屏', value: yyylData.rear_lcd_screen || '--' },
                { label: '声音模拟', value: yyylData.voice_simulate || '--' },
                { label: '放松模式', value: yyylData.relax_mode || '--' },
                { label: '卡拉OK', value: yyylData.karaoke || '--' },
                { label: '音响品牌', value: yyylData.sound_brand || '--' },
                { label: '扬声器数量', value: yyylData.speaker || '--' },
                { label: '杜比全景声', value: yyylData.dolby_panoramic_sound || '--' },
                { label: '音响系统布局', value: yyylData.sound_system_layout || '--' },
                { label: '放大器最大输出功率(W)', value: yyylData.amplifier_max_output_power_watt || '--' },
                { label: '天空声道', value: yyylData.sky_sound_channel || '--' },
                { label: '后排触控系统', value: yyylData.rear_touch_control_system || '--' }
              ];
            } catch (e) {
              console.error('解析影音娱乐数据失败', e);
            }
          }

          // 解析灯光配置数据
          if (spec.body_dgpz) {
            // 先打印原始数据
            console.log('【灯光配置】原始数据:', spec.body_dgpz);

            try {
              // 首先尝试直接将●字符处理为普通字符串内容
              let gzpzString = spec.body_dgpz.replace(/'/g, '"');
              console.log('【灯光配置】替换单引号后:', gzpzString);

              let gzpzData = {};

              try {
                // 先尝试直接解析
                gzpzData = JSON.parse(gzpzString);
                console.log('【灯光配置】第一次解析成功:', gzpzData);
              } catch (jsonError) {
                console.error('【灯光配置】第一次JSON解析失败，尝试特殊处理', jsonError);

                // 尝试更严格的字符替换
                gzpzString = spec.body_gzpz.replace(/'/g, '"').replace(/●/g, '"●"');
                console.log('【灯光配置】替换特殊字符后:', gzpzString);

                try {
                  gzpzData = JSON.parse(gzpzString);
                  console.log('【灯光配置】第二次解析成功:', gzpzData);
                } catch (jsonError2) {
                  console.error('【灯光配置】第二次JSON解析失败，尝试使用正则表达式', jsonError2);

                  // 如果两次JSON解析都失败，使用正则表达式提取
                  const keyValuePattern = /'([^']+)':'([^']+)'/g;
                  let match;
                  while ((match = keyValuePattern.exec(spec.body_gzpz)) !== null) {
                    gzpzData[match[1]] = match[2];
                    console.log(`【灯光配置】正则提取: ${match[1]} = ${match[2]}`);
                  }
                  console.log('【灯光配置】正则解析结果:', gzpzData);
                }
              }

              // 打印解析后的数据到控制台
              console.log('【灯光配置】解析后的数据:', gzpzData);
              console.log('【灯光配置】低灯值:', gzpzData.low_headlamp_type);
              console.log('【灯光配置】高灯值:', gzpzData.high_headlamp_type);
              console.log('【灯光配置】日行灯值:', gzpzData.daytime_light);

              // 只进行一次赋值操作
              paramConfigs[15] = [
                { label: 'LED近光灯', value: gzpzData.low_headlamp_type || '--' },
                { label: 'LED远光灯', value: gzpzData.high_headlamp_type || '--' },
                { label: 'LED日间行车灯', value: gzpzData.daytime_light || '--' },
                { label: '自适应远近光', value: gzpzData.adaptive_light || '--' },
                { label: '自动头灯', value: gzpzData.auto_headlamp || '--' },
                { label: '转向辅助灯', value: gzpzData.steer_assist_light || '--' },
                { label: '前雾灯', value: gzpzData.front_fog_light || '--' },
                { label: '大灯随动转向', value: gzpzData.headlamp_follow_up || '--' },
                { label: '大灯高度可调', value: gzpzData.headlight_height_adjustment || '--' },
                { label: '大灯清洗装置', value: gzpzData.headlight_clean_function || '--' },
                { label: '车内氛围灯', value: gzpzData.interior_light || '--' },
                { label: '主动氛围灯', value: gzpzData.active_ambient_light || '--' },
                { label: '灯光特殊功能', value: gzpzData.light_special_function || '--' },
                { label: '灯光投影技术', value: gzpzData.light_projection_technology || '--' },
                { label: '大灯延时关闭', value: gzpzData.headlamp_delay_off || '--' },
                { label: '大灯雨雾模式', value: gzpzData.headlamp_rain_fog_mode || '--' }
              ];

              // 打印处理后的参数配置
              console.log('【灯光配置】最终结果:', paramConfigs[15]);

            } catch (e) {
              console.error('【灯光配置】解析失败，错误详情:', e);
              console.error('【灯光配置】原始数据:', spec.body_gzpz);
            }
          } else {
            console.warn('【灯光配置】数据不存在，spec.body_gzpz为空');
          }

          // 解析玻璃/后视镜数据
          if (spec.body_blhsj) {
            // 先打印原始数据
            console.log('【玻璃后视镜】原始数据:', spec.body_blhsj);

            try {
              // 首先尝试直接将●字符处理为普通字符串内容
              let blhsjString = spec.body_blhsj.replace(/'/g, '"');
              console.log('【玻璃后视镜】替换单引号后:', blhsjString);

              let blhsjData = {};

              try {
                // 先尝试直接解析
                blhsjData = JSON.parse(blhsjString);
                console.log('【玻璃后视镜】第一次解析成功:', blhsjData);
              } catch (jsonError) {
                console.error('【玻璃后视镜】第一次JSON解析失败，尝试特殊处理', jsonError);

                // 尝试更严格的字符替换
                blhsjString = spec.body_blhsj.replace(/'/g, '"').replace(/●/g, '"●"');
                console.log('【玻璃后视镜】替换特殊字符后:', blhsjString);

                try {
                  blhsjData = JSON.parse(blhsjString);
                  console.log('【玻璃后视镜】第二次解析成功:', blhsjData);
                } catch (jsonError2) {
                  console.error('【玻璃后视镜】第二次JSON解析失败，尝试使用正则表达式', jsonError2);

                  // 如果两次JSON解析都失败，使用正则表达式提取
                  const keyValuePattern = /'([^']+)':'([^']+)'/g;
                  let match;
                  while ((match = keyValuePattern.exec(spec.body_blhsj)) !== null) {
                    blhsjData[match[1]] = match[2];
                    console.log(`【玻璃后视镜】正则提取: ${match[1]} = ${match[2]}`);
                  }
                  console.log('【玻璃后视镜】正则解析结果:', blhsjData);
                }
              }

              // 打印解析后的数据到控制台
              console.log('【玻璃后视镜】解析后的数据:', blhsjData);

              // 更新玻璃/后视镜参数配置
              paramConfigs[16] = [
                { label: '电动车窗', value: blhsjData.electric_window || '--' },
                { label: '车窗一键升降', value: blhsjData.window_one_key_lift || '--' },
                { label: '车窗防夹手功能', value: blhsjData.window_anti_clip_function || '--' },
                { label: '外后视镜功能', value: blhsjData.exter_mirror_functional || '--' },
                { label: '内后视镜功能', value: blhsjData.inside_mirror_functional || '--' },
                { label: '车窗遮阳镜', value: blhsjData.car_window_sunshade_mirror || '--' },
                { label: '后侧隐私玻璃', value: blhsjData.backside_privacy_glass || '--' },
                { label: '车窗遮阳帘', value: blhsjData.window_sunshade || '--' },
                { label: '雨感应雨刷', value: blhsjData.rain_induction_wiper || '--' },
                { label: '后雨刷', value: blhsjData.rear_wiper || '--' },
                { label: '后窗开启方式', value: blhsjData.rear_window_open_method || '--' },
                { label: '多层隔音玻璃', value: blhsjData.multilayer_soundproof_glass || '--' },
                { label: '前风挡电加热', value: blhsjData.front_windshield_electric_heating || '--' },
                { label: '喷水嘴加热', value: blhsjData.heated_nozzle || '--' }
              ];

              // 打印处理后的参数配置
              console.log('【玻璃后视镜】最终结果:', paramConfigs[16]);

            } catch (e) {
              console.error('【玻璃后视镜】解析失败，错误详情:', e);
              console.error('【玻璃后视镜】原始数据:', spec.body_blhsj);
            }
          } else {
            console.warn('【玻璃后视镜】数据不存在，spec.body_blhsj为空');
          }

          // 解析空调冰箱数据
          if (spec.body_ktbx) {
            // 先打印原始数据
            console.log('【空调冰箱】原始数据:', spec.body_ktbx);

            try {
              // 首先尝试直接将●字符处理为普通字符串内容
              let ktbxString = spec.body_ktbx.replace(/'/g, '"');
              console.log('【空调冰箱】替换单引号后:', ktbxString);

              let ktbxData = {};

              try {
                // 先尝试直接解析
                ktbxData = JSON.parse(ktbxString);
                console.log('【空调冰箱】第一次解析成功:', ktbxData);
              } catch (jsonError) {
                console.error('【空调冰箱】第一次JSON解析失败，尝试特殊处理', jsonError);

                // 尝试更严格的字符替换
                ktbxString = spec.body_ktbx.replace(/'/g, '"').replace(/●/g, '"●"');
                console.log('【空调冰箱】替换特殊字符后:', ktbxString);

                try {
                  ktbxData = JSON.parse(ktbxString);
                  console.log('【空调冰箱】第二次解析成功:', ktbxData);
                } catch (jsonError2) {
                  console.error('【空调冰箱】第二次JSON解析失败，尝试使用正则表达式', jsonError2);

                  // 如果两次JSON解析都失败，使用正则表达式提取
                  const keyValuePattern = /'([^']+)':'([^']+)'/g;
                  let match;
                  while ((match = keyValuePattern.exec(spec.body_ktbx)) !== null) {
                    ktbxData[match[1]] = match[2];
                    console.log(`【空调冰箱】正则提取: ${match[1]} = ${match[2]}`);
                  }
                  console.log('【空调冰箱】正则解析结果:', ktbxData);
                }
              }

              // 打印解析后的数据到控制台
              console.log('【空调冰箱】解析后的数据:', ktbxData);

              // 更新空调/冰箱参数配置
              paramConfigs[17] = [
                { label: '空调控制方式', value: ktbxData.air_control_model || '--' },
                { label: '后排独立空调', value: ktbxData.rear_independent_air_conditioning || '--' },
                { label: '后座出风口', value: ktbxData.rear_exhaust || '--' },
                { label: '隐藏式出风口', value: ktbxData.hidden_air_vent || '--' },
                { label: '温度分区控制', value: ktbxData.temperature_partition_control || '--' },
                { label: '车载空气净化器', value: ktbxData.car_purifier || '--' },
                { label: '车内PM2.5过滤装置', value: ktbxData.pm25_filtrating_equipment || '--' },
                { label: 'HEPA过滤装置', value: ktbxData.hepa_filter_device || '--' },
                { label: '负离子发生器', value: ktbxData.negative_ion_generator || '--' },
                { label: '车载香氛装置', value: ktbxData.car_fragrance_device || '--' },
                { label: 'AQS空气质量管理系统', value: ktbxData.aqs_air_quality_management_system || '--' },
                { label: '车载冰箱', value: ktbxData.car_refrigerator || '--' },
                { label: '冰箱特性', value: ktbxData.car_fridge_feature || '--' }
              ];

              // 打印处理后的参数配置
              console.log('【空调冰箱】最终结果:', paramConfigs[17]);

            } catch (e) {
              console.error('【空调冰箱】解析失败，错误详情:', e);
              console.error('【空调冰箱】原始数据:', spec.body_ktbx);
            }
          } else {
            console.warn('【空调冰箱】数据不存在，spec.body_ktbx为空');
          }

          // 解析智能化配置数据
          if (spec.body_znhpz) {
            // 先打印原始数据
            console.log('【智能化配置】原始数据:', spec.body_znhpz);

            try {
              // 首先尝试直接将●字符处理为普通字符串内容
              let znhpzString = spec.body_znhpz.replace(/'/g, '"');
              console.log('【智能化配置】替换单引号后:', znhpzString);

              let znhpzData = {};

              try {
                // 先尝试直接解析
                znhpzData = JSON.parse(znhpzString);
                console.log('【智能化配置】第一次解析成功:', znhpzData);
              } catch (jsonError) {
                console.error('【智能化配置】第一次JSON解析失败，尝试特殊处理', jsonError);

                // 尝试更严格的字符替换
                znhpzString = spec.body_znhpz.replace(/'/g, '"').replace(/●/g, '"●"');
                console.log('【智能化配置】替换特殊字符后:', znhpzString);

                try {
                  znhpzData = JSON.parse(znhpzString);
                  console.log('【智能化配置】第二次解析成功:', znhpzData);
                } catch (jsonError2) {
                  console.error('【智能化配置】第二次JSON解析失败，尝试使用正则表达式', jsonError2);

                  // 如果两次JSON解析都失败，使用正则表达式提取
                  const keyValuePattern = /'([^']+)':'([^']+)'/g;
                  let match;
                  while ((match = keyValuePattern.exec(spec.body_znhpz)) !== null) {
                    znhpzData[match[1]] = match[2];
                    console.log(`【智能化配置】正则提取: ${match[1]} = ${match[2]}`);
                  }
                  console.log('【智能化配置】正则解析结果:', znhpzData);
                }
              }

              // 打印解析后的数据到控制台
              console.log('【智能化配置】解析后的数据:', znhpzData);

              // 更新智能化参数配置
              paramConfigs[18] = [
                { label: '驾驶辅助操作系统', value: znhpzData.driving_assist_op_system || '--' },
                { label: '驾驶辅助芯片', value: znhpzData.driving_assist_chip || '--' },
                { label: '驾驶辅助芯片算力', value: znhpzData.driving_assist_chip_computing || '--' },
                { label: '车机智能芯片', value: znhpzData.car_intelligent_chip || '--' },
                { label: '车机系统内存', value: znhpzData.car_system_memory || '--' },
                { label: '车机系统存储', value: znhpzData.car_system_storage || '--' },
                { label: '手机远程控制', value: znhpzData.mobile_remote_control || '--' },
                { label: '热泵管理系统', value: znhpzData.heat_pump_management_system || '--' },
                { label: '摄像头数量', value: znhpzData.camera_count || '--' },
                { label: '车外摄像头像素', value: znhpzData.outer_camera_pixel || '--' },
                { label: '车内摄像头数量', value: znhpzData.incar_camera_count || '--' },
                { label: '车内摄像头像素', value: znhpzData.inner_camera_pixel || '--' },
                { label: '超声波雷达', value: znhpzData.ultrasonic_radar || '--' },
                { label: '毫米波雷达', value: znhpzData.millimeter_wave_radar || '--' },
                { label: '激光雷达', value: znhpzData.laser_radar || '--' },
                { label: '激光雷达品牌', value: znhpzData.laser_radar_brand || '--' },
                { label: '激光雷达线数', value: znhpzData.laser_radar_ray_nums || '--' },
                { label: '激光雷达点云数', value: znhpzData.laser_radar_point_cloud_nums || '--' },
                { label: '高精定位系统', value: znhpzData.high_precision_position_system || '--' },
                { label: '高精地图', value: znhpzData.high_precision_map || '--' },
                { label: '哨兵模式', value: znhpzData.sentinel_mode || '--' },
                { label: 'V2X通信', value: znhpzData.v2x_communication || '--' }
              ];

              // 打印处理后的参数配置
              console.log('【智能化配置】最终结果:', paramConfigs[18]);

            } catch (e) {
              console.error('【智能化配置】解析失败，错误详情:', e);
              console.error('【智能化配置】原始数据:', spec.body_znhpz);
            }
          } else {
            console.warn('【智能化配置】数据不存在，spec.body_znhpz为空');
          }

          // 解析选装包数据
          if (spec.body_xzb) {
            // 先打印原始数据
            console.log('【选装包】原始数据:', spec.body_xzb);

            try {
              // 首先尝试直接将●字符处理为普通字符串内容
              let xzbString = spec.body_xzb.replace(/'/g, '"');
              console.log('【选装包】替换单引号后:', xzbString);

              let xzbData = {};

              try {
                // 先尝试直接解析
                xzbData = JSON.parse(xzbString);
                console.log('【选装包】第一次解析成功:', xzbData);
              } catch (jsonError) {
                console.error('【选装包】第一次JSON解析失败，尝试特殊处理', jsonError);

                // 尝试更严格的字符替换
                xzbString = spec.body_xzb.replace(/'/g, '"').replace(/●/g, '"●"');
                console.log('【选装包】替换特殊字符后:', xzbString);

                try {
                  xzbData = JSON.parse(xzbString);
                  console.log('【选装包】第二次解析成功:', xzbData);
                } catch (jsonError2) {
                  console.error('【选装包】第二次JSON解析失败，尝试使用正则表达式', jsonError2);

                  // 如果两次JSON解析都失败，使用正则表达式提取
                  const keyValuePattern = /'([^']+)':'([^']+)'/g;
                  let match;
                  while ((match = keyValuePattern.exec(spec.body_xzb)) !== null) {
                    xzbData[match[1]] = match[2];
                    console.log(`【选装包】正则提取: ${match[1]} = ${match[2]}`);
                  }
                  console.log('【选装包】正则解析结果:', xzbData);
                }
              }

              // 打印解析后的数据到控制台
              console.log('【选装包】解析后的数据:', xzbData);

              // 更新选装包参数配置
              paramConfigs[19] = [
                { label: '用户自定义选装包', value: xzbData.user_custom_pkg || '--' },
                { label: '智能驾驶选装包', value: xzbData.intelligent_driving_package || '--' },
                { label: '舒适选装包', value: xzbData.comfortable_package || '--' },
                { label: '豪华内饰选装包', value: xzbData.luxury_interior_package || '--' },
                { label: '运动外观选装包', value: xzbData.sports_appearance_package || '--' },
                { label: '音响升级包', value: xzbData.sound_upgrade_package || '--' },
                { label: '座椅升级包', value: xzbData.seat_upgrade_package || '--' },
                { label: '安全防护包', value: xzbData.safety_protection_package || '--' },
                { label: '冬季选装包', value: xzbData.winter_package || '--' }
              ];

              // 打印处理后的参数配置
              console.log('【选装包】最终结果:', paramConfigs[19]);

            } catch (e) {
              console.error('【选装包】解析失败，错误详情:', e);
              console.error('【选装包】原始数据:', spec.body_xzb);
            }
          } else {
            console.warn('【选装包】数据不存在，spec.body_xzb为空');
          }

          // 其他字段的解析可以按照类似的方式添加
        }

        // 更新页面数据
        this.setData({
          vehicleInfo,
          paramConfigs  // 更新paramConfigs
        });
      }
    } catch (error) {
      console.error('获取车辆详情失败', error);
      wx.showToast({
        title: '获取数据失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 返回上一页
  goBack: function () {
    wx.navigateBack({
      delta: 1
    });
  },

  // 切换主标签页
  switchTab: function (e) {
    const index = parseInt(e.currentTarget.dataset.index);
    this.setData({
      currentTab: index
    });
  },

  // 切换参数配置子标签页
  switchParamTab: function (e) {
    const index = parseInt(e.currentTarget.dataset.index);
    this.setData({
      paramTab: index
    });
  },

  // 获取当前参数配置
  getCurrentParamConfig: function () {
    return this.data.paramConfigs[this.data.paramTab] || [];
  },

  // 获取参数配置名称
  getParamTabName: function (index) {
    const names = [
      '发动机', '电动机', '电池/充电', '变速箱', '底盘转向',
      '车轮制动', '主动安全', '被动安全', '辅助操控', '外部配置',
      '内部配置', '舒适/防盗配置', '座椅配置', '智能互联', '影音娱乐',
      '灯光配置', '玻璃/后视镜', '空调冰箱', '智能化配置', '选装包'
    ];
    return names[index] || '未知配置';
  }
})