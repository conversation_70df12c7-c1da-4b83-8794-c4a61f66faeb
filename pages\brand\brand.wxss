/* pages/brand/brand.wxss */
.container_brand{
  padding: 10px;
}

/* 推荐头部调整上边距 */
.recommend-header {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin: 5rpx 0 15rpx 0;
  /* 进一步减小上边距 */
  padding-left: 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.recommend-header .more-cars {
  font-size: 24rpx;
  color: #999999;
  font-weight: normal;
  background-color: transparent;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
}

/* 热门品牌区域 - 调整上边距 */
.hot-brands-section {
  background-color: #fff;
  margin: 5rpx 0 10rpx 0;
  /* 进一步减小上边距 */
  padding: 15rpx 24rpx 20rpx 24rpx;
  /* 减小顶部内边距 */
  border-radius: 10rpx;
  box-sizing: border-box;
}

.hot-brands-list {
  display: flex;
  justify-content: space-between;
  flex-wrap: nowrap;
  overflow-x: auto;
  white-space: nowrap;
  padding: 5rpx 0;
  /* 减小内边距 */
  margin-left: 30rpx;
}

.hot-brand-item {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  margin-right: 45rpx;
  transition: transform 0.2s ease;
}

.hot-brand-item:active {
  transform: scale(0.95);
}

.hot-brand-icon {
  width: 64rpx;
  /* 32px */
  height: 64rpx;
  /* 32px */
  margin-bottom: 8rpx;
  /* 4px */
}

.hot-brand-name {
  font-size: 24rpx;
  height: 32rpx;
  /* 16px */
  line-height: 32rpx;
  color: #333;
}







/* pages/car/series.wxss */
.container {
  padding: 0 30rpx;
  min-height: 100vh;
  background-color: #f8f8f8;
}

.header {
  padding: 30rpx 0;
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 36rpx;
  color: #333;
  margin-right: 20rpx;
}

.title {
  display: flex;
  flex-direction: column;
}

.brand-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 4rpx;
}

.subtitle {
  font-size: 24rpx;
  color: #999;
}

.series-list {
  padding: 20rpx 0;
}

.series-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.series-image {
  width: 140rpx;
  height: 80rpx;
  margin-right: 20rpx;
}

.series-info {
  flex: 1;
}

.series-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.series-desc {
  font-size: 24rpx;
  color: #999;
}

.arrow {
  font-size: 36rpx;
  color: #ccc;
}

.loading {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.empty {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.series-page {
    padding: 20rpx;
}

.page-title {
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
    padding-left: 20rpx;
} 

.custom-cell-content {
  display: flex;
  align-items: center; /* 垂直居中对齐 */
}

.logo {
  width: 24px; /* 根据实际需求调整 logo 大小 */
  height: 24px;
  margin-right: 8px; /* logo 和文字之间的间距 */
}

.name {
  font-size: 16px; /* 根据实际需求调整字体大小 */
  color: #333;
}