/* 整体容器 */
.result-container {
  position: relative;
  padding: 30rpx;
  min-height: 100vh;
  background: linear-gradient(to bottom, #e74c50 0%, #f8f8f8 80%);
}

/* 导航栏样式 */
.nav-bar {
  width: 100%;
  height: 88rpx;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  color: #fff;
  position: relative;
  margin-top: 100rpx;
}

.nav-back {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

.back-icon {
  font-size: 40rpx;
  font-weight: bold;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  margin-right: 90rpx;
}

.back-btn image {
  width: 30rpx;
  height: 30rpx;
}

/* 加载中样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
  margin-top: 200rpx;
  padding: 40rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
}

/* 错误提示样式 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 50rpx;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
}

.error-text {
  font-size: 30rpx;
  color: #666;
  margin-bottom: 30rpx;
}

.error-btn {
  font-size: 28rpx;
  color: #fff;
  background-color: #e74c50;
  padding: 12rpx 40rpx;
  border-radius: 30rpx;
  box-shadow: 0 4rpx 10rpx rgba(231, 76, 80, 0.2);
}

/* 结果卡片样式 */
.result-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  margin-top: 100rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
}

.card-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

/* 价格区域 */
.price-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
  padding-bottom: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.price-area::after {
  content: '';
  position: absolute;
  bottom: -1rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 80rpx;
  height: 4rpx;
  background: linear-gradient(to right, #e74c50, #ff9b9d);
  border-radius: 2rpx;
}

.price-range {
  display: flex;
  align-items: baseline;
  margin-bottom: 10rpx;
}

.price-value {
  font-size: 56rpx;
  font-weight: bold;
  color: #e74c50;
  text-shadow: 0 2rpx 4rpx rgba(231, 76, 80, 0.1);
}

.price-unit {
  font-size: 28rpx;
  color: #ffc62c;
  margin-left: 10rpx;
}

.price-desc {
  font-size: 24rpx;
  color: #999;
}

/* 车辆信息 */
.car-info {
  padding: 20rpx 0;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.item-label {
  font-size: 28rpx;
  color: #666;
}

.item-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 按钮区域 */
.action-btns {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
}

.sell-btn, .share-btn {
  width: 45%;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.sell-btn {
  background-color: #e74c50;
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(231, 76, 80, 0.3);
  transition: all 0.3s ease;
}

.share-btn {
  background-color: #fff;
  color: #e74c50;
  border: 1rpx solid #e74c50;
  transition: all 0.3s ease;
}

.sell-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(231, 76, 80, 0.2);
}

.share-btn:active {
  transform: scale(0.98);
  background-color: rgba(231, 76, 80, 0.05);
}

/* 经销商区域 */
.dealer-section {
  margin-top: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.dealer-list {
  padding: 10rpx 0;
}

.dealer-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.dealer-item:last-child {
  border-bottom: none;
}

.dealer-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.dealer-logo {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  background-color: #f5f5f5;
}

.dealer-detail {
  flex: 1;
}

.dealer-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.dealer-address {
  font-size: 24rpx;
  color: #999;
  width: 350rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.contact-btn {
  font-size: 26rpx;
  color: #e74c50;
  padding: 10rpx 30rpx;
  border: 1rpx solid #e74c50;
  border-radius: 30rpx;
  transition: all 0.3s ease;
}

.contact-btn:active {
  background-color: rgba(231, 76, 80, 0.05);
  transform: scale(0.98);
}

/* 无数据展示 */
.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 200rpx;
}

.no-data image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.no-data text {
  font-size: 28rpx;
  color: #999;
} 