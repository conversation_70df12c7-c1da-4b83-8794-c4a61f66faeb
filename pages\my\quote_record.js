// 导入 API 模块
import api from '../../utils/api';
import util from '../../utils/util';
import share from '../../utils/share';

Page({
  behaviors: [share], // 分享行为
  data: {
    // 分享设置
    shareData: {
      title: '车辆报价单',
      path: '/pages/my/quote_record',
      isDetailPage: true,
    },
    quoteData: {
      carTitle: '帕萨特 2025款 出众款 330TSI 星空精英版',
      guidancePrice: '188500',
      marketDiscount: '-40000',
      purchaseTax: 'XXX',
      purchaseCost: 'XXX',
      downPayment: '44550',
      advancePayment: 'XXX',
      domesticFees: '500',
      exportFees: '0',
      serviceCost: '3500',
      afterTaxPrice: 'XXX',
      taxRefund: 'XXX',
      totalAdvanceCost: 'XXX'
    },
    quoteId: '', // 报价单ID
    isLoading: true,
    statusBarHeight: 0, // 状态栏高度
  },

  onLoad(options) {
    if (options.id) {
      this.setData({ quoteId: options.id });
      this.getQuoteDetail(options.id);
    } else {
      this.setData({ isLoading: false });
    }

    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });
  },

  // 返回上一页
  navigateBack() {
    wx.redirectTo({
      url: '/pages/my/quote_list'
    });
  },

  // 格式化价格（添加千分位）
  formatPrice(price) {
    if (!price || price === 'XXX') return '0';
    return parseFloat(price).toLocaleString('zh-CN');
  },

  // 获取报价单详情
  async getQuoteDetail(id) {
    try {
      wx.showLoading({ title: '加载中...' });
      this.setData({ isLoading: true });

      const res = await api.quote.getDetail({
        id: id,
        app_id: util.getAppId()
      });
      // console.log(res)
      if (res) {
        // 设置报价数据
        const data = res;
        this.setData({
          quoteData: {
            carTitle: data.content.carName,
            guidancePrice: this.formatPrice(data.content.cg_zdj),
            marketDiscount: this.formatPrice(data.content.cg_cjxf),
            purchaseTax: data.content.cg_gzs,
            purchaseCost: this.formatPrice(data.content.cb_cg),
            downPayment: this.formatPrice(data.content.cb_cgyfk),
            advancePayment: data.content.cb_cgdk,
            domesticFees: this.formatPrice(data.content.cb_gn),
            exportFees: this.formatPrice(data.content.cb_ck),
            serviceCost: this.formatPrice(data.content.zcb),
            afterTaxPrice: this.formatPrice(data.content.zcb_shj),
            taxRefund: this.formatPrice(data.content.zcb_ts),
            totalAdvanceCost: this.formatPrice(data.content.zcb_dk)
          },
          // 更新分享数据
          'shareData.title': `${data.car_title || '车辆'}报价单`,
          'shareData.path': `/pages/my/quote_record?id=${id}`
        });
      } else {
        wx.showToast({
          title: '获取报价单失败',
          icon: 'none',
          duration: 2000
        });
      }
    } catch (error) {
      console.error('获取报价单详情失败:', error);
      wx.showToast({
        title: '获取数据失败',
        icon: 'none',
        duration: 2000
      });
    } finally {
      wx.hideLoading();
      this.setData({ isLoading: false });
    }
  },

  // 分享报价单
  onShareButtonTap() {
    wx.showActionSheet({
      itemList: ['分享给朋友', '生成分享图片', '生成PDF报价单'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 分享给朋友
          wx.showToast({
            title: '请点击右上角分享',
            icon: 'none',
            duration: 2000
          });
        } else if (res.tapIndex === 1) {
          // 生成分享图片
          this.generateShareImage();
        } else if (res.tapIndex === 2) {
          // 生成PDF报价单
          this.generatePDF();
        }
      }
    });
  },

  // 生成分享图片 - 使用微信新版Canvas 2D API
  generateShareImage() {
    wx.showLoading({
      title: '生成图片中...',
      mask: true
    });

    // 直接调用统一的画布渲染方法，生成标准A4比例图片
    this.renderReportToCanvas('分享图片已保存到相册');
  },

  // 生成PDF报价单
  generatePDF() {
    wx.showLoading({
      title: '生成PDF中...',
      mask: true
    });

    // 使用相同的画布渲染方法，只是提示信息不同
    this.renderReportToCanvas('PDF格式的报价单已保存到相册');
  },

  // 统一的画布渲染方法，用于生成报价单图片
  renderReportToCanvas(successMessage) {
    const query = wx.createSelectorQuery();
    query.select('#shareCanvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (!res[0] || !res[0].node) {
          wx.hideLoading();
          wx.showToast({
            title: '创建画布失败',
            icon: 'none',
            duration: 2000
          });
          return;
        }

        const canvas = res[0].node;
        const ctx = canvas.getContext('2d');
        const systemInfo = wx.getSystemInfoSync();
        const pixelRatio = systemInfo.pixelRatio || 2;

        // 使用A4纵向比例尺寸 (595 x 842 pts)
        const canvasWidth = 595;
        const canvasHeight = 912;

        // 设置画布尺寸并考虑高清显示
        canvas.width = canvasWidth * pixelRatio;
        canvas.height = canvasHeight * pixelRatio;
        ctx.scale(pixelRatio, pixelRatio);

        // 清空画布
        ctx.clearRect(0, 0, canvasWidth, canvasHeight);

        // 绘制背景
        ctx.fillStyle = '#FFFFFF';
        ctx.fillRect(0, 0, canvasWidth, canvasHeight);

        // 设置字体
        const fontFamily = 'PingFang SC, -apple-system, BlinkMacSystemFont, Helvetica Neue, Helvetica, Arial, sans-serif';

        // 绘制页眉 - 调整高度和背景色
        ctx.fillStyle = '#F7FAFD';
        ctx.fillRect(0, 0, canvasWidth, 100);

        // 增加顶部间距，使标题更靠上
        // 绘制标题
        ctx.fillStyle = '#333333';
        ctx.textAlign = 'center';
        ctx.font = `bold ${28}px ${fontFamily}`;
        ctx.fillText('车辆报价单', canvasWidth / 2, 50);

        // 绘制日期
        const now = new Date();
        const dateStr = `生成日期：${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')}`;
        ctx.font = `${12}px ${fontFamily}`;
        ctx.fillStyle = '#666666';
        ctx.fillText(dateStr, canvasWidth / 2, 75);

        // 调整车型名称位置，贴近页眉区域
        // 绘制车型名称
        ctx.fillStyle = '#333333';
        ctx.font = `bold ${16}px ${fontFamily}`;
        ctx.textAlign = 'left';
        ctx.fillText('车型名称：', 50, 150);

        // 处理长车型名
        const carTitle = this.data.quoteData.carTitle;
        if (carTitle.length > 40) {
          const line1 = carTitle.substring(0, 40);
          const line2 = carTitle.substring(40);
          ctx.font = `${14}px ${fontFamily}`;
          ctx.fillText(line1, 130, 150);
          ctx.fillText(line2, 130, 170);
        } else {
          ctx.font = `${14}px ${fontFamily}`;
          ctx.fillText(carTitle, 130, 150);
        }

        // 绘制分割线
        ctx.strokeStyle = '#DDDDDD';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(50, 160);
        ctx.lineTo(canvasWidth - 50, 160);
        ctx.stroke();

        // 绘制报价表格
        // 表格标题 - 减小标题到表格的距离
        let startY = 185;
        ctx.fillStyle = '#444444';
        ctx.font = `bold ${16}px ${fontFamily}`;
        ctx.textAlign = 'left';
        ctx.fillText('车辆采购', 50, startY);

        // 表格顶部与标题更紧凑
        startY += 5;
        const tableWidth = canvasWidth - 100;
        const colWidth1 = tableWidth * 0.6;
        const colWidth2 = tableWidth * 0.4;

        // 表头 - 使用更深的背景色
        ctx.fillStyle = '#F0F5F9';
        ctx.fillRect(50, startY, tableWidth, 30);

        ctx.fillStyle = '#333333';
        ctx.font = `bold ${14}px ${fontFamily}`;
        ctx.textAlign = 'left';
        ctx.fillText('报价项目', 70, startY + 20);

        ctx.textAlign = 'right';
        ctx.fillText('金额', canvasWidth - 70, startY + 20);

        // 绘制表格边框 - 使用更淡的边框色
        ctx.strokeStyle = '#E5E9ED';
        ctx.lineWidth = 1;
        ctx.strokeRect(50, startY, tableWidth, 30);

        // 垂直分隔线
        ctx.beginPath();
        ctx.moveTo(50 + colWidth1, startY);
        ctx.lineTo(50 + colWidth1, startY + 30);
        ctx.stroke();

        // 表格内容 - 车辆采购部分
        startY += 30;
        const rowHeight = 30;
        const purchaseItems = [
          { label: '厂家指导价', value: this.data.quoteData.guidancePrice + ' 万元' },
          { label: '市场下浮', value: this.data.quoteData.marketDiscount + ' 万元' },
          { label: '购置税', value: this.data.quoteData.purchaseTax + ' 万元' },
          { label: '购车成本', value: this.data.quoteData.purchaseCost + ' 万元' },
          { label: '购车预付款', value: this.data.quoteData.downPayment + ' 万元' },
          { label: '购车垫款', value: this.data.quoteData.advancePayment + ' 万元' }
        ];

        this.drawTableRows(ctx, purchaseItems, startY, rowHeight, 50, colWidth1, colWidth2, fontFamily);
        startY += rowHeight * purchaseItems.length + 25; // 减小表格间距

        // 车务部分
        ctx.fillStyle = '#444444';
        ctx.font = `bold ${16}px ${fontFamily}`;
        ctx.textAlign = 'left';
        ctx.fillText('车务', 50, startY);

        // 表头
        startY += 5; // 减小标题与表头的间距
        ctx.fillStyle = '#F0F5F9';
        ctx.fillRect(50, startY, tableWidth, 30);

        ctx.fillStyle = '#333333';
        ctx.font = `bold ${14}px ${fontFamily}`;
        ctx.textAlign = 'left';
        ctx.fillText('报价项目', 70, startY + 20);

        ctx.textAlign = 'right';
        ctx.fillText('金额', canvasWidth - 70, startY + 20);

        // 表格边框
        ctx.strokeStyle = '#E5E9ED';
        ctx.lineWidth = 1;
        ctx.strokeRect(50, startY, tableWidth, 30);

        // 垂直分隔线
        ctx.beginPath();
        ctx.moveTo(50 + colWidth1, startY);
        ctx.lineTo(50 + colWidth1, startY + 30);
        ctx.stroke();

        // 车务内容
        startY += 30;
        const vehicleItems = [
          { label: '国内总杂费', value: this.data.quoteData.domesticFees + ' 万元' }
        ];

        this.drawTableRows(ctx, vehicleItems, startY, rowHeight, 50, colWidth1, colWidth2, fontFamily);
        startY += rowHeight * vehicleItems.length + 25; // 减小表格间距

        // 出口物流部分
        ctx.fillStyle = '#444444';
        ctx.font = `bold ${16}px ${fontFamily}`;
        ctx.textAlign = 'left';
        ctx.fillText('出口物流', 50, startY);

        // 表头
        startY += 5; // 减小标题与表头的间距
        ctx.fillStyle = '#F0F5F9';
        ctx.fillRect(50, startY, tableWidth, 30);

        ctx.fillStyle = '#333333';
        ctx.font = `bold ${14}px ${fontFamily}`;
        ctx.textAlign = 'left';
        ctx.fillText('报价项目', 70, startY + 20);

        ctx.textAlign = 'right';
        ctx.fillText('金额', canvasWidth - 70, startY + 20);

        // 表格边框
        ctx.strokeStyle = '#E5E9ED';
        ctx.lineWidth = 1;
        ctx.strokeRect(50, startY, tableWidth, 30);

        // 垂直分隔线
        ctx.beginPath();
        ctx.moveTo(50 + colWidth1, startY);
        ctx.lineTo(50 + colWidth1, startY + 30);
        ctx.stroke();

        // 出口物流内容
        startY += 30;
        const exportItems = [
          { label: '出口费用合计', value: this.data.quoteData.exportFees + ' 万元' }
        ];

        this.drawTableRows(ctx, exportItems, startY, rowHeight, 50, colWidth1, colWidth2, fontFamily);
        startY += rowHeight * exportItems.length + 25; // 减小表格间距

        // 垫资账税部分
        ctx.fillStyle = '#444444';
        ctx.font = `bold ${16}px ${fontFamily}`;
        ctx.textAlign = 'left';
        ctx.fillText('垫资账税', 50, startY);

        // 表头
        startY += 5; // 减小标题与表头的间距
        ctx.fillStyle = '#F0F5F9';
        ctx.fillRect(50, startY, tableWidth, 30);

        ctx.fillStyle = '#333333';
        ctx.font = `bold ${14}px ${fontFamily}`;
        ctx.textAlign = 'left';
        ctx.fillText('报价项目', 70, startY + 20);

        ctx.textAlign = 'right';
        ctx.fillText('金额', canvasWidth - 70, startY + 20);

        // 表格边框
        ctx.strokeStyle = '#E5E9ED';
        ctx.lineWidth = 1;
        ctx.strokeRect(50, startY, tableWidth, 30);

        // 垂直分隔线
        ctx.beginPath();
        ctx.moveTo(50 + colWidth1, startY);
        ctx.lineTo(50 + colWidth1, startY + 30);
        ctx.stroke();

        // 垫资账税内容
        startY += 30;
        const taxItems = [
          { label: '服务成本', value: this.data.quoteData.serviceCost + ' 万元' },
          { label: '税后价', value: this.data.quoteData.afterTaxPrice + ' 万元' },
          { label: '退税金额', value: this.data.quoteData.taxRefund + ' 万元' },
          { label: '垫资总成本', value: this.data.quoteData.totalAdvanceCost + ' 万元' }
        ];

        this.drawTableRows(ctx, taxItems, startY, rowHeight, 50, colWidth1, colWidth2, fontFamily);
        startY += rowHeight * taxItems.length + 25;

        // 添加备注区域
        ctx.fillStyle = '#444444';
        ctx.font = `bold ${16}px ${fontFamily}`;
        ctx.textAlign = 'left';
        ctx.fillText('备注', 50, startY);
        startY += 20;

        ctx.fillStyle = '#666666';
        ctx.font = `${12}px ${fontFamily}`;
        ctx.fillText('1. 本报价单仅供参考，最终价格以合同为准', 70, startY);
        startY += 18;
        ctx.fillText('2. 价格有效期: 7天', 70, startY);
        startY += 18;
        ctx.fillText('3. 如有任何疑问，请随时与我们联系', 70, startY);

        // 页脚 - 增加到底部的距离，确保不会被遮挡
        ctx.fillStyle = '#F7FAFD';
        ctx.fillRect(0, canvasHeight - 60, canvasWidth, 60);

        // 使用更深的文字颜色和清晰的字体
        ctx.fillStyle = '#555555';
        ctx.font = `${12}px ${fontFamily}`;
        ctx.textAlign = 'center';
        ctx.fillText('本报价单由汽车交易平台自动生成，仅供参考', canvasWidth / 2, canvasHeight - 30);

        // 转换为图片
        setTimeout(() => {
          // 将Canvas转换为图片
          wx.canvasToTempFilePath({
            canvas: canvas,
            x: 0,
            y: 0,
            width: canvasWidth * pixelRatio,
            height: canvasHeight * pixelRatio,
            destWidth: canvasWidth * pixelRatio,
            destHeight: canvasHeight * pixelRatio,
            fileType: 'jpg',
            quality: 0.95, // 提高图片质量
            success: (res) => {
              // 保存图片到相册
              wx.saveImageToPhotosAlbum({
                filePath: res.tempFilePath,
                success: () => {
                  wx.hideLoading();
                  wx.showModal({
                    title: '报价单已生成',
                    content: successMessage,
                    showCancel: false
                  });
                },
                fail: (err) => {
                  console.error('保存图片失败:', err);
                  wx.hideLoading();

                  if (err.errMsg && err.errMsg.indexOf('auth deny') >= 0) {
                    wx.showModal({
                      title: '提示',
                      content: '需要您授权保存图片到相册',
                      showCancel: true,
                      confirmText: '去授权',
                      success: (res) => {
                        if (res.confirm) {
                          wx.openSetting();
                        }
                      }
                    });
                  } else {
                    wx.showToast({
                      title: '保存报价单失败',
                      icon: 'none',
                      duration: 2000
                    });
                  }
                }
              });
            },
            fail: (err) => {
              console.error('生成图片失败:', err);
              wx.hideLoading();
              wx.showToast({
                title: '生成报价单失败',
                icon: 'none',
                duration: 2000
              });
            }
          });
        }, 300);
      });
  },

  // 绘制表格行的辅助方法
  drawTableRows(ctx, items, startY, rowHeight, startX, colWidth1, colWidth2, fontFamily) {
    items.forEach((item, index) => {
      // 交替行背景色 - 使用更淡的背景色
      if (index % 2 === 1) {
        ctx.fillStyle = '#F7FAFD';
        ctx.fillRect(startX, startY + index * rowHeight, colWidth1 + colWidth2, rowHeight);
      }

      // 绘制标签
      ctx.textAlign = 'left';
      ctx.font = `${14}px ${fontFamily}`;
      ctx.fillStyle = '#444444';
      ctx.fillText(item.label, startX + 20, startY + index * rowHeight + rowHeight / 2 + 5);

      // 绘制值
      ctx.textAlign = 'right';
      ctx.fillStyle = '#333333';
      ctx.fillText(item.value, startX + colWidth1 + colWidth2 - 20, startY + index * rowHeight + rowHeight / 2 + 5);

      // 边框 - 使用更淡的边框色
      ctx.strokeStyle = '#E5E9ED';
      ctx.beginPath();
      ctx.moveTo(startX, startY + (index + 1) * rowHeight);
      ctx.lineTo(startX + colWidth1 + colWidth2, startY + (index + 1) * rowHeight);
      ctx.stroke();

      // 左右边框
      ctx.beginPath();
      ctx.moveTo(startX, startY + index * rowHeight);
      ctx.lineTo(startX, startY + (index + 1) * rowHeight);
      ctx.stroke();

      ctx.beginPath();
      ctx.moveTo(startX + colWidth1 + colWidth2, startY + index * rowHeight);
      ctx.lineTo(startX + colWidth1 + colWidth2, startY + (index + 1) * rowHeight);
      ctx.stroke();

      // 中间分隔线
      ctx.beginPath();
      ctx.moveTo(startX + colWidth1, startY + index * rowHeight);
      ctx.lineTo(startX + colWidth1, startY + (index + 1) * rowHeight);
      ctx.stroke();
    });
  },

  // 处理PDF生成错误
  handlePDFError(message) {
    wx.hideLoading();
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  },
});
