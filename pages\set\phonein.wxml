<!--pages/set/phonein.wxml-->
<view class="container">
  <!-- 自定义导航栏 -->
  <view class="custom-nav" style="padding-top: {{statusBarHeight}}px;">
    <view class="nav-content">
      <view class="nav-back" bindtap="goBack">
        <view class="back-icon"></view>
      </view>
      <view class="nav-title">修改手机号</view>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="main-content" style="margin-top: {{statusBarHeight + 44}}px;">
    <!-- 将所有内容包裹在卡片中 -->
    <view class="phone-card">
      <view class="phone-title">更换手机号</view>
      
      <view class="phone-desc">
        一个手机号只能绑定一个帐号，更换后可使用新手机号登录此账号，对于已绑定其他帐号的手机号，本次操作后将解除绑定
      </view>
      
      <view class="card-divider"></view>
      
      <!-- 国家/地区选择 -->
      <view class="input-item country-select" bindtap="selectCountry">
        <view class="item-label">国家/地区</view>
        <view class="item-value">
          <text>中国大陆</text>
          <van-icon name="arrow" size="16px" color="#999" />
        </view>
      </view>
      
      <!-- 手机号输入 -->
      <view class="input-item phone-input" bindtap="inputPhone">
        <view class="item-label">手机号</view>
        <view class="item-value">
          <text class="prefix">+86</text>
          <text class="hint" wx:if="{{!phone}}">请输入手机号码</text>
          <text wx:else>{{phone}}</text>
          <van-icon name="arrow" size="16px" color="#999" />
        </view>
      </view>
    </view>
    
    <!-- 下一步按钮 -->
    <view class="next-btn {{phone ? 'active' : ''}}" bindtap="{{phone ? 'nextStep' : ''}}">下一步</view>
  </view>
</view>