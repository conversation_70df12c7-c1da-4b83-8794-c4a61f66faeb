<view class="favorite-page">
  <!-- 自定义导航栏 -->
  <view class="custom-nav" style="padding-top: {{statusBarHeight}}px; --status-bar-height: {{statusBarHeight}}px;">
    <view class="status-bar"></view>
    <view class="nav-content">
      <view class="back-icon" bindtap="navigateBack">
        <image src="/icons/moments/back.svg" mode="aspectFit"></image>
      </view>
      <view class="nav-title">我的收藏</view>
    </view>
  </view>

  <!-- 收藏车辆列表 -->
  <view class="car-list-container">
    <view class="edit-btn" bindtap="toggleEditMode">
      <image class="edit-icon" src="/icons/favorite/edit.png" mode="aspectFit"></image>
      <text>{{isEditMode ? '取消' : '编辑'}}</text>
    </view>
    <block wx:if="{{favoriteList.length > 0}}">
      <view class="car-list">
        <view class="car-item {{isEditMode ? 'edit-mode' : ''}}" wx:for="{{favoriteList}}" wx:key="id" bindtap="onCarItemTap" data-id="{{item.id}}">
          <!-- 选择框 -->
          <view class="select-checkbox {{isEditMode ? 'show' : ''}}" catchtap="selectItem" data-id="{{item.id}}">
            <view class="checkbox {{item.selected ? 'checked' : ''}}">
              <icon wx:if="{{item.selected}}" type="success" size="16" color="#fff"></icon>
            </view>
          </view>
          
          <!-- 车辆图片 -->
          <view class="car-image-container">
            <image class="car-image" src="{{item.main_url}}" mode="aspectFill"></image>
            <view class="status-badge" wx:if="{{item.status === 'sold'}}">已售出</view>
          </view>
          
          <!-- 车辆信息 -->
          <view class="car-info">
            <!-- 车辆标题 -->
            <view class="car-title">{{item.title}}</view>
            
            <!-- 车辆详情 -->
            <view class="car-details">
              <text class="car-year">{{item.first_registration_time}}年</text>
              <text class="car-mileage">{{item.mileage}}万公里</text>
            </view>
            
            <!-- 车辆价格 -->
            <view class="car-price">
              <text class="price-value">{{item.sell_price}}万元</text>
              <text class="origin-price">新车指导价{{item.official_price}}万元</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 加载更多提示 -->
      <view class="loading-more" wx:if="{{isLoadingMore && !isEditMode}}">
        <view class="loading-indicator"></view>
        <text>加载更多...</text>
      </view>
      
      <!-- 没有更多数据提示 -->
      <view class="no-more-data" wx:if="{{!hasMoreData && favoriteList.length > 0 && !isEditMode}}">
        <text>— 已经到底啦 —</text>
      </view>
      
      <!-- 底部操作栏（编辑模式下显示） -->
      <view class="bottom-bar {{isEditMode ? 'show' : ''}}">
        <view class="select-all" bindtap="selectAllItems">
          <view class="checkbox {{isAllSelected ? 'checked' : ''}}">
            <icon wx:if="{{isAllSelected}}" type="success" size="16" color="#fff"></icon>
          </view>
          <text>全选</text>
        </view>
        <view class="delete-btn" bindtap="deleteSelected">
          <text>删除</text>
          <text class="delete-count">({{getSelectedCount}})</text>
        </view>
      </view>
    </block>
    
    <!-- 空状态 -->
    <view class="empty-state" wx:else>
      <view class="empty-text">暂无收藏车辆</view>
      <view class="browse-btn" bindtap="navigateToHome">去收藏车辆</view>
    </view>
  </view>
</view>