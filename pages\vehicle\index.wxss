/* 引入登录弹窗样式 */
@import "../../templates/loginPopup/loginPopup.wxss";

/* 全局溢出控制 - 防止横向滚动 */
page {
  overflow-x: hidden;
  width: 100%;
  box-sizing: border-box;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
}

.container {
  padding: 0;
  background-color: transparent;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
  position: relative;
}

/* ==================== 顶部导航区域样式 ==================== */

/* 头部占位符 - 防止内容被固定顶部遮挡 */
.header-placeholder {
  width: 100%;
  height: calc(var(--status-bar-height, 20px) + 88rpx + 100rpx + 80rpx + 30rpx);
  /* 状态栏+导航栏+搜索栏+筛选菜单+额外间距 */
}

/* 固定头部样式 */
.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 顶部状态栏 */
.status-bar {
  width: 100%;
  background: transparent;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  width: 100%;
  background: transparent;
  position: relative;
  padding: 0 20rpx;
  box-sizing: border-box;
}

.nav-back {
  width: 88rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  z-index: 10;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.back-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.nav-title {
  position: absolute;
  left: 0;
  right: 0;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin: 0 auto;
  width: 60%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  z-index: 5;
}

.nav-placeholder {
  width: 88rpx;
  height: 88rpx;
  visibility: hidden;
  flex-shrink: 0;
}

/* 搜索区域 */
.search-bar {
  width: 100%;
  height: 100rpx;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  background-color: transparent;
  position: relative;
}

.search-input-wrapper {
  flex: 1;
  display: flex;
  height: 70rpx;
  box-sizing: border-box;
  border-radius: 10rpx;
  overflow: hidden;
  background-color: #f7f7f7;
  align-items: center;
  position: relative;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid #1C82F5;
}

.search-icon {
  margin-left: 20rpx;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-input {
  flex: 1;
  height: 100%;
  padding: 0 110rpx 0 15rpx;
  font-size: 28rpx;
  background-color: transparent;
  border: none;
  color: #333;
}

.search-btn {
  height: 56rpx;
  line-height: 56rpx;
  background: linear-gradient(to right, #1C82F5, #27AFF5);
  color: #fff;
  font-size: 28rpx;
  text-align: center;
  padding: 0 30rpx;
  border-radius: 8rpx;
  position: absolute;
  right: 7rpx;
  top: 7rpx;
  bottom: 7rpx;
  box-shadow: 0 2rpx 8rpx rgba(56, 136, 255, 0.3);
}

/* 收藏按钮样式 */
.collect-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  width: 70rpx;
  transition: all 0.2s ease-in-out;
  margin-right: 5rpx;
  flex-shrink: 0;
}

.collect-icon {
  width: 40rpx;
  height: 40rpx;
  color: #3888ff;
  line-height: 1;
  transition: all 0.2s ease-in-out;
}

.collect-text {
  font-size: 22rpx;
  color: #666;
  margin-top: 4rpx;
  transition: all 0.2s ease-in-out;
}

.collect-badge {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  background-color: #ff3b30;
  color: #fff;
  font-size: 20rpx;
  width: 30rpx;
  height: 30rpx;
  line-height: 30rpx;
  text-align: center;
  border-radius: 15rpx;
  transition: all 0.2s ease-in-out;
}

/* 收藏按钮活跃状态 */
.collect-btn.active {
  transform: scale(1.1);
}

.collect-btn.active .collect-icon {
  color: #2970e0;
  text-shadow: 0 0 8rpx rgba(56, 136, 255, 0.5);
}

.collect-btn.active .collect-text {
  color: #2970e0;
}

.collect-btn:active {
  transform: scale(0.95);
  opacity: 0.8;
}

/* 筛选容器样式 */
.filter-container {
  position: relative;
  z-index: 100;
  width: 100%;
  background-color: #fff;
  margin: 0;
  padding: 0;
}

/* 筛选菜单样式 */
.filter-menu {
  display: flex;
  width: 100%;
  height: 80rpx;
  background-color: #fff;
  border-top: none;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
  z-index: 101;
}

.filter-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 26rpx;
  color: #333;
}

.filter-item text {
  margin-right: 4rpx;
}

.filter-arrow {
  color: #999;
  transition: transform 0.3s ease;
}

.filter-item.active .filter-arrow {
  transform: rotate(180deg);
  color: #ff0000;
}

/* 筛选内容 */
.filter-content {
  position: absolute;
  top: 80rpx;
  /* 与筛选菜单高度一致 */
  left: 0;
  width: 100%;
  background: #fff;
  z-index: 99;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease-out;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  opacity: 0;
  transform: translateY(-10rpx);
}

.filter-content.show {
  max-height: 600rpx;
  opacity: 1;
  transform: translateY(0);
}

/* 筛选选项容器 */
.filter-options {
  padding: 10rpx 20rpx;
  max-height: 600rpx;
  overflow-y: auto;
  overflow-x: hidden;
  /* 防止水平滚动 */
  box-sizing: border-box;
  border-top: none;
  /* 移除上边框，消除间隙 */
}

/* 筛选选项 */
.filter-option {
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1rpx solid #eee;
}

.filter-option.active {
  color: #ff0000;
  background-color: #f7f7f7;
}

/* 遮罩层 */
.filter-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 998;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s;
}

.filter-mask.show {
  opacity: 1;
  visibility: visible;
}

/* 车辆列表 */
.car-list {
  width: 100%;
  padding: 0 20rpx 120rpx;
  background-color: transparent;
  box-sizing: border-box;

}

/* 车辆卡片样式 */
.car-item {
  display: flex;
  flex-direction: column;
  padding: 16rpx 20rpx;
  border-bottom: none;
  background-color: #fff;
  margin: 0 0 16rpx 0;
  align-items: flex-start;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
}

.custom-nav {
  width: 100%;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
  position: relative;
  padding-bottom: 0;
}

.filter-tabs {
  width: 100%;
}

/* 自定义 van-tabs 样式 */
.filter-tabs .van-tabs__nav {
  display: flex !important;
}

.filter-tabs .van-tab {
  -webkit-box-flex: 1 !important;
  flex: 1 !important;
  min-width: 0 !important;
}

.filter-tabs .van-tabs__line {
  width: 40rpx !important;
  background-color: #ff0000 !important;
}

.filter-nav {
  width: 100%;
}

.filter-tab {
  flex: 1 !important;
  min-width: 100% !important;
  font-size: 28rpx;
}

.filter-tab-active {
  font-weight: bold;
  color: #ff0000 !important;
}

.car-main-content {
  display: flex;
  flex-direction: row;
  width: 100%;
  margin-bottom: 0;
  align-items: flex-start;
  height: auto;
  min-height: 160rpx;
  position: relative;
}

.car-image-container {
  width: 220rpx;
  height: 160rpx;
  margin-right: 16rpx;
  border-radius: 8rpx;
  overflow: hidden;
  flex-shrink: 0;
  align-self: flex-start;
  margin-top: 0;
}

.car-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6rpx;
  /* 添加圆角 */
}

.car-content-wrapper {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 2rpx;
  margin-top: 1rpx;
  /* 与标题的间距 */
}

.car-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  height: auto;
  box-sizing: border-box;
  position: relative;
  padding: 0;
  margin-left: 0;
  gap: 6rpx;
}

.car-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
  max-height: 72rpx;
  width: 100%;
  margin: 0 0 4rpx 0;
}

/* 车辆参数信息样式 */
.car-params {
  display: flex;
  align-items: center;
  font-size: 22rpx;
  color: #999;
  margin: 0 0 2rpx 0;
  flex-wrap: nowrap;
  width: 100%;
}

.param-item {
  white-space: nowrap;
}

.separator {
  margin: 0 6rpx;
  color: #ddd;
}

.car-attrs {
  font-size: 24rpx;
  color: #666;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  gap: 12rpx;
  justify-content: space-between;
  width: 100%;
  margin: 0;
  padding-top: 6rpx;
  /* 减少顶部内边距，从36rpx调整为6rpx */
}

/* 调整价格区域样式 */
.price-info {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: space-between;
  margin: 4rpx 0;
}

.sale-price {
  font-size: 28rpx;
  color: #2B68F6;
  font-weight: 500;
  white-space: nowrap;
  text-align: right;
}

.new-price {
  font-size: 20rpx;
  color: #999;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 65%;
}

.car-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6rpx;
  width: 100%;
  margin-top: 4rpx;
}

.tag {
  font-size: 18rpx;
  padding: 0 8rpx;
  border-radius: 4rpx;
  line-height: 1.2;
  height: 28rpx;
  display: inline-flex;
  align-items: center;
}

.tag-blue {
  color: #3B82F6;
  background-color: #EEF4FF;
}

.tag-red {
  color: #FF4800;
  background-color: #F9E5DB;
}

/* 加载状态样式 */
.loading,
.no-more,
.empty {
  text-align: center;
  padding: 20rpx 0;
  font-size: 24rpx;
  color: #999;
  width: 100%;
}

/* 底部区域样式 */
.car-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 22rpx;
  color: #999;
  margin-top: auto;
  /* 将底部信息推到底部 */
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  /* 防止内容溢出 */
}

/* 筛选弹出层样式 */
.filter-popup {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background: #fff;
  position: relative;
  margin-top: 100rpx;
  /* 添加顶部间距 */
}

.filter-popup-safe-area {
  width: 100%;
  height: 50rpx;
  background: transparent;
}

.filter-popup-header {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 120rpx;
  position: relative;
  border-bottom: 1px solid #eee;
  width: 100%;
  padding: 20rpx 0;
}

.filter-popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.filter-popup-close {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32rpx;
  color: #999;
  padding: 20rpx;
  z-index: 1110;
  height: 44rpx;
  width: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filter-popup-content {
  flex: 1;
  overflow-y: auto;
}

.filter-section {
  padding: 30rpx;
  border-bottom: 1px solid #eee;
}

.section-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: bold;
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
  padding: 0 15rpx;
}

.slider-labels text {
  font-size: 24rpx;
  color: #666;
  transform: translateX(-50%);
}

/* 自定义滑块样式 */
.van-slider {
  margin: 30rpx 0;
}

.van-slider__button {
  width: 40rpx !important;
  height: 40rpx !important;
  background-color: #fff !important;
  border: 2rpx solid #ffc62c !important;
}

.van-slider__bar {
  background-color: #ffc62c !important;
}

.filter-popup-footer {
  display: flex;
  padding: 20rpx 30rpx;
  border-top: 1px solid #eee;
}

.reset-btn,
.confirm-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 10rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.reset-btn {
  background: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background: #ffc62c;
  color: #fff;
}

/* van-area 组件的自定义样式 */
.van-area {
  height: 400rpx;
}

.van-area__title {
  font-size: 28rpx;
  color: #333;
  padding: 20rpx;
}

.van-picker-column__item {
  font-size: 28rpx;
}

.van-picker-column__item--selected {
  color: #ffc62c;
  font-weight: bold;
}

/* 地区选择器样式优化 */
.filter-section .van-area {
  height: 400rpx;
}

.filter-section .van-picker-column {
  height: 400rpx !important;
}

.filter-section .van-picker-column__item {
  font-size: 28rpx;
  line-height: 80rpx;
}

.filter-section .van-picker-column__item--selected {
  color: #ffc62c;
  font-weight: bold;
}

/* 确保选择器内容居中显示 */
.filter-section .van-picker-column__wrapper {
  height: 400rpx !important;
}

/* 搜索容器 */
.search-container {
  padding: 0;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
}

/* 批量车信息行 */
.batch-info {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
  margin-bottom: 8rpx;
  flex-wrap: nowrap;
  width: 100%;
}

/* 地区选择器头部样式 */
.area-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 88rpx;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #fff;
}

.area-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.area-cancel {
  font-size: 28rpx;
  color: #666;
  padding: 10rpx;
}

.area-placeholder {
  width: 80rpx;
}

/* 地区选择器底部按钮栏 */
.area-footer {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  background-color: #fff;
  z-index: 1001;
  width: 100%;
  box-sizing: border-box;
  position: relative;
}

.area-reset-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  color: #666;
  font-size: 28rpx;
  margin-right: 20rpx;
}

.area-confirm-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background: linear-gradient(to right, #1C82F5, #27AFF5);
  border-radius: 8rpx;
  color: #fff;
  font-size: 28rpx;
}

/* 城市弹出层样式 */
.city-popup {
  height: auto !important;
  max-height: 70vh !important;
}

/* 地区选择容器样式 */
.area-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: auto;
  /* 修改为自适应高度 */
  max-height: 70vh;
  /* 最大高度限制 */
  overflow: hidden;
  background-color: #fff;
}

/* 全国选项样式 */
.all-city {
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #f9f9f9;
  border-bottom: 1rpx solid #eee;
  font-weight: 500;
}

/* 修改van-area样式，确保不会占满整个容器 */
.custom-area {
  height: 400rpx !important;
  overflow: hidden;
}

/* 地区选择器容器的picker容器 */
.area-picker-container {
  height: 400rpx;
  overflow: hidden;
  width: 100%;
  background-color: #fff;
}

/* 批量车卡片样式（完全独立） */
.batch-car-item {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx 30rpx;
  margin: 0 0 20rpx 0;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.batch-car-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.3;
  margin-bottom: 16rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 批量车价格区域 */
.batch-price-section {
  display: flex;
  align-items: center;
  width: 100%;
  margin: 20rpx 0;
}

.batch-price-main {
  font-size: 36rpx;
  color: #2B68F6;
  font-weight: bold;
  line-height: 1;
  margin-left: 16rpx;
}

.batch-price-sub {
  font-size: 22rpx;
  color: #999;
  margin-left: auto;
  margin-right: 16rpx;
  font-weight: normal;
}

.batch-price-location {
  font-size: 24rpx;
  color: #666;
  margin-left: 0;
}

/* 批量车属性区域 */
.batch-attrs-section {
  display: flex;
  width: 100%;
  margin: 16rpx 0;
  padding: 20rpx 0;
  border-top: none;
  border-bottom: none;
}

.batch-attr-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  flex: 1;
}

.batch-attr-label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.batch-attr-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 批量车标签区域 */
.batch-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  margin-top: 16rpx;
}

.batch-tag {
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  background-color: #EEF4FF;
  color: #3B82F6;
  border-radius: 4rpx;
}