<!-- 登录弹窗模板 -->
<template name="loginPopup">
  <view
    class="login-popup"
    wx:if="{{showLoginPopup}}"
    catchtouchmove="true"
  >
    <view
      class="login-popup-mask"
      bindtap="{{loginPopupOptions.clickMaskClose ? 'closeLoginPopup' : ''}}"
    />
    <view class="login-popup-content">
      <view
        class="login-popup-close"
        bindtap="closeLoginPopup"
        wx:if="{{!loginPopupOptions.hideCloseButton}}"
      ></view>
      <view class="login-popup-image">
        <image
          src="{{loginPopupOptions.imageUrl || 'https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/uploads/proof/1751274411926_900.png'}}"
          mode="aspectFit"
        ></image>
      </view>
      <view class="login-popup-text">{{loginPopupOptions.title || '点击登录，解锁更多功能'}}</view>
      <button
        class="login-popup-button"
        bindtap="handleLogin"
      >{{loginPopupOptions.buttonText || '登录'}}</button>
    </view>
  </view>
</template>