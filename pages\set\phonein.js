// pages/set/phonein.js
import util from '../../utils/util';
import api from '../../utils/api';

Page({

  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: 20, // 默认状态栏高度，会在onLoad中动态获取
    phone: '', // 手机号
    countryCode: '+86', // 国家代码
    country: '中国大陆' // 国家/地区
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });

    // 隐藏原生导航栏
    wx.hideNavigationBarLoading();
    wx.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: 'transparent',
      animation: {
        duration: 0,
        timingFunc: 'easeIn'
      }
    });

    // 不再从缓存获取用户信息，确保手机号默认为空
    // this.getUserInfoFromCache();
  },

  /**
   * 从缓存中获取用户信息
   */
  getUserInfoFromCache() {
    const userInfo = util.getCacheWithExpiry('userInfo');
    if (userInfo && userInfo.phone) {
      this.setData({
        phone: userInfo.phone
      });
    }
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 选择国家/地区
   */
  selectCountry() {
    // 这里可以打开一个选择国家/地区的页面或弹窗
    // 目前仅支持中国大陆，所以暂不实现具体逻辑
    wx.showToast({
      title: '暂仅支持中国大陆',
      icon: 'none'
    });
  },

  /**
   * 输入手机号
   */
  inputPhone() {
    wx.showModal({
      title: '输入手机号',
      editable: true,
      placeholderText: '请输入手机号',
      success: (res) => {
        if (res.confirm && res.content) {
          // 简单验证手机号格式（11位数字）
          if (/^1\d{10}$/.test(res.content)) {
            this.setData({
              phone: res.content
            });
          } else {
            wx.showToast({
              title: '请输入正确的手机号',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  /**
   * 下一步
   */
  nextStep() {
    const { phone } = this.data;

    // 确保手机号已填写，虽然UI上已经限制，这里再做一次判断以增加安全性
    if (!phone) {
      wx.showToast({
        title: '请输入手机号',
        icon: 'none'
      });
      return;
    }

    // 跳转到获取验证码页面
    wx.navigateTo({
      url: `/pages/set/phoneget?phone=${phone}`,
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})