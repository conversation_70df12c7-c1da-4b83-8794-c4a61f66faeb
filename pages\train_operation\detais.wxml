<!--pages/train_operation/detais.wxml-->
<view
    class="container"
    style="--status-bar-height: {{statusBarHeight}}px;"
>
    <!-- 自定义导航栏 -->
    <view
        class="custom-nav"
        style="padding-top: {{statusBarHeight}}px;"
    >
        <view class="nav-content">
            <view
                class="back-icon"
                bindtap="navigateBack"
            >
                <van-icon
                    name="arrow-left"
                    size="20px"
                    color="#333"
                />
            </view>
            <view class="nav-title">车务详情</view>
        </view>
    </view>

    <!-- 主内容区域 -->
    <view
        class="main-content"
        style="padding-top: {{statusBarHeight + 44}}px;"
    >
        <!-- 切换标签卡片 -->
        <view class="tabs-card">
            <view class="tabs-container">
                <view
                    class="tab-item {{currentTab === 0 ? 'active' : ''}}"
                    bindtap="switchTab"
                    data-index="0"
                >
                    <text class="tab-text">信息详情</text>
                    <view class="tab-line"></view>
                </view>
                <view
                    class="tab-item {{currentTab === 1 ? 'active' : ''}}"
                    bindtap="switchTab"
                    data-index="1"
                >
                    <text class="tab-text">车务流程</text>
                    <view class="tab-line"></view>
                </view>
            </view>
        </view>

        <!-- 信息详情内容 -->
        <view wx:if="{{currentTab === 0}}">
            <!-- 基本信息区域 -->
            <view class="info-section">
                <view class="section-title">基本信息</view>
                <view class="info-row">
                    <view class="info-label">订单类型</view>
                    <view class="info-value">
                        <block wx:if="{{orderDetail.order_category === 'domestic'}}">国内订单</block>
                        <block wx:elif="{{orderDetail.order_category === 'foreign'}}">国外订单</block>
                        <block wx:else>{{orderDetail.order_category || '--'}}</block>
                    </view>
                </view>
                <view class="info-row">
                    <view class="info-label">是否需要提资源</view>
                    <view class="info-value">{{orderDetail.is_need_advance_payment == 1 ? '是' : '否'}}</view>
                </view>
                <view class="info-row">
                    <view class="info-label">是否指定4S店</view>
                    <view class="info-value">{{orderDetail.is_appoint_store == 1 ? '指定4S店' : '无指定4S店'}}</view>
                </view>
                <view class="info-row">
                    <view class="info-label">4S店联系方式</view>
                    <view class="info-value">{{orderDetail.store_phone || '--'}}</view>
                </view>
                <view class="info-row">
                    <view class="info-label">车务公司所在地</view>
                    <view class="info-value">{{orderDetail.address || '--'}}</view>
                </view>
            </view>

            <!-- 交付相关信息区域 -->
            <view class="info-section">
                <view class="section-title">车务完成后交付相关信息</view>
                <view class="info-row">
                    <view class="info-label">最晚时间</view>
                    <view class="info-value">{{orderDetail.end_time || '--'}}</view>
                </view>
                <view class="info-row">
                    <view class="info-label">交付地点</view>
                    <view class="info-value">{{orderDetail.duijie_address || '--'}}</view>
                </view>
                <view class="info-row">
                    <view class="info-label">详细地址</view>
                    <view class="info-value">{{orderDetail.duijie_address_detail || '--'}}</view>
                </view>
                <view class="info-row">
                    <view class="info-label">对接人</view>
                    <view class="info-value">{{orderDetail.duijie_name || '--'}}</view>
                </view>
                <view class="info-row">
                    <view class="info-label">联系电话</view>
                    <view class="info-value">{{orderDetail.duijie_tel || '--'}}</view>
                </view>
            </view>

            <!-- 车辆信息区域，如果有details数据则显示 -->
            <view
                class="info-section"
                wx:if="{{orderDetail.details && orderDetail.details.length > 0}}"
            >
                <view class="section-title">车辆信息</view>
                <block
                    wx:for="{{orderDetail.details}}"
                    wx:key="item_no"
                >
                    <view class="vehicle-detail-item">
                        <view class="info-row">
                            <view class="info-label">车辆型号</view>
                            <view class="info-value">{{item.ui_vehicle_name || '--'}}</view>
                        </view>
                        <view class="info-row">
                            <view class="info-label">品牌名称</view>
                            <view class="info-value">{{item.brand_name || '--'}}</view>
                        </view>
                        <view class="info-row">
                            <view class="info-label">预估价格</view>
                            <view class="info-value">{{item.price_pre || '--'}}</view>
                        </view>
                    </view>
                </block>
            </view>

            <!-- 备注信息区域 -->
            <view
                class="info-section"
                wx:if="{{orderDetail.remarks}}"
            >
                <view class="section-title">备注信息</view>
                <view class="info-row">
                    <view class="info-value remarks">{{orderDetail.remarks || '--'}}</view>
                </view>
            </view>
        </view>

        <!-- 车务流程内容 -->
        <view wx:if="{{currentTab === 1}}">
            <!-- 车务流程信息区域 -->
            <view class="info-section">
                <view class="section-title">车务流程信息</view>
                <view class="info-row">
                    <view class="info-label">订单类型</view>
                    <view class="info-value">
                        <block wx:if="{{orderDetail.order_category === 'domestic'}}">国内订单</block>
                        <block wx:elif="{{orderDetail.order_category === 'foreign'}}">国外订单</block>
                        <block wx:else>{{orderDetail.order_category || '--'}}</block>
                    </view>
                </view>
                <view class="info-row">
                    <view class="info-label">订单编号</view>
                    <view class="info-value">{{orderDetail.chewu_orderid || '--'}}</view>
                </view>
                <view class="info-row">
                    <view class="info-label">单证人员</view>
                    <view class="info-value">{{orderDetail.applicant || '--'}}</view>
                </view>
                <view class="info-row">
                    <view class="info-label">立项时间</view>
                    <view class="info-value">{{orderDetail.setup_time || '--'}}</view>
                </view>
                <view class="info-row">
                    <view class="info-label">立项天数</view>
                    <view class="info-value">{{orderDetail.days || '--'}}</view>
                </view>
                <view class="info-row">
                    <view class="info-label">甲方</view>
                    <view class="info-value">{{orderDetail.duijie_name || '--'}}</view>
                </view>
            </view>

            <!-- 流程时间轴 -->
            <view class="process-timeline">
                <!-- 合同立项 - 动态状态 -->
                <view class="timeline-item">
                    <view class="timeline-left">
                        <view class="timeline-content-vertical {{processStatus['101']}}">
                            <view class="timeline-icon {{processStatus['101']}}">
                                <image
                                    src="{{processStatus['101'] === 'completed' ? '/icons/train_operation/complete.png' : processStatus['101'] === 'in-progress' ? '/icons/train_operation/ongoing.png' : '/icons/train_operation/pending.png'}}"
                                    class="status-icon"
                                ></image>
                            </view>
                            <view class="timeline-step">合同立项</view>
                            <view class="timeline-info">{{processStatus['101'] !== 'pending' ? processExecutors['101']
                                || '--' : '--'}}</view>
                            <view class="timeline-date">{{processStatus['101'] !== 'pending' ? processTimestamps['101']
                                || '--' :
                                '--'}}</view>
                            <view class="timeline-status {{processStatus['101']}}">
                                {{processStatus['101'] === 'completed' ? '完成' : processStatus['101'] === 'in-progress' ?
                                '进行中' : '待处理'}}
                            </view>
                        </view>
                    </view>
                    <view class="timeline-right">
                        <view class="action-buttons">
                            <block
                                wx:for="{{processGroups['101']}}"
                                wx:key="id"
                            >
                                <button
                                    class="action-btn {{processStatus['101'] === 'completed' && item.process_txt === '上传合同附件' ? 'disabled' : processStatus['101'] === 'pending' ? 'pending-btn' : attachmentStatus[item.id] && attachmentStatus[item.id].status == 1 ? 'completed-btn' : ''}}"
                                    bindtap="handleButtonClick"
                                    data-type="{{item.type || 'contract'}}"
                                    data-name="{{item.process_txt}}"
                                    data-process-id="{{item.process_id}}"
                                    data-id="{{item.id}}"
                                    data-special-action="{{item.special_action}}"
                                    disabled="{{(processStatus['101'] === 'completed' && item.process_txt === '上传合同附件') || processStatus['101'] === 'pending'}}"
                                >{{item.process_txt}}</button>
                                <!-- 每2个按钮换一行 -->
                                <view
                                    class="action-buttons"
                                    wx:if="{{index % 2 === 1 && index < processGroups['101'].length - 1}}"
                                ></view>
                            </block>
                        </view>
                        <view class="timeline-divider"></view>
                    </view>
                </view>

                <!-- 收付款 - 动态状态 -->
                <view class="timeline-item">
                    <view class="timeline-left">
                        <view class="timeline-content-vertical {{processStatus['201']}}">
                            <view class="timeline-icon {{processStatus['201']}}">
                                <image
                                    src="{{processStatus['201'] === 'completed' ? '/icons/train_operation/complete.png' : processStatus['201'] === 'in-progress' ? '/icons/train_operation/ongoing.png' : '/icons/train_operation/pending.png'}}"
                                    class="status-icon"
                                ></image>
                            </view>
                            <view class="timeline-step">收付款</view>
                            <view class="timeline-info">{{processStatus['201'] !== 'pending' ? processExecutors['201']
                                || '--' : '--'}}</view>
                            <view class="timeline-date">{{processStatus['201'] !== 'pending' ? processTimestamps['201']
                                || '--' :
                                '--'}}</view>
                            <view class="timeline-status {{processStatus['201']}}">
                                {{processStatus['201'] === 'completed' ? '完成' : processStatus['201'] === 'in-progress' ?
                                '进行中' : '待处理'}}
                            </view>
                        </view>
                    </view>
                    <view class="timeline-right">
                        <view class="action-buttons">
                            <block
                                wx:for="{{processGroups['201']}}"
                                wx:key="id"
                            >
                                <button
                                    class="action-btn {{processStatus['201'] === 'pending' ? 'pending-btn' : attachmentStatus[item.id] && attachmentStatus[item.id].status == 1 ? 'completed-btn' : ''}}"
                                    bindtap="handleButtonClick"
                                    data-type="{{item.type || 'payment'}}"
                                    data-name="{{item.process_txt}}"
                                    data-process-id="{{item.process_id}}"
                                    data-id="{{item.id}}"
                                    data-special-action="{{item.special_action}}"
                                    disabled="{{processStatus['201'] === 'pending'}}"
                                >{{item.process_txt}}</button>
                                <!-- 每2个按钮换一行 -->
                                <view
                                    class="action-buttons"
                                    wx:if="{{index % 2 === 1 && index < processGroups['201'].length - 1}}"
                                ></view>
                            </block>
                        </view>
                        <view class="timeline-divider"></view>
                    </view>
                </view>

                <!-- 许可证 - 动态状态 -->
                <view class="timeline-item">
                    <view class="timeline-left">
                        <view class="timeline-content-vertical {{processStatus['301']}}">
                            <view class="timeline-icon {{processStatus['301']}}">
                                <image
                                    src="{{processStatus['301'] === 'completed' ? '/icons/train_operation/complete.png' : processStatus['301'] === 'in-progress' ? '/icons/train_operation/ongoing.png' : '/icons/train_operation/pending.png'}}"
                                    class="status-icon"
                                ></image>
                            </view>
                            <view class="timeline-step">许可证</view>
                            <view class="timeline-info">{{processStatus['301'] !== 'pending' ? processExecutors['301']
                                || '--' : '--'}}</view>
                            <view class="timeline-date">{{processStatus['301'] !== 'pending' ? processTimestamps['301']
                                || '--' :
                                '--'}}</view>
                            <view class="timeline-status {{processStatus['301']}}">
                                {{processStatus['301'] === 'completed' ? '完成' : processStatus['301'] === 'in-progress' ?
                                '进行中' : '待处理'}}
                            </view>
                        </view>
                    </view>
                    <view class="timeline-right">
                        <view class="action-buttons">
                            <block
                                wx:for="{{processGroups['301']}}"
                                wx:key="id"
                            >
                                <button
                                    class="action-btn {{processStatus['301'] === 'pending' ? 'pending-btn' : attachmentStatus[item.id] && attachmentStatus[item.id].status == 1 ? 'completed-btn' : ''}}"
                                    bindtap="handleButtonClick"
                                    data-type="{{item.type || 'license'}}"
                                    data-name="{{item.process_txt}}"
                                    data-process-id="{{item.process_id}}"
                                    data-id="{{item.id}}"
                                    data-special-action="{{item.special_action}}"
                                    disabled="{{processStatus['301'] === 'pending'}}"
                                >{{item.process_txt}}</button>
                                <!-- 每2个按钮换一行 -->
                                <view
                                    class="action-buttons"
                                    wx:if="{{index % 2 === 1 && index < processGroups['301'].length - 1}}"
                                ></view>
                            </block>
                        </view>
                        <view class="timeline-divider"></view>
                    </view>
                </view>

                <!-- 物流 - 动态状态 -->
                <view class="timeline-item">
                    <view class="timeline-left">
                        <view class="timeline-content-vertical {{processStatus['401']}}">
                            <view class="timeline-icon {{processStatus['401']}}">
                                <image
                                    src="{{processStatus['401'] === 'completed' ? '/icons/train_operation/complete.png' : processStatus['401'] === 'in-progress' ? '/icons/train_operation/ongoing.png' : '/icons/train_operation/pending.png'}}"
                                    class="status-icon"
                                ></image>
                            </view>
                            <view class="timeline-step">物流</view>
                            <view class="timeline-info">{{processStatus['401'] !== 'pending' ? processExecutors['401']
                                || '--' : '--'}}</view>
                            <view class="timeline-date">{{processStatus['401'] !== 'pending' ? processTimestamps['401']
                                || '--' :
                                '--'}}</view>
                            <view class="timeline-status {{processStatus['401']}}">
                                {{processStatus['401'] === 'completed' ? '完成' : processStatus['401'] === 'in-progress' ?
                                '进行中' : '待处理'}}
                            </view>
                        </view>
                    </view>
                    <view class="timeline-right">
                        <view class="action-buttons">
                            <block
                                wx:for="{{processGroups['401']}}"
                                wx:key="id"
                            >
                                <button
                                    class="action-btn {{processStatus['401'] === 'pending' ? 'pending-btn' : attachmentStatus[item.id] && attachmentStatus[item.id].status == 1 ? 'completed-btn' : ''}}"
                                    bindtap="handleButtonClick"
                                    data-type="{{item.type || 'logistics'}}"
                                    data-name="{{item.process_txt}}"
                                    data-process-id="{{item.process_id}}"
                                    data-id="{{item.id}}"
                                    data-special-action="{{item.special_action}}"
                                    disabled="{{processStatus['401'] === 'pending'}}"
                                >{{item.process_txt}}</button>
                                <!-- 每2个按钮换一行 -->
                                <view
                                    class="action-buttons"
                                    wx:if="{{index % 2 === 1 && index < processGroups['401'].length - 1}}"
                                ></view>
                            </block>
                        </view>
                        <view class="timeline-divider"></view>
                    </view>
                </view>

                <!-- 报关 - 动态状态 -->
                <view class="timeline-item">
                    <view class="timeline-left">
                        <view class="timeline-content-vertical {{processStatus['501']}}">
                            <view class="timeline-icon {{processStatus['501']}}">
                                <image
                                    src="{{processStatus['501'] === 'completed' ? '/icons/train_operation/complete.png' : processStatus['501'] === 'in-progress' ? '/icons/train_operation/ongoing.png' : '/icons/train_operation/pending.png'}}"
                                    class="status-icon"
                                ></image>
                            </view>
                            <view class="timeline-step">报关</view>
                            <view class="timeline-info">{{processStatus['501'] !== 'pending' ? processExecutors['501']
                                || '--' : '--'}}</view>
                            <view class="timeline-date">{{processStatus['501'] !== 'pending' ? processTimestamps['501']
                                || '--' :
                                '--'}}</view>
                            <view class="timeline-status {{processStatus['501']}}">
                                {{processStatus['501'] === 'completed' ? '完成' : processStatus['501'] === 'in-progress' ?
                                '进行中' : '待处理'}}
                            </view>
                        </view>
                    </view>
                    <view class="timeline-right">
                        <view class="action-buttons">
                            <block
                                wx:for="{{processGroups['501']}}"
                                wx:key="id"
                            >
                                <button
                                    class="action-btn {{processStatus['501'] === 'pending' ? 'pending-btn' : attachmentStatus[item.id] && attachmentStatus[item.id].status == 1 ? 'completed-btn' : ''}}"
                                    bindtap="handleButtonClick"
                                    data-type="{{item.type || 'customs'}}"
                                    data-name="{{item.process_txt}}"
                                    data-process-id="{{item.process_id}}"
                                    data-id="{{item.id}}"
                                    data-special-action="{{item.special_action}}"
                                    disabled="{{processStatus['501'] === 'pending'}}"
                                >{{item.process_txt}}</button>
                                <!-- 每2个按钮换一行 -->
                                <view
                                    class="action-buttons"
                                    wx:if="{{index % 2 === 1 && index < processGroups['501'].length - 1}}"
                                ></view>
                            </block>
                        </view>
                        <view class="timeline-divider"></view>
                    </view>
                </view>

                <!-- 退税 - 动态状态 -->
                <view class="timeline-item">
                    <view class="timeline-left">
                        <view class="timeline-content-vertical {{processStatus['601']}}">
                            <view class="timeline-icon {{processStatus['601']}}">
                                <image
                                    src="{{processStatus['601'] === 'completed' ? '/icons/train_operation/complete.png' : processStatus['601'] === 'in-progress' ? '/icons/train_operation/ongoing.png' : '/icons/train_operation/pending.png'}}"
                                    class="status-icon"
                                ></image>
                            </view>
                            <view class="timeline-step">退税</view>
                            <view class="timeline-info">{{processStatus['601'] !== 'pending' ? processExecutors['601']
                                || '--' : '--'}}</view>
                            <view class="timeline-date">{{processStatus['601'] !== 'pending' ? processTimestamps['601']
                                || '--' :
                                '--'}}</view>
                            <view class="timeline-status {{processStatus['601']}}">
                                {{processStatus['601'] === 'completed' ? '完成' : processStatus['601'] === 'in-progress' ?
                                '进行中' : '待处理'}}
                            </view>
                        </view>
                    </view>
                    <view class="timeline-right">
                        <view class="action-buttons">
                            <block
                                wx:for="{{processGroups['601']}}"
                                wx:key="id"
                            >
                                <button
                                    class="action-btn {{processStatus['601'] === 'pending' ? 'pending-btn' : attachmentStatus[item.id] && attachmentStatus[item.id].status == 1 ? 'completed-btn' : ''}}"
                                    bindtap="handleButtonClick"
                                    data-type="{{item.type || 'taxRefund'}}"
                                    data-name="{{item.process_txt}}"
                                    data-process-id="{{item.process_id}}"
                                    data-id="{{item.id}}"
                                    data-special-action="{{item.special_action}}"
                                    disabled="{{processStatus['601'] === 'pending'}}"
                                >{{item.process_txt}}</button>
                                <!-- 每2个按钮换一行 -->
                                <view
                                    class="action-buttons"
                                    wx:if="{{index % 2 === 1 && index < processGroups['601'].length - 1}}"
                                ></view>
                            </block>
                        </view>
                        <view class="timeline-divider"></view>
                    </view>
                </view>

                <!-- 完结 - 动态状态 -->
                <view class="timeline-item">
                    <view class="timeline-left">
                        <view class="timeline-content-vertical {{processStatus['701']}}">
                            <view class="timeline-icon {{processStatus['701']}}">
                                <image
                                    src="{{processStatus['701'] === 'completed' ? '/icons/train_operation/complete.png' : processStatus['701'] === 'in-progress' ? '/icons/train_operation/ongoing.png' : '/icons/train_operation/pending.png'}}"
                                    class="status-icon"
                                ></image>
                            </view>
                            <view class="timeline-step">完结</view>
                            <view class="timeline-info">{{processStatus['701'] !== 'pending' ? processExecutors['701']
                                || '--' : '--'}}</view>
                            <view class="timeline-date">{{processStatus['701'] !== 'pending' ? processTimestamps['701']
                                || '--' :
                                '--'}}</view>
                            <view class="timeline-status {{processStatus['701']}}">
                                {{processStatus['701'] === 'completed' ? '完成' : processStatus['701'] === 'in-progress' ?
                                '进行中' : '待处理'}}
                            </view>
                        </view>
                    </view>
                    <view class="timeline-right">
                        <view class="action-buttons">
                            <block
                                wx:for="{{processGroups['701']}}"
                                wx:key="id"
                            >
                                <button
                                    class="action-btn {{processStatus['701'] === 'pending' ? 'pending-btn' : attachmentStatus[item.id] && attachmentStatus[item.id].status == 1 ? 'completed-btn' : ''}}"
                                    bindtap="handleButtonClick"
                                    data-type="{{item.type || 'completion'}}"
                                    data-name="{{item.process_txt}}"
                                    data-process-id="{{item.process_id}}"
                                    data-id="{{item.id}}"
                                    data-special-action="{{item.special_action}}"
                                    disabled="{{processStatus['701'] === 'pending'}}"
                                >{{item.process_txt}}</button>
                                <!-- 每2个按钮换一行 -->
                                <view
                                    class="action-buttons"
                                    wx:if="{{index % 2 === 1 && index < processGroups['701'].length - 1}}"
                                ></view>
                            </block>
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</view>