/* pages/member/pay.wxss */

/* 页面容器 */
.container {
    min-height: 100vh;
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    position: relative;
}

/* 渐变背景 */
.gradient-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
    background-attachment: fixed;
    /* 固定背景，避免滚动时背景移动 */
    z-index: -1;
}

/* 自定义导航栏 */
.custom-navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 100;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 100%);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.status-bar {
    width: 100%;
}

.navbar-content {
    display: flex;
    height: 44px;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
}

.nav-back {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 44px;
    z-index: 5;
}

.nav-title {
    flex: 1;
    text-align: center;
    font-size: 34rpx;
    font-weight: 500;
    color: #333;
}

.nav-placeholder {
    width: 40px;
    height: 44px;
}

/* 内容区域 */
.content {
    width: 100%;
    padding: 20rpx;
    box-sizing: border-box;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding-top: 30rpx;
}

/* 会员标题 */
.membership-title {
    font-size: 36rpx;
    color: #333;
    text-align: center;
    margin-bottom: 40rpx;
    font-weight: normal;
}

/* 价格容器 */
.price-container {
    display: flex;
    align-items: flex-end;
    justify-content: center;
    margin-bottom: 60rpx;
}

/* 价格符号 */
.price-symbol {
    font-size: 40rpx;
    font-weight: normal;
    color: #333;
    line-height: 1;
    align-self: flex-start;
    margin-top: 16rpx;
    margin-right: 4rpx;
}

/* 价格数值 */
.price-value {
    font-size: 80rpx;
    font-weight: normal;
    color: #333;
    line-height: 1;
}

/* 支付按钮容器 */
.pay-button-container {
    width: auto;
    margin-top: 40rpx;
    display: flex;
    justify-content: center;
}

/* 支付按钮 */
.pay-button {
    background-color: #4080FF;
    color: #FFFFFF;
    border-radius: 8rpx;
    font-size: 32rpx;
    width: 320rpx;
    height: 70rpx;
    line-height: 70rpx;
    padding: 0;
    border: none;
    font-weight: normal;
    box-shadow: 0 4rpx 8rpx rgba(64, 128, 255, 0.2);
}

.pay-button::after {
    border: none;
}

/* 支付订单状态卡片 */
.payment-status-card {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-image: linear-gradient(135deg, #AAD3FE 0%, #DCE5F3 30%, #F8EFED 70%, #EFF3F9 100%);
    border-radius: 12rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0;
    width: 400rpx;
    height: 240rpx;
    z-index: 999;
    animation: fadeIn 0.3s ease-in-out;
    text-align: center;
    box-sizing: border-box;
}

.payment-diamond-icon {
    width: 160rpx;
    height: 160rpx;
    margin: 0 auto 10rpx;
    display: block;
    transform: translateX(-20rpx);
}

.payment-status-text {
    font-size: 28rpx;
    color: #333;
    text-align: center;
    font-weight: 500;
    line-height: 1.5;
    margin: 0;
    padding: 0;
    transform: translateY(5rpx);
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.9);
    }

    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}