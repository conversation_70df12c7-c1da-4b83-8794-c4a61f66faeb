<!-- 车辆详情页 - 全新设计 -->
<view class="detail-container">
  <!-- 固定内容区域 -->
  <view class="fixed-header">
    <!-- 轮播图区域 -->
    <view class="swiper-container">
      <!-- 返回按钮 -->
      <view
        class="back-button"
        bindtap="goBack"
      >
        <view class="back-icon"></view>
      </view>

      <swiper
        class="image-swiper"
        indicator-dots="{{false}}"
        autoplay="{{true}}"
        interval="4000"
        duration="400"
        circular="{{true}}"
        bindchange="swiperChange"
      >
        <block
          wx:for="{{vehicleInfo.images}}"
          wx:key="index"
        >
          <swiper-item>
            <image
              src="{{item}}"
              mode="aspectFill"
              class="swiper-image"
              bindtap="previewImage"
              data-url="{{item}}"
              lazy-load="{{true}}"
            />
          </swiper-item>
        </block>
      </swiper>

      <!-- 计数器固定在轮播图容器上 -->
      <view
        class="image-counter"
        wx:if="{{vehicleInfo.images && vehicleInfo.images.length > 1}}"
      >{{current+1}}/{{vehicleInfo.images.length}}</view>
    </view>

    <!-- 车辆标题与价格 -->
    <view class="vehicle-title-section">
      <view class="vehicle-title">{{vehicleInfo.title || '--'}}</view>
      <view class="vehicle-info-row">
        <view class="vehicle-location">{{vehicleInfo.location || '未知地区'}}</view>
        <view class="vehicle-date">发布日期：{{vehicleInfo.publishTime || vehicleInfo.date || '未知日期'}}</view>
      </view>
      <view class="price-container">
        <view class="price-left">
          <text>新车指导价：</text>
          <text class="strikethrough">{{vehicleInfo.guidePrice || '--'}}</text>
          <text>万</text>
        </view>
        <view class="price-right">

          <text class="price-value">{{vehicleInfo.salePrice || '--'}}</text>
          <text class="price-unit">万元</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 可滚动内容区域 -->
  <scroll-view
    class="scrollable-content"
    scroll-y="true"
  >
    <!--车辆参数卡片 -->
    <view class="vehicle-params-card">
      <view class="params-card-header">
        <text class="params-title">车辆参数</text>
        <view
          class="detailed-config-btn"
          bindtap="showDetailedParams"
        >
          <text>详细参数配置</text>
          <view class="arrow-right"></view>
        </view>
      </view>

      <view class="params-content">
        <view class="param-row">
          <view class="param-item">
            <text class="param-label">品牌</text>
            <text class="param-value">{{vehicleInfo.brand || '比亚迪'}}</text>
          </view>
          <view class="param-item">
            <text class="param-label">车系</text>
            <text class="param-value">{{vehicleInfo.series || '汉DM'}}</text>
          </view>
        </view>

        <view class="param-row">
          <view class="param-item">
            <text class="param-label">厂商</text>
            <text class="param-value">{{vehicleInfo.manufacturer || '比亚迪'}}</text>
          </view>
          <view class="param-item">
            <text class="param-label">车身类型</text>
            <text class="param-value">{{vehicleInfo.bodyType || 'sedan'}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 商家信息卡片 -->
    <view class="merchant-card">
      <view class="params-card-header">
        <text class="params-title">商家信息</text>
      </view>
      <view class="merchant-info">
        <view class="merchant-logo">
          <image
            src="{{merchantInfo.logo || 'https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/uploads/proof/1751273082786_745.png'}}"
            mode="aspectFit"
          ></image>
        </view>
        <view class="merchant-details">
          <view class="merchant-name">{{merchantInfo.company_name || '暂无商家信息'}}</view>
          <view class="merchant-address">地址: {{merchantInfo.address || '暂无地址信息'}}</view>
          <view class="merchant-stats">
            <text class="merchant-rating">⭐ {{merchantInfo.score || '4.9'}}</text>
            <text class="merchant-stock">在售 {{merchantInfo.car_count || 0}} 辆</text>
          </view>
        </view>
        <view class="merchant-contact">
          <button
            class="verify-btn"
            bindtap="viewMerchantDetail"
          >进入店铺</button>
        </view>
      </view>
    </view>

    <!-- 车辆实拍区域 -->
    <view class="vehicle-photos-card">
      <view class="params-card-header">
        <text class="params-title">车辆实拍</text>
      </view>
      <view class="vehicle-photos-container">
        <view class="photos-column">
          <block
            wx:for="{{vehicleInfo.images}}"
            wx:key="index"
          >
            <view class="photo-item">
              <image
                src="{{item}}"
                mode="aspectFill"
                class="real-photo"
                bindtap="previewImage"
                data-url="{{item}}"
                lazy-load="{{true}}"
              />
            </view>
          </block>
        </view>
      </view>
    </view>

    <!-- 底部空白区域，防止内容被底部按钮遮挡 -->
    <view class="bottom-placeholder"></view>
  </scroll-view>

  <!-- 底部固定按钮 -->
  <view class="bottom-action-bar">
    <view class="action-buttons-container">
      <view
        class="action-btn-contact"
        bindtap="contactSeller"
      >
        联系商家
      </view>
      <view
        class="action-btn-manual"
        bindtap="downloadManual"
      >
        下载车辆手册
      </view>
    </view>
    <view
      class="action-btn-collect"
      bindtap="toggleFavorite"
    >
      <image
        class="collect-icon"
        src="{{isFavorite ? 'https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/uploads/proof/1751273844218_454.svg' : 'https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/uploads/proof/1751273929799_524.svg'}}"
      ></image>
      <text>收藏</text>
    </view>
  </view>
</view>