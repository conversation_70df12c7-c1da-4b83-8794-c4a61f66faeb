<!--pages/set/revise.wxml-->
<view class="container">
  <!-- 固定头部区域 -->
  <view class="fixed-header">
    <!-- 顶部状态栏 -->
    <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>
    
    <!-- 导航栏 -->
    <view class="nav-bar">
      <view class="nav-back" bindtap="goBack">
        <van-icon name="arrow-left" size="20px" color="#333" />
      </view>
      <view class="nav-title">个人信息</view>
      <view class="nav-placeholder"></view>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="main-content" style="margin-top: {{statusBarHeight + 44}}px;">
    <!-- 头像区域 - 独立显示 -->
    <view class="avatar-standalone">
      <button class="avatar-button" open-type="chooseAvatar" bindchooseavatar="onChooseAvatar">
        <image class="avatar-image" src="{{userInfo.logo}}" mode="aspectFill"></image>
      </button>
    </view>
    
    <!-- 个人信息区域 -->
    <view class="user-info-section">      
      <!-- 昵称区域 -->
      <view class="info-item">
        <view class="info-label">昵称</view>
        <view class="info-value" bindtap="editNickname">
          <text>{{userInfo.short_name}}</text>
          <van-icon name="arrow" size="16px" color="#999" />
        </view>
      </view>
      
      <!-- 手机号区域 -->
      <!-- <view class="info-item">
        <view class="info-label">手机号</view>
        <view class="info-value" bindtap="editPhone">
          <text>{{userInfo.phone}}</text>
          <van-icon name="arrow" size="16px" color="#999" />
        </view>
      </view> -->
    </view>
  </view>
</view>