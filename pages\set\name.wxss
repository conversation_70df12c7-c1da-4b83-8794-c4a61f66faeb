/* pages/set/name.wxss */
page {  
  width: 100%;  
  box-sizing: border-box;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
}

.container {  
  padding: 0;  
  background-color: transparent;  
  width: 100%;  
  box-sizing: border-box;  
  min-height: 100vh;
  position: relative;
}

/* ==================== 顶部导航区域样式 ==================== */

/* 固定头部样式 */
.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
}

/* 顶部状态栏 */
.status-bar {
  width: 100%;
  background: transparent;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  width: 100%;
  background: transparent;
  position: relative;
  padding: 0 20rpx;
  box-sizing: border-box;
  border-bottom: none;
}

.nav-back {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  z-index: 10;
}

.nav-title {
  position: absolute;
  left: 0;
  right: 0;
  height: 44px;
  line-height: 44px;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin: 0 auto;
  width: 60%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  z-index: 5;
}

.nav-placeholder {
  width: 44px;
  height: 44px;
  visibility: hidden;
  flex-shrink: 0;
}

/* 主内容区域 */
.main-content {
  width: 100%;
  padding: 30rpx;
  box-sizing: border-box;
  position: relative;
}

/* 昵称输入框样式 */
.name-input-box {
  width: 100%;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12rpx;
  padding: 12rpx 20rpx;
  margin-bottom: 40rpx;
  box-sizing: border-box;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.name-input-box input {
  width: 100%;
  height: 60rpx;
  font-size: 28rpx;
  color: #333;
  padding-right: 50rpx;
  box-sizing: border-box;
}

.clear-icon {
  position: absolute;
  right: 10rpx;
  z-index: 10;
  padding: 8rpx;
}

/* 保存按钮样式 */
.save-btn {
  width: 100%;
  height: 88rpx;
  background: #4080FF;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  margin-top: 60rpx;
}