/* 页面容器 */
.feedback-container {
  min-height: 100vh;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  padding-bottom: 30rpx;
}

/* 自定义导航栏样式 */
.custom-nav {
  width: 100%;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}

.status-bar {
  width: 100%;
}

.nav-content {
  height: 44px;
  display: flex;
  align-items: center;
  position: relative;
}

.back-icon {
  position: absolute;
  left: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx;
}

.back-icon image {
  width: 40rpx;
  height: 40rpx;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

/* 表单内容 */
.feedback-form {
  margin-top: calc(44px + 40rpx + var(--status-bar-height, 20px));
  padding: 20rpx 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

.required::after {
  content: ' *';
  color: #e74c3c;
}

/* 问题类型选项 - 优化布局 */
.type-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.type-option {
  width: calc(33.33% - 12rpx);
  min-height: 72rpx;
  background-color: #fff;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #3D3D3D;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  padding: 8rpx 4rpx;
  text-align: center;
  box-sizing: border-box;
  word-break: break-word;
  overflow: visible;
}

.type-option.selected {
  background-color: #4080ff;
  color: #fff;
}

/* 文本域样式 */
.textarea-container {
  position: relative;
  width: 100%;
  line-height: 40rpx;
}
.feedback-placeholder{
  color:#C1C1C1;
  font-size:20rpx;
  line-height: 40rpx;
}

.feedback-textarea {
  width: 100%;
  height: 240rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.word-count {
  position: absolute;
  right: 20rpx;
  bottom: 10rpx;
  font-size: 24rpx;
  color: #999;
}

/* 图片上传 */
.upload-hint {
  font-size: 24rpx;
  color: #999;
  font-weight: normal;
}

.upload-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.upload-item {
  width: 160rpx;
  height: 160rpx;
  position: relative;
  border-radius: 8rpx;
  overflow: hidden;
}

.upload-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.delete-icon {
  position: absolute;
  top: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  z-index: 2;
}

.upload-btn {
  width: 160rpx;
  height: 160rpx;
  background-color: #fff;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.upload-icon {
  width: 60rpx;
  height: 60rpx;
  opacity: 0.5;
}

/* 联系方式 */
.contact-input {
  width: 100%;
  height: 88rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  color: #333;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.contact-hint {
  font-size: 24rpx;
  color: #999;
  margin-top: 16rpx;
}

/* 提交按钮 */
.submit-btn-container {
  padding: 20rpx 30rpx;
  margin-top: 30rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background-color: #4080ff;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(64, 128, 255, 0.3);
}

/* 媒体查询适配小屏设备 */
@media screen and (max-width: 375px) {
  .type-options {
    gap: 12rpx;
  }
  
  .type-option {
    width: calc(33.33% - 8rpx);
    font-size: 22rpx;
    min-height: 68rpx;
  }
  
  .form-item {
    margin-bottom: 24rpx;
  }
  
  .feedback-form {
    padding: 20rpx 24rpx;
  }
  
  .submit-btn-container {
    padding: 16rpx 24rpx;
    margin-top: 24rpx;
  }
}
