// pages/login/forgot_password.js
const api = require('../../utils/api').default;
const util = require('../../utils/util');
const langData = require('../../utils/lang-function.js');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: 0,
    phone: '',
    verifyCode: '',
    password: '',
    confirmPassword: '',
    showPassword: false,
    isAgree: false,
    loading: false,
    codeBtnText: langData.t('get_phone_code'),
    isCountDown: false,
    errorInfo: {
      show: false,
      type: 'info',
      icon: 'info-o',
      msg: ''
    },
    text:{}
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.updateText();
    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });
  },
  //语言刷新
  refreshLanguage(newLang){
    console.log('页面语言已切换到:', newLang);
  },
  // 更新页面文本
  updateText() {
    this.setData({
      text:{
        i_agree_to_abide_by:langData.t('i_agree_to_abide_by'),
        user_service_agreement:langData.t('user_service_agreement'),
        privacy_policy:langData.t('privacy_policy'),
        enter_phone_number:langData.t('enter_phone_number'),
        pass_chars:langData.t('pass_chars'),
        please_enter_the_verification_code:langData.t('please_enter_the_verification_code'),
        confirm_new_password:langData.t('confirm_new_password'),
        reset_password:langData.t('reset_password')
      }
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 返回上一页
   */
  navigateBack() {
    wx.navigateBack();
  },

  /**
   * 切换密码可见性
   */
  togglePasswordVisibility() {
    this.setData({
      showPassword: !this.data.showPassword
    });
  },

  /**
   * 切换协议同意状态
   */
  toggleAgree() {
    this.setData({
      isAgree: !this.data.isAgree
    });
  },

  /**
   * 查看协议
   */
  viewAgreement(e) {
    const type = e.currentTarget.dataset.type;
    let url = '';

    if (type === 'service') {
      url = '/pages/agreement/service';
    } else if (type === 'privacy') {
      url = '/pages/agreement/privacy';
    }

    wx.navigateTo({
      url: url
    });
  },

  /**
   * 获取验证码
   */
  getVerifyCode() {
    // 如果正在倒计时，不处理点击
    if (this.data.isCountDown) {
      return;
    }

    // 隐藏之前的错误信息
    this.setData({
      errorInfo: {
        show: false,
        type: 'info',
        icon: 'info-o',
        msg: ''
      }
    });

    const phone = this.data.phone;
    if (!phone) {
      this.setData({
        errorInfo: {
          show: true,
          type: 'warning',
          icon: 'warning-o',
          msg: langData.t('enter_phone_number')
        }
      });
      return;
    }

    // 验证手机号格式
    if (!/^1[3-9]\d{9}$/.test(phone)) {
      this.setData({
        errorInfo: {
          show: true,
          type: 'warning',
          icon: 'warning-o',
          msg: langData.t('enter_phone_number')
        }
      });
      return;
    }

    // 调用发送验证码API
    const params = {
      mobile: phone,
      event: 'changepwd',
      type: 'admin'
    };

    wx.showLoading({
      title: '发送中...',
    });

    api.user.codeLogin(params)
      .then(res => {
        wx.hideLoading();

        // 处理不同的响应状态
        if (res.code === 0) {
          // 成功发送验证码，开始倒计时
          this.setData({
            isCountDown: true,
            errorInfo: {
              show: true,
              type: 'success',
              icon: 'checked',
              msg: langData.t('code_sent')
            }
          });

          let countdown = 60;
          this.setData({
            codeBtnText: `${countdown}`+langData.t('s_to_retry')
          });

          const timer = setInterval(() => {
            countdown--;

            if (countdown <= 0) {
              clearInterval(timer);
              this.setData({
                isCountDown: false,
                codeBtnText: langData.t('get_phone_code')
              });
            } else {
              this.setData({
                codeBtnText: `${countdown}`+langData.t('s_to_retry')
              });
            }
          }, 1000);
        } else {
          // 处理错误信息
          this.setData({
            errorInfo: {
              show: true,
              type: 'error',
              icon: 'close',
              msg: res.msg || '发送验证码失败，请稍后重试'
            }
          });
        }
      })
      .catch(err => {
        wx.hideLoading();
        this.setData({
          errorInfo: {
            show: true,
            type: 'error',
            icon: 'close',
            msg: '网络错误，请稍后重试'
          }
        });
        console.error('发送验证码失败:', err);
      });
  },

  /**
   * 监听确认密码输入，实时验证两次密码是否一致
   */
  onConfirmPasswordInput(e) {
    const confirmValue = e.detail.value;
    const password = this.data.password;

    // 只有当确认密码有值时才进行验证
    if (confirmValue) {
      if (password !== confirmValue) {
        // 密码不一致，显示错误提示
        this.setData({
          errorInfo: {
            show: true,
            type: 'warning',
            icon: 'warning-o',
            msg: langData.t('passwords_dont_match')
          }
        });
      } else {
        // 密码一致，隐藏错误提示
        this.setData({
          errorInfo: {
            show: false,
            type: 'info',
            icon: 'info-o',
            msg: ''
          }
        });
      }
    } else {
      // 清空确认密码时，隐藏错误提示
      this.setData({
        errorInfo: {
          show: false,
          type: 'info',
          icon: 'info-o',
          msg: ''
        }
      });
    }
  },

  /**
   * 处理重置密码
   */
  handleResetPassword() {
    // 重置错误信息
    this.setData({
      errorInfo: {
        show: false,
        type: 'info',
        icon: 'info-o',
        msg: ''
      }
    });

    // 验证表单数据
    const { phone, verifyCode, password, confirmPassword, isAgree } = this.data;

    // 验证手机号
    if (!phone) {
      this.setData({
        errorInfo: {
          show: true,
          type: 'warning',
          icon: 'warning-o',
          msg: langData.t('enter_phone_number')
        }
      });
      return;
    }

    // 验证手机号格式
    if (!/^1[3-9]\d{9}$/.test(phone)) {
      this.setData({
        errorInfo: {
          show: true,
          type: 'warning',
          icon: 'warning-o',
          msg:  langData.t('enter_phone_number')
        }
      });
      return;
    }

    // 验证验证码
    if (!verifyCode) {
      this.setData({
        errorInfo: {
          show: true,
          type: 'warning',
          icon: 'warning-o',
          msg: langData.t('please_enter_the_verification_code')
        }
      });
      return;
    }

    // 验证密码
    if (!password) {
      this.setData({
        errorInfo: {
          show: true,
          type: 'warning',
          icon: 'warning-o',
          msg: langData.t('confirm_new_password')
        }
      });
      return;
    }

    // 验证密码长度
    if (password.length < 8 || password.length > 16) {
      this.setData({
        errorInfo: {
          show: true,
          type: 'warning',
          icon: 'warning-o',
          msg: langData.t('pass_chars')
        }
      });
      return;
    }

    // 验证确认密码
    if (!confirmPassword) {
      this.setData({
        errorInfo: {
          show: true,
          type: 'warning',
          icon: 'warning-o',
          msg: langData.t('confirm_new_password')
        }
      });
      return;
    }

    // 验证两次密码是否一致
    if (password !== confirmPassword) {
      this.setData({
        errorInfo: {
          show: true,
          type: 'warning',
          icon: 'warning-o',
          msg: langData.t('passwords_dont_match')
        }
      });
      return;
    }

    // 验证是否同意协议
    if (!isAgree) {
      this.setData({
        errorInfo: {
          show: true,
          type: 'warning',
          icon: 'warning-o',
          msg: '请阅读并同意用户协议'
        }
      });
      return;
    }

    // 显示加载状态
    this.setData({ loading: true });

    // 调用修改密码API
    const params = {
      mobile: phone,
      code: verifyCode,
      password: password
    };

    api.user.updatePassword(params)
      .then(res => {
        this.setData({ loading: false });

        // 添加调试输出，查看响应数据
        console.log('修改密码接口返回数据:', res);

        // 检查返回的数据是否包含用户信息
        if (res && (res.phoneNumber || res.openid)) {
          // 密码修改成功，调用统一的登录数据处理方法
          const userInfo = util.storeLoginFlags(res);

          // 显示成功提示
          wx.showToast({
            title: langData.t('password_updated'),
            icon: 'success',
            duration: 1500
          });

          // 延迟跳转
          setTimeout(() => {
            // 根据是否有用户详细信息决定跳转路径
            if (!res.info) {
              wx.navigateTo({
                url: '/pages/info/index?phone=' + userInfo.phone
              });
            } else {
              wx.switchTab({
                url: '/pages/my/index'  // 确保与账号密码登录使用相同的路径
              });
            }
          }, 1500);
        } else {
          // 处理错误信息
          this.setData({
            errorInfo: {
              show: true,
              type: 'error',
              icon: 'close',
              msg: res.msg || '密码修改失败，请稍后重试'
            }
          });
        }
      })
      .catch(err => {
        this.setData({ loading: false });
        this.setData({
          errorInfo: {
            show: true,
            type: 'error',
            icon: 'close',
            msg: '网络错误，请稍后重试'
          }
        });
        console.error('重置密码失败:', err);
      });
  }
})