// pages/set/revise.js
import util from '../../utils/util';
import api from '../../utils/api';
import cosUpload from '../../utils/cos-upload';

Page({

  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: 20, // 默认状态栏高度，会在onLoad中动态获取
    userInfo: {
      logo: '',
      short_name: '',
      phone: ''
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });

    // 隐藏原生导航栏
    wx.hideNavigationBarLoading();
    wx.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: 'transparent',
      animation: {
        duration: 0,
        timingFunc: 'easeIn'
      }
    });

    // 从缓存中获取用户信息
    this.getUserInfoFromCache();
  },

  /**
   * 从缓存中获取用户信息
   */
  getUserInfoFromCache() {
    const userInfo = util.getCacheWithExpiry('userInfo');
    if (userInfo) {
      this.setData({
        userInfo: {
          logo: userInfo.logo || '/icons/default_avatar.png',
          short_name: userInfo.short_name || '未设置',
          phone: userInfo.phone || '未绑定'
        }
      });
    }
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 使用微信开放能力选择头像
   */
  onChooseAvatar(e) {
    const { avatarUrl } = e.detail;
    if (avatarUrl) {
      // 显示上传中提示
      wx.showLoading({
        title: '上传中...',
        mask: true
      });

      // 将微信临时链接转为本地临时文件
      wx.getImageInfo({
        src: avatarUrl,
        success: (res) => {
          // 上传图片到腾讯云COS
          cosUpload.uploadFile(res.path, 'my')
            .then(result => {
              // 上传成功，保存图片URL
              const userInfo = util.getCacheWithExpiry('userInfo');
              userInfo.logo = result.url;

              // 更新本地存储的用户信息
              util.setCacheWithExpiry('userInfo', userInfo);  // 默认7天过期

              // 保存图片到服务器
              api.user.updateUserLogo({
                avatar: "/" + result.key,
                app_id: userInfo.app_id
              }).then(result => {
                console.log('头像更新成功', result);
              }).catch(err => {
                console.error('头像更新失败:', err);
              });

              // 更新页面数据
              this.setData({
                'userInfo.logo': result.url
              });

              wx.hideLoading();
              wx.showToast({
                title: '上传成功',
                icon: 'success'
              });
            })
            .catch(error => {
              console.error('上传失败:', error);
              wx.hideLoading();
              wx.showToast({
                title: '上传失败',
                icon: 'none'
              });
            });
        },
        fail: (error) => {
          console.error('获取图片信息失败:', error);
          wx.hideLoading();
          wx.showToast({
            title: '获取图片信息失败',
            icon: 'none'
          });
        }
      });
    }
  },

  /**
   * 编辑昵称
   */
  editNickname() {
    wx.navigateTo({
      url: '/pages/set/name'
    });
  },

  /**
   * 编辑手机号
   */
  editPhone() {
    wx.navigateTo({
      url: '/pages/set/phonein'
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时刷新用户信息，以便在昵称修改后返回显示新昵称
    this.getUserInfoFromCache();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})