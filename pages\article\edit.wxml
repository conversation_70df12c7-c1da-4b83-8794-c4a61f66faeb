<!--pages/moments/release.wxml-->
<view class="release-container">
  <view class="form-container" bindtap="onPageClick">
    <view class="form-item">
      <text class="label required">品牌</text>
      <view class="custom-picker" catchtap="stopPropagation">
        <view class="picker-value" bindtap="showBrandPicker">
          <text>{{brandSelected || '请选择品牌'}}</text>
          <text class="arrow">▼</text>
        </view>
        <view class="picker-dropdown" wx:if="{{showBrandPicker}}">
          <view class="picker-header">
            <text class="picker-title">选择品牌</text>
            <text class="picker-cancel" catchtap="cancelSelection">取消</text>
          </view>
          <scroll-view scroll-y style="max-height: 400rpx;">
            <view wx:if="{{brands.length === 0}}" class="empty-tip">暂无品牌数据</view>
            <view wx:else class="brands-count-debug">共 {{brands.length}} 个品牌</view>
            <view 
              class="picker-option {{brandSelected === item.brand_name ? 'active' : ''}}" 
              wx:for="{{brands}}" 
              wx:key="id" 
              data-id="{{item.id}}"
              data-value="{{item.brand_name}}" 
              catchtap="selectBrand">
              {{item.brand_name}}
            </view>
          </scroll-view>
        </view>
      </view>
    </view>

    <view class="form-item">
      <text class="label required">车系</text>
      <view class="custom-picker" catchtap="stopPropagation">
        <view class="picker-value" bindtap="showSeriesPicker">
          <text>{{seriesSelected || '请选择车系'}}</text>
          <text class="arrow">▼</text>
        </view>
        <view class="picker-dropdown" wx:if="{{showSeriesPicker}}">
          <view class="picker-header">
            <text class="picker-title">选择车系</text>
            <text class="picker-cancel" catchtap="cancelSelection">取消</text>
          </view>
          <scroll-view scroll-y style="max-height: 400rpx;">
            <view 
              class="picker-option {{seriesSelected === item.series_name ? 'active' : ''}}" 
              wx:for="{{series}}" 
              wx:key="id" 
              data-id="{{item.series_id}}"
              data-value="{{item.series_name}}" 
              catchtap="selectSeries">
              {{item.series_name}}
            </view>
          </scroll-view>
        </view>
      </view>
    </view>

    <view class="form-item">
      <text class="label required">车型</text>
      <view class="custom-picker" catchtap="stopPropagation">
        <view class="picker-value" bindtap="showModelPicker">
          <text>{{modelSelected || '请选择车型'}}</text>
          <text class="arrow">▼</text>
        </view>
        <view class="picker-dropdown" wx:if="{{showModelPicker}}">
          <view class="picker-header">
            <text class="picker-title">选择车型</text>
            <text class="picker-cancel" catchtap="cancelSelection">取消</text>
          </view>
          <scroll-view scroll-y style="max-height: 400rpx;">
            <view 
              class="picker-option {{modelSelected === (item.ui_vehicle_name + (item.official_price ? ' ¥' + item.official_price + '万元' : '')) ? 'active' : ''}}" 
              wx:for="{{models}}" 
              wx:key="id" 
              data-id="{{item.id}}"
              data-value="{{item.ui_vehicle_name + (item.official_price ? ' ¥' + item.official_price + '万元' : '' )}}" 
              catchtap="selectModel">
              {{item.ui_vehicle_name}}{{item.official_price ? ' ¥' + item.official_price + '万元' : ''}}
            </view>
          </scroll-view>
        </view>
      </view>
    </view>

    <view class="form-item">
      <text class="label required">截止日期</text>
      <picker mode="date" bindchange="bindDateChange" value="{{deadline}}">
        <view class="picker-value">
          <text>{{deadline || '选择日期'}}</text>
          <text class="arrow">▼</text>
        </view>
      </picker>
    </view>

    <view class="form-item">
      <text class="label">新旧类型</text>
      <view class="radio-group">
        <view class="radio-item">
          <radio checked="{{isNew}}" bindtap="toggleIsNew" />
          <text>新车</text>
        </view>
        <view class="radio-item">
          <radio checked="{{!isNew}}" bindtap="toggleIsNew" />
          <text>二手车</text>
        </view>
      </view>
    </view>

    <view class="form-item">
      <text class="label">过户次数</text>
      <input type="number" placeholder="输入过户次数" value="{{transferCount}}" bindinput="inputTransferCount" />
    </view>

    <view class="form-item">
      <text class="label required">目标价格</text>
      <input type="digit" placeholder="输入目标价格" value="{{targetPrice}}" bindinput="inputTargetPrice" />
    </view>

    <view class="form-item">
      <text class="label required">采购数量</text>
      <input type="number" placeholder="输入采购数量" value="{{purchaseCount}}" bindinput="inputPurchaseCount" />
    </view>

    <view class="form-item">
      <text class="label">是否带保险</text>
      <view class="radio-group">
        <view class="radio-item">
          <radio checked="{{hasInsurance}}" bindtap="toggleHasInsurance" />
          <text>是</text>
        </view>
        <view class="radio-item">
          <radio checked="{{!hasInsurance}}" bindtap="toggleHasInsurance" />
          <text>否</text>
        </view>
      </view>
    </view>

    <view class="form-item">
      <text class="label">是否带发票</text>
      <view class="radio-group">
        <view class="radio-item">
          <radio checked="{{hasInvoice}}" bindtap="toggleHasInvoice" />
          <text>是</text>
        </view>
        <view class="radio-item">
          <radio checked="{{!hasInvoice}}" bindtap="toggleHasInvoice" />
          <text>否</text>
        </view>
      </view>
    </view>

    <view class="form-item">
      <text class="label required">交付地点</text>
      <view class="custom-picker" catchtap="stopPropagation">
        <view class="picker-value" bindtap="showProvincePicker">
          <text>{{locationSelected || '请选择地点'}}</text>
          <text class="arrow">▼</text>
        </view>
        
        <!-- 省份选择下拉框 -->
        <view class="picker-dropdown" wx:if="{{showProvincePicker}}">
          <view class="picker-header">
            <text class="picker-title">选择省份</text>
            <text class="picker-cancel" catchtap="cancelSelection">取消</text>
          </view>
          <scroll-view scroll-y style="max-height: 300rpx;">
            <view 
              class="picker-option {{provinceSelected === item.name ? 'active' : ''}}" 
              wx:for="{{provinces}}" 
              wx:key="code" 
              data-value="{{item.name}}" 
              data-code="{{item.code}}"
              bindtap="selectProvince">
              {{item.name}}
            </view>
          </scroll-view>
        </view>
        
        <!-- 城市选择下拉框 -->
        <view class="picker-dropdown" wx:if="{{showCityPicker}}">
          <view class="picker-header">
            <text class="picker-title">选择{{provinceSelected}}的城市</text>
            <text class="picker-cancel" catchtap="cancelSelection">取消</text>
          </view>
          <scroll-view scroll-y style="max-height: 300rpx;">
            <view 
              class="picker-option"
              wx:for="{{cities[provinceCode]}}" 
              wx:key="code" 
              data-value="{{item.name}}" 
              data-code="{{item.code}}"
              bindtap="selectCity">
              {{item.name}}
            </view>
          </scroll-view>
        </view>
      </view>
    </view>

    <view class="form-item">
      <text class="label">配置说明</text>
      <textarea placeholder="输入配置、车况及售后服务说明" value="{{description}}" bindinput="inputDescription" maxlength="500" />
      <view class="word-count">{{descriptionLength || 0}}/500</view>
    </view>

    <view class="form-item">
      <text class="label required">采购证明</text>
      <view class="upload-container">
        <view class="upload-btn" bindtap="chooseImage" wx:if="{{!proofImages.length}}">
          <text class="plus">+</text>
        </view>
        <view class="image-preview" wx:if="{{proofImages.length > 0}}">
          <image src="{{proofImages[0]}}" mode="aspectFill" binderror="imageError" show-menu-by-longpress></image>
          <view class="reupload" catchtap="chooseImage">重新上传</view>
        </view>
      </view>
    </view>
  </view>

  <view class="bottom-actions">
    <view class="save-draft" bindtap="saveDraft" wx:if="{{!fromDraft}}">存草稿</view>
    <view class="publish" bindtap="publish">{{(mode === 'edit' && !fromDraft) ? '更新' : '发布'}}</view>
  </view>
</view>