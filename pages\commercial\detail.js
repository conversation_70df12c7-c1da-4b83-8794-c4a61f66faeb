import api from '../../utils/api';  // 导入API模块
import config from '../../config';  // 导入配置模块
import util from '../../utils/util';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    commercialInfo: {}, // 商业详情数据
    current: 0, // 当前轮播图索引
    isCollected: false, // 是否收藏
    statusBarHeight: wx.getSystemInfoSync().statusBarHeight, // 状态栏高度
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if (options.id) {
      this.getCommercialDetail(options.id);
      // 检查是否已收藏
      this.checkIsFavorite(options.id);
    }
  },

  /**
   * 获取商业详情数据
   */
  getCommercialDetail: function (id) {
    wx.showLoading({
      title: '加载中...',
    });

    // 构建查询参数
    const queryParams = {
      id,
      type: 'commercial'  // 设置类型为商用车
    };

    // 调用API获取详情数据
    api.car.getDetail(queryParams).then(result => {
      // 如果result本身就包含了车辆数据而不是嵌套在car属性中
      const carData = result.car || result.data || result;

      if (carData) {
        // 处理图片URLs
        let imageUrls = [];

        // 先尝试从image_urls获取图片
        if (carData.image_urls) {
          try {
            // 检查image_urls是否已经是数组
            if (Array.isArray(carData.image_urls)) {
              imageUrls = carData.image_urls;
            }
            // 尝试将字符串转为数组
            else if (typeof carData.image_urls === 'string') {
              try {
                // 尝试作为JSON解析
                imageUrls = JSON.parse(carData.image_urls);
              } catch (jsonError) {
                // 可能是逗号分隔的字符串
                imageUrls = carData.image_urls.split(',');
              }
            }
          } catch (error) {
            console.error('处理图片URL失败:', error);
          }
        }

        // 始终添加main_url到图片列表中（如果存在）
        if (carData.main_url) {
          // 检查main_url是否已经在列表中，如果不在则添加
          const mainUrl = carData.main_url;
          if (!imageUrls.some(url => url === mainUrl || url.endsWith(mainUrl))) {
            imageUrls.unshift(mainUrl); // 将主图添加到数组开头
          }
        }

        // 处理每个图片URL，确保它们是完整的URL
        imageUrls = imageUrls.map(url => {
          return url.startsWith('http') ? url :
            url.includes('uploads/') ?
              config.COS_CONFIG.url + url :
              config.COS_CONFIG.url + config.COS_CONFIG.path + url;
        });

        // 如果没有任何图片，使用默认图片
        if (imageUrls.length === 0) {
          imageUrls = ['https://p9-dcd.byteimg.com/motor-mis-img/9bf27d1eccd74d35d7efc4917dbaf13e~tplv-f042mdwyw7-original:960:0.image'];
        }

        // 构建商业详情数据
        const commercialInfo = {
          id: carData.id,
          title: carData.car_title,
          brand: carData.brand_name || '',
          location: carData.vehicle_source_location || '',
          date: carData.create_time ? carData.create_time.substring(0, 10) : '',
          salePrice: carData.sell_price || '',
          newPrice: carData.official_price || '',
          images: imageUrls,

          // 商用车属性
          type: carData.type_text || '',
          subtype: carData.subtype_text || '',
          color: carData.color || '',
          dimensions: carData.dimensions || '',
          length: carData.length || '',

          // 发动机/动力相关
          displacement: carData.displacement_text || carData.displacement || '',
          energy_type: carData.energy_type_text || '',
          tractive_force: carData.tractive_force_text || '',
          maximum_speed: carData.maximum_speed_without_load || '',
          full_load_maximum_speed: carData.full_load_maximum_speed || '',

          // 变速箱相关
          drive_mode: carData.drive_mode_text || '',
          shift_mode: carData.shift_mode_text || '',
          number_of_gears: carData.number_of_gears_text || carData.number_of_gears || '',

          // 乘客相关
          seats: carData.seats || '',
          number_of_approved_passengers: carData.number_of_approved_passengers_text || carData.number_of_approved_passengers || '',
          number_of_seating_rows: carData.number_of_seating_rows_text || carData.number_of_seating_rows || '',

          // 其他规格
          emission_standard: carData.emission_standard_text || '',
          load_capacity: carData.load_capacity_text || carData.load_capacity || '',
          traction_force: carData.traction_force || '',

          // 详细描述
          description: util.parseHtml(carData.describe) || ''
        };

        this.setData({
          commercialInfo: commercialInfo
        });
      } else {
        wx.showToast({
          title: '获取详情失败',
          icon: 'none'
        });
      }
    }).catch(error => {
      console.error('获取商业详情失败:', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    }).finally(() => {
      wx.hideLoading();
    });
  },

  /**
   * 检查是否已收藏
   */
  checkIsFavorite: function (id) {
    // 实际应用中应该从本地存储或服务器获取收藏状态
    const favoriteList = wx.getStorageSync('favoriteCommercials') || [];
    const isCollected = favoriteList.includes(id);
    this.setData({
      isCollected: isCollected
    });
  },

  /**
   * 轮播图切换事件
   */
  swiperChange: function (e) {
    this.setData({
      current: e.detail.current
    });
  },

  /**
   * 预览图片
   */
  previewImage: function (e) {
    const url = e.currentTarget.dataset.url;
    wx.previewImage({
      current: url,
      urls: this.data.commercialInfo.images
    });
  },

  /**
   * 切换收藏状态
   */
  toggleCollect: function () {
    const id = this.data.commercialInfo.id;
    let favoriteList = wx.getStorageSync('favoriteCommercials') || [];

    if (this.data.isCollected) {
      // 取消收藏
      favoriteList = favoriteList.filter(item => item !== id);
      wx.showToast({
        title: '已取消收藏',
        icon: 'none'
      });
    } else {
      // 添加收藏
      if (!favoriteList.includes(id)) {
        favoriteList.push(id);
      }
      wx.showToast({
        title: '收藏成功',
        icon: 'success'
      });
    }

    wx.setStorageSync('favoriteCommercials', favoriteList);
    this.setData({
      isCollected: !this.data.isCollected
    });
  },

  /**
   * 分享商铺
   */
  shareCommercial: function () {
    // 微信小程序分享功能通过onShareAppMessage实现
  },

  /**
   * 联系商家
   */
  contactSeller: function () {
    // 实际应用中应该有商家的联系方式
    wx.makePhoneCall({
      phoneNumber: '10086', // 示例电话
      fail: function () {
        wx.showToast({
          title: '拨打电话失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 查看位置
   */
  viewLocation: function () {
    // 实际应用中应有商铺的经纬度信息
    wx.showToast({
      title: '即将查看商铺位置',
      icon: 'none'
    });

    // 如果有经纬度信息，可以使用以下代码
    // wx.openLocation({
    //   latitude: 23.099994,
    //   longitude: 113.324520,
    //   name: this.data.commercialInfo.title,
    //   address: this.data.commercialInfo.address,
    //   scale: 18
    // });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: this.data.commercialInfo.title,
      path: '/pages/commercial/detail?id=' + this.data.commercialInfo.id
    };
  },

  /**
   * 自定义导航栏返回按钮事件
   */
  onBackTap: function () {
    wx.navigateBack({
      delta: 1
    });
  },
});
