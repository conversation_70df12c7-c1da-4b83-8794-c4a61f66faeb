.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #DCECFF 75%, #F1F4F9 100%);
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  display: flex;
  flex-direction: column;
}

/* 自定义导航栏样式 */
.custom-nav {
  width: 100%;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #DCECFF 75%, #F1F4F9 100%);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}

.status-bar {
  width: 100%;
}

.nav-content {
  height: 44px;
  display: flex;
  align-items: center;
  position: relative;
}

.back-icon {
  position: absolute;
  left: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx;
}

.back-icon image {
  width: 40rpx;
  height: 40rpx;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

/* 头像上传区域 */
.avatar-upload {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 210rpx;
}

.avatar-button {
  background: transparent;
  border: none;
  padding: 0;
  margin: 0;
  line-height: normal;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.avatar-button::after {
  border: none;
}

.avatar-image {
  width: 128rpx;
  height: 128rpx;
  border-radius: 50%;
  border: 2rpx solid #fff;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.avatar-text {
  font-size: 28rpx;
  color: #666;
  margin-top: 20rpx;
}

/* 表单区域 */
.form-section {
  width: 700rpx;
  padding: 30rpx;
  margin-top: -40rpx;
  /* margin: 40rpx 0rpx 40rpx; */
}

.form-item {
  margin-bottom: 40rpx;
  position: relative;
}

.form-label {
  display: block;
  font-size: 24rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.form-label.required::after {
  content: '*';
  color: #f00;
  margin-left: 5rpx;
}

/* 输入框样式 */
.input-wrapper {
  position: relative;
  background-color: #fff;
  border-radius: 12rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  font-size: 22rpx;
}

/* 下拉框样式 */
.dropdown-wrapper {
  position: relative;
  background-color: #fff;
  border-radius: 12rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  font-size: 22rpx;
  border: 2rpx solid #eee;
  color: #999;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.dropdown-wrapper:active {
  transform: scale(0.98);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
}

.dropdown-text {
  flex: 1;
  height: 100%;
  font-size: 22rpx;
  color: #333;
  display: flex;
  align-items: center;
}

.dropdown-placeholder {
  color: #999;
}

.dropdown-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dropdown-icon image {
  width: 24rpx;
  height: 24rpx;
}

/* 用户类型选择 */
.user-type-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.user-type-item {
  background-color: #fff;
  border-radius: 12rpx;
  height: 40rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.user-type-item .type-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 10rpx;
  transition: transform 0.2s ease;
}

.user-type-item text {
  font-size: 24rpx;
  color: #333;
  transition: color 0.3s ease;
}

.user-type-item.active {
  background-color: #e6f7ff;
  border: 2rpx solid #4080ff;
  box-shadow: 0 4rpx 12rpx rgba(64, 128, 255, 0.2);
}

.user-type-item.active .type-icon {
  transform: scale(1.1);
}

.user-type-item.active text {
  color: #4080ff;
  font-weight: 600;
}

/* 车型类型选择 */
.car-type-grid {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  gap: 20rpx;
  width: 100%;
  height: auto;
  min-height: 80rpx;
}

/* 新增车辆类型容器样式 */
.car-type-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  width: 100%;
  height: auto;
  min-height: 80rpx;
  overflow: visible;
  padding: 8rpx 5rpx;
  justify-content: space-between;
}

.car-type-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx 30rpx;
  font-size: 22rpx;
  color: #333;
  text-align: center;
  width: 22%;
  min-width: 76rpx;
  flex: 0 0 auto;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: visible;
  margin-bottom: 10rpx;
  box-sizing: border-box;
}

.car-type-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.car-type-item.active {
  background-color: #e6f7ff;
  border: 2rpx solid #4080ff;
  color: #4080ff;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(64, 128, 255, 0.2);
  transform: none;
}

.car-type-item.active::before {
  left: 100%;
}

/* 底部区域 - 不再固定 */
.bottom-area {
  width: 450rpx;
  padding: 30rpx;
  margin: 0 30rpx 30rpx;
}

/* 协议同意 */
.agreement-container {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.checkbox {
  transform: scale(0.8);
  margin-right: 10rpx;
}

.agreement-text {
  font-size: 24rpx;
  color: #999;
}

.link {
  color: #4080ff;
}

/* 提交按钮 */
.submit-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #4080ff;
  color: #fff;
  font-size: 32rpx;
  border-radius: 16rpx;
  border: none;
  font-weight: normal;
  letter-spacing: 2rpx;
  text-align: center;
}

.submit-btn::after {
  border: none;
}

/* 下拉菜单 */
.dropdown-menu {
  position: absolute;
  width: 100%;
  max-height: 400rpx;
  overflow-y: auto;
  background-color: #fff;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  z-index: 10;
}

/* 向下展示的下拉菜单 */
.dropdown-menu {
  top: 100%;
  margin-top: 10rpx;
  max-height: 350rpx;
}

/* 向上展示的下拉菜单 - 特别是针对汽配商的主营配件类目 */
.form-item .dropdown-menu.popup-top,
.dynamic-form-container .form-item:nth-of-type(3) .dropdown-menu.popup-top {
  bottom: 100%;
  top: auto;
  margin-top: 0;
  margin-bottom: 60rpx;
  max-height: 350rpx;
}

.dropdown-item {
  padding: 20rpx 30rpx;
  font-size: 22rpx;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dropdown-item.active {
  background-color: #e6f7ff;
  color: #4080ff;
}

.dropdown-item-check {
  color: #4080ff;
  font-weight: bold;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:active,
.dropdown-item:hover {
  background-color: #f5f7fa;
}

.dropdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 9;
}

.dropdown-actions {
  padding: 20rpx;
  display: flex;
  justify-content: center;
  border-top: 1rpx solid #f0f0f0;
}

.dropdown-confirm {
  background-color: #4080ff;
  color: white;
  font-size: 24rpx;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  min-width: 120rpx;
  text-align: center;
  line-height: 1.5;
}

.dropdown-confirm::after {
  border: none;
}

.dynamic-form-container {
  min-height: 560rpx;
  /* 增加固定高度，确保足够容纳所有可能的表单项 */
  position: relative;
  width: 100%;
}

.hidden {
  display: none;
  /* 改为display:none而不是visibility:hidden，避免占用空间 */
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  pointer-events: none;
}

/* Remove the empty placeholder since we're using a different approach */
.empty-placeholder {
  display: none;
}

/* 配件类目多级联动下拉框样式 */
.parts-dropdown-menu {
  position: absolute;
  width: 100%;
  max-height: 600rpx;
  background-color: #fff;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  z-index: 10;
  margin-top: -260rpx;
  /* overflow: hidden; */
}

.parts-nav {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #f8f9fa;
  border-bottom: 1rpx solid #e9ecef;
  font-size: 24rpx;
}

.parts-nav-item {
  color: #666;
  font-weight: 500;
}

.parts-nav-item.active {
  color: #4080ff;
  font-weight: 600;
}

.parts-nav-arrow {
  margin: 0 15rpx;
  color: #999;
  font-size: 20rpx;
}

.parts-back-btn {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #f8f9fa;
  border-bottom: 1rpx solid #e9ecef;
  font-size: 24rpx;
  color: #4080ff;
}

.parts-back-btn image,
.region-back-button image {
  width: 24rpx;
  height: 24rpx;
  margin-right: 10rpx;
  flex-shrink: 0;
  vertical-align: middle;
}

.parts-level-container {
  max-height: 400rpx;
  overflow-y: auto;
}

.parts-item {
  padding: 25rpx 30rpx;
  font-size: 26rpx;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.parts-item:last-child {
  border-bottom: none;
}

.parts-item:active,
.parts-item:hover {
  background-color: #f5f7fa;
}

.parts-item.selected {
  background-color: #e6f7ff;
  color: #4080ff;
}

.parts-arrow {
  color: #999;
  font-size: 24rpx;
  font-weight: bold;
}

.parts-check {
  color: #4080ff;
  font-weight: bold;
  font-size: 28rpx;
}

.parts-confirm-container {
  padding: 20rpx 30rpx;
  background-color: #f8f9fa;
  border-top: 1rpx solid #e9ecef;
}

.parts-confirm-btn {
  width: 100%;
  height: 70rpx;
  line-height: 70rpx;
  background-color: #4080ff;
  color: #fff;
  font-size: 28rpx;
  border-radius: 8rpx;
  border: none;
  text-align: center;
}

.parts-confirm-btn::after {
  border: none;
}

/* 国家地区下拉框样式 */
.country-region-dropdown {
  position: absolute;
  width: 100%;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  z-index: 100;
  margin-top: -300rpx;
  overflow: hidden;
}

.country-region-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  text-align: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #e0e0e0;
  background: #fff;
}

.country-list,
.region-list {
  background-color: #fff;
  max-height: 600rpx;
  overflow-y: auto;
}

.country-item,
.region-item {
  padding: 30rpx;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #fff;
}

.country-item:active,
.region-item:active {
  background-color: #f9f9f9;
}

.region-back-button {
  padding: 20rpx 30rpx;
  font-size: 26rpx;
  color: #4080ff;
  background-color: #f8f9fa;
  border-bottom: 1rpx solid #e0e0e0;
  display: flex;
  align-items: center;
  height: 58rpx;
  line-height: 58rpx;
}

.no-regions {
  padding: 40rpx 0;
  text-align: center;
  color: #999;
  font-size: 26rpx;
}

/* 修改下拉菜单的样式，保持一致性 */
.dropdown-menu,
.parts-dropdown-menu {
  border-radius: 12rpx;
}

/* 加载中提示样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(64, 128, 255, 0.2);
  border-radius: 50%;
  border-top: 4rpx solid #4080ff;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 26rpx;
  color: #666;
}

.empty-message {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 26rpx;
}

/* 新的车型选择器样式 */
.car-type-new-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  width: 100%;
  margin-top: 10rpx;
}

.car-type-new-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx 10rpx;
  font-size: 24rpx;
  color: #333;
  text-align: center;
  border: 2rpx solid #eee;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.car-type-new-item.active {
  background-color: #e6f7ff;
  border: 2rpx solid #4080ff;
  color: #4080ff;
  font-weight: 600;
}