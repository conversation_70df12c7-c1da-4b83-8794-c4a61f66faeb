<!--pages/info/certification_fail.wxml-->
<view class="container" style="--status-bar-height: {{statusBarHeight}}px;">
  <!-- 自定义导航栏 -->
  <view
    class="custom-nav"
    style="padding-top: {{statusBarHeight}}px;"
  >
    <view class="nav-content">
      <view class="back-icon" bindtap="navigateBack">
        <van-icon
          name="arrow-left"
          size="20px"
          color="#333"
        />
      </view>
      <view class="nav-title">企业认证失败</view>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="main-content" style="padding-top: {{statusBarHeight + 44}}px;">
    <!-- 认证失败卡片 -->
    <view class="fail-card">
      <!-- 失败状态图标 -->
      <view class="status-section">
        <view class="status-icon">
          <text class="status-x">×</text>
        </view>
        <view class="status-title">认证审核失败</view>
        <view class="status-subtitle">很抱歉，您的企业认证审核未能通过审核</view>
      </view>

      <!-- 失败原因 -->
      <view class="reason-section">
        <view class="reason-header">
          <van-icon
            name="warning-o"
            size="16px"
            color="#ff4444"
          />
          <text class="reason-title">审核未通过原因</text>
        </view>
        <view class="reason-content">
          <view class="reason-item">
            <text class="reason-label">驳回原因：</text>
            <text class="reason-value">{{failReason || '信息有误，不正确'}}</text>
          </view>
        </view>
      </view>

      <!-- 温馨提示 -->
      <view class="tips-text">
        请根据以上原因修改信息并重新提交认证申请
      </view>
    </view>

    <!-- 操作按钮区域 -->
    <view class="action-section">
      <button class="primary-btn" bindtap="reApply">
        修改信息
      </button>
      <button class="secondary-btn" bindtap="contactService">
        联系客服
      </button>
    </view>

    <!-- 客服信息卡片 -->
    <view class="service-card">
      <view class="service-header">
        <van-icon
          name="info-o"
          size="16px"
          color="#1296db"
        />
        <text class="service-title">如果您对审核结果有疑问，可以联系客服进行咨询，客服电话：18855554544</text>
      </view>
    </view>
  </view>
</view>