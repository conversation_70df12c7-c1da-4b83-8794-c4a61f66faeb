/* pages/set/phoneget.wxss */
page {
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
  min-height: 100%;
  height: 100%;
}

.container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background: transparent;
}

/* 自定义导航栏 */
.custom-nav {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
}

.nav-content {
  position: relative;
  display: flex;
  align-items: center;
  height: 44px;
  padding: 0 16px;
}

.nav-back {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.back-icon {
  width: 12px;
  height: 20px;
  background-image: url("data:image/svg+xml,%3Csvg width='12' height='20' viewBox='0 0 12 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10 2L2 10L10 18' stroke='%23333333' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-size: contain;
}

.nav-title {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

/* 主要内容区域 */
.main-content {
  padding: 20px 16px;
}

/* 电话卡片样式 */
.phone-card {
  background: #FFFFFF;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 40px;
  padding: 20px 16px;
}

.phone-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}

/* 输入行 */
.input-row {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #F5F5F5;
}

.input-row:last-child {
  margin-bottom: 0;
}

.label {
  width: 70px;
  font-size: 15px;
  color: #333;
}

.input-value {
  flex: 1;
  font-size: 15px;
  color: #333;
}

.input-container {
  flex: 1;
  display: flex;
  align-items: center;
}

/* 验证码输入区域 */
.verify-input {
  flex: 1;
  height: 40px;
  font-size: 15px;
  color: #333;
  padding: 0;
}

.verify-input::placeholder {
  color: #AAAAAA;
  font-size: 14px;
}

.verify-btn {
  min-width: 100px;
  height: 32px;
  background: #E8F3FF;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #3B7FF3;
  font-size: 13px;
}

.verify-btn.counting {
  color: #999;
}

/* 提交按钮 */
.submit-btn {
  width: 100%;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #E5E7ED;
  color: #999;
  font-size: 16px;
  border-radius: 8px;
}

.submit-btn.active {
  background: #3B7FF3;
  color: #FFFFFF;
}