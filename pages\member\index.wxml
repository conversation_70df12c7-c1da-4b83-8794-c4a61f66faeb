<!--pages/member/index.wxml-->
<view class="container">
    <view class="gradient-bg"></view>

    <!-- 自定义导航栏 -->
    <view
        class="custom-navbar"
        style="height: {{navBarHeight}}px;"
    >
        <view
            class="status-bar"
            style="height: {{statusBarHeight}}px;"
        ></view>
        <view class="navbar-content">
            <view
                class="nav-back"
                bindtap="goBack"
            >
                <van-icon
                    name="arrow-left"
                    size="20px"
                    color="#333"
                />
            </view>
            <view class="nav-title">会员中心</view>
            <view class="nav-placeholder"></view>
        </view>
    </view>

    <!-- 页面内容容器 -->
    <view
        class="content"
        style="margin-top: {{navBarHeight}}px;"
    >
        <!-- 会员卡片 -->
        <view class="member-card">
            <!-- 会员等级勋章提示 -->
            <view class="medal-pill-container">
                <view class="medal-text">会员等级勋章</view>
                <view
                    class="medal-action"
                    bindtap="showMedalDetails"
                >
                    <text class="medal-action-text">展示</text>
                    <text class="medal-action-arrow">></text>
                </view>
            </view>

            <!-- 会员徽章 -->
            <view class="badge-container">
                <view class="badge-and-arrow-container">
                    <view class="member-swiper-container">
                        <image
                            class="member-badge"
                            src="{{memberLevels[currentMemberIndex].image}}"
                            mode="aspectFit"
                        ></image>
                    </view>
                </view>
                <view class="member-level">{{memberLevels[currentMemberIndex].name}}</view>
            </view>

            <!-- 会员进度条 -->
            <view class="progress-container">
                <view class="progress-wrapper">
                    <view class="progress-bar">
                        <view
                            class="progress-inner"
                            style="width: {{memberLevels[currentMemberIndex].progress / memberLevels[currentMemberIndex].total * 100}}%;"
                        >
                            <view class="progress-pill">
                                {{memberLevels[currentMemberIndex].progress}}/{{memberLevels[currentMemberIndex].total}}权益解锁
                            </view>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 会员过期提示 -->
            <view
                class="expiration-hint"
                style="text-align: center; margin: 10rpx 0; font-size: 24rpx; color: #AAAAAA;"
            >
                {{expirationText}}
            </view>

            <!-- 会员选项 -->
            <view class="member-options">
                <view
                    class="option-item"
                    bindtap="goToRechargeRecord"
                >
                    <text>会员充值记录</text>
                </view>
                <view class="divider">|</view>
                <view
                    class="option-item"
                    bindtap="goToAutoRenew"
                >
                    <text>自动续费管理</text>
                    <view class="arrow-icon">
                        <van-icon
                            name="arrow"
                            size="14px"
                            color="#999"
                        />
                    </view>
                </view>
            </view>
        </view>

        <!-- 会员类型选择按钮 -->
        <view
            class="member-type-tabs"
            style="padding: 0 0rpx;"
        >
            <view
                class="member-type-tab {{currentMemberType === 'silver' ? 'active-silver' : 'inactive-silver'}}"
                bindtap="onSelectMemberType"
                data-type="silver"
            >
                <image
                    class="member-type-icon"
                    src="https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/wechat/assets/images/member_silver.png"
                    mode="aspectFit"
                ></image>
                <text class="member-type-text">白银会员</text>
            </view>
            <view
                class="member-type-tab {{currentMemberType === 'gold' ? 'active-gold' : 'inactive-gold'}}"
                bindtap="onSelectMemberType"
                data-type="gold"
            >
                <image
                    class="member-type-icon"
                    src="https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/wechat/assets/images/member_gold.png"
                    mode="aspectFit"
                ></image>
                <text class="member-type-text">黄金会员</text>
            </view>
        </view>

        <!-- 价格卡片滚动容器 -->
        <view class="price-cards-container">
            <scroll-view
                id="priceCardScroll"
                scroll-x="{{true}}"
                class="price-scroll-view"
                enhanced="{{true}}"
                show-scrollbar="{{false}}"
                enable-flex
                scroll-into-view="card-{{currentSelectedCardIndex}}"
            >
                <block
                    wx:for="{{filteredPriceCards}}"
                    wx:key="id"
                >
                    <view
                        id="card-{{index}}"
                        class="price-card {{currentSelectedCardIndex === index ? 'selected' : ''}}"
                        style="background-color: {{currentSelectedCardIndex === index ? '' : '#ffffff'}};"
                        bindtap="onSelectPriceCard"
                        data-index="{{index}}"
                    >
                        <view
                            wx:if="{{currentSelectedCardIndex === index}}"
                            class="card-bg-image"
                            style="background-image: url('{{item.backgroundImage}}');"
                        ></view>
                        <view class="price-card-title">{{item.title}}</view>
                        <view class="price-card-content">
                            <text class="price-symbol">¥</text>
                            <text class="price-value">{{item.price}}</text>
                            <view
                                class="original-price-container"
                                wx:if="{{item.originalPrice}}"
                            >
                                <view
                                    class="price-tag"
                                    wx:if="{{item.tag && item.title !== '年度卡'}}"
                                >{{item.tag}}</view>
                                <text class="price-original">¥{{item.originalPrice}}</text>
                            </view>
                        </view>
                        <view
                            class="annual-promo-text"
                            wx:if="{{item.title === '年度卡' && item.tag === '首年'}}"
                        >首年专属优惠</view>
                    </view>
                </block>
            </scroll-view>
        </view>

        <!-- 会员说明区域 -->
        <view class="member-description">
            <text class="description-text">{{memberDescriptionText}}</text>
        </view>

        <!-- 确认协议并开通按钮 -->
        <view class="confirm-button-container">
            <button
                class="confirm-button"
                bindtap="onConfirmSubscription"
            >确认协议并以 ¥{{filteredPriceCards[currentSelectedCardIndex].price}}开通</button>
        </view>

        <!-- 勾选协议 -->
        <view class="agreement-container">
            <radio-group bindchange="onAgreementChange">
                <view class="agreement-item">
                    <radio
                        value="agree"
                        checked="{{isAgreed}}"
                        color="#4080FF"
                    />
                    <text class="agreement-text">开通会员代表接受</text>
                    <text
                        class="agreement-link"
                        bindtap="onViewAgreement"
                    >《会员服务协议》</text>
                    <text class="agreement-text">。</text>
                </view>
            </radio-group>
        </view>

        <!-- 会员权益对比 -->
        <view class="member-benefits-container">
            <view class="member-benefits-title">会员权益对比</view>
            <view class="member-benefits-image-container">
                <image
                    src="https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/wechat/assets/images/member-benefits-comparison.png"
                    mode="widthFix"
                    class="member-benefits-image"
                ></image>
            </view>
        </view>
    </view>

    <!-- 支付订单状态卡片 -->
    <view
        class="payment-status-card"
        wx:if="{{showPaymentCard}}"
    >
        <image
            class="payment-diamond-icon"
            src="https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/wechat/assets/images/diamond_icon.png"
            mode="aspectFit"
        ></image>
        <view class="payment-status-text">{{paymentStatus}}</view>
    </view>

    <!-- 会员套餐限制卡片 -->
    <view
        class="member-limit-card"
        wx:if="{{showMemberLimitCard}}"
    >
        <view class="member-limit-header">
            <view class="member-limit-text">非常抱歉</view>
            <view class="member-limit-subtext">暂时无法更改会员套餐</view>
        </view>
        <image
            class="member-diamond-icon"
            src="https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/wechat/assets/images/diamond_icon.png"
            mode="aspectFit"
        ></image>
        <view class="member-limit-message">{{memberLimitText}}</view>
        <button
            class="member-limit-close"
            bindtap="closeMemberLimitCard"
        >关闭</button>
    </view>

    <!-- 会员勋章轮播图卡片 -->
    <view
        class="medal-carousel-drawer {{showMedalCarousel ? 'show' : ''}}"
        wx:if="{{showMedalCarousel}}"
        style="top: {{navBarHeight}}px;"
        bindtap="closeMedalCarousel"
    >
        <view
            class="medal-carousel-content"
            catchtap="stopPropagation"
        >
            <view class="medal-carousel-nav">
                <view
                    class="medal-carousel-arrow medal-carousel-prev"
                    bindtap="prevMedalCarousel"
                >
                    <van-icon
                        name="arrow-left"
                        size="24px"
                        color="#ffffff"
                    />
                </view>
                <view
                    class="medal-carousel-arrow medal-carousel-next"
                    bindtap="nextMedalCarousel"
                >
                    <van-icon
                        name="arrow"
                        size="24px"
                        color="#ffffff"
                    />
                </view>
            </view>

            <swiper
                class="medal-carousel-swiper"
                circular="{{true}}"
                current="{{currentMedalCarouselIndex}}"
                bindchange="onMedalSwiperChange"
            >
                <block
                    wx:for="{{memberLevels}}"
                    wx:key="index"
                >
                    <swiper-item class="medal-carousel-item">
                        <image
                            class="medal-carousel-image"
                            src="{{item.image}}"
                            mode="aspectFit"
                        ></image>
                        <view class="medal-carousel-name">{{item.name}}</view>
                    </swiper-item>
                </block>
            </swiper>

            <!-- 添加收回按钮 -->
            <view
                class="medal-carousel-collapse"
                bindtap="closeMedalCarousel"
            >
                <van-icon
                    name="arrow-up"
                    size="24px"
                    color="#ffffff"
                />
            </view>
        </view>
    </view>
</view>