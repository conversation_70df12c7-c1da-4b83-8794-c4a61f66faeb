<!--pages/login/register_form.wxml-->
<language-switcher position="top-left" showLangList="{{true}}" size="normal" />
<view
    class="container"
    style="--status-bar-height: {{statusBarHeight}}px;"
>
    <!-- 自定义导航栏 -->
    <view
        class="custom-nav"
        style="padding-top: {{statusBarHeight}}px;"
    >
        <view class="nav-content">
            <view
                class="back-icon"
                bindtap="navigateBack"
            >
                <van-icon
                    name="arrow-left"
                    size="20px"
                    color="#333"
                />
            </view>
            <view class="nav-title">{{text.sign_up}}</view>
        </view>
    </view>

    <!-- 注册内容区域 -->
    <view
        class="main-content"
        style="padding-top: {{statusBarHeight + 44}}px;"
    >
        <!-- 注册表单 -->
        <view class="register-form">
            <!-- 手机号输入框 -->
            <view class="input-item">
                <view class="input-icon">
                    <van-icon
                        name="phone-o"
                        size="20px"
                        color="#999"
                    />
                </view>
                <input
                    class="input-field"
                    type="number"
                    placeholder="{{text.enter_phone_number}}"
                    placeholder-class="placeholder"
                    model:value="{{phone}}"
                />
            </view>

            <!-- 密码输入框 -->
            <view class="input-item">
                <view class="input-icon">
                    <van-icon
                        name="lock"
                        size="20px"
                        color="#999"
                    />
                </view>
                <input
                    class="input-field"
                    password="{{!showPassword}}"
                    placeholder="{{text.pass_chars}}"
                    placeholder-class="placeholder"
                    model:value="{{password}}"
                />
            </view>

            <!-- 验证码输入框 -->
            <view class="input-item">
                <view class="input-icon">
                    <van-icon
                        name="certificate"
                        size="20px"
                        color="#999"
                    />
                </view>
                <input
                    class="input-field"
                    type="number"
                    placeholder="{{text.please_enter_the_verification_code}}"
                    placeholder-class="placeholder"
                    model:value="{{verifyCode}}"
                />
                <button
                    class="verify-code-btn {{isCountDown ? 'disabled-btn' : ''}}"
                    bindtap="getVerifyCode"
                    disabled="{{isCountDown}}"
                >
                    {{codeBtnText}}
                </button>
            </view>

            <!-- 验证码错误提示，调整为与input-item相同宽度 -->
            <view class="error-container">
                <view
                    class="error-message {{errorInfo.type}}"
                    hidden="{{!errorInfo.show}}"
                >
                    <van-icon
                        name="{{errorInfo.icon || 'info-o'}}"
                        size="14px"
                    />
                    <text>{{errorInfo.msg}}</text>
                </view>
            </view>
        </view>

        <!-- 协议区域 -->
        <view class="agreement-container">
            <checkbox
                class="checkbox"
                checked="{{isAgree}}"
                bindtap="toggleAgree"
            ></checkbox>
            <view class="agreement-text">
                {{text.i_agree_to_abide_by}}<text
                    class="link"
                    bindtap="viewAgreement"
                    data-type="service"
                >《{{text.user_service_agreement}}》</text>、<text
                    class="link"
                    bindtap="viewAgreement"
                    data-type="privacy"
                >《{{text.privacy_policy}}》</text>
            </view>
        </view>

        <!-- 注册按钮 -->
        <button
            class="register-btn {{isAgree ? '' : 'register-btn-disabled'}}"
            bindtap="handleRegister"
            disabled="{{!isAgree}}"
            loading="{{loading}}"
        >
            {{text.sign_up}}
        </button>
    </view>

    <!-- 注册成功提示窗口 -->
    <view
        class="success-modal-mask"
        wx:if="{{showSuccessModal}}"
    >
        <view class="success-modal">
            <view class="success-icon">
                <!-- 使用汽车/机器人图标 -->
                <image
                    src="https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/uploads/proof/1751274246972_981.png"
                    mode="aspectFit"
                ></image>
            </view>
            <view class="success-title">恭喜您</view>
            <view class="success-desc">距离注册成功只差最后一步啦</view>
            <button
                class="go-info-btn"
                bindtap="goToInfoPage"
            >前往完善个人信息</button>
        </view>
    </view>
</view>