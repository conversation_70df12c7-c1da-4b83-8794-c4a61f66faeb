// 导入 API 模块
import api from '../../utils/api';
import util from '../../utils/util';
import share from '../../utils/share';  // 确保导入分享功能

Page({
  behaviors: [share], //分享设置
  data: {
    // 分享页面数据
    shareData: {
      title: '找车侠',
      path: '/pages/moments/detail',
      isDetailPage: true,
    },
    // 详情数据
    id: null,
    postDetail: null,
    isLoading: true,
    commentList: [],
    commentPage: 1,
    commentPageSize: 10,
    hasMoreComments: true,
    isLoadingComments: false,
    commentValue: '',
    commentFocus: false,
    imageUrls: [], // 用于预览图片
    commentPlaceholder: '说点什么...',
    replyToUser: null, // 记录回复的用户
    statusBarHeight: 0, // 状态栏高度
  },

  onLoad(options) {
    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });

    // 获取详情ID
    const id = options.id;
    if (!id) {
      wx.showToast({
        title: '参数错误',
        icon: 'none',
        duration: 2000
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
      return;
    }

    this.setData({
      id: id,
      // 更新分享路径
      ['shareData.path']: `/pages/moments/detail?id=${id}`
    });

    // 加载详情数据
    this.loadDetail();

    // 加载评论数据
    this.loadComments();
  },

  // 加载详情数据
  async loadDetail() {
    this.setData({ isLoading: true });

    try {
      // 调用API获取详情
      const result = await api.moments.getDetail({
        id: this.data.id,
        app_id: util.getAppId()
      });
      if (result) {
        // 格式化数据
        const detail = result;

        // 处理图片数组，用于预览
        let imageUrls = [];
        if (detail.image_urls && detail.image_urls.length > 0) {
          imageUrls = detail.image_urls;
        }

        // 设置详情数据
        this.setData({
          postDetail: {
            id: detail.id,
            title: detail.title || detail.ui_vehicle_name || '新车直出',
            desc: detail.vehicle_condition || '热浪车新车直出，不限车型，可惠运可陆运，费用低，提前运返税，专业出口退税，会处理时间。',
            price: detail.price || 0,
            quantity: detail.quantity || 0,
            used_type_name: detail.used_type_name || '二手车',
            delivery_location: detail.delivery_location || '全国',
            images: detail.image_urls || [],
            avatar: detail.avatar || '/static/images/default-avatar.png',
            username: detail.short_name || detail.company_name || '侠友' + detail.id,
            time: detail.create_time || new Date().toISOString().substring(0, 10),
            comments: detail.comments,

            likes: detail.likes || 0,
            tags: detail.tags || ['行业热议', '出口退税', '出口问答'],

            isLiked: detail.likeds,
            views: detail.views,
            ui_vehicle_name: detail.ui_vehicle_name
          },
          imageUrls: imageUrls,
          isLoading: false,
          // 更新分享标题
          ['shareData.title']: detail.ui_vehicle_name || '新车直出',
          ['shareData.path']: '/pages/moments/detailid=' + detail.id
        });
      } else {
        this.showError('获取详情失败');
      }
    } catch (error) {
      console.error('加载详情失败:', error);
      this.showError('加载详情失败');
    }
  },

  // 加载评论数据
  async loadComments(isLoadMore = false) {
    if (this.data.isLoadingComments && !isLoadMore) {
      return;
    }

    this.setData({ isLoadingComments: true });

    try {
      const page = isLoadMore ? this.data.commentPage + 1 : 1;

      // 调用API获取评论
      const result = await api.moments.getCommentsList({
        id: this.data.id,
        app_id: util.getAppId(),
        page: page,
        list_rows: this.data.commentPageSize
      });

      if (result && result.data) {
        const comments = Array.isArray(result.data) ? result.data : [];

        // 格式化评论数据
        const formattedComments = comments.map(comment => {
          // 处理回复数据，确保含有必要的字段
          const replies = (comment.comments || []).map(reply => {
            // 从数据中获取回复的关联信息
            const username = reply.short_name || reply.company_name || '侠友' + reply.app_id;

            return {
              id: reply.id,
              content: reply.content,
              createTime: reply.create_time || reply.time,
              userId: reply.app_id,
              avatar: reply.avatar || '/static/images/default-avatar.png',
              username: username,
              likes: reply.likes || 0,
              isLiked: reply.likeds || false
            };
          });

          return {
            id: comment.id,
            content: comment.content,
            createTime: comment.create_time,
            userId: comment.app_id,
            avatar: comment.avatar || '/static/images/default-avatar.png',
            username: comment.short_name || comment.company_name || '侠友' + comment.app_id,
            likes: comment.likes || 0,
            isLiked: comment.likeds || false,
            replies: replies
          };
        });

        // 更新评论列表
        this.setData({
          commentList: isLoadMore ?
            [...this.data.commentList, ...formattedComments] : formattedComments,
          commentPage: page,
          hasMoreComments: comments.length >= this.data.commentPageSize,
          isLoadingComments: false
        });
      } else {
        this.setData({
          isLoadingComments: false,
          hasMoreComments: false
        });
      }
    } catch (error) {
      console.error('加载评论失败:', error);
      this.setData({ isLoadingComments: false });
    }
  },

  // 图片预览
  previewImage(e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.imageUrls;

    if (images && images.length > 0) {
      wx.previewImage({
        current: images[index],
        urls: images
      });
    }
  },

  // 提交评论
  async submitComment() {
    if (!this.data.commentValue.trim()) {
      wx.showToast({
        title: '请输入评论内容',
        icon: 'none'
      });
      return;
    }

    util.checkLoginWithPopup(async (userInfo) => {
      // 提交评论逻辑
      try {
        const params = {
          vehicle_opportunity_id: this.data.id,
          content: this.data.commentValue,
          app_id: userInfo.app_id
        };

        // 如果是回复，添加回复用户ID
        if (this.data.replyToUser) {
          params.comment_id = this.data.replyToUser.commentId;
          params.is_comment_on_post = 2;
        }

        const result = await api.moments.addComment(params);
        // console.log(result);

        wx.showToast({
          title: '评论成功',
          icon: 'success'
        });

        // 清空输入框
        this.setData({
          commentValue: '',
          replyToUser: null,
          commentPlaceholder: '说点什么...'
        });

        // 重新加载评论
        this.loadComments();

        // 更新评论数量
        if (this.data.postDetail) {
          this.setData({
            'postDetail.comments': (this.data.postDetail.comments || 0) + 1
          });
        }
      } catch (error) {
        console.error('提交评论失败:', error);
        wx.showToast({
          title: '评论失败，请稍后再试',
          icon: 'none'
        });
      }
    })
  },

  // 回复评论
  replyComment(e) {
    const { userId, username, commentId } = e.currentTarget.dataset;

    this.setData({
      commentFocus: true,
      commentPlaceholder: `回复 ${username}：`,
      replyToUser: {
        id: userId,
        name: username,
        commentId: commentId
      }
    });
  },

  // 取消回复
  cancelReply() {
    this.setData({
      replyToUser: null,
      commentPlaceholder: '说点什么...'
    });
  },

  // 点赞帖子
  async toggleLike() {
    if (!this.data.postDetail) return;

    // 检查登录状态
    util.checkLoginWithPopup(async (userInfo) => {
      try {
        const currentLikeStatus = this.data.postDetail.isLiked;

        // 乐观更新UI
        this.setData({
          'postDetail.isLiked': !currentLikeStatus,
          'postDetail.likes': currentLikeStatus ?
            this.data.postDetail.likes - 1 : this.data.postDetail.likes + 1
        });

        // 调用点赞/取消点赞API
        await api.moments.toggleLike({
          app_id: userInfo.app_id,
          type: 1,
          liked: !currentLikeStatus,
          type_id: this.data.id,
        });

      } catch (error) {
        console.error('点赞操作失败:', error);
        // 回滚UI更新
        const currentLikeStatus = this.data.postDetail.isLiked;
        this.setData({
          'postDetail.isLiked': !currentLikeStatus,
          'postDetail.likes': !currentLikeStatus ?
            this.data.postDetail.likes - 1 : this.data.postDetail.likes + 1
        });
      }
    })
  },

  // 点赞评论
  async toggleCommentLike(e) {
    const { index } = e.currentTarget.dataset;
    const comment = this.data.commentList[index];

    if (!comment) return;

    util.checkLoginWithPopup(async (userInfo) => {
      try {
        const currentLikeStatus = comment.isLiked;

        // 乐观更新UI
        const key = `commentList[${index}].isLiked`;
        const likesKey = `commentList[${index}].likes`;

        this.setData({
          [key]: !currentLikeStatus,
          [likesKey]: currentLikeStatus ? comment.likes - 1 : comment.likes + 1
        });

        // 调用点赞/取消点赞API
        await api.moments.toggleLike({
          app_id: userInfo.app_id,
          type: 2,
          liked: !currentLikeStatus,
          type_id: comment.id,
        });

      } catch (error) {
        console.error('评论点赞操作失败:', error);
        // 回滚UI更新
        const currentLikeStatus = comment.isLiked;
        const key = `commentList[${index}].isLiked`;
        const likesKey = `commentList[${index}].likes`;

        this.setData({
          [key]: !currentLikeStatus,
          [likesKey]: !currentLikeStatus ? comment.likes - 1 : comment.likes + 1
        });
      }
    })
  },

  // 点赞回复评论
  async toggleReplyLike(e) {
    const { commentId, replyId, commentIndex, replyIndex } = e.currentTarget.dataset;

    const res = util.checkLogin()
    if (!res) return false

    try {
      // 获取当前评论和回复
      const commentList = this.data.commentList;
      const comment = commentList[commentIndex];

      if (!comment || !comment.replies) {
        return;
      }

      const reply = comment.replies[replyIndex];
      if (!reply) {
        return;
      }

      // 反转点赞状态
      const isLiked = !reply.isLiked;
      const likesCount = isLiked ? (parseInt(reply.likes || 0) + 1) : (parseInt(reply.likes || 0) - 1);

      // 立即更新UI
      this.setData({
        [`commentList[${commentIndex}].replies[${replyIndex}].isLiked`]: isLiked,
        [`commentList[${commentIndex}].replies[${replyIndex}].likes`]: likesCount >= 0 ? likesCount : 0
      });

      // 调用API更新点赞状态
      await api.moments.toggleLike({
        app_id: res.app_id,
        type: 2,
        liked: isLiked,
        type_id: replyId,
      });

      // console.log(`${isLiked ? '点赞' : '取消点赞'}回复 ${replyId}`);
    } catch (error) {
      console.error('点赞回复评论失败:', error);
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
    }
  },

  // 输入评论
  onCommentInput(e) {
    this.setData({
      commentValue: e.detail.value
    });
  },

  // 加载更多评论
  loadMoreComments() {
    if (this.data.hasMoreComments && !this.data.isLoadingComments) {
      this.loadComments(true);
    }
  },

  // 收藏帖子
  async toggleFavorite() {
    if (!this.data.postDetail) return;

    try {
      const currentFavoriteStatus = this.data.postDetail.isFavorite;

      // 乐观更新UI
      this.setData({
        'postDetail.isFavorite': !currentFavoriteStatus
      });

      // 调用收藏/取消收藏API
      const result = await api.moments.toggleFavorite({
        id: this.data.id,
        action: currentFavoriteStatus ? 'unfavorite' : 'favorite'
      });

      if (!result || !result.success) {
        // 如果失败，回滚UI更新
        this.setData({
          'postDetail.isFavorite': currentFavoriteStatus
        });
      } else {
        wx.showToast({
          title: currentFavoriteStatus ? '已取消收藏' : '已收藏',
          icon: 'success'
        });
      }
    } catch (error) {
      console.error('收藏操作失败:', error);
      // 回滚UI更新
      this.setData({
        'postDetail.isFavorite': !this.data.postDetail.isFavorite
      });
    }
  },

  // 显示错误信息
  showError(message) {
    this.setData({ isLoading: false });

    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: this.data.postDetail ? this.data.postDetail.title : '找车侠',
      query: `id=${this.data.id}`
    };
  },

  // 分享给朋友
  onShareAppMessage() {
    return {
      title: this.data.postDetail ? this.data.postDetail.title : '找车侠',
      path: `/pages/moments/detail?id=${this.data.id}`,
      // imageUrl: this.data.postDetail && this.data.postDetail.images && this.data.postDetail.images.length > 0 ? 
      //   this.data.postDetail.images[0] : undefined
    };
  },

  // 返回上一页
  navigateBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    Promise.all([
      this.loadDetail(),
      this.loadComments()
    ]).finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 上拉加载更多评论
  onReachBottom() {
    this.loadMoreComments();
  }
}); 