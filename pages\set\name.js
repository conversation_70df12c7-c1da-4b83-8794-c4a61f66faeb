// pages/set/name.js
import util from '../../utils/util';
import api from '../../utils/api';

Page({

  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: 20, // 默认状态栏高度，会在onLoad中动态获取
    nickname: '', // 用户昵称
    originalNickname: '' // 用于比较是否有修改
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });

    // 隐藏原生导航栏
    wx.hideNavigationBarLoading();
    wx.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: 'transparent',
      animation: {
        duration: 0,
        timingFunc: 'easeIn'
      }
    });

    // 从缓存中获取用户信息
    this.getUserInfoFromCache();
  },

  /**
   * 从缓存中获取用户信息
   */
  getUserInfoFromCache() {
    const userInfo = util.getCacheWithExpiry('userInfo');
    if (userInfo) {
      this.setData({
        nickname: userInfo.short_name || '',
        originalNickname: userInfo.short_name || ''
      });
    }
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 监听昵称输入
   */
  onNicknameInput(e) {
    this.setData({
      nickname: e.detail.value
    });
  },

  /**
   * 清除输入内容
   */
  clearInput() {
    this.setData({
      nickname: ''
    });
  },

  /**
   * 保存昵称
   */
  saveNickname() {
    const { nickname, originalNickname } = this.data;

    // 验证昵称
    if (!nickname.trim()) {
      wx.showToast({
        title: '昵称不能为空',
        icon: 'none'
      });
      return;
    }

    // 如果昵称没有变化，直接返回
    if (nickname === originalNickname) {
      this.goBack();
      return;
    }

    // 显示加载提示
    wx.showLoading({
      title: '保存中...',
      mask: true
    });

    // 获取用户信息
    const userInfo = util.getCacheWithExpiry('userInfo');

    // 调用接口更新昵称
    api.user.updateUserNickname({
      short_name: nickname,
      app_id: userInfo.app_id
    }).then(result => {
      // 更新缓存中的用户信息
      userInfo.short_name = nickname;
      util.setCacheWithExpiry('userInfo', userInfo);

      wx.hideLoading();
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });

      // 返回上一页
      setTimeout(() => {
        this.goBack();
      }, 1500);
    }).catch(err => {
      console.error('昵称更新失败:', err);
      wx.hideLoading();
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})