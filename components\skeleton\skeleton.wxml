<view class="skeleton" wx:if="{{loading && firstLoading}}">
  <!-- 导航栏骨架 -->
  <view class="sk-nav">
    <view class="sk-title"></view>
  </view>
  
  <!-- 搜索栏骨架 -->
  <view class="sk-search">
    <view class="sk-city"></view>
    <view class="sk-input"></view>
    <view class="sk-btn"></view>
  </view>
  
  <!-- 轮播图骨架 -->
  <view class="sk-banner"></view>
  
  <!-- 导航菜单骨架 -->
  <view class="sk-nav-grid">
    <view class="sk-nav-item" wx:for="{{10}}" wx:key="index">
      <view class="sk-nav-icon"></view>
      <view class="sk-nav-text"></view>
    </view>
  </view>

  
  
  <!-- 热门品牌骨架 -->
  <view class="sk-section-header">
    <view class="sk-section-title"></view>
    <view class="sk-more"></view>
  </view>
  <view class="sk-brands">
    <view class="sk-brand-item" wx:for="{{5}}" wx:key="index">
      <view class="sk-brand-icon"></view>
      <view class="sk-brand-name"></view>
    </view>
  </view>
  
  <!-- 推荐车辆骨架 -->
  <view class="sk-section-header">
    <view class="sk-section-title"></view>
    <view class="sk-more"></view>
  </view>
  <view class="sk-car-list">
    <view class="sk-car-item" wx:for="{{6}}" wx:key="index">
      <view class="sk-car-img"></view>
      <view class="sk-car-title"></view>
      <view class="sk-car-info"></view>
      <view class="sk-car-price"></view>
    </view>
  </view>
</view>

<!-- 实际内容 -->
<view wx:if="{{!loading || !firstLoading}}">
  <slot></slot>
</view> 