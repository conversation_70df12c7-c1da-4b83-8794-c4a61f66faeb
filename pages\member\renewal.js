// pages/member/renewal.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: 20, // 默认状态栏高度
    navBarHeight: 64, // 导航栏高度
    merchantLogo: 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132', // 默认微信头像
    merchantName: '尚品国际', // 商家名称
    memberIcon: 'https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/wechat/assets/images/member_silver.png', // 白银会员图标
    memberType: '白银会员', // 会员类型
    validUntil: '2025-06-30', // 有效期
    nextRenewalDate: '2025-06-29' // 下次续费日期
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取状态栏高度和菜单按钮位置信息
    const systemInfo = wx.getSystemInfoSync();
    const menuButtonInfo = wx.getMenuButtonBoundingClientRect();

    // 计算导航栏高度
    const navBarHeight = menuButtonInfo.bottom + 6;

    this.setData({
      statusBarHeight: systemInfo.statusBarHeight,
      navBarHeight: navBarHeight
    });

    // 获取会员信息
    this.fetchMemberInfo();
  },

  /**
   * 获取会员信息
   */
  fetchMemberInfo() {
    // 这里应该发起请求获取会员信息，这里用模拟数据
    // wx.request({
    //   url: 'API地址',
    //   method: 'GET',
    //   success: (res) => {
    //     const data = res.data;
    //     this.setData({
    //       merchantName: data.merchantName,
    //       memberType: data.memberType,
    //       validUntil: data.validUntil,
    //       nextRenewalDate: data.nextRenewalDate
    //     });
    //   }
    // });
  },

  /**
   * 关闭自动续费
   */
  closeAutoRenewal() {
    wx.showModal({
      title: '关闭自动续费',
      content: '确定要关闭自动续费功能吗？关闭后将不再自动扣款。',
      confirmText: '确定关闭',
      cancelText: '再想想',
      success: (res) => {
        if (res.confirm) {
          // 处理确认关闭逻辑
          wx.showLoading({
            title: '处理中',
          });

          // 模拟请求
          setTimeout(() => {
            wx.hideLoading();
            wx.showToast({
              title: '已关闭自动续费',
              icon: 'success'
            });
            // 关闭后可返回上一页
            setTimeout(() => {
              wx.navigateBack();
            }, 1500);
          }, 1000);
        }
      }
    });
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})