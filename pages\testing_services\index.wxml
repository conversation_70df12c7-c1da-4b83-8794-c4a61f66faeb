<!--pages/testing_services/index.wxml-->
<!--pages/train_operation/index.wxml-->
<view
  class="container"
  style="--status-bar-height: {{statusBarHeight}}px;"
>
  <!-- 自定义导航栏 -->
  <view
    class="custom-nav"
    style="padding-top: {{statusBarHeight}}px;"
  >
    <view class="nav-content">
      <view
        class="back-icon"
        bindtap="navigateBack"
      >
        <image
          src="/icons/moments/back.svg"
          mode="aspectFit"
        ></image>
      </view>
      <view class="nav-title">检测服务</view>
    </view>
  </view>

  <!-- 主内容区域 -->
  <view
    class="main-content"
    style="padding-top: {{statusBarHeight + 50}}px;"
  >

    <!-- 顶部区域：固定高度容器，用于容纳搜索框或筛选标题 -->
    <view class="fixed-header">
      <!-- 搜索区域 -->
      <view
        class="top-container"
        style="height: {{ topContainerHeight }}px;"
      >
        <!-- 搜索区域 -->
        <view class="search-bar">
          <view class="search-input-wrapper">
            <icon
              type="search"
              size="14"
              class="search-icon"
            ></icon>
            <input
              class="search-input"
              placeholder="请输入检查内容或公司名称"
              placeholder-style="color: #aaa; font-size: 26rpx;"
              value="{{ searchValue || ''}}"
              bindinput="onSearchInput"
              bindconfirm="onSearch"
            />
            <view
              class="search-btn"
              bindtap="onSearch"
            >搜索</view>
          </view>
        </view>
      </view>
    </view>
    <!-- 内容区域 - 添加一个占位符，高度等于固定头部的高度 -->
    <view class="header-placeholder"></view>

    <!-- 车辆列表(使用scroll-view替换普通view) -->
    <scroll-view
      class="vehicle-list"
      scroll-y="true"
      bindscrolltolower="onScrollToLower"
      lower-threshold="100"
      enable-back-to-top="true"
      refresher-enabled="{{false}}"
      style="height: calc(100vh - {{statusBarHeight + 44}}px - 90rpx - 60rpx);"
    >
      <view
        wx:for="{{vehicleList}}"
        wx:key="id"
        class="vehicle-item"
        data-id="{{item.id}}"
        bindtap="tapItem"
      >
        <!-- 车型信息和右箭头 -->
        <view class="vehicle-name-row">
          <view class="vehicle-name">{{item.company_name}}</view>
          <!-- 右箭头 -->
          <view class="arrow">
            <image
              src="https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/wechat/assets/icons/arrow-right.png"
              mode="aspectFit"
            ></image>
          </view>
        </view>

        <!-- 详细信息区块 -->
        <view class="vehicle-info">
          <view class="content-info">
            <view class="content-item">
              <text class="content-item-title">车辆类型</text>
              <text class="content-item-content">{{item.carTypeText || '-'}}</text>
            </view>
            <view class="content-item">
              <view class="content-item-content">
                <text class="content-item-title">地点</text> {{item.place || '-'}}
              </view>
            </view>
          </view>
        </view>

        <view class="vehicle-info">
          <view class="content-info">
            <view class="content-item">
              <text class="content-item-title">检查内容</text>
              <text class="content-item-content">{{item.regional || '-'}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 没有数据时显示的提示 -->
      <view
        class="empty-tip"
        wx:if="{{vehicleList.length === 0}}"
      >
        暂无数据
      </view>

      <!-- 加载更多提示 -->
      <view
        class="loading-more"
        wx:if="{{vehicleList.length > 0}}"
      >
        <view
          wx:if="{{isLoading}}"
          class="loading"
        >
          <view class="loading-icon"></view>
          <text>加载中...</text>
        </view>
        <view
          wx:elif="{{!hasMore}}"
          class="no-more"
        >
          <text>—— 已经到底了 ——</text>
        </view>
        <view
          wx:else
          class="pull-tip"
        >
          <text>上拉加载更多</text>
        </view>
      </view>

      <!-- 添加手动加载更多按钮 -->
      <view
        wx:if="{{hasMore && !isLoading && vehicleList.length > 0}}"
        class="load-more-btn"
        bindtap="loadMore"
      >
        点击加载更多
      </view>
    </scroll-view>
  </view>
</view>