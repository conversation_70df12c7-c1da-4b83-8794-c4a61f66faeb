/* pages/set/phonein.wxss */
page {
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
  min-height: 100%;
  height: 100%;
}

.container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background: transparent;
}

/* 自定义导航栏 */
.custom-nav {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
}

.nav-content {
  position: relative;
  display: flex;
  align-items: center;
  height: 44px;
  padding: 0 16px;
}

.nav-back {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.back-icon {
  width: 12px;
  height: 20px;
  background-image: url("data:image/svg+xml,%3Csvg width='12' height='20' viewBox='0 0 12 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10 2L2 10L10 18' stroke='%23333333' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-size: contain;
}

.nav-title {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

/* 主要内容区域 */
.main-content {
  padding: 20px 16px;
}

/* 电话卡片样式 */
.phone-card {
  background: #FFFFFF;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 40px;
  padding: 20px 16px;
}

.phone-title {
  font-size: 22px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  text-align: center;
}

.phone-desc {
  font-size: 14px;
  color: #999;
  line-height: 1.6;
  margin-bottom: 24px;
}

/* 卡片内分隔线 */
.card-divider {
  height: 1px;
  background-color: #F5F5F5;
  margin: 0 -16px 16px;
}

.input-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #F5F5F5;
}

.input-item:last-child {
  border-bottom: none;
}

.item-label {
  font-size: 16px;
  color: #333;
}

.item-value {
  display: flex;
  align-items: center;
  color: #333;
  font-size: 16px;
}

.item-value .prefix {
  margin-right: 5px;
}

.item-value .hint {
  color: #999;
}

.next-btn {
  width: 100%;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #E5E7ED;
  color: #999;
  font-size: 16px;
  border-radius: 8px;
}

.next-btn.active {
  background: #3B7FF3;
  color: #FFFFFF;
}