<!--pages/video/detail.wxml-->
<!-- 引入登录弹窗模板 -->
<import src="../../templates/loginPopup/loginPopup.wxml" />

<view class="video-container">
  <!-- 加载状态显示 -->
  <view
    class="loading-container"
    wx:if="{{loading || !isVideoReady}}"
  >
    <view class="loading-spinner"></view>
    <text class="loading-text">视频加载中...</text>
  </view>
  <!-- 视频组件 -->
    <video 
      style="{{ 'width: 100%;' + (videoInfo.specifications == 1 ? 'height: 84%;' : '') }}"
      id="myVideo" 
      src="{{videoUrl}}"
      binderror="videoErrorCallback" 
      show-play-btn="{{true}}" 
      controls
      picture-in-picture-mode="{{['push', 'pop']}}"
      enable-progress-gesture="{{true}}" 
      wx:if="{{videoUrl}}"
      custom-cache="{{false}}"
      bindloadedmetadata="onVideoLoaded"
      bindtap="handleVideoTap"
      referrerPolicy="origin"
      >
    </video>

  <!-- 未登录模糊遮罩 -->
  <view
    class="blur-mask {{showMask ? 'show' : ''}}"
    wx:if="{{needLogin && isVideoReady && !loading}}"
    catchtap="handleLoginClick"
  >
    <view class="blur-mask-content">
      <view class="lock-icon"></view>
      <view class="mask-title">您需要登录</view>
      <view class="mask-text">登录后即可观看完整视频</view>
      <view
        class="mask-button"
        catchtap="handleLoginClick"
      >立即登录</view>
    </view>
  </view>

  <!-- 视频加载失败显示 -->
  <view
    class="error-container"
    wx:if="{{!loading && !videoUrl}}"
  >
    <text class="error-text">视频加载失败</text>
    <text class="error-detail">请检查网络连接或稍后重试</text>
    <view
      class="retry-btn"
      bindtap="goBack"
    >返回</view>
  </view>

  <!-- 返回按钮 - 仅在已登录状态或非分享进入时显示 -->
  <view
    class="back-btn"
    bindtap="goBack"
    wx:if="{{!needLogin}}"
  >
    <van-icon
      name="arrow-left"
      size="20px"
      color="#fff"
    />
  </view>

  <!-- 未登录状态下的伪返回按钮 - 点击后提示需要登录 -->
  <view
    class="back-btn disabled"
    bindtap="handleBackWhenNotLogin"
    wx:if="{{needLogin}}"
  >
    <van-icon
      name="arrow-left"
      size="20px"
      color="#fff"
    />
  </view>

  <!-- 商家信息 - 始终显示 -->
  <view
    class="merchant-info-container"
    wx:if="{{videoUrl}}"
  >
    <view class="merchant-info">
      <view
        class="merchant-avatar"
        bindtap="goToMerchantPage"
      >
        <image
          src="{{merchantInfo.avatar}}"
          mode="aspectFill"
        ></image>
      </view>
      <text
        class="merchant-name"
        bindtap="goToMerchantPage"
      >{{merchantInfo.name}}</text>
    </view>
  </view>

  <!-- 底部说明和进度条 -->
  <view
    class="bottom-info"
    wx:if="{{videoUrl}}"
    hidden="{{!isVideoReady}}"
  >

    <!-- 视频描述 -->
    <view class="video-description {{isDescExpanded ? 'expanded' : ''}}">
      <text class="desc-text">{{videoInfo.description}}</text>
      <view
        class="expand-icon"
        bindtap="toggleDescription"
      >
        <view class="toggle-icon {{isDescExpanded ? 'up' : 'down'}}"></view>
      </view>
    </view>
    <!-- 占位容器，替代原来的点赞转发评论图标按钮 -->
    <!-- <view class="action-buttons-placeholder"></view> -->

    <!-- 移除了商家信息，现在单独显示 -->
  </view>
  <!-- 调试信息 -->
  <view
    class="debug-info"
    wx:if="{{false}}"
  >
    <text>loading: {{loading}}</text>
    <text>videoUrl: {{videoUrl ? '有值' : '无值'}}</text>
    <text>videoUrlLength: {{videoUrl.length}}</text>
  </view>

  <!-- 使用登录弹窗模板 -->
  <template
    is="loginPopup"
    data="{{ showLoginPopup, loginPopupOptions }}"
  />
</view>