/* pages/member/renewal.wxss */

/* 页面容器 */
.container {
    min-height: 100vh;
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    position: relative;
}

/* 渐变背景 */
.gradient-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
    background-attachment: fixed;
    /* 固定背景，避免滚动时背景移动 */
    z-index: -1;
}

/* 自定义导航栏 */
.custom-navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 100;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 100%);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.status-bar {
    width: 100%;
}

.navbar-content {
    display: flex;
    height: 44px;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
}

.nav-back {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 44px;
    z-index: 5;
}

.nav-title {
    flex: 1;
    text-align: center;
    font-size: 34rpx;
    font-weight: 500;
    color: #333;
}

.nav-placeholder {
    width: 40px;
    height: 44px;
}

/* 内容区域 */
.content {
    width: 100%;
    padding: 20rpx;
    box-sizing: border-box;
    position: relative;
    background-color: transparent;
    /* 改为透明背景 */
}

/* 商家信息 */
.merchant-info {
    display: flex;
    align-items: center;
    padding: 20rpx;
    background-color: transparent;
    margin-bottom: 30rpx;
}

.merchant-logo {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    margin-right: 16rpx;
    background-color: #ff4444;
    /* 临时红色背景 */
}

.merchant-name {
    font-size: 30rpx;
    color: #333;
    font-weight: 500;
}

/* 部分标题 */
.section-title {
    font-size: 28rpx;
    color: #333;
    font-weight: normal;
    padding: 0 20rpx 20rpx;
    background-color: transparent;
}

/* 会员卡信息 - 外层容器 */
.renewal-card {
    background-color: #fff;
    border-radius: 12rpx;
    overflow: hidden;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 会员卡信息 */
.member-card {
    background-color: #fff;
    padding: 20rpx;
    border-bottom: 1rpx solid #f5f5f5;
}

.member-card-header {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
}

.member-icon {
    width: 50rpx;
    height: 50rpx;
    border-radius: 50%;
    margin-right: 16rpx;
}

.member-type {
    font-size: 30rpx;
    color: #333;
    flex: 1;
}

.member-status {
    font-size: 26rpx;
    color: #999;
}

.member-card-content {
    padding-left: 66rpx;
}

.member-info-item {
    font-size: 26rpx;
    color: #666;
    margin-bottom: 10rpx;
    line-height: 1.6;
}

.member-info-desc {
    font-size: 24rpx;
    color: #999;
    margin-top: 16rpx;
    line-height: 1.6;
}

/* 支付方式 */
.payment-method {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    padding: 20rpx;
    border-bottom: 1rpx solid #f5f5f5;
}

.payment-method-title {
    display: flex;
    align-items: center;
}

.wechat-pay-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40rpx;
    height: 40rpx;
    margin-right: 16rpx;
}

.payment-method-title text {
    font-size: 30rpx;
    color: #333;
}

.payment-switch {
    display: flex;
    align-items: center;
}

.switch-text {
    font-size: 26rpx;
    color: #999;
    margin-right: 8rpx;
}

/* 支付说明 */
.payment-description {
    background-color: #fff;
    padding: 20rpx;
}

.payment-description text {
    font-size: 26rpx;
    color: #999;
    line-height: 1.6;
}