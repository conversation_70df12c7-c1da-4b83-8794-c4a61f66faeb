<!--index.wxml-->
<!-- 引入登录弹窗模板 -->
<import src="../../templates/loginPopup/loginPopup.wxml" />

<skeleton loading="{{pageLoading}}">
  <view
    class="container"
    style="background: transparent;"
    bindscroll="onPageScroll"
  >
    <!-- 自定义导航栏 - 始终固定在顶部 -->
    <view class="fixed-nav">
      <view
        class="status-bar"
        style="height: {{statusBarHeight}}px;"
      ></view>
      <view class="nav-title">找车侠</view>
    </view>

    <!-- 导航栏占位元素 -->
    <view
      class="nav-placeholder"
      style="height: {{statusBarHeight + 44}}px;"
    ></view>

    <!-- 固定在页面顶部的部分 -->
    <view class="content-container">
      <!-- 滚动头部区域 (搜索和轮播) -->
      <view
        class="scroll-header"
        style="display: block; visibility: visible; z-index: 30; margin-top: {{topSpace}}px;"
      >
        <view class="top-container">
          <!-- 顶部banner和搜索区域 -->
          <view class="top-search-container">
            <!-- Logo图标 -->
            <image
              src="{{COS_CONFIG.url}}wechat/assets/images/logo.png"
              mode="aspectFit"
              class="inline-logo"
            ></image>
            <!-- 搜索框 -->
            <view class="search-section">
              <view
                class="city-selector"
                bindtap="onCityTap"
              >
                <text>{{city}}</text>
                <view class="arrow-down"></view>
              </view>
              <view class="search-input-container">
                <icon
                  type="search"
                  size="14"
                  color="#999"
                ></icon>
                <input
                  placeholder="请输入品牌或车型"
                  bindinput="onSearchInput"
                  bindconfirm="onSearch"
                  confirm-type="search"
                />
              </view>
              <view
                class="search-btn"
                bindtap="onSearch"
              >搜索</view>
            </view>
          </view>

          <!-- 轮播图 -->
          <view class="banner-container">
            <swiper
              class="banner-swiper"
              indicator-dots="{{true}}"
              autoplay="{{true}}"
              interval="3000"
              duration="500"
              circular="{{true}}"
              indicator-color="rgba(255, 255, 255, 0.3)"
              indicator-active-color="#ffffff"
            >
              <swiper-item
                wx:for="{{banners}}"
                wx:key="id"
              >
                <image
                  src="{{item.image}}"
                  class="banner-image"
                  mode="aspectFill"
                ></image>
              </swiper-item>
            </swiper>
          </view>
        </view>
      </view>

      <!-- 快捷导航 - 当需要固定时会添加fixed-nav-container类 -->
      <view
        class="quick-nav-container {{isNavFixed ? 'fixed-nav-container' : ''}}"
        style="{{isNavFixed ? 'top: ' + (statusBarHeight + 44) + 'px;' : ''}}"
      >
        <swiper
          class="quick-nav-swiper"
          indicator-dots="{{true}}"
          indicator-color="rgba(0, 0, 0, .1)"
          indicator-active-color="#1296db"
        >
          <!-- 第一页 -->
          <swiper-item>
            <view class="quick-nav-page">
              <!-- 第一行 -->
              <view class="quick-nav-row">
                <view
                  class="quick-nav-item"
                  bindtap="onQuickNavTap"
                  data-type="used"
                >
                  <image
                    src="{{COS_CONFIG.url}}wechat/assets/icons/new-energy.png"
                    class="quick-nav-icon"
                    mode="aspectFit"
                  />
                  <text class="quick-nav-text">二手车</text>
                </view>
                <view
                  class="quick-nav-item"
                  bindtap="onQuickNavTap"
                  data-type="batch"
                >
                  <image
                    src="{{COS_CONFIG.url}}wechat/assets/icons/new-car.png"
                    class="quick-nav-icon"
                    mode="aspectFit"
                  />
                  <text class="quick-nav-text">批量车</text>
                </view>
                <view
                  class="quick-nav-item"
                  bindtap="onQuickNavTap"
                  data-type="right"
                >
                  <image
                    src="{{COS_CONFIG.url}}wechat/assets/icons/right-car.png"
                    class="quick-nav-icon"
                    mode="aspectFit"
                  />
                  <text class="quick-nav-text">右舵车</text>
                </view>
                <view
                  class="quick-nav-item"
                  bindtap="onQuickNavTap"
                  data-type="new"
                >
                  <image
                    src="{{COS_CONFIG.url}}wechat/assets/icons/high-car.png"
                    class="quick-nav-icon"
                    mode="aspectFit"
                  />
                  <text class="quick-nav-text">新车</text>
                </view>


                <view
                  class="quick-nav-item"
                  bindtap="onQuickNavTapCommercial"
                  data-type="3"
                >
                  <image
                    src="{{COS_CONFIG.url}}wechat/assets/icons/commercial.png"
                    class="quick-nav-icon"
                    mode="aspectFit"
                  />
                  <text class="quick-nav-text">低速车</text>
                </view>
              </view>

              <!-- 第二行 -->
              <view class="quick-nav-row">
                <view
                  class="quick-nav-item"
                  bindtap="onQuickNavTapCommercial"
                  data-type="1"
                >
                  <image
                    src="{{COS_CONFIG.url}}wechat/assets/icons/truck.png"
                    class="quick-nav-icon"
                    mode="aspectFit"
                  />
                  <text class="quick-nav-text">卡车</text>
                </view>
                <view
                  class="quick-nav-item"
                  bindtap="onQuickNavTapCommercial"
                  data-type="2"
                >
                  <image
                    src="{{COS_CONFIG.url}}wechat/assets/icons/bus.png"
                    class="quick-nav-icon"
                    mode="aspectFit"
                  />
                  <text class="quick-nav-text">客车</text>
                </view>
                <view
                  class="quick-nav-item"
                  bindtap="onQuickNavTapCommercial"
                  data-type="3"
                >
                  <image
                    src="{{COS_CONFIG.url}}wechat/assets/icons/special.png"
                    class="quick-nav-icon"
                    mode="aspectFit"
                  />
                  <text class="quick-nav-text">专用车</text>
                </view>
                <view
                  class="quick-nav-item"
                  bindtap="onQuickNavTapCommercial"
                  data-type="4"
                >
                  <image
                    src="{{COS_CONFIG.url}}wechat/assets/icons/trailer.png"
                    class="quick-nav-icon"
                    mode="aspectFit"
                  />
                  <text class="quick-nav-text">挂车</text>
                </view>
                <view
                  class="quick-nav-item"
                  bindtap="onQuickNavTapCommercial"
                  data-type="3"
                >
                  <image
                    src="{{COS_CONFIG.url}}wechat/assets/icons/auto_parts.png"
                    class="quick-nav-icon"
                    mode="aspectFit"
                  />
                  <text class="quick-nav-text">汽配</text>
                </view>
              </view>
            </view>
          </swiper-item>

          <!-- 第二页  -->
          <swiper-item>
            <view class="quick-nav-page">
              <view class="quick-nav-row">
                <view
                  class="quick-nav-item"
                  bindtap="onQuickNavTap"
                  data-type="luxury"
                >
                  <image
                    src="{{COS_CONFIG.url}}wechat/assets/icons/car_service.png"
                    class="quick-nav-icon"
                    mode="aspectFit"
                  />
                  <text class="quick-nav-text">车务列表</text>
                </view>
                <view
                  class="quick-nav-item"
                  bindtap="onQuickNavTap"
                  data-type="FinancialServices"
                >
                  <image
                    src="{{COS_CONFIG.url}}wechat/assets/icons/supply_chain.png"
                    class="quick-nav-icon"
                    mode="aspectFit"
                  />
                  <text class="quick-nav-text">供应链</text>
                </view>
                <view
                  class="quick-nav-item"
                  bindtap="onQuickNavTap"
                  data-type="flashing_machine"
                >
                  <image
                    src="{{COS_CONFIG.url}}wechat/assets/icons/flashing_service.png"
                    class="quick-nav-icon"
                    mode="aspectFit"
                  />
                  <text class="quick-nav-text">刷机服务</text>
                </view>
                <view
                  class="quick-nav-item"
                  bindtap="onQuickNavTap"
                  data-type="testing_services"
                >
                  <image
                    src="{{COS_CONFIG.url}}wechat/assets/icons/testing_services.png"
                    class="quick-nav-icon"
                    mode="aspectFit"
                  />
                  <text class="quick-nav-text">检查服务</text>
                </view>
                <view
                  class="quick-nav-item"
                  bindtap="onQuickNavTap"
                  data-type="domestic_trailer"
                >
                  <image
                    src="{{COS_CONFIG.url}}wechat/assets/icons/domestic_trailer.png"
                    class="quick-nav-icon"
                    mode="aspectFit"
                  />
                  <text class="quick-nav-text">国内拖车</text>
                </view>
              </view>
              <view class="quick-nav-row">
                <view
                  class="quick-nav-item"
                  bindtap="onQuickNavTap"
                  data-type="foreign_logistics"
                >
                  <image
                    src="{{COS_CONFIG.url}}wechat/assets/icons/foreign_logistics.png"
                    class="quick-nav-icon"
                    mode="aspectFit"
                  />
                  <text class="quick-nav-text">国外物流</text>
                </view>
              </view>
            </view>
          </swiper-item>


        </swiper>
      </view>

      <!-- 快捷导航占位元素 - 当导航固定时用于占位 -->
      <view
        class="nav-placeholder {{isNavFixed ? 'nav-placeholder-visible' : ''}}"
        style="height: {{navContentHeight}}px;"
      ></view>

      <view class="recommend-section">
        <!-- 热门品牌区域 -->
        <view class="recommend-header">
          <text>热门品牌</text>
          <text
            class="more-cars"
            bindtap="onMoreCarsBrand"
          >更多品牌 ></text>
        </view>
        <view class="hot-brands-section">
          <view class="hot-brands-list">
            <view
              class="hot-brand-item"
              wx:for="{{hotBrands}}"
              wx:key="id"
              bindtap="onBrandTap"
              data-id="{{item.id}}"
              data-name="{{item.name}}"
            >
              <image
                class="hot-brand-icon"
                src="{{item.icon}}"
                mode="aspectFit"
              ></image>
              <text class="hot-brand-name">{{item.name}}</text>
            </view>
          </view>
        </view>

        <!-- 车辆推荐区域 -->
        <view class="recommend-header">
          <text>今日推荐</text>
          <text
            class="more-cars"
            bindtap="onMoreCarsTap"
          >更多好车 ></text>
        </view>
        <view class="recommend-grid">
          <!-- 推荐车辆卡片 -->
          <view
            class="recommend-card"
            wx:for="{{recommendCars}}"
            wx:key="id"
            bindtap="onCarTap"
            data-id="{{item.id}}"
            animation="{{item.animation}}"
          >
            <!-- 卡片主体 -->
            <view class="recommend-card-body">
              <!-- 车辆图片 -->
              <image
                class="recommend-car-image"
                src="{{item.image}}"
                mode="aspectFill"
                lazy-load="{{true}}"
              ></image>

              <!-- 热销/新到标签 -->
              <view class="recommend-tag-label {{index % 2 === 0 ? 'hot' : 'new'}}">
                {{index % 2 === 0 ? '热销' : '新到'}}
              </view>

              <!-- 车辆标题 -->
              <view class="recommend-car-title">
                {{item.title}}
              </view>

              <!-- 车辆参数 -->
              <view class="recommend-car-params">
                <text>{{item.year}}年 | {{item.mileage}}万公里</text>
              </view>

              <!-- 价格信息 -->
              <view class="recommend-price-info">
                <text class="recommend-new-price">新车指导价{{item.originalPrice}}万元</text>
                <text class="recommend-sale-price">{{item.price}}万元</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 添加地区选择弹出层 -->
    <van-popup
      show="{{ showAreaPopup }}"
      position="bottom"
      bind:close="onCloseAreaPopup"
    >
      <!-- 添加全国选项 -->
      <view
        class="all-city"
        bindtap="onSelectAllCity"
      >
        <text>全国</text>
      </view>
      <van-area
        area-list="{{ areaList }}"
        columns-num="{{ 2 }}"
        bind:confirm="onConfirmArea"
        bind:cancel="onCloseAreaPopup"
      />
    </van-popup>

    <!-- 引用登录弹窗模板 -->
    <template
      is="loginPopup"
      data="{{ showLoginPopup, loginPopupOptions }}"
    />
  </view>
</skeleton>