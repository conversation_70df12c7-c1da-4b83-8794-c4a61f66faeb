Page({
    data: {
        index: -1,
        name: '',
        phone: '',
        email: '',
        type: 'staff' // 默认是报价人
    },

    onLoad(options) {
        // 获取编辑的索引
        if (options.index) {
            const index = parseInt(options.index);
            this.setData({
                index: index
            });

            // 获取上一个页面的数据
            const pages = getCurrentPages();
            const prevPage = pages[pages.length - 2];
            const user = prevPage.data.staffList[index];

            this.setData({
                name: user.name,
                phone: user.phone,
                email: user.email || '',
                type: prevPage.data.type || 'staff'
            });

            // 根据类型设置页面标题
            wx.setNavigationBarTitle({
                title: this.data.type === 'customer' ? '编辑客户' : '编辑报价人'
            });
        }
    },

    // 输入事件
    inputChange(e) {
        const field = e.currentTarget.dataset.field;
        const value = e.detail.value;
        const data = {};
        data[field] = value;
        this.setData(data);
    },

    // 取消
    onCancel() {
        wx.navigateBack();
    },

    // 保存
    onSave() {
        // 表单验证
        if (!this.data.name) {
            wx.showToast({
                title: '请输入姓名',
                icon: 'none'
            });
            return;
        }

        if (!this.data.phone) {
            wx.showToast({
                title: '请输入手机号',
                icon: 'none'
            });
            return;
        }

        // 手机号格式验证
        if (!/^1\d{10}$/.test(this.data.phone)) {
            wx.showToast({
                title: '请输入正确的手机号',
                icon: 'none'
            });
            return;
        }

        // 将编辑后的用户更新到select页面的列表中
        const pages = getCurrentPages();
        const prevPage = pages[pages.length - 2]; // select页面

        const editedUser = {
            name: this.data.name,
            phone: this.data.phone,
            email: this.data.email || ''
        };

        // 更新select页面的数据
        prevPage.updateUser(this.data.index, editedUser);

        wx.navigateBack();
    }
}) 