// 修改引入方式
import { areaList } from '@vant/area-data';
// 导入 API 模块
import api from '../../utils/api';
// 导入util工具类
import util from '../../utils/util';

import share from '../../utils/share';  // 确保导入 分享

Page({
    behaviors: [share], //分享设置
    data: {
        //处理分享页面 统一写
        shareData: {},
        id:0,
        email:'-',
        company_name:'-',
        remark:'-',
        result_item:'-',
        classes:'-',
        province:'-',
        city:'-',
        trailer_type: '-',
        taketime:'-',
        charge:'-',
        cost:'-',
        // 骨架屏相关
        isLoading: true, // 默认加载中状态
        showLoadingAnimation: false, // 默认显示骨架屏而非加载动画
    },
    async onLoad(options) {
        const { id } = options;
        this.setData({ id })
        this.getInfo(id);
        // 设置初始加载状态
        this.setData({
            isLoading: true,
            showLoadingAnimation: false
        });

        // 获取顶部容器高度
        const query = wx.createSelectorQuery();
        query.select('.search-bar').boundingClientRect();
        query.exec((res) => {
            if (res && res[0]) {
                this.setData({
                    topContainerHeight: res[0].height
                });
            }
        });

        // 获取系统状态栏高度
        wx.getSystemInfo({
            success: (res) => {
                this.setData({
                    statusBarHeight: res.statusBarHeight
                });
                // console.log('状态栏高度：', res.statusBarHeight);
            }
        });

        try {
            // 数据加载完成后，设置3秒延迟隐藏骨架屏
            setTimeout(() => {
                this.setData({
                    isLoading: false,
                    isFirstLoad: false // 标记首次加载完成
                });
            }, 3000);
        } catch (error) {
            console.error('数据加载失败:', error);
            // 加载失败时也设置3秒延迟隐藏骨架屏
            setTimeout(() => {
                this.setData({
                    isLoading: false,
                    isFirstLoad: false // 标记首次加载完成
                });
            }, 3000);
        }

        // 在 onLoad 或 onReady 函数中
        wx.getSystemInfo({
            success: (res) => {
                const topContainerHeight = this.data.topContainerHeight || 50;
                const filterMenuHeight = 44; // 根据您的实际筛选菜单高度调整

                // 设置 CSS 变量
                this.setData({
                    headerStyle: `--top-container-height: ${topContainerHeight}px; --filter-menu-height: ${filterMenuHeight}px;`
                });

                // 更新页面样式
                this.setData({
                    pageStyle: this.data.pageStyle + this.data.headerStyle
                });
            }
        });
    },
    // 分享按钮点击事件
    onShareTap() {
        wx.showShareMenu({
            withShareTicket: true,
            menus: ['shareAppMessage', 'shareTimeline']
        });
    },
    // 更多按钮点击事件
    onMoreTap() {
        wx.showActionSheet({
            itemList: ['筛选', '排序', '收藏'],
            success(res) {
                console.log(res.tapIndex);
            }
        });
    },
    // 下拉菜单打开事件
    onDropdownOpen(e) {
        // 获取当前打开的下拉菜单索引
        const index = e.currentTarget.dataset.index;

        // 设置当前打开的下拉菜单索引
        this.setData({
            isDropdownOpen: true,
            pageStyle: 'overflow: hidden; height: 100vh;',
            currentDropdownIndex: index
        });

        // 关闭其他下拉菜单
        const dropdownItems = this.selectAllComponents('.van-dropdown-item');
        dropdownItems.forEach((item, i) => {
            if (i !== index && item.data.showPopup) {
                item.toggle(false);
            }
        });
    },
    // 下拉菜单关闭事件
    onDropdownClose() {
        this.setData({
            isDropdownOpen: false,
            pageStyle: ''
        });
    },
    // 锁定页面滚动
    lockPage() {
        // 获取页面根元素
        const pageContainer = document.querySelector('.container');
        if (pageContainer) {
            pageContainer.style.overflow = 'hidden';
            pageContainer.style.height = '100vh';
        }
    },
    // 解锁页面滚动
    unlockPage() {
        // 获取页面根元素
        const pageContainer = document.querySelector('.container');
        if (pageContainer) {
            pageContainer.style.overflow = '';
            pageContainer.style.height = '';
        }
    },
    // 阻止页面滚动
    preventScroll() {
        return false;
    },
    // 添加返回按钮功能
    navigateBack() {
      wx.navigateBack({
          delta: 1
      });
    },
    //金融服务详情
    async getInfo(id) {
      try {
        wx.showLoading({
          title: '加载中...',
        });
        
        console.log(id)
        const info = await api.foreignLogistics.getInfo(id);
        const shareData = {
          title: info.company_name || '暂无标题',
          path: '/pages/foreign_logistics/foreign_logistics_info?id=' + id, // 分享路径，默认为当前页面路径
          imageUrl: "", // 分享图片（可选）
          isDetailPage: true, // 标记是否详情页
        }
        this.setData({ shareData })
        let classesText = {1:'海运',2:'班列',3:'拖车'};
        let trailerTypeText = {1:'滚装',2:'柜装',3:'笼车',4:'拖车'};
        // 设置页面数据
        this.setData({
          company_name: info.company_name || '-',
          email:info.email || '-',
          phone:info.mobile || '-',
          remark:info.remark || '-',
          classesText:classesText[info.classes] || '-',
          start_place:info.start_place || '-',
          end_place:info.end_place || '-',
          trailerTypeText:trailerTypeText[info.trailer_type] || '-',
          taketime:info.taketime || '-',
          charge:info.charge || '-',
          cost:info.cost || '-',
          loading: false
        }, () => {
          wx.hideLoading();
        });
      } catch (error) {
        console.error('获取车辆详情失败', error);
        this.setData({
          loading: false,
          carImages: []
        });
        wx.hideLoading();
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      }
    },
    onHide: function() {
     console.log('离开找车页面了')
    },
}) 