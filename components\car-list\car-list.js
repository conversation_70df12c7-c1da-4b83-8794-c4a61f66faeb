Component({
    properties: {
        title: {
            type: String,
            value: '特价清仓'
        },
        cars: {
            type: Array,
            value: [
                {
                    id: 1,
                    image: '/assets/images/car1.jpg',
                    name: '沃尔沃 S90 2019款',
                    price: '10.6',
                    tag: '性价比高'
                },
                {
                    id: 2,
                    image: '/assets/images/car2.jpg',
                    name: '2015款 玛莎拉蒂',
                    price: '21.68'
                }
            ]
        }
    },
    methods: {
        onMoreTap() {
            wx.navigateTo({
                url: '/pages/car-list/index'
            })
        },
        onCarTap(e) {
            const { id } = e.currentTarget.dataset
            wx.navigateTo({
                url: `/pages/car-detail/index?id=${id}`
            })
        }
    }
}) 