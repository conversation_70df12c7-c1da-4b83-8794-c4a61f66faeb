// pages/article/draft.js
import api from '../../utils/api';
import util from '../../utils/util';

Page({

  /**
   * 页面的初始数据
   */
  data: {
    draftList: [],
    page: 1,
    pageSize: 15,
    total: 0,
    noMore: false,
    hasOpenMenu: false,
    isManageMode: false,  // 是否处于管理模式
    isAllSelected: false, // 是否全选
    selectedCount: 0,     // 已选中的数量
    statusBarHeight: 0    // 状态栏高度
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取系统信息设置状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });

    // onLoad中不调用接口，避免与onShow重复调用
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    wx.setNavigationBarTitle({
      title: '草稿箱'
    });

    // 页面显示时重置页码并获取数据
    this.setData({
      page: 1
    });
    this.getDraftList();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.setData({
      page: 1
    });
    this.getDraftList(false, true);
  },

  /**
   * 获取草稿列表
   */
  getDraftList(isLoadMore = false, isPullDownRefresh = false) {
    wx.showLoading({
      title: '加载中...',
    });

    const params = {
      page: this.data.page,
      limit: this.data.pageSize,
      app_id: util.getAppId(), // 固定参数
    };

    api.moments.draftList(params).then(res => {
      // console.log('原始接口返回:', res);

      if (res) {
        let draftList = [];
        let resData = res.data || [];

        // 调试输出数据
        // console.log('接口返回的草稿数据:', resData);

        if (isLoadMore) {
          // 加载更多
          draftList = [...this.data.draftList, ...resData];
        } else {
          // 首次加载或下拉刷新
          draftList = [...resData];
        }

        // console.log('处理后的草稿列表数据:', draftList);

        // 处理每个草稿项
        draftList = draftList.map(item => {
          return {
            ...item,
            selected: false,      // 选中状态
            is_draft: true,       // 草稿标识
            showActions: false    // 操作菜单状态
          };
        });

        // 更新到页面
        this.setData({
          draftList: draftList,
          total: res.total || 0,
          page: res.current_page || 1,
          noMore: (res.current_page || 1) >= (res.last_page || 1)
        });

        // console.log('更新后的页面数据:', this.data);

        // 重新计算选中数量
        this.countSelectedItems();
      } else {
        wx.showToast({
          title: '获取草稿列表失败',
          icon: 'none'
        });
      }

      // 隐藏加载提示
      wx.hideLoading();

      if (isPullDownRefresh) {
        wx.stopPullDownRefresh();
      }
    }).catch(err => {
      console.error('获取草稿列表失败', err);
      wx.showToast({
        title: '网络异常，请稍后重试',
        icon: 'none'
      });

      // 确保在错误情况下也隐藏加载提示
      wx.hideLoading();

      if (isPullDownRefresh) {
        wx.stopPullDownRefresh();
      }
    });
  },

  /**
   * 切换显示更多操作菜单
   */
  toggleMoreActions(e) {
    const index = e.currentTarget.dataset.index;
    const draftList = this.data.draftList;

    // 关闭所有其他打开的菜单
    draftList.forEach((item, idx) => {
      if (idx !== index && item.showActions) {
        item.showActions = false;
      }
    });

    // 切换当前菜单状态
    draftList[index].showActions = !draftList[index].showActions;

    // 更新是否有打开的菜单状态
    const hasOpenMenu = draftList.some(item => item.showActions);

    this.setData({
      draftList,
      hasOpenMenu
    });
  },


  onEditTap(e) {
    const id = e.currentTarget.dataset.id;
    // 模拟跳转到编辑页面
    // console.log('编辑帖子ID:', id);
    wx.navigateTo({
      url: `/pages/article/edit?id=${id}&fromDraft=true`
    });
  },

  /**
   * 单个删除
   */
  onDeleteTap(e) {
    const { id, index } = e.currentTarget.dataset;
    wx.showModal({
      title: '提示',
      content: '确定要删除这篇草稿吗？',
      success: (res) => {
        if (res.confirm) {
          this.deleteDraft([id], index);
        }
      }
    });
  },

  /**
   * 删除草稿(单个或批量)
   * @param {Array} ids 需要删除的草稿ID数组
   * @param {Number} singleIndex 单个删除时的索引（可选）
   */
  deleteDraft(ids, singleIndex) {
    if (!ids || ids.length === 0) {
      return;
    }

    wx.showLoading({
      title: '删除中...'
    });

    // 判断是单个删除还是批量删除
    if (ids.length === 1) {
      // 单个删除
      api.moments.delete({
        id: ids[0],        // 传递单个id，不是数组
        is_delete: 1       // 显式添加is_delete参数
      }).then(res => {
        this.handleDeleteSuccess(ids, singleIndex);
      }).catch(err => {
        this.handleDeleteError(err);
      });
    } else {
      // 批量删除 - 需要逐个删除
      let promises = [];

      // 为每个ID创建一个删除请求
      ids.forEach(id => {
        const promise = api.moments.delete({
          id: id,          // 每次只发送一个ID
          is_delete: 1
        });
        promises.push(promise);
      });

      // 使用Promise.all等待所有请求完成
      Promise.all(promises)
        .then(results => {
          this.handleDeleteSuccess(ids, singleIndex);
        })
        .catch(err => {
          this.handleDeleteError(err);
        });
    }
  },

  /**
   * 处理删除成功的情况
   */
  handleDeleteSuccess(ids, singleIndex) {
    wx.hideLoading();

    // 本地更新列表
    let draftList = [...this.data.draftList];

    if (singleIndex !== undefined) {
      // 单个删除
      draftList.splice(singleIndex, 1);
    } else {
      // 批量删除
      draftList = draftList.filter(item => !ids.includes(item.id));
    }

    this.setData({
      draftList,
      total: this.data.total - (singleIndex !== undefined ? 1 : ids.length),
      selectedCount: 0,
      isAllSelected: false
    });

    wx.showToast({
      title: '删除成功',
      icon: 'success'
    });
  },

  /**
   * 处理删除错误的情况
   */
  handleDeleteError(err) {
    wx.hideLoading();
    console.error('删除失败', err);
    wx.showToast({
      title: '删除失败，请重试',
      icon: 'none'
    });
  },

  /**
   * 关闭所有菜单
   */
  closeAllMenus() {
    const draftList = this.data.draftList;

    // 关闭所有菜单
    draftList.forEach(item => {
      item.showActions = false;
    });

    this.setData({
      draftList,
      hasOpenMenu: false
    });
  },

  /**
   * 切换管理模式
   */
  toggleManageMode() {
    const isManageMode = !this.data.isManageMode;

    // 退出管理模式时，清除所有选中状态
    if (!isManageMode) {
      const draftList = this.data.draftList.map(item => {
        item.selected = false;
        return item;
      });

      this.setData({
        draftList,
        isManageMode,
        isAllSelected: false,
        selectedCount: 0
      });
    } else {
      this.setData({ isManageMode });
    }
  },

  /**
   * 切换选中状态
   */
  toggleSelectItem(e) {
    const index = e.currentTarget.dataset.index;
    const draftList = this.data.draftList;

    // 切换选中状态
    draftList[index].selected = !draftList[index].selected;

    this.setData({ draftList });

    // 更新选中数量和全选状态
    this.countSelectedItems();
  },

  /**
   * 切换全选状态
   */
  toggleSelectAll() {
    const isAllSelected = !this.data.isAllSelected;
    const draftList = this.data.draftList.map(item => {
      item.selected = isAllSelected;
      return item;
    });

    this.setData({
      draftList,
      isAllSelected,
      selectedCount: isAllSelected ? draftList.length : 0
    });
  },

  /**
   * 计算选中的数量
   */
  countSelectedItems() {
    const draftList = this.data.draftList;
    const selectedCount = draftList.filter(item => item.selected).length;
    const isAllSelected = selectedCount === draftList.length && draftList.length > 0;

    this.setData({
      selectedCount,
      isAllSelected
    });
  },

  /**
   * 批量删除
   */
  onBatchDeleteTap() {
    const selectedCount = this.data.selectedCount;

    if (selectedCount === 0) {
      return; // 没有选中任何草稿，不执行操作
    }

    wx.showModal({
      title: '提示',
      content: `确定要删除选中的${selectedCount}篇草稿吗？`,
      success: (res) => {
        if (res.confirm) {
          // 获取选中的草稿ID数组
          const selectedIds = this.data.draftList
            .filter(item => item.selected)
            .map(item => item.id);

          // 调用删除方法
          this.deleteDraft(selectedIds);
        }
      }
    });
  },

  /**
   * 点击发布按钮
   */
  onPublishTap(e) {
    const { id, index } = e.currentTarget.dataset;
    const item = this.data.draftList[index];

    wx.showModal({
      title: '提示',
      content: '确定发布此帖子吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '发布中...'
          });

          // 调用编辑帖子接口执行发布操作
          api.moments.edit({
            id: id,
            is_draft: 0 // 将草稿状态改为非草稿(发布状态)
          }).then(res => {
            wx.hideLoading();
            wx.showToast({
              title: '发布成功',
              icon: 'success',
              duration: 2000,
              success: () => {
                // 更新全局变量，标记需要刷新相关列表
                getApp().globalData = getApp().globalData || {};
                getApp().globalData.needRefreshMomentsList = true;
                getApp().globalData.needRefreshArticleList = true;

                // 从列表中移除已发布的草稿
                const draftList = this.data.draftList.filter((item, idx) => idx !== index);
                this.setData({
                  draftList,
                  total: this.data.total - 1
                });

                // 重新计算选中数量
                this.countSelectedItems();
              }
            });
          }).catch(err => {
            wx.hideLoading();
            console.error('发布失败', err);
            wx.showToast({
              title: '发布失败，请重试',
              icon: 'none'
            });
          });
        }
      }
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (this.data.noMore) return;

    this.setData({
      page: this.data.page + 1
    });

    this.getDraftList(true);
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 返回上一页
   */
  navigateBack() {
    wx.navigateBack({
      delta: 1
    });
  },
})