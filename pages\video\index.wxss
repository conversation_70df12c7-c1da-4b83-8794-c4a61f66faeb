/* pages/video/index.wxss */

page {
  padding: 0;
  margin: 0;
  background-image: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
  background-attachment: fixed;
  background-size: cover;
  overflow-x: hidden;
  min-height: 100vh;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.video-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  box-sizing: border-box;
  overflow: hidden;
}

/* .video-container {
  padding: 0;
  margin: 0;
  background-image: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
  background-attachment: fixed;
  background-size: cover;
  overflow-x: hidden;
  min-height: 100vh;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
} */

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 100%);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.status-bar {
  width: 100%;
}

.navbar-content {
  display: flex;
  height: 44px;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
}

.nav-back {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 44px;
  z-index: 5;
  margin-left: -8rpx;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 34rpx;
  font-weight: 500;
  color: #333;
}

.nav-placeholder {
  width: 40px;
  height: 44px;
}

/* Channel Info */
.channel-info {
  display: flex;
  padding: 30rpx 30rpx 0rpx 30rpx;
  margin-top: 180rpx;
}

.channel-avatar {
  width: 100rpx;
  height: 100rpx;
  margin-right: 20rpx;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.channel-avatar image {
  width: 100%;
  height: 100%;
}

.channel-details {
  flex: 1;
}

.channel-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
  min-height: 42rpx;
  display: block;
}

.channel-company {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
}

.auth-icon {
  width: 28rpx;
  height: 28rpx;
  margin-left: 8rpx;
  margin-right: 6rpx;
}

.verify-icon {
  color: #4080ff;
  margin-left: 6rpx;
  font-size: 32rpx;
}

.channel-description {
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
  padding-left: 30rpx;
  padding-top: 16rpx;
}

.video-count {
  font-size: 26rpx;
  color: #999;
  padding: 20rpx 30rpx;
}

/* Tab Navigation */
.tabs-container {
  background-color: #fff;
  margin: 0;
  position: relative;
  margin: 0 16rpx;
  border-top-right-radius: 16rpx;
  border-top-left-radius: 16rpx;
}

.tab-container {
  position: relative;
  display: flex;
  justify-content: center;
  padding: 0;
}

.tab-wrapper {
  flex: 1;
  display: flex;
  width: 400rpx;
  padding-left: 18rpx;
  padding-right: 18rpx;
  position: relative;
  justify-content: space-between;
  margin: 0 auto;
}

.tab {
  position: relative;
  padding: 20rpx 0;
  width: 140rpx;
  text-align: center;
  font-size: 32rpx;
  color: #666;
  transition: color 0.3s;

}

.tab.active {
  color: #3B82F6;
  /* font-weight: 500; */
}

.tab-line {
  position: absolute;
  bottom: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #3B82F6 40%, #69AEF9 70%, #EFF5FF 100%);
  border-radius: 3rpx;
  transition: all 0.3s ease;
  width: 140rpx;
}

.tabs-divider {
  height: 1px;
  background-color: #eee;
  width: 100%;
  margin: 0;
}

/* Video Grid */
.video-grid {
  padding: 20rpx;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  background: #fff;
  margin: 0 16rpx;
  border-bottom-right-radius: 16rpx;
  border-bottom-left-radius: 16rpx;
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
}

.video-card {
  width: 330rpx;
  margin-right: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  position: relative;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  margin-bottom: 32rpx;
}

.video-card-more {
  margin-bottom: 48rpx !important;
}

.video-card-less {
  margin-bottom: -350rpx !important;
}

/* 每行第2个元素（也就是每行最后一个）不需要右边距 */
.video-card:nth-child(2n) {
  margin-right: 0;
}

/* 添加整个卡片的遮罩层 */
.video-card.locked {
  position: relative;
}

.video-card.locked::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 2;
  pointer-events: none;
  border-radius: 12rpx;
}

.video-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.video-thumbnail {
  position: relative;
  width: 100%;
  height: 240rpx;
  background-color: #444b8f;
  overflow: hidden;
}

.series-tag {
  position: absolute;
  left: 0;
  top: 12rpx;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 18rpx;
  padding: 4rpx 12rpx;
  z-index: 2;
  border-radius: 0 4rpx 4rpx 0;
}

.video-thumbnail image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.view-count-badge {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  padding: 4rpx 16rpx;
  background: rgba(0, 0, 0, 0.3);
  color: #fff;
  font-size: 20rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2);
}

.like-icon {
  width: 20rpx !important;
  height: 20rpx !important;
  margin-right: 8rpx;
}

.video-badge {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  padding: 4rpx 12rpx;
  background-color: #4080ff;
  color: #fff;
  font-size: 20rpx;
  border-radius: 8rpx;
  font-weight: 500;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  z-index: 3;
}

.video-badge.member {
  background-color: #ff7043;
  z-index: 3;
}

.lock-icon {
  position: absolute;
  top: 120rpx;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  /* background-color: rgba(255, 255, 255, 0.2); */
  border-radius: 50%;
  padding: 8rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
  z-index: 3;
}

.lock-icon image {
  width: 48rpx;
  height: 48rpx;
}

.video-title {
  padding: 12rpx 12rpx 0rpx 12rpx;
  font-size: 24rpx;
  color: #333;
  line-height: 48rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  height: 96rpx; /* 2行的高度 */
  /* font-weight: 500; */
}

.video-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12rpx 8rpx;
  /* border-top: 1px solid rgba(0,0,0,0.03); */
  margin-top: 15rpx;
}

.update-time {
  font-size: 20rpx;
  color: #999;
  /* margin-bottom: 12rpx; */
}

.view-count {
  font-size: 16rpx;
  color: #999;
  display: flex;
  align-items: center;
}

.view-count .iconfont {
  margin-right: 6rpx;
  font-size: 24rpx;
}

/* For iconfont icons */
@font-face {
  font-family: "iconfont";
  src: url('data:font/woff2;charset=utf-8;base64,BASE64CONTENTWILLBEHERE') format('woff2');
}

.iconfont {
  font-family: "iconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-back:before {
  content: "\e697";
}

.icon-verified:before {
  content: "\e6cc";
}

.icon-view:before {
  content: "\e63d";
}

/* 空数据提示样式 */
.empty-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载更多样式 */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
  width: 100%;
}

.loading-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #3B82F6;
  margin: 0 6rpx;
  animation: loading-bounce 1.4s infinite ease-in-out both;
}

.loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes loading-bounce {

  0%,
  80%,
  100% {
    transform: scale(0);
  }

  40% {
    transform: scale(1);
  }
}

.loading-more text {
  font-size: 24rpx;
  color: #999;
  margin-left: 10rpx;
}

/* 没有更多数据样式 */
.no-more {
  width: 100%;
  text-align: center;
  padding: 30rpx 0;
}

.no-more text {
  font-size: 24rpx;
  color: #999;
  position: relative;
}

/* 会员弹窗样式 */
.member-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.member-dialog {
  width: 550rpx;
  background-color: #eef3ff;
  border-radius: 24rpx;
  overflow: hidden;
  position: relative;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

.member-dialog-content {
  padding: 40rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.member-dialog-title {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
  text-align: center;
}

.member-dialog-subtitle {
  font-size: 32rpx;
  color: #3D3D3D;
  font-weight: 500;
  margin-bottom: 30rpx;
  text-align: center;
}

.member-icon-container {
  width: 140rpx;
  height: 140rpx;
  margin: 10rpx auto 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.member-diamond-icon {
  width: 100%;
  height: 100%;
}

/* 首次开通优惠样式 */
.first-open-discount {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 0 20rpx;
  width: 100%;
}

.discount-line {
  width: 104rpx;
  height: 1rpx;
  background-color: #82B0FA;
}

.star-icon {
  color: #4b7bfe;
  font-size: 24rpx;
  margin: 0 10rpx;
}

.discount-text {
  color: #4b7bfe;
  font-size: 22rpx;
}

.member-benefits {
  width: 100%;
  margin: 20rpx 0 40rpx;
  display: flex;
  justify-content: space-around;
}

.benefit-item {
  font-size: 22rpx;
  color: #3B82F6;
  padding: 8rpx 0;
  text-align: center;
  position: relative;
}

.member-dialog-buttons {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: 10rpx;
}

.btn-cancel,
.btn-confirm {
  width: 45%;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 30rpx;
  padding: 0;
  margin: 0;
  line-height: 80rpx;
}

.btn-cancel {
  background-color: #eef3ff;
  color: #3B82F6;
  border: 1rpx solid #3B82F6;
}

.btn-confirm {
  background-color: #4b7bfe;
  color: white;
  border: none;
}

.close-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 48rpx;
  height: 48rpx;
  line-height: 48rpx;
  text-align: center;
  font-size: 60rpx;
  padding-bottom: 8rpx;
  padding-left: 8rpx;
  padding-right: 8rpx;
  color: #999;
  z-index: 10;
  background: rgba(255,255,255,0.8);
  border-radius: 50%;
  cursor: pointer;
}