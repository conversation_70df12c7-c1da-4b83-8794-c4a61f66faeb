/* pages/train_operation/appendix.wxss */
page {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: fixed;
    -webkit-overflow-scrolling: touch;
}

.container {
    min-height: 100vh;
    height: 100%;
    background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    overflow: hidden;
}

/* 自定义导航栏样式 */
.custom-nav {
    width: 100%;
    background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
}

.nav-content {
    height: 44px;
    display: flex;
    align-items: center;
    position: relative;
}

.back-icon {
    position: absolute;
    left: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10rpx;
}

.nav-title {
    flex: 1;
    text-align: center;
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
}

/* 主内容区域 */
.main-content {
    padding-left: 0;
    padding-right: 0;
    position: relative;
    z-index: 10;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    overflow: auto;
    background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
}

/* 信息卡片样式 */
.appendix-info {
    display: flex;
    flex-direction: column;
    padding: 30rpx;
    margin-bottom: 20rpx;
}

.info-card {
    background-color: #ffffff;
    border-radius: 12rpx;
    padding: 24rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.info-title {
    font-size: 28rpx;
    color: #888;
    margin-bottom: 12rpx;
}

.info-content {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
}

/* 内容区域样式 */
.appendix-content {
    padding: 30rpx;
    background-color: #ffffff;
    margin: 0 30rpx 30rpx;
    border-radius: 12rpx;
    min-height: 400rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

/* 上传卡片样式 */
.upload-card {
    width: 100%;
    background-color: #ffffff;
    border-radius: 12rpx;
    padding: 20rpx;
    box-sizing: border-box;
}

.card-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 30rpx;
    padding-left: 10rpx;
}

/* 上传区域样式 */
.upload-section {
    margin-bottom: 30rpx;
}

.upload-title {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 20rpx;
    padding-left: 10rpx;
}

.upload-area {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin-bottom: 10rpx;
}

.upload-button {
    width: 140rpx;
    height: 140rpx;
    border: 2rpx dashed #ddd;
    border-radius: 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20rpx;
    margin-bottom: 20rpx;
    background-color: #f9f9f9;
}

.upload-limit {
    font-size: 24rpx;
    color: #999;
    padding-left: 10rpx;
}

/* 文件卡片样式 */
.file-cards {
    display: flex;
    flex-wrap: wrap;
}

.file-card {
    width: 140rpx;
    margin-right: 20rpx;
    margin-bottom: 20rpx;
}

.file-card-content {
    width: 140rpx;
    height: 140rpx;
    background-color: #f5f5f5;
    border-radius: 8rpx;
    overflow: hidden;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.file-icon {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f0f5ff;
}

.file-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.file-delete {
    position: absolute;
    top: 0;
    right: 0;
    width: 40rpx;
    height: 40rpx;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 0 0 0 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.file-name {
    font-size: 22rpx;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-top: 10rpx;
    text-align: center;
}

/* 备注说明区域样式 */
.remark-section {
    margin-bottom: 40rpx;
}

.remark-title {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 20rpx;
    padding-left: 10rpx;
}

.remark-input {
    width: 100%;
    height: 200rpx;
    background-color: #f9f9f9;
    border: 1rpx solid #eee;
    border-radius: 8rpx;
    padding: 20rpx;
    font-size: 28rpx;
    box-sizing: border-box;
}

/* 按钮组样式 */
.button-group {
    display: flex;
    justify-content: space-between;
    margin-top: 30rpx;
}

.btn-save,
.btn-submit {
    flex: 1;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    border-radius: 40rpx;
    font-size: 28rpx;
    padding: 0;
}

.btn-save {
    background-color: #f5f5f5;
    color: #666;
    margin-right: 20rpx;
}

.btn-submit {
    background-color: #3B82F6;
    color: #ffffff;
}

/* iOS底部安全区域适配 */
@supports (padding-bottom: constant(safe-area-inset-bottom)) {
    .container {
        padding-bottom: constant(safe-area-inset-bottom);
    }
}

@supports (padding-bottom: env(safe-area-inset-bottom)) {
    .container {
        padding-bottom: env(safe-area-inset-bottom);
    }
}