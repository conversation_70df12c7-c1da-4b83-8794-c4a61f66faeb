.skeleton {
  width: 100%;
  background-color: #fff;
  overflow: hidden;
  position: relative;
  z-index: 100;
}

/* 基础骨架元素 */
.sk-nav, .sk-search, .sk-banner, .sk-nav-grid, .sk-section-header, 
.sk-brands, .sk-car-list, .sk-car-item, .sk-car-img, .sk-car-title, 
.sk-car-info, .sk-car-price, .sk-brand-item, .sk-brand-icon, 
.sk-brand-name, .sk-nav-item, .sk-nav-icon, .sk-nav-text, 
.sk-title, .sk-city, .sk-input, .sk-btn, .sk-section-title, .sk-more {
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.4s ease infinite;
  border-radius: 4px;
}

/* 导航栏骨架 */
.sk-nav {
  height: 44px;
  padding: 10px 15px;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.sk-title {
  width: 80px;
  height: 20px;
}

/* 搜索栏骨架 */
.sk-search {
  height: 36px;
  margin: 10px 15px;
  display: flex;
  align-items: center;
}

.sk-city {
  width: 50px;
  height: 20px;
  margin-right: 10px;
}

.sk-input {
  flex: 1;
  height: 36px;
  border-radius: 18px;
}

.sk-btn {
  width: 60px;
  height: 36px;
  margin-left: 10px;
  border-radius: 18px;
}

/* 轮播图骨架 */
.sk-banner {
  height: 150px;
  margin: 10px 15px;
  border-radius: 8px;
}

/* 导航菜单骨架 */
.sk-nav-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 10px;
  justify-content: space-between;
}

.sk-nav-item {
  width: 20%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 15px;
}

.sk-nav-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-bottom: 8px;
}

.sk-nav-text {
  width: 50px;
  height: 16px;
}

/* 区域标题骨架 */
.sk-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 15px;
  height: 24px;
}

.sk-section-title {
  width: 100px;
  height: 20px;
}

.sk-more {
  width: 60px;
  height: 18px;
}

/* 品牌骨架 */
.sk-brands {
  display: flex;
  overflow-x: auto;
  padding: 10px 15px;
}

.sk-brand-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 20px;
}

.sk-brand-icon {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  margin-bottom: 8px;
}

.sk-brand-name {
  width: 40px;
  height: 16px;
}

/* 车辆列表骨架 */
.sk-car-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  padding: 0 15px;
}

.sk-car-item {
  background: transparent;
  overflow: hidden;
  border-radius: 8px;
  margin-bottom: 15px;
  display: flex;
  flex-direction: column;
}

.sk-car-img {
  height: 120px;
  border-radius: 8px;
  margin-bottom: 8px;
}

.sk-car-title {
  height: 18px;
  margin-bottom: 8px;
  width: 90%;
}

.sk-car-info {
  height: 16px;
  margin-bottom: 8px;
  width: 70%;
}

.sk-car-price {
  height: 20px;
  width: 60%;
}

/* 骨架屏闪烁动画 */
@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
} 