<!--pages/set/name.wxml-->
<view class="container">
  <!-- 固定头部区域 -->
  <view class="fixed-header">
    <!-- 顶部状态栏 -->
    <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>
    
    <!-- 导航栏 -->
    <view class="nav-bar">
      <view class="nav-back" bindtap="goBack">
        <van-icon name="arrow-left" size="20px" color="#333" />
      </view>
      <view class="nav-title">昵称修改</view>
      <view class="nav-placeholder"></view>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="main-content" style="margin-top: {{statusBarHeight + 44}}px;">
    <view class="name-input-box">
      <view class="input-wrapper">
        <input type="nickname" value="{{nickname}}" bindinput="onNicknameInput" placeholder="请输入昵称" focus />
        <van-icon wx:if="{{nickname.length > 0}}" name="clear" size="16px" color="#999" class="clear-icon" bindtap="clearInput" />
      </view>
    </view>
    
    <view class="save-btn" bindtap="saveNickname">保存</view>
  </view>
</view>