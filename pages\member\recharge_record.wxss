/* pages/member/recharge_record.wxss */
page {
  padding: 0;
  margin: 0;
  background-image: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
  background-attachment: fixed;
  background-size: cover;
  overflow-x: hidden;
  min-height: 100vh;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.recharge-record-container {
  padding: 0;
  margin: 0;
  width: 100%;
  min-height: 100vh;
  position: relative;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 100%);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.status-bar {
  width: 100%;
}

.navbar-content {
  display: flex;
  height: 44px;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
}

.nav-back {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 44px;
  z-index: 5;
  margin-left: -8rpx;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 34rpx;
  font-weight: 500;
  color: #333;
}

.nav-placeholder {
  width: 40px;
  height: 44px;
}

/* 内容区域 */
.content-area {
  padding: 20rpx;
  width: 100%;
  box-sizing: border-box;
  position: relative;
}

/* 会员信息 */
.member-info {
  margin-top: 30rpx;
  margin-bottom: 30rpx;
  display: flex;
  flex-direction: column;
}

.member-header {
  display: flex;
  align-items: center;
  /* background-color: #f5f7fa; */
  padding: 20rpx;
  border-radius: 12rpx;
}

.company-logo {
  width: 80rpx;
  height: 80rpx;
  /* border-radius: 50%; */
  margin-right: 20rpx;
}

.company-info {
  flex: 1;
}

.company-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  /* margin-bottom: 10rpx; */
}

.member-status {
  display: flex;
  align-items: center;
  margin-top: -20rpx;
}


.expire-info {
  font-size: 24rpx;
  color: #666;
}

/* 充值标题 */
.recharge-title {
  font-size: 32rpx;
  font-weight: bold;
  margin: 40rpx 0 30rpx 20rpx;
  color: #333;
}

/* 充值记录列表 */
.recharge-list {
  margin-bottom: 40rpx;
}

.recharge-item {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 优化过期卡片的置灰效果 */
.expired-card {
  filter: grayscale(100%);
  opacity: 0.8;
  will-change: transform;
  /* 提示浏览器该元素会有变化 */
}

/* 优化状态标签 */
.status-tag {
  width: 100rpx;
  height: 35rpx;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  /* 优化iOS渲染 */
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  -webkit-font-smoothing: antialiased;
  will-change: transform;
  perspective: 1000;
  backface-visibility: hidden;
}

/* 优化在线状态标签 */
.active {
  color: #4080ff;
  border: none;
}

/* 优化过期状态标签 */
.expired {
  color: #6B7280;
  border: none;
  position: relative;
}

/* 为已过期卡片中的过期标签特殊处理 */
.expired-card .expired {
  /* 移除边框和阴影 */
}

/* 移除之前添加的伪元素边框 */
.expired:after {
  display: none;
}

.item-header {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.top-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 20rpx;
}

.member-icon {
  width: 44rpx;
  height: 44rpx;
  flex-shrink: 0;
}

.member-icon image {
  width: 100%;
  height: 100%;
}

.content-container {
  padding-left: 0;
  width: 100%;
}

.member-type {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  margin-left: 16rpx;
}

.package-row {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.validity-period {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.item-privileges {
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
}

/* 为空状态提示添加样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  margin: 30rpx 0;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.empty-icon image {
  width: 100%;
  height: 100%;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  text-align: center;
}

/* 添加待支付卡片样式 */
.payable-card {
  position: relative;
}

.payable-card::after {
  display: none;
}

.status-tag.pending {
  background-color: transparent;
  color: #3B82F6;
  border: none;
}

.pay-hint {
  margin-top: 10px;
  color: #3894FF;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* 倒计时样式 */
.countdown-badge {
  display: none;
  /* 其他样式保持不变，但已被隐藏 */
  position: relative;
  margin-right: 10rpx;
  background: linear-gradient(135deg, #ff9500, #ff5e3a);
  color: #fff;
  font-size: 22rpx;
  padding: 6rpx 16rpx;
  border-radius: 8rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 8rpx rgba(255, 94, 58, 0.3);
  align-items: center;
  justify-content: center;
  z-index: 10;
  max-width: 120rpx;
  white-space: nowrap;
}

/* 倒计时图标 */
.countdown-icon {
  margin-right: 6rpx;
  font-size: 24rpx;
}

/* 订单操作按钮样式 */
.order-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
  gap: 10px;
}

.pay-button,
.cancel-button {
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  text-align: center;
  flex: none;
}

.pay-button {
  background-color: #07c160;
  color: white;
}

.cancel-button {
  background-color: #f8f8f8;
  color: #ff4d4f;
  border: 1rpx solid #ff4d4f;
}