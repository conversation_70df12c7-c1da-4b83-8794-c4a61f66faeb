// app.js
import api from '/utils/api';
import util from '/utils/util';

App({
  onLaunch() {
    // 初始化扩展能力
    // if (wx.miniapp) {
    //   console.log('miniapp API 可用');
    // } else {
    //   console.log('miniapp API 不可用');
    // }

    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    //session_key存储的时间失效或者登录存储的时间失效
    if (!util.getCacheWithExpiry('session_key')) {
      // 登录
      wx.login({
        success: res => {
          // 发送 res.code 到后台换取 openId, sessionKey, unionId
          if (res.code) {
            api.user.getWechatInfo({
              code: res.code
            }).then(result => {
              util.setCacheWithExpiry('openid', result.openid, 1, "hours");
              util.setCacheWithExpiry('session_key', result.session_key, 1, "hours");
            })
          }
        }
      })
    }
  },
  globalData: {
    userInfo: null,
    tabBarHeight: 50, // tabBar 高度，单位 px dsdsds,
    brandData: {
      b_id: 0,
      b_name: '',
    }
  }
})
