/* pages/car/model.wxss */
.container {
  padding: 0 30rpx;
  min-height: 100vh;
  background-color: #f8f8f8;
}

.header {
  padding: 30rpx 0;
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 36rpx;
  color: #333;
  margin-right: 20rpx;
}

.title {
  display: flex;
  flex-direction: column;
}

.series-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 4rpx;
}

.subtitle {
  font-size: 24rpx;
  color: #999;
}

.model-list {
  padding: 20rpx 0;
}

.model-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.model-info {
  flex: 1;
}

.model-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.model-price {
  font-size: 24rpx;
  color: #ff6b00;
  margin-bottom: 6rpx;
}

.model-desc {
  font-size: 24rpx;
  color: #999;
}

.select-icon {
  padding: 8rpx 30rpx;
  background-color: #3498db;
  color: #fff;
  font-size: 24rpx;
  border-radius: 30rpx;
}

.loading {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.empty {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.model-page {
    padding: 20rpx;
}

.page-title {
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
    padding-left: 20rpx;
} 