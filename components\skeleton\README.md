# 骨架屏组件 (Skeleton)

这是一个通用的骨架屏组件，用于在页面数据加载前显示页面结构的占位效果，优化用户体验。

## 特性

- 自定义骨架屏样式
- 支持淡入淡出过渡效果
- 闪烁动画效果
- 自动显示/隐藏
- 适配多种页面结构

## 使用方法

### 1. 引入组件

在页面的 JSON 配置文件中引入组件：

```json
{
  "usingComponents": {
    "skeleton": "../../components/skeleton/skeleton"
  }
}
```

### 2. 使用组件

在页面 WXML 中使用组件包裹实际内容：

```html
<skeleton loading="{{pageLoading}}">
  <!-- 页面实际内容 -->
  <view class="your-page-content">
    <!-- 你的页面内容 -->
  </view>
</skeleton>
```

### 3. 控制骨架屏显示/隐藏

在页面 JS 中添加控制逻辑：

```js
Page({
  data: {
    pageLoading: true, // 控制骨架屏显示
    // 其他数据...
  },
  
  onLoad() {
    // 初始显示骨架屏
    this.setData({ pageLoading: true });
    
    // 加载数据
    this.fetchData().then(() => {
      // 数据加载完成后，添加一点延迟隐藏骨架屏
      setTimeout(() => {
        this.setData({ pageLoading: false });
      }, 800);
    });
  },
  
  async fetchData() {
    // 数据加载逻辑...
  }
})
```

## 属性说明

| 属性名          | 类型      | 默认值     | 说明                      |
|----------------|-----------|-----------|--------------------------|
| loading        | Boolean   | true      | 是否显示骨架屏             |
| bgcolor        | String    | #f2f2f2   | 骨架背景色                |
| selector       | String    | skeleton  | 组件选择器                |
| unit           | String    | px        | 单位(px, rpx)            |
| animated       | Boolean   | true      | 是否启用动画效果          |
| contentBgcolor | String    | #f8f8f8   | 内容区背景色              |

## 自定义骨架屏

如果需要为不同页面自定义骨架屏样式，可以修改 `skeleton.wxml` 文件，添加符合特定页面布局的骨架元素。 