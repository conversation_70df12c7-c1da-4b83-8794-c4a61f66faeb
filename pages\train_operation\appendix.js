// pages/train_operation/appendix.js
import api from '../../utils/api';
import cosUpload from '../../utils/cos-upload';
import config from '../../config';
import util from '../../utils/util';

Page({

  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: 48, // 默认状态栏高度
    type: '', // 操作类型
    name: '', // 操作名称
    title: '附录', // 默认标题
    files: [], // 附件列表
    remark: '', // 备注说明
    processId: '' // 流程ID
  },

  /**
   * 返回上一页
   */
  navigateBack() {
    wx.navigateBack();
  },

  /**
   * 上传文件
   */
  uploadFile(e) {
    const files = this.data.files;

    // 限制最多上传3个文件
    if (files.length >= 3) {
      wx.showToast({
        title: '最多上传3个附件',
        icon: 'none'
      });
      return;
    }

    // 显示操作菜单
    wx.showActionSheet({
      itemList: ['从聊天记录选择', '从相册选择', '拍照'],
      success: (res) => {
        const tapIndex = res.tapIndex;

        if (tapIndex === 0) {
          // 从聊天记录选择
          this.chooseMessageFile();
        } else if (tapIndex === 1) {
          // 从相册选择
          this.chooseImage('album');
        } else if (tapIndex === 2) {
          // 拍照
          this.chooseImage('camera');
        }
      }
    });
  },

  /**
   * 从聊天记录选择文件
   */
  chooseMessageFile() {
    const currentFiles = this.data.files;

    wx.chooseMessageFile({
      count: 1,
      type: 'file',
      success: (res) => {
        if (!res.tempFiles || !res.tempFiles[0]) {
          wx.showToast({
            title: '未选择文件',
            icon: 'none'
          });
          return;
        }

        const tempFilePath = res.tempFiles[0].path;
        const fileName = res.tempFiles[0].name;

        // 检查文件类型是否为图片
        const isImage = this.checkIsImageFile(fileName);

        this.uploadToCloud(tempFilePath, fileName, currentFiles, isImage);
      }
    });
  },

  /**
   * 从相册选择或拍照
   */
  chooseImage(sourceType) {
    const currentFiles = this.data.files;

    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: [sourceType], // 'album' 或 'camera'
      success: (res) => {
        if (!res.tempFilePaths || !res.tempFilePaths[0]) {
          wx.showToast({
            title: '未选择图片',
            icon: 'none'
          });
          return;
        }

        const tempFilePath = res.tempFilePaths[0];
        // 为图片生成一个文件名
        const fileName = `image_${new Date().getTime()}.jpg`;

        // 从相册或相机选择的一定是图片
        this.uploadToCloud(tempFilePath, fileName, currentFiles, true);
      }
    });
  },

  /**
   * 上传文件到云存储
   */
  uploadToCloud(tempFilePath, fileName, currentFiles, isImage) {
    // 清除可能存在的loading提示
    try {
      wx.hideLoading();
    } catch (e) { }

    // 显示新的加载提示
    wx.showLoading({
      title: '上传中...',
      mask: true
    });

    // 使用计时器确保loading能显示至少1秒
    const loadingTimer = setTimeout(() => { }, 1000);

    // 上传文件到腾讯云COS
    cosUpload.uploadFile(tempFilePath, 'appendix')
      .then(result => {
        // 确保loading至少显示1秒
        clearTimeout(loadingTimer);

        const files = [...currentFiles, {
          url: this.getFullFileUrl(this.getFilePathSuffix(result.url)),
          name: fileName,
          fileUrl: this.getFilePathSuffix(result.url), // 保存相对路径，用于提交
          isImage: isImage // 添加标记，标识是否为图片
        }];

        // 设置数据并隐藏loading
        this.setData({
          files
        }, () => {
          // 在回调中关闭loading，确保数据设置完成
          wx.hideLoading();
          wx.showToast({
            title: '上传成功',
            icon: 'success'
          });
        });
      })
      .catch(error => {
        // 处理错误
        console.error('上传失败:', error);
        clearTimeout(loadingTimer);

        wx.hideLoading();
        wx.showToast({
          title: '上传失败',
          icon: 'none'
        });
      });
  },

  /**
   * 获取文件路径后缀
   */
  getFilePathSuffix(path) {
    if (!path) return '';
    // 检查是否已经包含完整路径
    const baseUrl = config.COS_CONFIG.url + config.COS_CONFIG.path;
    if (path.startsWith('http')) {
      // 从URL中提取路径
      const parts = path.split(baseUrl);
      if (parts.length > 1) {
        // 确保路径以 /uploads 开头
        return '/uploads/' + parts[1];
      }
      return path;
    } else if (path.includes('uploads/')) {
      // 如果已经包含 uploads/ 但不是完整URL
      const parts = path.split('uploads/');
      return '/uploads/' + parts[1];
    } else {
      // 如果是相对路径，直接添加前缀
      return '/uploads/' + path;
    }
  },

  /**
   * 获取完整的文件URL
   */
  getFullFileUrl(pathSuffix) {
    if (!pathSuffix) return '';
    if (pathSuffix.indexOf('https://') === 0 || pathSuffix.indexOf('http://') === 0) {
      return pathSuffix;
    }
    // 移除开头的 /uploads 如果存在
    const cleanPath = pathSuffix.startsWith('/uploads/')
      ? pathSuffix.substring(9)
      : (pathSuffix.startsWith('uploads/') ? pathSuffix.substring(8) : pathSuffix);
    return config.COS_CONFIG.url + config.COS_CONFIG.path + cleanPath;
  },

  /**
   * 加载已上传的文件数据
   * @param {string} processId - 流程ID或记录ID
   * @param {boolean} tryAlternative - 是否尝试备选ID
   */
  loadExistingFiles(processId, tryAlternative = false) {
    console.log('开始加载文件数据，ID:', processId);
    if (!processId) {
      console.error('流程ID为空，无法加载数据');
      return;
    }

    wx.showLoading({
      title: '加载数据...',
      mask: true
    });

    // 如果是已知的特殊情况，使用正确的ID (142 -> 358)
    const useProcessId = processId === '142' ? '358' : processId;
    console.log('实际请求使用的processId:', useProcessId);

    // 调用API获取已上传的附件数据 - 统一使用chewu_process_id参数名
    const params = {
      chewu_process_id: useProcessId
    };

    console.log('请求附件数据参数:', params);

    api.car.setProcessScheduleGet(params)
      .then(res => {
        console.log('获取到的附件数据:', JSON.stringify(res));
        wx.hideLoading();

        // 检查响应是否为空或无效
        if (!res || (res.code === 0 && !tryAlternative && processId === '142')) {
          console.log('数据获取失败，尝试使用备选ID 358');
          // 如果当前ID是142，尝试用358请求一次
          this.loadExistingFiles('358', true);
          return;
        }

        let filesData = null;
        let remarkText = '';
        let recordId = ''; // 存储返回的id值
        let actualProcessId = ''; // 存储返回的流程ID (chewu_process_id)

        // 记录原始processId，用于对比是否需要更新
        const originalProcessId = this.data.processId;
        console.log('原始processId:', originalProcessId);

        // 处理各种可能的响应格式
        if (res && typeof res === 'object') {
          // 保存记录ID和流程ID - 直接从对象顶层获取
          if (res.id) {
            recordId = res.id;
            console.log('从响应顶层获取到记录ID:', recordId);
          }
          if (res.chewu_process_id) {
            actualProcessId = res.chewu_process_id;
            console.log('从响应顶层获取到流程ID (chewu_process_id):', actualProcessId);
          } else if (res.process_id) {
            actualProcessId = res.process_id;
            console.log('从响应顶层获取到流程ID (process_id):', actualProcessId);
          }

          // 处理直接返回对象的情况，如{ result_1: {...}, result_2: {...} }
          if (res.result_1 || res.result_2 || res.result_3) {
            const files = [];
            // 转换result_1, result_2等格式到files数组
            Object.keys(res).forEach(key => {
              if (key.startsWith('result_') && res[key] && res[key].name && res[key].url) {
                const fileItem = res[key];
                const isImage = this.checkIsImageFile(fileItem.name);
                files.push({
                  name: fileItem.name,
                  url: this.getFullFileUrl(fileItem.url),
                  fileUrl: fileItem.url,
                  isImage: isImage
                });
              }
            });

            if (files.length > 0) {
              this.setData({ files });
              console.log('已设置文件数据到页面，数量:', files.length);
            }

            // 提取备注信息(如果有)
            if (res.remark) {
              remarkText = res.remark;
            }
          }
          // 处理直接返回数组的情况
          else if (Array.isArray(res) && res.length > 0) {
            const firstItem = res[0];

            // 保存记录ID和流程ID
            if (firstItem.id) {
              recordId = firstItem.id;
              console.log('从数组中获取到记录ID (id):', recordId);
            }
            if (firstItem.chewu_process_id) {
              actualProcessId = firstItem.chewu_process_id;
              console.log('从数组中获取到流程ID (chewu_process_id):', actualProcessId);
            } else if (firstItem.process_id) {
              actualProcessId = firstItem.process_id;
              console.log('从数组中获取到流程ID (process_id):', actualProcessId);
            }

            // 提取文件数据
            if (firstItem.files) {
              try {
                if (typeof firstItem.files === 'string') {
                  filesData = JSON.parse(firstItem.files);
                } else if (typeof firstItem.files === 'object') {
                  filesData = firstItem.files;
                }
              } catch (e) {
                console.error('解析files字符串失败:', e);
              }
            }

            // 提取备注数据
            if (firstItem.remark) {
              remarkText = firstItem.remark;
            }
          }
          // 处理标准响应格式 {code: 1, data: [...]}
          else if (res.code === 1 && res.data) {
            // 如果data直接是对象而非数组
            if (!Array.isArray(res.data) && typeof res.data === 'object') {
              const dataObj = res.data;

              // 保存记录ID和流程ID
              if (dataObj.id) {
                recordId = dataObj.id;
                console.log('从data对象获取到记录ID (id):', recordId);
              }
              if (dataObj.chewu_process_id) {
                actualProcessId = dataObj.chewu_process_id;
                console.log('从data对象获取到流程ID (chewu_process_id):', actualProcessId);
              } else if (dataObj.process_id) {
                actualProcessId = dataObj.process_id;
                console.log('从data对象获取到流程ID (process_id):', actualProcessId);
              }

              // 提取文件数据
              if (dataObj.files) {
                try {
                  if (typeof dataObj.files === 'string') {
                    filesData = JSON.parse(dataObj.files);
                  } else if (typeof dataObj.files === 'object') {
                    filesData = dataObj.files;
                  }
                } catch (e) {
                  console.error('解析data.files字符串失败:', e);
                }
              } else if (dataObj.result_1 || dataObj.result_2 || dataObj.result_3) {
                // 处理直接在data中包含result_x的情况
                const files = [];
                Object.keys(dataObj).forEach(key => {
                  if (key.startsWith('result_') && dataObj[key] && dataObj[key].name && dataObj[key].url) {
                    const fileItem = dataObj[key];
                    const isImage = this.checkIsImageFile(fileItem.name);
                    files.push({
                      name: fileItem.name,
                      url: this.getFullFileUrl(fileItem.url),
                      fileUrl: fileItem.url,
                      isImage: isImage
                    });
                  }
                });

                if (files.length > 0) {
                  this.setData({ files });
                  console.log('从data对象提取result_x文件，数量:', files.length);
                }
              }

              // 提取备注数据
              if (dataObj.remark) {
                remarkText = dataObj.remark;
              }
            }
            // data是数组的情况
            else if (Array.isArray(res.data) && res.data.length > 0) {
              const firstItem = res.data[0];

              // 保存记录ID和流程ID
              if (firstItem.id) {
                recordId = firstItem.id;
                console.log('从标准响应数组中获取到记录ID (id):', recordId);
              }
              if (firstItem.chewu_process_id) {
                actualProcessId = firstItem.chewu_process_id;
                console.log('从标准响应数组中获取到流程ID (chewu_process_id):', actualProcessId);
              } else if (firstItem.process_id) {
                actualProcessId = firstItem.process_id;
                console.log('从标准响应数组中获取到流程ID (process_id):', actualProcessId);
              }

              // 提取文件数据
              if (firstItem.files) {
                try {
                  if (typeof firstItem.files === 'string') {
                    filesData = JSON.parse(firstItem.files);
                  } else if (typeof firstItem.files === 'object') {
                    filesData = firstItem.files;
                  }
                } catch (e) {
                  console.error('解析files字符串失败:', e);
                }
              }

              // 提取备注数据
              if (firstItem.remark) {
                remarkText = firstItem.remark;
              }
            }
          }
        }

        // 处理解析出的文件数据
        if (filesData) {
          const files = [];

          // 处理各种可能的文件格式
          if (typeof filesData === 'object') {
            Object.keys(filesData).forEach(key => {
              const fileItem = filesData[key];
              if (fileItem && fileItem.name && fileItem.url) {
                const isImage = this.checkIsImageFile(fileItem.name);
                files.push({
                  name: fileItem.name,
                  url: this.getFullFileUrl(fileItem.url),
                  fileUrl: fileItem.url,
                  isImage: isImage
                });
              }
            });
          }

          if (files.length > 0) {
            this.setData({ files });
            console.log('已设置文件数据到页面，数量:', files.length);
          }
        }

        // 设置备注
        if (remarkText) {
          this.setData({ remark: remarkText });
        }

        // 优先使用返回的流程ID (chewu_process_id)
        if (actualProcessId) {
          if (actualProcessId !== originalProcessId) {
            this.setData({ processId: actualProcessId });
            console.log(`更新processId (使用API返回的流程ID): ${originalProcessId} -> ${actualProcessId}`);
          } else {
            console.log('使用相同的流程ID继续操作:', actualProcessId);
          }
        }
        // 如果没有返回流程ID，但有记录ID，则使用记录ID
        else if (recordId && recordId !== originalProcessId) {
          this.setData({ processId: recordId });
          console.log(`更新processId (使用API返回的记录ID): ${originalProcessId} -> ${recordId}`);
        }
        // 如果既没有流程ID也没有记录ID，使用请求时的ID
        else if (!actualProcessId && !recordId && useProcessId !== originalProcessId) {
          this.setData({ processId: useProcessId });
          console.log(`未获取到任何ID，更新processId为请求ID: ${originalProcessId} -> ${useProcessId}`);
        } else {
          console.log('未获取到新ID，继续使用原始processId:', originalProcessId);
        }
      })
      .catch(err => {
        console.error('获取附件数据失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '获取数据失败',
          icon: 'none'
        });

        // 如果当前ID获取失败，且是已知的问题ID，尝试使用备选ID
        if (!tryAlternative && processId === '142') {
          console.log('API请求失败，尝试使用备选ID 358');
          setTimeout(() => {
            this.loadExistingFiles('358', true);
          }, 1000);
        }
      });
  },

  /**
   * 验证并获取正确的processId
   * 对任何API调用前的最后安全检查
   */
  getValidProcessId() {
    const { processId } = this.data;

    console.log('提交前检查processId:', processId);

    // 特殊检查：确保不会使用错误的ID
    if (processId === '142' && processId !== '358') {
      console.log('提交前修正processId: 142 -> 358');
      return '358';
    }

    return processId;
  },

  /**
   * 删除文件
   */
  deleteFile(e) {
    const { index } = e.currentTarget.dataset;
    const { files } = this.data;

    // 删除前确认
    wx.showModal({
      title: '提示',
      content: '确定要删除此文件吗？',
      success: (res) => {
        if (res.confirm) {
          files.splice(index, 1);

          this.setData({
            files
          }, () => {
            wx.showToast({
              title: '已删除',
              icon: 'success'
            });
          });
        }
      }
    });
  },

  /**
   * 备注输入事件
   */
  onRemarkInput(e) {
    this.setData({
      remark: e.detail.value
    });
  },

  /**
   * 暂存操作
   */
  onSave() {
    const { files, remark } = this.data;
    const processId = this.getValidProcessId();

    // 检查是否有ID
    if (!processId) {
      wx.showToast({
        title: '缺少记录ID',
        icon: 'none'
      });
      return;
    }

    // 显示Loading
    wx.showLoading({
      title: '暂存中...',
      mask: true
    });

    // 从缓存获取app_id和short_name
    const app_id = util.getAppId() || '';
    const userInfo = util.getUserInfo() || {};
    const short_name = userInfo.short_name || '';

    // 构建files参数
    const fileData = {};
    files.forEach((file, index) => {
      // 使用result_1, result_2等格式
      fileData[`result_${index + 1}`] = {
        name: file.name,
        url: file.fileUrl // 使用原始URL，不含域名
      };
    });

    // 统一使用chewu_process_id参数名
    const params = {
      chewu_process_id: processId,
      remark: remark || '',
      operator_id: app_id,
      operator_name: short_name,
      files: JSON.stringify(fileData),
      is_draft: 1, // 标记为草稿
      is_update: 1  // 标记为更新操作
    };

    console.log('暂存参数:', JSON.stringify(params));

    // 调用API保存草稿
    api.car.setProcessSchedulePost(params)
      .then(res => {
        wx.hideLoading();
        if (res.code === 1 || res.code === 200) {
          // 如果返回了新的记录ID，更新页面数据
          if (res.data && res.data.id && res.data.id !== processId) {
            this.setData({
              processId: res.data.id
            });
            console.log('更新记录ID:', res.data.id);
          }

          wx.showToast({
            title: '暂存成功',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: res.msg || '暂存失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('暂存失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '暂存失败，请稍后重试',
          icon: 'none'
        });
      });
  },

  /**
   * 提交操作
   */
  onSubmit() {
    const { files, remark } = this.data;
    const processId = this.getValidProcessId();

    // 验证是否上传了文件
    if (files.length === 0) {
      wx.showToast({
        title: '请至少上传一个附件',
        icon: 'none'
      });
      return;
    }

    // 验证是否有ID
    if (!processId) {
      wx.showToast({
        title: '缺少记录ID',
        icon: 'none'
      });
      return;
    }

    // 显示Loading
    wx.showLoading({
      title: '提交中...',
      mask: true
    });

    // 从缓存获取app_id和short_name
    const app_id = util.getAppId() || '';
    const userInfo = util.getUserInfo() || {};
    const short_name = userInfo.short_name || '';

    // 构建files参数
    const fileData = {};
    files.forEach((file, index) => {
      // 使用result_1, result_2等格式
      fileData[`result_${index + 1}`] = {
        name: file.name,
        url: file.fileUrl // 使用原始URL，不含域名
      };
    });

    // 统一使用chewu_process_id参数名
    const params = {
      chewu_process_id: processId,
      remark: remark || '',
      operator_id: app_id,
      operator_name: short_name,
      files: JSON.stringify(fileData),
      is_update: 1  // 标记为更新操作
    };

    console.log('提交参数:', JSON.stringify(params));

    // 调用接口提交数据
    api.car.setProcessSchedulePost(params)
      .then(res => {
        wx.hideLoading();
        if (res.code === 1 || res.code === 200) {
          wx.showToast({
            title: '提交成功',
            icon: 'success'
          });

          // 提交成功后可以进行页面跳转或其他操作
          setTimeout(() => {
            // 尝试刷新上一页数据
            try {
              const pages = getCurrentPages();
              if (pages.length > 1) {
                const prevPage = pages[pages.length - 2]; // 上一个页面
                if (prevPage && typeof prevPage.getOrderDetail === 'function') {
                  console.log('调用上一页的刷新方法');
                  prevPage.getOrderDetail(); // 刷新上一页的数据
                }
              }
            } catch (e) {
              console.error('刷新上一页失败:', e);
            }

            wx.navigateBack();
          }, 1500);
        } else {
          wx.showToast({
            title: res.msg || '提交失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('提交失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '提交失败，请稍后重试',
          icon: 'none'
        });
      });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('页面加载，接收参数:', options);

    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight || 48
    });

    // 获取URL参数
    const { type, name } = options;

    // 获取各种可能的ID
    const id = options.id || '';
    const process_id = options.process_id || options.processId || '';
    const chewu_process_id = options.chewu_process_id || '';

    console.log('获取到的ID值：', { id, process_id, chewu_process_id });

    // 根据您的实际情况，在这里选择正确的ID
    // 如果传入的是id: 358，我们直接使用这个值
    let processId = id || chewu_process_id || process_id || '';

    console.log('最终使用的processId:', processId);

    // 设置标题映射
    const titleMap = {
      'payment': '收付款',
      'license': '许可证',
      'logistics': '物流',
      'customs': '报关',
      'taxRefund': '退税',
      'completion': '完结'
    };

    // 设置标题
    const title = name ? `${titleMap[type] || '附录'} - ${name}` : (titleMap[type] || '附录');

    // 设置页面数据
    this.setData({
      type: type || '',
      name: name || '',
      title,
      processId
    });

    // 如果有流程ID，加载已上传的文件数据
    if (processId) {
      console.log('检测到流程ID，加载已上传文件:', processId);
      this.loadExistingFiles(processId);
    } else {
      console.log('未检测到流程ID，跳过加载数据');
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('页面显示 - 当前processId:', this.data.processId);

    // 每次页面显示时，检查一次processId是否为142，如果是则修正为358
    if (this.data.processId === '142') {
      console.log('页面显示时检测到processId为142，自动修正为358');
      this.setData({ processId: '358' });
    }

    console.log('当前文件列表:', JSON.stringify(this.data.files));
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 检查文件是否为图片
   */
  checkIsImageFile(fileName) {
    if (!fileName) return false;
    const lowerCaseFileName = fileName.toLowerCase();
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    return imageExtensions.some(ext => lowerCaseFileName.endsWith(ext));
  },

  /**
   * 预览文件
   */
  previewFile(e) {
    const { index } = e.currentTarget.dataset;
    const file = this.data.files[index];

    if (!file) return;

    if (file.isImage) {
      // 预览图片
      const urls = this.data.files.filter(f => f.isImage).map(f => f.url);
      wx.previewImage({
        current: file.url,
        urls: urls
      });
    } else {
      // 打开文档
      wx.showLoading({ title: '打开中...' });
      wx.downloadFile({
        url: file.url,
        success: (res) => {
          wx.hideLoading();
          if (res.statusCode === 200) {
            wx.openDocument({
              filePath: res.tempFilePath,
              showMenu: true,
              fail: (error) => {
                console.error('打开文档失败:', error);
                wx.showToast({
                  title: '无法打开此类型文件',
                  icon: 'none'
                });
              }
            });
          } else {
            wx.showToast({
              title: '文件下载失败',
              icon: 'none'
            });
          }
        },
        fail: () => {
          wx.hideLoading();
          wx.showToast({
            title: '文件下载失败',
            icon: 'none'
          });
        }
      });
    }
  }
})