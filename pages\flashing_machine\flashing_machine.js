// pages/train_operation/index.js
import api from '../../utils/api';
import share from '../../utils/share';  // 确保导入 分享
import util from '../../utils/util';  // 导入util工具类
Page({

  /**
   * 页面的初始数据
   */
  data: {
    behaviors: [share], //分享设置
    //处理分享页面 统一写
    shareData: {
      title: '找车侠',
      path: '/pages/flashing_machine/flashing_machine',
      isDetailPage: false, // 标记是否详情页
    },
    statusBarHeight: 48, // 默认状态栏高度
    isIOS: false, // 是否是iOS设备
    filterTypes: ['全部品牌', '语言', '远程'],
    currentFilter: -1,
    showFilterPanel: false,
    sortOrder: '', // 排序顺序：'' - 无排序, 'asc' - 升序, 'desc' - 降序
    vehicleList: [], // 存储车务列表数据
    brandList: [], // 存储品牌列表
    selectedBrand: '', // 选中的品牌
    LangList: [
      { id: 0, name: '请选择' },
      { id: 1, name: '英语' },
      { id: 2, name: '阿拉伯语' },
      { id: 3, name: '西班牙语' }
    ],
    LangListType: 0, // 默认选择全部
    onlineStatus: [
      { id: -1, name: '请选择' },
      { id: 0, name: '否' },
      { id: 1, name: '是' },
    ],
    onlineStatusType: -1, // 默认选择全部
    filterParams: {}, // 筛选参数对象
    refreshing: false, // 是否正在刷新
    pageNum: 1, // 当前页码
    pageSize: 10, // 每页数据条数
    hasMore: true, // 是否有更多数据
    isLoading: false // 是否正在加载数据
  },

  /**
   * 点击筛选项
   */
  tapFilter(e) {
    const index = e.currentTarget.dataset.index;
    const currentFilter = this.data.currentFilter;
    // 其他筛选项的处理
    this.setData({
      currentFilter: currentFilter === index ? -1 : index,
      showFilterPanel: currentFilter !== index
    });

    // 如果是品牌筛选并且没有品牌列表数据，则获取品牌列表
    if (index === 0 && this.data.brandList.length === 0) {
      this.getBrandList();
    }
  },

  /**
   * 获取品牌列表
   */
  async getBrandList() {
    try {
      wx.showLoading({
        title: '加载中...',
        mask: true
      });

      // 调用API获取品牌列表
      const result = await api.car.getBrandList();

      // 处理结果
      if (Array.isArray(result)) {
        // 添加"全部"选项
        const brandList = [{ id: 0, brand_name: '全部' }].concat(result);

        this.setData({
          brandList: brandList
        });
      } else {
        console.error('获取品牌列表失败:', result);
        wx.showToast({
          title: '获取品牌列表失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('获取品牌列表出错:', error);
      wx.showToast({
        title: '获取品牌列表失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 选择品牌
   */
  selectBrand(e) {
    const brandId = e.currentTarget.dataset.id;
    const brandName = e.currentTarget.dataset.name;

    this.setData({
      selectedBrand: brandId,
      showFilterPanel: false,
      currentFilter: -1,
      pageNum: 1, // 重置页码
      hasMore: true // 重置更多数据标志
    });

    // 更新筛选参数
    const filterParams = { ...this.data.filterParams };
    if (brandId === 0) {
      delete filterParams.brand_id;
    } else {
      filterParams.brand_id = brandId;
    }

    this.setData({ filterParams }, () => {
      // 触发搜索
      this.searchVehicles();
    });
  },

  /**
   * 选择订单类型
   */
  selectLangType(e) {
    const typeId = e.currentTarget.dataset.id;

    this.setData({
      LangListType: typeId,
      showFilterPanel: false,
      currentFilter: -1,
      pageNum: 1, // 重置页码
      hasMore: true // 重置更多数据标志
    });

    // 更新筛选参数
    const filterParams = { ...this.data.filterParams };
    if (typeId === 0) {
      delete filterParams.LangListType;
    } else {
      filterParams.LangListType = typeId;
    }

    this.setData({ filterParams }, () => {
      // 触发搜索
      this.searchVehicles();
    });
  },
  selectOnline(e) {
    const typeId = e.currentTarget.dataset.id;
    console.log(typeId)
    this.setData({
      onlineStatusType: typeId,
      showFilterPanel: false,
      currentFilter: -1,
      pageNum: 1, // 重置页码
      hasMore: true // 重置更多数据标志
    });

    // 更新筛选参数
    const filterParams = { ...this.data.filterParams };
    if (typeId === 0) {
      delete filterParams.onlineType;
    } else {
      filterParams.onlineType = typeId;
    }

    this.setData({ filterParams }, () => {
      // 触发搜索
      this.searchVehicles();
    });
  },

  /**
   * 搜索车辆
   */
  searchVehicles(isLoadMore = false) {
    // 详细记录请求参数
    console.log('调用搜索功能:', isLoadMore ? '加载更多' : '初始加载',
      '参数:', this.data.filterParams,
      '页码:', this.data.pageNum,
      '每页数量:', this.data.pageSize
    );

    // 根据筛选条件搜索车辆
    const params = {
      ...this.data.filterParams,
      page: this.data.pageNum,
      limit: this.data.pageSize,
      // 尝试额外添加常见的分页参数名称
      pageNum: this.data.pageNum,
      pageSize: this.data.pageSize,
      current: this.data.pageNum,
      size: this.data.pageSize
    };

    // 记录最终请求参数
    console.log('最终请求参数:', params);

    // 避免重复加载
    if (this.data.isLoading) {
      console.log('正在加载中，忽略本次请求');
      return;
    }

    // 如果是加载更多，但没有更多数据，直接返回
    if (isLoadMore && !this.data.hasMore) {
      console.log('没有更多数据，忽略加载更多请求');
      wx.showToast({
        title: '没有更多数据了',
        icon: 'none'
      });
      return;
    }

    this.setData({
      isLoading: true
    });

    // 显示加载提示
    if (!isLoadMore) {
      wx.showLoading({
        title: '搜索中...',
        mask: true
      });
    } else {
      wx.showToast({
        title: '加载更多...',
        icon: 'loading'
      });
    }

    // 调用API获取车辆列表
    api.serviceFlash.getList(params)
      .then(res => {
        console.log('搜索结果:', res);
        console.log('数据长度:', res && res.data ? res.data.length : 0);
        console.log('pageSize:', this.data.pageSize);

        if (res && res.data) {
          // 优化判断是否有更多数据的逻辑
          // 如果返回的数据量小于每页数量，则认为没有更多数据
          // 同时防止数据刚好是pageSize的倍数时的误判
          const hasMore = res.data.length > 0 && res.data.length >= this.data.pageSize;
          console.log('是否有更多数据:', hasMore);

          // 如果是加载更多，则合并数据，否则替换数据
          const newList = isLoadMore ? [...this.data.vehicleList, ...res.data] : res.data;
          console.log('更新后列表长度:', newList.length);
          let onlineMap = { 0: '否', 1: '是' };
          let languageText = { 1: '英语', 2: '阿拉伯语', 3: '西班牙语' };
          newList.forEach(item => {
            item.onlineText = onlineMap[item.online] || '-';
            if (item.language) {
              item.languageText = item.language.split(',').map(key => languageText[key] || '-').join(',')
            } else {
              item.languageText = '-';
            }
          });
          this.setData({
            vehicleList: newList,
            hasMore: hasMore,
            isLoading: false
          });
        } else {
          // 没有数据时的处理
          if (!isLoadMore) {
            console.log('初始加载没有数据');
            this.setData({
              vehicleList: [],
              hasMore: false,
              isLoading: false
            });
          } else {
            console.log('加载更多没有数据');
            this.setData({
              hasMore: false,
              isLoading: false
            });
            wx.showToast({
              title: '没有更多数据了',
              icon: 'none'
            });
          }
        }
      })
      .catch(err => {
        console.error('搜索失败:', err);
        this.setData({
          isLoading: false
        });
        wx.showToast({
          title: '搜索失败',
          icon: 'none'
        });
      })
      .finally(() => {
        if (!isLoadMore) {
          wx.hideLoading();
        }
      });
  },

  /**
   * 将日期字符串转换为时间戳
   * 处理iOS设备上日期格式兼容性问题
   */
  parseDateTime(dateTimeStr) {
    // 检查日期字符串格式
    if (!dateTimeStr) return 0;

    try {
      // 将 "yyyy-MM-dd HH:mm:ss" 格式转换为 "yyyy/MM/dd HH:mm:ss"
      // 这种格式在 iOS 上也支持
      const formattedDate = dateTimeStr.replace(/-/g, '/');
      return new Date(formattedDate).getTime();
    } catch (e) {
      // 如果转换失败，尝试手动解析
      try {
        // 手动解析格式为 "yyyy-MM-dd HH:mm:ss" 的字符串
        const parts = dateTimeStr.split(' ');
        const dateParts = parts[0].split('-');
        const timeParts = parts[1].split(':');

        const year = parseInt(dateParts[0], 10);
        const month = parseInt(dateParts[1], 10) - 1; // 月份从0开始
        const day = parseInt(dateParts[2], 10);
        const hour = parseInt(timeParts[0], 10);
        const minute = parseInt(timeParts[1], 10);
        const second = parseInt(timeParts[2], 10);

        return new Date(year, month, day, hour, minute, second).getTime();
      } catch (err) {
        console.error('日期解析错误:', err);
        return 0;
      }
    }
  },

  /**
   * 根据发布时间排序车辆列表
   */
  sortVehicleList(order) {
    const filterParams = { ...this.data.filterParams };
    filterParams.sort = this.data.sortOrder;

    this.setData({
      filterParams,
      pageNum: 1, // 重置页码
      hasMore: true // 重置更多数据标志
    }, () => {
      // 触发搜索
      this.searchVehicles();
    });
  },

  /**
   * 点击列表项
   */
  tapItem(e) {
    const id = e.currentTarget.dataset.id;

    // 获取用户信息
    const userInfo = util.getUserInfo();

    // 检查登录状态
    if (userInfo && userInfo.app_id) {
      // 用户已登录，直接跳转到详情页
      wx.navigateTo({
        url: `/pages/flashing_machine/flashing_machine_info?id=${id}`
      });
    } else {
      // 用户未登录，直接跳转到登录页面
      wx.navigateTo({
        url: '/pages/login/index'
      });
    }
  },

  /**
   * 返回上一页
   */
  navigateBack() {
    wx.navigateBack();
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('页面onLoad');
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();

    // 检查是否是iOS设备
    const isIOS = systemInfo.platform === 'ios';

    // 设置状态栏高度并更新iOS标志
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight,
      isIOS: isIOS
    });

    // 设置CSS变量用于样式计算
    wx.nextTick(() => {
      const query = wx.createSelectorQuery();
      query.select('.container').boundingClientRect().exec(res => {
        if (res && res[0]) {
          const container = res[0];
          container.node && container.node.style.setProperty('--status-bar-height', systemInfo.statusBarHeight + 'px');
        }
      });
    });

    // 为iOS设备添加自定义样式变量
    if (isIOS) {
      wx.setNavigationBarColor({
        frontColor: '#000000',
        backgroundColor: '#FFFFFF',
        animation: {
          duration: 0,
          timingFunc: 'easeIn'
        }
      });
    }

    // 直接调用接口获取初始数据
    this.loadInitialData();
  },

  // 初始数据加载
  loadInitialData() {
    console.log('开始加载初始数据');
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    // 重置页码
    this.setData({
      pageNum: 1,
      hasMore: true,
      isLoading: true
    });

    // 添加多种可能的分页参数
    const params = {
      page: this.data.pageNum,
      limit: this.data.pageSize,
      // 尝试额外添加常见的分页参数名称
      pageNum: this.data.pageNum,
      pageSize: this.data.pageSize,
      current: this.data.pageNum,
      size: this.data.pageSize
    };

    console.log('初始加载请求参数:', params);

    api.serviceFlash.getList(params)
      .then(res => {
        console.log('初始数据结果:', res);
        console.log('数据长度:', res && res.data ? res.data.length : 0);

        if (res && res.data) {
          // 优化判断是否有更多数据的逻辑
          const hasMore = res.data.length > 0 && res.data.length >= this.data.pageSize;
          console.log('是否有更多数据:', hasMore);
          let onlineMap = { 0: '否', 1: '是' };
          let languageText = { 1: '英语', 2: '阿拉伯语', 3: '西班牙语' };
          res.data.forEach(item => {
            item.onlineText = onlineMap[item.online] || '未知';
            if (item.language) {
              item.languageText = item.language.split(',').map(key => languageText[key] || '-').join(',');
            } else {
              item.languageText = '-';
            }
          })
          this.setData({
            vehicleList: res.data,
            hasMore: hasMore,
            isLoading: false
          });
        } else {
          console.warn('返回数据格式异常:', res);
          this.setData({
            vehicleList: [],
            hasMore: false,
            isLoading: false
          });
        }
      })
      .catch(err => {
        console.error('初始数据加载失败:', err);
        this.setData({
          isLoading: false,
          hasMore: false
        });
      })
      .finally(() => {
        wx.hideLoading();
      });
  },

  /**
   * 获取车辆列表数据
   */
  getVehicleList() {
    // 调用搜索方法获取数据
    this.searchVehicles();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时重新加载数据
    // this.getVehicleList();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('触发下拉刷新');

    // 重置页码
    this.setData({
      pageNum: 1,
      hasMore: true
    });

    // 简化接口调用，确保下拉刷新时直接调用接口
    wx.showLoading({
      title: '刷新中...',
      mask: true
    });

    // 直接调用trainOperation接口
    const params = {
      page: this.data.pageNum,
      limit: this.data.pageSize,
      // 尝试额外添加常见的分页参数名称
      pageNum: this.data.pageNum,
      pageSize: this.data.pageSize,
      current: this.data.pageNum,
      size: this.data.pageSize
    };

    console.log('下拉刷新请求参数:', params);

    api.serviceFlash.getList(params)
      .then(res => {
        console.log('下拉刷新数据结果:', res);
        console.log('数据长度:', res && res.data ? res.data.length : 0);

        if (res && res.data) {
          // 优化判断是否有更多数据的逻辑
          const hasMore = res.data.length > 0 && res.data.length >= this.data.pageSize;
          console.log('是否有更多数据:', hasMore);
          let onlineMap = { 0: '否', 1: '可' };
          let languageText = { 1: '英语', 2: '阿拉伯语', 3: '西班牙语' };
          res.data.forEach(item => {
            item.onlineText = onlineMap[item.online] || '未知';
            if (item.language) {
              item.languageText = item.language.split(',').map(key => languageText[key] || '-').join(',');
            } else {
              item.languageText = '-';
            }
          })
          this.setData({
            vehicleList: res.data,
            // 重置筛选状态
            filterParams: {},
            currentFilter: -1,
            sortOrder: '',
            selectedBrand: '',
            selectedOrderType: 0,
            refreshing: false,
            hasMore: hasMore
          });
        } else {
          this.setData({
            vehicleList: [],
            refreshing: false,
            hasMore: false
          });
        }
        wx.showToast({
          title: '刷新成功',
          icon: 'success'
        });
      })
      .catch(err => {
        console.error('刷新失败:', err);
        wx.showToast({
          title: '刷新失败',
          icon: 'none'
        });
      })
      .finally(() => {
        // 确保停止下拉刷新动画
        wx.hideLoading();
        wx.stopPullDownRefresh();
      });
  },

  /**
   * 监听scroll-view滚动到底部事件
   */
  onScrollToLower() {
    console.log('===== scroll-view触发滚动到底部事件 =====');
    // 与onReachBottom逻辑相同
    if (!this.data.hasMore || this.data.isLoading) {
      console.log('条件不满足，不加载更多');
      if (!this.data.hasMore) {
        wx.showToast({
          title: '没有更多数据了',
          icon: 'none'
        });
      }
      return;
    }

    // 页码加1
    this.setData({
      pageNum: this.data.pageNum + 1
    }, () => {
      console.log('页码已增加为:', this.data.pageNum);
    });

    // 调用搜索函数，传入true表示加载更多模式
    this.searchVehicles(true);
  },

  /**
   * 点击加载更多按钮
   */
  loadMore() {
    console.log('===== 点击加载更多按钮 =====');
    // 避免重复触发
    if (this.data.isLoading) {
      return;
    }

    // 页码加1
    this.setData({
      pageNum: this.data.pageNum + 1
    });

    // 调用搜索函数，传入true表示加载更多模式
    this.searchVehicles(true);
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    console.log('=== 触发上拉加载更多 ===');
    console.log('当前页码:', this.data.pageNum);
    console.log('是否有更多数据:', this.data.hasMore);
    console.log('是否正在加载:', this.data.isLoading);

    // 显示触底提示，便于调试
    wx.showToast({
      title: '触底加载',
      icon: 'none',
      duration: 1000
    });

    // 如果没有更多数据或正在加载，直接返回
    if (!this.data.hasMore || this.data.isLoading) {
      console.log('条件不满足，不加载更多');
      if (!this.data.hasMore) {
        wx.showToast({
          title: '没有更多数据了',
          icon: 'none'
        });
      }
      return;
    }

    // 页码加1
    this.setData({
      pageNum: this.data.pageNum + 1
    }, () => {
      console.log('页码已增加为:', this.data.pageNum);
    });

    // 调用搜索函数，传入true表示加载更多模式
    this.searchVehicles(true);
  },
})