// pages/video/index.js

import api from '../../utils/api';
import util from '../../utils/util';
const config = require('../../config');
Page({

  /**
   * 页面的初始数据
   */
  data: {
    activeTab: 'all',
    totalVideos: 0,
    // 商家信息数据
    merchantInfo: {
      avatar: '',
      company_name: '',
      short_name: '',
      introduce: ''
    },
    videoList: [],
    statusBarHeight: 20, // 默认状态栏高度
    pageNum: 1,
    pageSize: 12,
    hasMore: true,
    loading: false,
    isEmpty: false,
    type: '1', // 视频类型，1为全部，2为直播回放
    userInfo: util.getUserInfo(),
    showMemberDialog: false,
    videoId: 0,
    price: 0,
    title: '-',
    video_cate:0,
    vcid:0,
    collectionsDetail:{
      collection_name:'',
      remark:'',
      id:'',
    }
  },

  /**
   * Tab切换
   */
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    const tabText = {all:1,live:2,collections:3};
    this.setData({
      activeTab: tab,
      videoList: [],
      pageNum: 1,
      hasMore: true,
      isEmpty: false,
      type: tabText[tab],
      activeTab:tab,
    });
    // 根据不同的tab加载不同的视频数据
    this.loadVideoData();
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateTo({
      url: '/pages/my/index',
    });
  },

  /**
   * 页面导航返回
   */
  navigateBack() {
    wx.switchTab({
      url: '/pages/my/index',
    });
  },

  /**
   * 观看视频
   */
  watchVideo(e) {
    const videoId = e.currentTarget.dataset.id;
    const price = e.currentTarget.dataset.price;
    const title = e.currentTarget.dataset.title;
    const payState = e.currentTarget.dataset.paystate;
    const video_cate = e.currentTarget.dataset.video_cate;
    if(video_cate == 3){
      wx.navigateTo({
        url: `/pages/video/index?video_cate=${video_cate}&&vcid=${videoId}`,
      });
    }else{
      this.setData({
        videoId: videoId,
        price: price,
        title: title,
      });
      // 根据视频ID和类型进行不同的处理
      const video = this.data.videoList.find(v => v.id === videoId);
  
      if (video.type === 'member') {
        if (this.data.userInfo.level === 1) {
          if (payState) {
            // 已付费，直接跳转到视频详情页
            wx.navigateTo({
              url: `/pages/video/detail?id=${videoId}`,
            });
          } else {
             // 会员视频，检查会员权限
            this.setData({
              showMemberDialog: true
            });
          }
        }else{
          // 已付费，直接跳转到视频详情页
          wx.navigateTo({
            url: `/pages/video/detail?id=${videoId}`,
          });
        }
      } else {
        // 免费视频，直接跳转到视频详情页
        wx.navigateTo({
          url: `/pages/video/detail?id=${videoId}`,
        });
      }
    }
  },

  /**
   * 关闭会员弹窗
   */
  closeMemberDialog() {
    this.setData({
      showMemberDialog: false
    });
    // this.Pay();
  },
  Pay() {
    const userInfo = util.getUserInfo();
    try {
      // 检查是否已经支付
      wx.request({
        url: `${config.BASE_URL}wechat/Video/PayState`,
        method: 'GET',
        data: { admin_id: userInfo.admin_id, videoId: this.data.videoId },
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`
        },
        success: (res) => {
          if (res.data.data.state == 1000) {
            //去支付
            this.proceedWithPayment();
          } else if (res.data.data.state == 1002) {
            //已支付
            wx.showToast({
              title: '跳转到订单页面',
              icon: 'error'
            });
            return
          } else {
            //已支付
            wx.showToast({
              title: '请勿重复支付！',
              icon: 'success'
            });
            return
          }
        },
        fail: (err) => {
          wx.hideLoading();
          console.error('检查订单失败:', err);
          // 出错时也继续支付流程
          this.proceedWithPayment();
        }
      });
    } catch (error) {
      // wx.hideLoading();
      console.error('订单检查或支付过程出错:', error);
      // wx.showToast({
      //   title: '操作失败，请重试',
      //   icon: 'none'
      // });
    }
  },
  proceedWithPayment() {
    //生成唯一订单号
    const timestamp = new Date().getTime();
    const randomNum = Math.floor(Math.random() * 1000)
    const outTradeNo = `VID${timestamp}${randomNum}`;
    //获取用户openid
    let openid = wx.getStorageSync('openid');
    //检查openid是否对象，如果是则尝试获取其中值
    if (openid && typeof openid === 'object') {
      //尝试从对象中获取openid字段，这取决于实际存储结构
      if (openid.openid) {
        openid = openid.openid;
      } else if (openid.value) {
        openid = openid.value;
      } else {
        console.log('无法从对象中获取有效的openid:', openid);
        wx.hideLoading();
        wx.showToast({
          title: '用户获取信息失败',
          icon: 'error'
        });
        return
      }
    }

    //检查openid是否有效
    if (!openid || typeof openid !== 'string' || openid === '[object Object]') {
      console.log('无效的openid:', openid)
      wx.hideLoading();
      wx.showToast({
        title: '获取用户信息失败',
        icon: 'error'
      });
      return
    }
    const userInfo = util.getUserInfo();
    //构造支付请求餐宿
    const payParams = {
      amount: this.data.price,
      amount_total: this.data.price,
      app_id: util.getAppId(),
      admin_id: userInfo.admin_id,
      body: this.data.title + '支付',
      openid: openid, // 使用处理后的openid
      type: 'Video',
      source_id: this.data.videoId,
    };

    //创建支付信息
    wx.request({
      url: `${config.BASE_URL}wechat/pay/unifiedOrderAdd`,
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded', // 使用表单格式
        'Authorization': `Bearer ${wx.getStorageSync('token')}`
      },
      data: payParams,
      success: (res) => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          const data = res.data;
          // 检查是否有标准的code响应
          if (data && (data.code === 1 || data.code === 0)) {
            this.setData({
              showMemberDialog: false
            });
            // 获取支付参数成功，拉起微信支付
            const payData = data.data;
            this.processPayment(payData);
          }else{
            //获取支付参数失败
            wx.showToast({
              title: data?.msg || '获取支付信息失败',
              icon:'none'
            })
          }
        }else{
          //HTTP错误
          wx.showToast({
            title: `请求失败：${res.statusCode}`,
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        wx.hideLoading();
        wx.showToast({
          title: '网络请求失败，请重试',
          icon: 'none'
        });
        console.error('请求支付参数失败', err);
      }
    })

  },
  processPayment(payData) {
    //确保timeStamp是字符串类型
    const timeStamp = typeof payData.timeStamp === 'string' ?
      payData.timeStamp : String(payData.timeStamp);
    wx.requestPayment({
      timeStamp: timeStamp,
      nonceStr: payData.nonceStr,
      package: payData.package,
      signType: payData.signType || 'MD5', // 默认使用MD5
      paySign: payData.paySign,
      success: (payRes) => {
        this.setData({
          showMemberDialog: false
        });
        const videoId = this.data.videoId;
        const videoList = this.data.videoList.map(item => {
          if (item.id === videoId) {
            return {
              ...item,
              payState: 1 // 允许观看
            }
          }
          return item;
        });
        this.setData({
          videoList
        });
      },
      fail: (payErr) => {
        this.setData({
          showMemberDialog: false
        });
        console.log(payErr)
      }
    })
  },
  /**
   * 立即开通会员
   */
  goToMemberPage() {
    this.setData({
      showMemberDialog: false
    });
    // 跳转到会员购买页面
    wx.navigateTo({
      url: '/pages/member/index',
    });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡，防止点击弹窗内容时关闭弹窗
    return;
  },

  /**
   * 更新用户信息
   */
  updateUserInfo() {
    const userInfo = util.getUserInfo();
    if (userInfo) {
      this.setData({
        userInfo: userInfo
      });
    }
  },

  /**
   * 获取商家信息
   */
  getMerchantInfo() {
    wx.showLoading({
      title: '加载中',
    });

    api.user.mainMerchantInfo().then(res => {
      if (res) {
        this.setData({
          merchantInfo: res
        });
      }
    }).catch(err => {
      console.error('获取商家信息失败', err);
    }).finally(() => {
      wx.hideLoading();
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if ('vcid' in options && 'video_cate' in options && options.video_cate == 3) {
      this.setData({
        vcid:options.vcid,
        video_cate:options.video_cate,
      })
      this.loadVideoData();
      this.collectionsInfo();
    }else{
      // 从接口加载视频数据
      this.loadVideoData();
    }

    // 获取商家信息
    this.getMerchantInfo();

    // 获取状态栏高度和菜单按钮位置信息
    const systemInfo = wx.getSystemInfoSync();

    this.setData({
      statusBarHeight: systemInfo.statusBarHeight,
      navBarHeight: systemInfo.statusBarHeight + 44
    });
  },
  collectionsInfo(){
    let vcid = this.data.vcid;
    api.user.getVideoCollectionsDetail({vcid})
    .then(res => {
      this.setData({
        collectionsDetail:res
      });
    })
  },
  /**
   * 加载视频数据
   */
  loadVideoData() {
    if (this.data.loading || !this.data.hasMore) return;
    this.setData({ loading: true });
    wx.showLoading({
      title: '加载中',
    });

    const params = {
      page: this.data.pageNum,
      list_rows: this.data.pageSize,
      app_id: util.getAppId(),
      admin_id: this.data.userInfo.admin_id,
    };

    // 如果是直播回放，添加video_cate参数
    if (this.data.type) {
      params.video_cate = this.data.type;
    }

    if(this.data.video_cate == 3){
      params.video_cate = this.data.video_cate;
      params.vcid = this.data.vcid;
    }

    api.user.getVideoList(params).then(res => {
      if (res.data) {
        const newList = res.data.map(item => {
          // 处理返回的数据，确保与前端展示格式一致
          return {
            id: item.id,
            title: item.title,
            coverUrl: item.cover_url,
            type: item.type == 2 ? 'member' : 'free',
            updateTime: item.update_time_text || '最近更新',
            likes: item.likes,
            isLiked: item.is_liked,
            seriesTag: item.series_tag || '',
            price: item.price || 0,
            payState: item.payState || 0,
            video_cate: item.video_cate || 0,
          };
        });

        const videoList = this.data.pageNum === 1 ? newList : this.data.videoList.concat(newList);
        const isEmpty = this.data.pageNum === 1 && newList.length === 0;

        
        this.setData({
          videoList: videoList,
          totalVideos: res.total || 0,
          hasMore: newList.length >= this.data.pageSize,
          pageNum: this.data.pageNum + 1,
          isEmpty: isEmpty
        });
      } else {
        this.setData({
          isEmpty: this.data.pageNum === 1,
          hasMore: false
        });
      }
    }).catch(err => {
      console.error('获取视频列表失败', err);
      this.setData({
        isEmpty: this.data.pageNum === 1,
        hasMore: false
      });
    }).finally(() => {
      this.setData({ loading: false });
      wx.hideLoading();
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    // 页面渲染完成后的逻辑
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时的逻辑
    this.updateUserInfo();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 下拉刷新，重置页码并重新加载数据
    this.setData({
      pageNum: 1,
      hasMore: true,
      videoList: []
    });
    this.loadVideoData();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    // 上拉加载更多
    if (this.data.hasMore) {
      this.loadVideoData();
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})