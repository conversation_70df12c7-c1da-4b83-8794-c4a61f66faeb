// pages/video/detail.js
import api from '../../utils/api';
import util from '../../utils/util';

Page({

  /**
   * 页面的初始数据
   */
  data: {
    loading: true, // 添加加载状态
    isVideoReady: false, // 添加视频准备好的状态
    videoUrl: "",
    videoInfo: {
      id: '',
      title: '',
      description: '',
      timeText: '',
      timeTextEn: '',
      likes: '0',
      comments: '0',
      shares: '0',
      specifications:0
    },
    merchantInfo: {
      name: '',
      avatar: ''
    },
    isDescExpanded: false,
    isPlaying: true,
    isEnded: false, // 添加视频是否已结束的状态
    currentTime: 0,
    duration: 0,
    progressWidth: 0,
    isDragging: false,
    formattedDragTime: '00:00',
    // 登录弹窗相关数据
    showLoginPopup: false,
    loginPopupOptions: {
    title: '登录后可以查看更多精彩视频',
    buttonText: '立即登录',
    imageUrl: 'https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/uploads/proof/1751274411926_900.png',
    hideCloseButton: true, // 在视频详情页隐藏关闭按钮
    clickMaskClose: false // 禁用点击遮罩关闭
    },
    // 用户登录状态
    needLogin: false,
    // 是否显示遮罩
    showMask: false,
    // 视频预览时间(秒)
    previewTime: 15
  },

  /**
   * 返回上一页
   */
  goBack() {
    // 获取页面栈信息
    const pages = getCurrentPages();

    // 如果页面栈只有当前一个页面（通过分享直接打开）
    if (pages.length <= 1) {
      // 没有历史记录，使用redirectTo跳转到视频列表页
      wx.redirectTo({
        url: '/pages/video/index'
      });
    } else {
      // 有历史记录，正常返回上一页
      wx.navigateBack({
        delta: 1
      });
    }
  },

  /**
   * 未登录状态下点击返回按钮的处理
   */
  handleBackWhenNotLogin() {
    console.log('未登录用户尝试点击返回按钮');

    // 显示登录提示并阻止返回
    wx.showToast({
      title: '请先登录后继续',
      icon: 'none',
      duration: 1500
    });

    // 显示登录弹窗
    this.setData({
      showLoginPopup: true
    });
  },

  /**
   * 视频播放出错回调
   */
  videoErrorCallback(e) {
    console.error('视频播放出错', e.detail.errMsg);

    // 增加更详细的错误日志，帮助调试
    console.error('视频URL:', this.data.videoUrl);
    console.error('错误详情:', e.detail);

    // 尝试重新加载视频
    if (this.data.videoUrl) {
      console.log('尝试重新初始化视频播放器');

      // 设置加载状态
      this.setData({
        isVideoReady: false
      });

      setTimeout(() => {
        const videoContext = wx.createVideoContext('myVideo');
        videoContext.stop();
        videoContext.play();
      }, 1000);
    }
  },

  /**
   * 展开/收起描述
   */
  toggleDescription() {
    this.setData({
      isDescExpanded: !this.data.isDescExpanded
    });
  },

  /**
   * 暂停/播放视频
   */
  togglePlay() {
    // const videoContext = wx.createVideoContext('myVideo');

    if (this.data.isPlaying) {
      this.videoContext.pause();
      this.setData({
        isPlaying: false
      });
    } else {
      this.videoContext.play();
      this.setData({
        isPlaying: true,
        isEnded: false
      });
    }
  },

  /**
   * 监听视频播放进度更新
   */
  onTimeUpdate(e) {
    const currentTime = e.detail.currentTime;
    const duration = e.detail.duration;
    const progressWidth = (currentTime / duration) * 100;

    // 更新进度条
    this.setData({
      currentTime,
      duration,
      progressWidth
    });

    // 确保未登录用户不能播放视频
    if (this.data.needLogin && !this.data.showMask) {
      console.log('未登录用户正在播放视频，暂停并显示遮罩');

      // 暂停视频
      const videoContext = wx.createVideoContext('myVideo');
      videoContext.pause();

      // 显示模糊遮罩
      this.setData({
        showMask: true,
        isPlaying: false
      });
    }
  },

  /**
   * 视频播放结束
   */
  onVideoEnded() {
    this.setData({
      isPlaying: false,
      isEnded: true,
      progressWidth: 100
    });

    // 可以在这里添加循环播放的逻辑
    // this.videoContext.seek(0);
    // this.videoContext.play();
  },

  /**
   * 格式化时间为 mm:ss 格式
   */
  formatTime(seconds) {
    const min = Math.floor(seconds / 60);
    const sec = Math.floor(seconds % 60);
    return `${min.toString().padStart(2, '0')}:${sec.toString().padStart(2, '0')}`;
  },

  /**
   * 进度条触摸开始
   */
  onProgressTouchStart(e) {
    this.setData({
      isDragging: true
    });
    this.updateProgressByTouch(e);
  },

  /**
   * 进度条触摸移动
   */
  onProgressTouchMove(e) {
    this.updateProgressByTouch(e);
  },

  /**
   * 进度条触摸结束
   */
  onProgressTouchEnd(e) {
    this.updateProgressByTouch(e);
    this.seekToCurrentProgress();
    this.setData({
      isDragging: false
    });
  },

  /**
   * 进度条点击
   */
  onProgressTap(e) {
    this.updateProgressByTouch(e);
    this.seekToCurrentProgress();
  },

  /**
   * 根据触摸位置更新进度
   */
  updateProgressByTouch(e) {
    // 获取进度条元素信息
    const query = wx.createSelectorQuery();
    query.select('.progress-bar').boundingClientRect();
    query.exec((res) => {
      if (res && res[0]) {
        const progressBar = res[0];
        const touch = e.touches && e.touches[0] ? e.touches[0] : e.changedTouches[0];

        // 计算触摸点相对于进度条起点的距离
        const touchX = touch.clientX - progressBar.left;

        // 计算百分比并限制在0-100之间
        let percent = (touchX / progressBar.width) * 100;
        percent = Math.max(0, Math.min(100, percent));

        // 计算对应的时间
        const dragTime = (percent / 100) * this.data.duration;

        this.setData({
          progressWidth: percent,
          formattedDragTime: this.formatTime(dragTime)
        });
      }
    });
  },

  /**
   * 根据当前进度跳转视频
   */
  seekToCurrentProgress() {
    const videoContext = wx.createVideoContext('myVideo');
    const seekTime = (this.data.progressWidth / 100) * this.data.duration;
    videoContext.seek(seekTime);

    // 如果视频是暂停状态，则自动播放
    if (!this.data.isPlaying) {
      videoContext.play();
      this.setData({
        isPlaying: true,
        isEnded: false
      });
    }
  },

  /**
   * 点赞
   */
  onLike() {
    console.log('点赞');
    // 这里添加点赞逻辑

    // 模拟点赞数增加
    const likes = parseInt(this.data.videoInfo.likes) + 1;
    this.setData({
      'videoInfo.likes': likes.toString()
    });
  },

  /**
   * 评论
   */
  onComment() {
    console.log('评论');
    // 这里添加评论逻辑，可以跳转到评论页面
  },

  /**
   * 分享
   */
  onShare() {
    console.log('分享');
    // 调用微信分享API
  },

  /**
   * 跳转到商家视频列表页
   */
  goToMerchantPage() {
    wx.navigateTo({
      url: '/pages/video/index',
    });
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { id } = options;
    console.log('进入视频详情页，ID:', id);

    // 判断用户是否从分享链接进入
    const pages = getCurrentPages();
    const isFromShare = pages.length <= 1;

    // 如果是从分享链接进入，检查登录状态
    if (isFromShare) {
      this.checkLoginStatus();
    }

    if (!id) {
      wx.showToast({
        title: '视频ID不能为空',
        icon: 'none'
      });
      return;
    }

    // 设置导航栏
    wx.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: '#444b8f'
    });

    // 隐藏导航栏
    wx.hideNavigationBarLoading();

    // 显示加载中
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    // 调用接口获取视频详情
    api.user.getVideoDetail({ id })
      .then(res => {
        console.log('视频详情数据:', res);

        // API直接返回了数据对象，不是嵌套在res.data中
        // 检查返回的数据是否包含视频URL
        if (res && res.video_url) {
          // 详细记录返回的视频URL
          console.log('返回的视频URL:', res.video_url);
          console.log('视频URL类型:', typeof res.video_url);

          // 确保URL是有效的，可能需要处理特殊字符
          const videoUrl = res.video_url.trim();

          console.log('处理后的视频URL:', videoUrl);

          this.setData({
            loading: false, // 先设置loading为false允许视频组件显示
            isVideoReady: false, // 初始视频未准备好
            videoUrl: videoUrl,
            videoInfo: {
            id: res.id || '',
            title: res.title || '',
            description: res.describe || '', // API返回的是describe而不是description
            timeText: res.create_time ? `发布时间: ${res.create_time}` : '',
            timeTextEn: '',
            likes: res.likes ? res.likes.toString() : '0',
            comments: res.comments ? res.comments.toString() : '0',
            shares: res.views ? res.views.toString() : '0', // 使用views作为分享数
            specifications:res.specifications
            }
          }, () => {
            // 数据设置完成后的回调
            console.log('数据设置完成，当前状态:', {
              loading: this.data.loading,
              videoUrl: this.data.videoUrl,
              videoUrlLength: this.data.videoUrl ? this.data.videoUrl.length : 0
            });

            // 检查条件渲染条件是否满足
            const shouldShowVideo = !this.data.loading && this.data.videoUrl;
            console.log('是否应该显示视频:', shouldShowVideo);

            // 如果设置了视频URL，初始化视频组件
            if (this.data.videoUrl) {
              setTimeout(() => {
                console.log('初始化视频播放器');
                const videoContext = wx.createVideoContext('myVideo');
                videoContext.play();
              }, 500);
            }
          });

          // 加载商家信息
          this.loadMerchantInfo();
        } else {
          console.error('API返回数据不包含视频URL:', res);
          wx.showToast({
            title: '获取视频失败，无效的视频地址',
            icon: 'none'
          });

          // 即使API返回错误，也设置loading为false，这样可以显示错误状态
          this.setData({
            loading: false
          });
        }
      })
      .catch(err => {
        console.error('获取视频详情失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });

        // 发生错误时，也要设置loading为false
        this.setData({
          loading: false
        });
      })
      .finally(() => {
        wx.hideLoading();
      });
  },

  /**
   * 加载商家信息
   */
  loadMerchantInfo() {
    api.user.mainMerchantInfo()
      .then(res => {
        console.log('商家信息:', res);
        if (res) {
          this.setData({
            merchantInfo: {
              name: res.company_name || '视频发布者',
              avatar: res.avatar || ''
            }
          });
        }
      })
      .catch(err => {
        console.error('获取商家信息失败', err);
      });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    console.log('页面初次渲染完成');
    this.videoContext = wx.createVideoContext('myVideo');
  },

  handleVideoTap() {
    if(this.data.isPlaying){
      this.videoContext = wx.createVideoContext('myVideo');
      this.setData({isPlaying: false});
      this.videoContext.play();
    }else{
      this.videoContext = wx.createVideoContext('myVideo');
      this.setData({isPlaying:true})
      this.videoContext.pause();
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('页面显示');

    // 如果已经加载了视频URL，尝试播放
    if (this.data.videoUrl) {
      console.log('页面显示时尝试播放视频');
      setTimeout(() => {
        const videoContext = wx.createVideoContext('myVideo');
        videoContext.play();
      }, 500);
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: this.data.videoInfo.title,
      path: `/pages/video/detail?id=${this.data.videoInfo.id}`
    };
  },

  /**
   * 视频元数据加载完成回调
   */
  onVideoLoaded(e) {
    console.log('视频元数据加载完成', e.detail);
    console.log('视频加载成功，可以播放');

    // 视频元数据加载完成，设置视频已准备好
    this.setData({
      isVideoReady: true
    });

    // 如果未登录，立即显示遮罩层并暂停视频
    if (this.data.needLogin) {
      console.log('用户未登录，显示模糊遮罩并暂停视频');
      this.videoContext = wx.createVideoContext('myVideo');
      // 暂停视频播放
      this.videoContext.pause();

      // 显示模糊遮罩
      this.setData({
        showMask: true,
        isPlaying: false,
      });

      // 显示登录弹窗
      setTimeout(() => {
        if (this.data.needLogin) {
          this.setData({
            showLoginPopup: true,
          });
        }
      }, 500);
    } else {
      // 视频加载成功后尝试播放
      setTimeout(() => {
        this.videoContext.play();
      }, 100);
    }
  },

  /**
   * 重播视频
   */
  replayVideo() {
    // const videoContext = wx.createVideoContext('myVideo');
    this.videoContext.seek(0);
    this.videoContext.play();

    this.setData({
      isPlaying: true,
      isEnded: false,
      progressWidth: 0
    });
  },

  // 登录弹窗相关方法
  checkLoginStatus() {
    // 检查用户是否已登录
    const userInfo = util.getUserInfo();
    if (!userInfo || !userInfo.app_id) {
      console.log('用户未登录，将限制视频播放时长');
      // 设置未登录状态，稍后会显示模糊遮罩
      this.setData({
        needLogin: true
      });
    }
  },

  // 遮罩层点击事件
  handleLoginClick() {
    // 如果登录弹窗没有显示，则显示
    if (!this.data.showLoginPopup) {
      this.setData({
        showLoginPopup: true
      });
    }
  },

  // 处理登录按钮点击
  handleLogin() {
    // 隐藏弹窗
    this.setData({
      showLoginPopup: false
    });

    // 跳转到登录页面
    wx.navigateTo({
      url: '/pages/login/index',
      success: () => {
        // 创建一个页面返回监听器，检查用户是否登录成功
        const originalOnShow = this.onShow;
        this.onShow = () => {
          // 调用原始的onShow方法
          if (originalOnShow) {
            originalOnShow.call(this);
          }

          // 检查登录状态
          const updatedUserInfo = util.getUserInfo();
          if (updatedUserInfo && updatedUserInfo.app_id) {
            console.log('用户登录成功，继续播放视频');
            // 用户登录成功，移除模糊遮罩和限制
            this.setData({
              needLogin: false,
              showMask: false
            });

            // 恢复视频播放
            setTimeout(() => {
              const videoContext = wx.createVideoContext('myVideo');
              videoContext.play();
              this.setData({
                isPlaying: true
              });
            }, 500);
          }

          // 恢复原始的onShow方法
          this.onShow = originalOnShow;
        };
      }
    });
  },

  // 关闭登录弹窗 - 阻止未登录用户关闭弹窗
  closeLoginPopup() {
    // 已登录用户可以关闭弹窗
    const userInfo = util.getUserInfo();
    if (userInfo && userInfo.app_id) {
      this.setData({
        showLoginPopup: false
      });
    }
    // 未登录用户不执行任何操作，保持弹窗显示
  },
})