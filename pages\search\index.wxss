.container {
  padding: 0;
  background: #f5f5f5;
}

.search-header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 100;
}

.search-input-wrap {
  flex: 1;
  height: 64rpx;
  background: #f5f5f5;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
}

.search-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 10rpx;
}

.search-input {
  flex: 1;
  height: 64rpx;
  font-size: 26rpx;
  color: #333;
}

.search-btn {
  width: 120rpx;
  height: 64rpx;
  background: #ffa500;
  color: #fff;
  font-size: 28rpx;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
}

.search-results {
  padding: 20rpx;
}

.no-result {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
}

.result-list {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.result-item {
  display: flex;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.result-item:last-child {
  border-bottom: none;
}

.result-image {
  width: 160rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.result-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.result-name {
  font-size: 28rpx;
  color: #333;
}

.result-price {
  font-size: 30rpx;
  color: #f60;
  font-weight: bold;
} 