<!--pages/train_operation/record.wxml-->
<view
    class="container"
    style="--status-bar-height: {{statusBarHeight}}px;"
>
    <!-- 自定义导航栏 -->
    <view
        class="custom-nav"
        style="padding-top: {{statusBarHeight}}px;"
    >
        <view class="nav-content">
            <view
                class="back-icon"
                bindtap="navigateBack"
            >
                <image
                    src="/icons/moments/back.svg"
                    mode="aspectFit"
                ></image>
            </view>
            <view class="nav-title">变更记录</view>
        </view>
    </view>

    <!-- 主内容区域 -->
    <view
        class="main-content"
        style="padding-top: {{statusBarHeight + 44}}px;"
    >
        <!-- 加载中提示 -->
        <view
            class="loading-container"
            wx:if="{{loading}}"
        >
            <view class="loading-spinner"></view>
            <text class="loading-text">加载中...</text>
        </view>

        <!-- 错误提示 -->
        <view
            class="error-container"
            wx:elif="{{error}}"
        >
            <text class="error-text">{{error}}</text>
        </view>

        <!-- 无数据提示 -->
        <view
            class="empty-container"
            wx:elif="{{!recordList || recordList.length === 0}}"
        >
            <text class="empty-text">暂无变更记录</text>
        </view>

        <!-- 记录列表 -->
        <scroll-view
            scroll-y
            class="record-scroll"
            wx:else
        >
            <view class="record-list">
                <view
                    class="record-item"
                    wx:for="{{recordList}}"
                    wx:key="operationTime"
                >
                    <view class="record-row">
                        <text class="record-label">操作时间</text>
                        <text class="record-value">{{item.operationTime}}</text>
                    </view>
                    <view class="record-row">
                        <text class="record-label">变更事项</text>
                        <text class="record-value">{{item.changeMatter}}</text>
                    </view>
                    <view class="record-row">
                        <text class="record-label">变更原因</text>
                        <text class="record-value">{{item.changeReason}}</text>
                    </view>
                    <view class="record-row">
                        <text class="record-label">操作人</text>
                        <text class="record-value">{{item.operator}}</text>
                    </view>
                </view>
            </view>
        </scroll-view>
    </view>
</view>