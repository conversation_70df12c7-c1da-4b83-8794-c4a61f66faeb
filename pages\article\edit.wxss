/* pages/moments/release.wxss */

.release-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  background-color: #f7f7f7;
  box-sizing: border-box;
  padding-bottom: 0; /* 移除底部内边距 */
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 20rpx;
  background-color: #fff;
  position: relative;
  border-bottom: 1rpx solid #eee;
}

.back-icon {
  font-size: 36rpx;
  color: #333;
  width: 60rpx;
}

.title {
  font-size: 34rpx;
  font-weight: 500;
  flex: 1;
  text-align: center;
}

.actions {
  display: flex;
  align-items: center;
  width: 60rpx;
}

.more {
  font-size: 40rpx;
  margin-right: 15rpx;
}

.circle {
  font-size: 30rpx;
  color: #999;
}

.form-container {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
  padding-bottom: 30rpx; /* 调整与底部按钮的距离 */
}

.form-item {
  background-color: #fff;
  padding: 20rpx;
  margin-bottom: 2rpx;
  display: flex;
  align-items: center;
  position: relative;
}

.label {
  width: 180rpx;
  font-size: 28rpx;
  color: #333;
}

.required::before {
  content: '*';
  color: #f00;
  margin-right: 6rpx;
}

.custom-picker {
  flex: 1;
  position: relative;
}

.picker-value {
  font-size: 28rpx;
  color: #333;
  text-align: right;
  position: relative;
  padding: 12rpx 40rpx 12rpx 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.picker-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  max-height: 400rpx;
  overflow-y: auto;
  background-color: #fff;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
  border-radius: 8rpx;
  border: 2rpx solid #ddd;
  z-index: 999;
  margin-top: 8rpx;
}

.picker-option {
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1rpx solid #f5f5f5;
  background-color: #fff;
}

.picker-option:hover,
.picker-option:active {
  background-color: #f0f0f0;
}

.picker-option.active {
  background-color: #1a73e8;
  color: #fff;
}

.arrow {
  position: absolute;
  right: 10rpx;
  font-size: 24rpx;
  color: #999;
}

input {
  flex: 1;
  text-align: right;
  font-size: 28rpx;
  padding: 12rpx 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  background-color: #fff;
}

.radio-group {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

.radio-item {
  display: flex;
  align-items: center;
  margin-left: 30rpx;
}

radio {
  transform: scale(0.7);
}

textarea {
  width: 100%;
  height: 180rpx;
  margin-top: 20rpx;
  padding: 20rpx;
  font-size: 28rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  box-sizing: border-box;
  background-color: #fff;
}

.word-count {
  font-size: 24rpx;
  color: #999;
  text-align: right;
  margin-top: 10rpx;
}

.upload-container {
  margin-top: 20rpx;
  display: flex;
  flex-wrap: wrap;
}

.upload-btn {
  width: 160rpx;
  height: 160rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f7f7f7;
  border: 1rpx dashed #ddd;
}

.plus {
  font-size: 60rpx;
  color: #ccc;
}

.image-preview {
  width: 160rpx;
  height: 160rpx;
  position: relative;
  overflow: hidden;
}

.image-preview image {
  width: 100%;
  height: 100%;
  display: block;
}

.image-preview .reupload {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 24rpx;
  text-align: center;
  padding: 6rpx 0;
}

.bottom-actions {
  display: flex;
  padding: 20rpx;
  background-color: #fff;
  border-top: 1rpx solid #eee;
  position: relative;
  z-index: 10;
  box-sizing: border-box;
  padding-bottom: calc(20rpx + constant(safe-area-inset-bottom)); /* 兼容 iOS < 11.2 */
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom)); /* 兼容 iOS >= 11.2 */
}

.save-draft {
  flex: 1;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f2f2f2;
  color: #666;
  border-radius: 44rpx;
  margin-right: 20rpx;
  font-size: 30rpx;
}

.publish {
  flex: 1;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #09c;
  color: #fff;
  border-radius: 44rpx;
  font-size: 30rpx;
}

/* 原有的picker类现在只用于日期选择器 */
.picker {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  text-align: right;
  position: relative;
  padding: 12rpx 40rpx 12rpx 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.picker-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.picker-cancel {
  font-size: 26rpx;
  color: #09c;
}

.empty-tip {
  padding: 20rpx;
  color: #999;
  text-align: center;
  font-size: 28rpx;
}

.brands-count-debug {
  background-color: #f0f8ff;
  padding: 10rpx 20rpx;
  color: #0066cc;
  text-align: center;
  font-size: 24rpx;
  border-bottom: 1rpx solid #eee;
}