import api from '../../utils/api';
import util from '../../utils/util';
const langData = require('../../utils/lang-function.js');

Page({
    data: {
        phoneNumber: '',
        code: '',
        countdown: 0,
        isAgree: false,
        loading: false,
        showMask: false,
        openid: '',
        hasOpenid: false,
        statusBarHeight: wx.getSystemInfoSync().statusBarHeight || 20,
        text:{}
    },

      //语言刷新
  refreshLanguage(newLang){
    console.log('页面语言已切换到:', newLang);
  },
  // 更新页面文本
  updateText() {
    this.setData({
      text:{
        zhaochexia:langData.t('zhaochexia'),
        i_agree_to_abide_by:langData.t('i_agree_to_abide_by'),
        user_service_agreement:langData.t('user_service_agreement'),
        privacy_policy:langData.t('privacy_policy'),
        please_log_in:langData.t('please_log_in'),
        quick_login:langData.t('quick_login'),
        login_with_password:langData.t('login_with_password')
      }
    })
  },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        this.updateText();
        console.log('登录页面加载');

        // 每次页面加载时都获取新的授权信息
        this.ensureOpenid()
            .then(openid => {
                console.log('页面加载时获取授权信息成功');
            })
            .catch(err => {
                console.error('页面加载时获取授权信息失败', err);
            });
    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {
        console.log('登录页面显示');

        // 每次页面显示时都获取新的授权信息
        this.ensureOpenid()
            .then(openid => {
                console.log('页面显示时获取授权信息成功');
            })
            .catch(err => {
                console.error('页面显示时获取授权信息失败', err);
            });
    },

    // 输入手机号
    onPhoneInput(e) {
        this.setData({
            phoneNumber: e.detail.value
        });
    },

    // 输入验证码
    onCodeInput(e) {
        this.setData({
            code: e.detail.value
        });
    },

    // 发送验证码
    sendCode() {
        const { phoneNumber, countdown } = this.data;

        // 如果正在倒计时，不允许再次发送
        if (countdown > 0) return;

        // 验证手机号
        if (!this.validatePhone(phoneNumber)) {
            wx.showToast({
                title: '请输入正确的手机号',
                icon: 'none'
            });
            return;
        }

        // 发送验证码
        wx.showLoading({
            title: '发送中...',
            mask: true
        });

        // 调用发送验证码接口
        api.user.sendSmsCode({
            phone: phoneNumber
        }).then(() => {
            wx.hideLoading();
            wx.showToast({
                title: '验证码已发送',
                icon: 'success'
            });

            // 开始倒计时
            this.startCountdown();
        }).catch(err => {
            wx.hideLoading();
            wx.showToast({
                title: err.message || '发送失败，请重试',
                icon: 'none'
            });
        });
    },

    // 开始倒计时
    startCountdown() {
        this.setData({
            countdown: 60
        });

        const timer = setInterval(() => {
            if (this.data.countdown <= 1) {
                clearInterval(timer);
                this.setData({
                    countdown: 0
                });
            } else {
                this.setData({
                    countdown: this.data.countdown - 1
                });
            }
        }, 1000);
    },

    // 验证手机号
    validatePhone(phone) {
        return /^1[3-9]\d{9}$/.test(phone);
    },

    // 切换协议同意状态
    toggleAgree() {
        this.setData({
            isAgree: !this.data.isAgree
        });
    },

    // 查看服务协议
    viewAgreement(e) {
        const type = e.currentTarget.dataset.type;
        let url = '';

        if (type === 'service') {
            url = '/pages/agreement/service';
        } else if (type === 'privacy') {
            url = '/pages/agreement/privacy';
        } else if (type === 'personal') {
            url = '/pages/agreement/personal';
        }

        wx.navigateTo({
            url
        });
    },

    // 手机号一键登录
    loginWithPhone() {
        if (!this.data.isAgree) {
            wx.showToast({
                title: langData.t('agree_to_terms_first'),
                icon: 'none'
            });
            return;
        }

        this.setData({
            loading: true
        });

        // 调用微信登录接口获取手机号
        wx.login({
            success: (res) => {
                if (res.code) {
                    // 获取到微信登录code后，调用后端接口登录
                    api.user.loginWithWechat({
                        code: res.code
                    }).then(result => {
                        this.loginSuccess(result);
                    }).catch(err => {
                        this.setData({ loading: false });
                        wx.showToast({
                            title: err.message || '登录失败，请重试',
                            icon: 'none'
                        });
                    });
                } else {
                    this.setData({ loading: false });
                    wx.showToast({
                        title: '微信登录失败，请重试',
                        icon: 'none'
                    });
                }
            },
            fail: () => {
                this.setData({ loading: false });
                wx.showToast({
                    title: '微信登录失败，请重试',
                    icon: 'none'
                });
            }
        });
    },

    // 验证码登录
    loginWithCode() {
        const { phoneNumber, code, isAgree } = this.data;

        if (!this.validatePhone(phoneNumber)) {
            wx.showToast({
                title: '请输入正确的手机号',
                icon: 'none'
            });
            return;
        }

        if (!code || code.length !== 6) {
            wx.showToast({
                title: langData.t('agree_to_terms_first'),
                icon: 'none'
            });
            return;
        }

        if (!isAgree) {
            wx.showToast({
                title: langData.t('agree_to_terms_first'),
                icon: 'none'
            });
            return;
        }

        this.setData({
            loading: true
        });

        // 调用验证码登录接口
        api.user.loginWithSmsCode({
            phone: phoneNumber,
            code: code
        }).then(result => {
            this.loginSuccess(result);
        }).catch(err => {
            this.setData({ loading: false });
            wx.showToast({
                title: err.message || '登录失败，请重试',
                icon: 'none'
            });
        });
    },

    // 登录成功处理
    loginSuccess(result) {

        // 保存用户信息和登录状态
        const userInfo = util.storeLoginFlags(result)

        wx.showToast({
            title: '登录成功',
            icon: 'success',
            duration: 1500
        });

        // 延迟跳转
        setTimeout(() => {
            if (!result.info) {
                wx.navigateTo({
                    url: '/pages/info/index?phone=' + userInfo.phone
                });
            } else {
                wx.switchTab({
                    url: '/pages/my/index'
                });
            }
        }, 1500);
    },

    // 创建全屏遮罩层
    createMask() {
        // 创建一个全屏的遮罩层
        const maskView = wx.createAnimation({
            duration: 0
        });

        // 将遮罩层添加到页面中
        const maskElement = wx.createSelectorQuery().select('.login-mask');
        if (!maskElement) {
            // 如果遮罩层不存在，创建一个
            const mask = document.createElement('view');
            mask.className = 'login-mask';
            mask.style.position = 'fixed';
            mask.style.top = '0';
            mask.style.left = '0';
            mask.style.width = '100%';
            mask.style.height = '100%';
            mask.style.backgroundColor = '#ffffff';
            mask.style.zIndex = '9999';
            document.body.appendChild(mask);
        }
    },

    // 添加返回按钮功能
    navigateBack() {
        wx.navigateBack({
            delta: 1
        });
    },

    // 获取手机号
    getPhoneNumber(e) {
        // 检查是否同意协议
        if (!this.data.isAgree) {
            wx.showToast({
                title: langData.t('agree_to_terms_first'),
                icon: 'none'
            });
            return;
        }

        // 如果用户拒绝授权
        if (e.detail.errMsg !== 'getPhoneNumber:ok') {
            wx.showToast({
                title: '获取手机号失败，请重试',
                icon: 'none'
            });
            return;
        }

        this.setData({
            loading: true
        });

        // 每次都重新获取授权信息，确保授权信息已写入数据库
        this.ensureOpenid()
            .then(openid => {
                console.log('获取最新授权信息成功，继续处理手机号登录:', openid);
                this.processPhoneLogin(e.detail);
            })
            .catch(err => {
                console.error('获取授权信息失败:', err);
                this.setData({ loading: false });
                wx.showToast({
                    title: '授权失败，请重试',
                    icon: 'none'
                });
            });
    },

    // 处理手机号登录逻辑
    processPhoneLogin(detail) {
        // 继续登录流程
        api.user.loginWithWechatPhone({
            encryptedData: detail.encryptedData,
            iv: detail.iv,
            openid: this.data.openid
        }).then(result => {
            this.loginSuccess(result);
        }).catch(err => {
            this.setData({ loading: false });
            wx.showToast({
                title: err.message || '登录失败，请重试',
                icon: 'none'
            });
        });
    },

    // 检查是否同意协议
    checkAgreement() {
        if (!this.data.isAgree) {
            wx.showToast({
                title: langData.t('agree_to_terms_first'),
                icon: 'none'
            });
        }
        // 如果已同意，不做任何处理，让 open-type="getPhoneNumber" 正常触发
    },

    // 账号密码登录跳转
    navigateToAccountLogin() {
        // 跳转到账号密码登录页面
        wx.navigateTo({
            url: '/pages/login/login_form'
        });
    },

    /**
     * 获取微信openid
     */
    getWechatOpenid() {
        // 此方法已被ensureOpenid取代，为保持代码兼容性，调用ensureOpenid
        this.ensureOpenid()
            .then(() => {
                console.log('授权信息获取成功');
            })
            .catch(err => {
                console.error('授权信息获取失败', err);
            });
    },

    /**
     * 确保获取到有效的openid，每次都获取新的授权码并调用接口
     * @returns {Promise} Promise对象，resolve时表示已获取到有效openid，reject表示获取失败
     */
    ensureOpenid() {
        return new Promise((resolve, reject) => {
            console.log('获取新的授权信息');
            wx.showLoading({
                title: langData.t('authorizing')+'...',
                mask: true
            });

            wx.login({
                success: res => {
                    if (res.code) {
                        console.log('获取微信授权码成功:', res.code);
                        api.user.getWechatInfo({
                            code: res.code
                        }).then(result => {
                            wx.hideLoading();
                            console.log('获取openid成功:', result.openid);

                            // 缓存openid
                            util.setCacheWithExpiry('openid', result.openid, 1, "hours");
                            util.setCacheWithExpiry('session_key', result.session_key, 1, "hours");

                            // 更新页面数据
                            this.setData({
                                openid: result.openid,
                                hasOpenid: true
                            });

                            resolve(result.openid);
                        }).catch(err => {
                            wx.hideLoading();
                            console.error('获取openid失败:', err);
                            wx.showToast({
                                title: '授权失败，请重试',
                                icon: 'none'
                            });
                            reject(err);
                        });
                    } else {
                        wx.hideLoading();
                        console.error('获取微信授权码失败');
                        wx.showToast({
                            title: '授权失败，请重试',
                            icon: 'none'
                        });
                        reject(new Error('获取微信授权码失败'));
                    }
                },
                fail: err => {
                    wx.hideLoading();
                    console.error('调用wx.login失败:', err);
                    wx.showToast({
                        title: '授权失败，请重试',
                        icon: 'none'
                    });
                    reject(err);
                }
            });
        });
    },

    /**
     * 登录前检查
     * 先检查协议是否同意，然后获取授权信息
     */
    preCheckLogin() {
        // 先检查是否同意协议
        if (!this.data.isAgree) {
            wx.showToast({
                title: langData.t('agree_to_terms_first'),
                icon: 'none'
            });
            return;
        }

        // 无论是否有openid，都重新获取授权信息
        console.log('获取授权信息');
        this.ensureOpenid()
            .then(() => {
                console.log('已获取最新授权信息');
                // 不需要做任何事情，页面会因为hasOpenid状态变更而刷新
                // 按钮的open-type属性会自动变为getPhoneNumber
            })
            .catch(err => {
                console.error('获取授权信息失败', err);
            });
    }
}) 