// 修改 API 引入方式
const api = require('../../utils/api').default;

import share from '../../utils/share';  // 确保导入 分享

Page({
    behaviors: [share], //分享设置
    data: {
        shareData: {},
        id: 2, // 写死的ID
        vehicleDetail: null,
        isLoading: true,
        isCollected: false,
        natureText: '',
        deadlineDate: '',
        isDebug: true, // 开发环境设为 true，生产环境设为 false
        imagesLoaded: {},
        imagesLoadError: {},
        showConfigPopup: false,
        showTimePopup: false,
        currentDetail: {},
        statusBarHeight: 20, // 初始化状态栏高度
        current: 0 // 添加轮播图当前索引
    },

    onLoad: function (options) {
        // 从页面参数中获取 ID，如果没有则使用默认值 2
        const id = options.id || 2;
        this.setData({
            id: id
        });
        this.fetchVehicleDetail();

        // 获取状态栏高度
        const that = this;
        wx.getSystemInfo({
            success: function (res) {
                that.setData({
                    statusBarHeight: res.statusBarHeight
                });
            }
        });
    },

    // 返回上一页
    goBack: function () {
        wx.navigateBack({
            delta: 1
        });
    },

    // 获取车辆详情
    fetchVehicleDetail: function () {
        const that = this;
        wx.showLoading({
            title: '加载中...',
        });

        // 使用 car API 调用接口
        api.car.getDetail({
            id: that.data.id,
            type: 'batch'
        }).then(res => {
            // console.log('获取到的批量车数据:', res);

            // 检查返回的数据结构
            // 如果直接返回了数据对象而不是标准响应结构
            let detail = res;

            // 如果是标准响应结构（包含code和data字段）
            if (res && res.code === 0 && res.data) {
                detail = res.data;
            }

            // console.log('处理后的详情数据:', detail);

            if (detail && detail.id) {
                // 处理车辆性质
                let natureText = '';
                switch (detail.nature) {
                    case 1:
                        natureText = '新车';
                        break;
                    case 2:
                        natureText = '二手车';
                        break;
                    case 3:
                        natureText = '库存车';
                        break;
                    default:
                        natureText = '未知';
                }

                // 处理截止日期
                let deadlineDate = '';
                if (detail.deadline_time) {
                    deadlineDate = detail.deadline_time.split(' ')[0];
                }

                // 处理车源地
                if (!detail.vehicle_source_location) {
                    detail.vehicle_source_location = '未知';
                }

                // 确保发票和出口情况有值
                if (detail.is_invoice === undefined) {
                    detail.is_invoice = false;
                }
                if (detail.is_export === undefined) {
                    detail.is_export = false;
                }

                // 处理图片数据
                let imageUrls = [];

                // 尝试从 urls 字段获取图片
                if (detail.urls) {
                    try {
                        if (typeof detail.urls === 'string') {
                            // 直接使用正则表达式提取所有 URL
                            const urlMatches = detail.urls.match(/(https?:\/\/[^",\s]+)/g);
                            if (urlMatches && urlMatches.length > 0) {
                                imageUrls = urlMatches;
                                // console.log('通过正则提取的图片URL:', imageUrls);
                            } else {
                                // 尝试 JSON 解析
                                try {
                                    // 移除可能的转义字符和多余的引号
                                    let urlsStr = detail.urls.replace(/\\/g, '');
                                    if (urlsStr.startsWith('"') && urlsStr.endsWith('"')) {
                                        urlsStr = urlsStr.substring(1, urlsStr.length - 1);
                                    }
                                    const parsedUrls = JSON.parse(urlsStr);
                                    if (Array.isArray(parsedUrls)) {
                                        imageUrls = parsedUrls;
                                    }
                                    // console.log('JSON解析后的图片数组:', imageUrls);
                                } catch (jsonError) {
                                    console.error('JSON解析失败:', jsonError);
                                }
                            }
                        } else if (Array.isArray(detail.urls)) {
                            imageUrls = detail.urls;
                        }
                    } catch (e) {
                        console.error('处理urls失败:', e);
                    }
                }

                // 如果 urls 处理失败，尝试使用 image_urls
                if (imageUrls.length === 0 && detail.image_urls) {
                    if (Array.isArray(detail.image_urls)) {
                        imageUrls = detail.image_urls;
                    } else if (typeof detail.image_urls === 'string') {
                        try {
                            imageUrls = JSON.parse(detail.image_urls);
                        } catch (e) {
                            console.error('解析image_urls失败:', e);
                        }
                    }
                }

                // 确保每个URL都是有效的
                imageUrls = imageUrls.filter(url => {
                    return typeof url === 'string' && url.startsWith('http');
                });

                // console.log('最终使用的图片数组:', imageUrls);

                // 在处理完图片 URL 后，添加一些测试图片
                if (imageUrls.length === 0) {
                    // 添加测试图片
                    imageUrls = [
                        'https://img.yzcdn.cn/vant/cat.jpeg',
                        'https://img.yzcdn.cn/vant/dog.jpeg'
                    ];
                    // console.log('使用测试图片:', imageUrls);
                }
                detail.image_urls = imageUrls;

                // 确保 details 中的每个项目都有 retail_price
                if (detail.details && Array.isArray(detail.details)) {
                    detail.details.forEach(item => {
                        if (!item.retail_price) {
                            item.retail_price = '暂无';
                        }
                        if (!item.large_mileage) {
                            item.large_mileage = '0';
                        }
                    });
                }

                const shareData = {
                    title: detail.ui_vehicle_name || '暂无标题',
                    path: '/pages/vehicle/bdetail?id=' + that.data.id, // 分享路径，默认为当前页面路径
                    imageUrl: imageUrls[0] || "", // 分享图片（可选）
                    isDetailPage: true, // 标记是否详情页
                }
                this.setData({ shareData })

                that.setData({
                    vehicleDetail: detail,
                    isLoading: false,
                    natureText: natureText,
                    deadlineDate: deadlineDate
                });

                // console.log('设置到页面的数据:', detail);
            } else {
                console.error('无效的车辆数据:', detail);
                wx.showToast({
                    title: '获取数据失败',
                    icon: 'none'
                });
                that.setData({
                    isLoading: false
                });
            }
        }).catch(err => {
            console.error('获取批量车详情失败:', err);
            wx.showToast({
                title: '网络错误',
                icon: 'none'
            });
            that.setData({
                isLoading: false
            });
        }).finally(() => {
            wx.hideLoading();
        });
    },

    // 格式化日期
    formatDate: function (dateString) {
        if (!dateString) return '无';
        return dateString.split(' ')[0];
    },

    // 处理轮播图切换事件
    swiperChange: function (e) {
        this.setData({
            current: e.detail.current
        });
    },

    // 预览图片
    previewImage: function (e) {
        const current = e.currentTarget.dataset.url;
        const urls = this.data.vehicleDetail.image_urls;

        wx.previewImage({
            current: current,
            urls: urls
        });
    },

    // 拨打电话
    makePhoneCall: function () {
        wx.makePhoneCall({
            phoneNumber: '************', // 替换为实际电话号码
            fail: function () {
                wx.showToast({
                    title: '拨打电话失败',
                    icon: 'none'
                });
            }
        });
    },

    // 打开聊天
    openChat: function () {
        // 这里可以跳转到聊天页面或者打开客服会话
        wx.showToast({
            title: '暂未开放此功能',
            icon: 'none'
        });
    },

    // 收藏车辆
    collectVehicle: function () {
        const isCollected = !this.data.isCollected;
        this.setData({
            isCollected: isCollected
        });

        wx.showToast({
            title: isCollected ? '收藏成功' : '已取消收藏',
            icon: 'success'
        });

        // 这里可以添加实际的收藏/取消收藏逻辑
    },

    onShareAppMessage: function () {
        const vehicleDetail = this.data.vehicleDetail;
        return {
            title: vehicleDetail ? vehicleDetail.ui_vehicle_name : '批量车源详情',
            path: '/pages/vehicle/bdetail?id=' + this.data.id + '&type=batch'
        };
    },

    // 修改图片加载处理方法
    handleImageLoad: function (e) {
        const index = e.currentTarget.dataset.index;
        const url = e.currentTarget.dataset.url;
        // console.log(`图片 ${index} 加载成功:`, url);

        let imagesLoaded = this.data.imagesLoaded;
        imagesLoaded[index] = true;
        this.setData({
            imagesLoaded: imagesLoaded
        });
    },

    handleImageError: function (e) {
        const index = e.currentTarget.dataset.index;
        const url = e.currentTarget.dataset.url;
        console.error(`图片 ${index} 加载失败:`, url, e);

        let imagesLoaded = this.data.imagesLoaded;
        let imagesLoadError = this.data.imagesLoadError;

        imagesLoaded[index] = true;
        imagesLoadError[index] = true;

        this.setData({
            imagesLoaded: imagesLoaded,
            imagesLoadError: imagesLoadError
        });
    },

    showConfigInfo: function (e) {
        const index = e.currentTarget.dataset.index;
        const detail = this.data.vehicleDetail.details[index];

        this.setData({
            currentDetail: detail,
            showConfigPopup: true
        });
    },

    closeConfigPopup: function () {
        this.setData({
            showConfigPopup: false
        });
    },

    showTimeInfo: function (e) {
        const index = e.currentTarget.dataset.index;
        const detail = this.data.vehicleDetail.details[index];

        this.setData({
            currentDetail: detail,
            showTimePopup: true
        });
    },

    closeTimePopup: function () {
        this.setData({
            showTimePopup: false
        });
    },

    // 收藏/取消收藏
    toggleCollect: function () {
        const isCollected = !this.data.isCollected;

        this.setData({
            isCollected: isCollected
        });

        // 显示操作结果提示
        wx.showToast({
            title: isCollected ? '收藏成功' : '已取消收藏',
            icon: 'success',
            duration: 1500
        });

        // TODO: 实际的收藏/取消收藏接口调用
        // api.user.toggleCollect({
        //    id: this.data.id,
        //    type: 'batch_vehicle',
        //    action: isCollected ? 'add' : 'remove'
        // });
    },

    // 联系商家
    contactMerchant: function () {
        // 可以根据实际需求调整联系方式
        wx.showActionSheet({
            itemList: ['拨打电话', '在线咨询'],
            success: (res) => {
                if (res.tapIndex === 0) {
                    // 拨打电话
                    wx.makePhoneCall({
                        phoneNumber: '************', // 替换为实际电话号码
                        fail: function () {
                            wx.showToast({
                                title: '拨打电话失败',
                                icon: 'none'
                            });
                        }
                    });
                } else if (res.tapIndex === 1) {
                    // 在线咨询
                    wx.showToast({
                        title: '正在跳转到聊天页面',
                        icon: 'loading',
                        duration: 1000,
                        success: () => {
                            // TODO: 跳转到聊天页面
                            setTimeout(() => {
                                wx.showToast({
                                    title: '功能开发中',
                                    icon: 'none'
                                });
                            }, 1000);
                        }
                    });
                }
            }
        });
    }
});
