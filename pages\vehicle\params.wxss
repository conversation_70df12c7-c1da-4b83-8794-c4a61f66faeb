/* pages/vehicle/params.wxss */
page {  
  overflow-x: hidden;  
  width: 100%;  
  box-sizing: border-box;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
}

.container {
  padding-bottom: calc(-60rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(-60rpx + env(safe-area-inset-bottom));
  background-color: transparent;
  margin: 0;
  padding-top: 0;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
  position: relative;
}

/* ==================== 顶部导航区域样式 ==================== */

/* 头部占位符 - 防止内容被固定顶部遮挡 */
.header-placeholder {
  width: 100%;
  /* 高度由 JavaScript 动态设置，这里减少高度 */
}

/* 固定头部样式 */
.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.2);
}

/* 顶部状态栏 */
.status-bar {
  width: 100%;
  background: transparent;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  width: 100%;
  background: transparent;
  position: relative;
  padding: 0 20rpx;
  box-sizing: border-box;
}

.nav-back {
  width: 88rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  z-index: 10;
}

.nav-title {
  position: absolute;
  left: 0;
  right: 0;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin: 0 auto;
  width: 60%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  z-index: 5;
}

.nav-placeholder {
  width: 88rpx;
  height: 88rpx;
  visibility: hidden;
  flex-shrink: 0;
}

/* ==================== Tab导航卡片样式 ==================== */

.tab-card {
  background-color: #fff;
  margin: 0 30rpx 10rpx 30rpx; /* 进一步减少上边距至0 */
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  display: flex;
  padding: 10rpx; /* 稍微减小内边距 */
  gap: 8rpx;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  position: relative;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.tab-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #666;
  transition: all 0.3s ease;
}

.tab-item.active {
  background-color: #fff;
}

.tab-item.active .tab-text {
  color: #1989fa;
  font-weight: 600;
}

.tab-line {
  position: absolute;
  bottom: 6rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: transparent;
  border-radius: 2rpx;
  opacity: 0;
  transition: all 0.3s ease;
}

.tab-item.active .tab-line {
  background-color: #1989fa;
  opacity: 1;
}

/* ==================== 内容区域样式 ==================== */

.content-container {
  margin: 0;
  padding-bottom: 50rpx;
}

.tab-content {
  width: 100%;
  text-align: left;
  padding: 0 30rpx;
  box-sizing: border-box;
}

/* 内容卡片样式 */
.content-card {
  margin-bottom: 30rpx;
  padding: 30rpx;
  border-radius: 20rpx;
  background-color: #fff;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  padding-left: 10rpx;
  position: relative;
}

.card-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 5rpx;
  height: 28rpx;
  background-color: #1989fa;
  border-radius: 3rpx;
}

.info-row {
  display: flex;
  border-bottom: 1rpx solid #f5f5f5;
  padding: 24rpx 10rpx;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  width: 25%;
  font-size: 28rpx;
  color: #666;
}

.info-value {
  width: 25%;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* ==================== 参数配置样式 ==================== */

/* 参数横向导航 */
.param-nav {
  display: flex;
  white-space: nowrap;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 0 20rpx;
  margin-bottom: 0;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  height: 80rpx;
  width: 100%;
  box-sizing: border-box;
}

.param-nav-item {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 30rpx;
  height: 80rpx;
  font-size: 28rpx;
  position: relative;
  color: #666;
  font-weight: 400;
}

.param-nav-item.active {
  color: #1989fa;
  font-weight: 500;
  position: relative;
}

.param-nav-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 30rpx;
  height: 4rpx;
  background-color: #1989fa;
  border-radius: 2rpx;
}

/* 参数内容区域 */
.param-content {
  background-color: #fff;
  border-radius: 0 0 20rpx 20rpx;
  padding: 20rpx 0;
  margin: 0 0 30rpx 0;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.param-panel {
  padding: 0 20rpx;
}

/* 参数行样式 */
.param-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.param-row:nth-child(odd) {
  background-color: #f8f8f8;
}

.param-row:last-child {
  border-bottom: none;
}

.param-label {
  font-size: 28rpx;
  color: #666;
  font-weight: 400;
  flex: 1;
  text-align: left;
}

.param-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  text-align: right;
}

/* 灰色交替行 */
.info-row.gray {
  background-color: #f8f8f8;
}

/* 空内容提示 */
.empty-tip {
  text-align: center;
  padding: 60rpx 0;
  color: #999;
  font-size: 28rpx;
}

/* 增加底部的最后一个卡片额外边距 */
.content-card:last-child,
.param-panel:last-child {
  margin-bottom: 50rpx;
}