<!--pages/train_operation/appendix.wxml-->
<view
    class="container"
    style="--status-bar-height: {{statusBarHeight}}px;"
>
    <!-- 自定义导航栏 -->
    <view
        class="custom-nav"
        style="padding-top: {{statusBarHeight}}px;"
    >
        <view class="nav-content">
            <view
                class="back-icon"
                bindtap="navigateBack"
            >
                <van-icon
                    name="arrow-left"
                    size="20px"
                    color="#333"
                />
            </view>
            <view class="nav-title">{{title}}</view>
        </view>
    </view>

    <!-- 主内容区域 -->
    <view
        class="main-content"
        style="padding-top: calc({{statusBarHeight}}px + 44px);"
    >
        <!-- 操作类型和名称信息 -->
        <view class="appendix-info">

            <view class="info-card">
                <view class="info-title">操作名称</view>
                <view class="info-content">{{name || '未指定'}}</view>
            </view>
        </view>

        <!-- 上传附件区域 -->
        <view class="appendix-content">
            <view class="upload-card">
                <view class="card-title">上传附件</view>

                <view class="upload-section">
                    <view class="upload-title">附件</view>
                    <view class="upload-area">
                        <view
                            class="upload-button"
                            bindtap="uploadFile"
                            data-type="appendix"
                        >
                            <van-icon
                                name="plus"
                                size="24px"
                                color="#999"
                            />
                        </view>
                        <!-- 文件卡片列表 -->
                        <view
                            class="file-cards"
                            wx:if="{{files.length > 0}}"
                        >
                            <view
                                class="file-card"
                                wx:for="{{files}}"
                                wx:key="index"
                            >
                                <view class="file-card-content">
                                    <!-- 根据文件类型显示不同内容 -->
                                    <view
                                        class="file-icon"
                                        wx:if="{{!item.isImage}}"
                                    >
                                        <van-icon
                                            name="description"
                                            size="40rpx"
                                            color="#3B82F6"
                                        />
                                    </view>
                                    <!-- 图片缩略图 -->
                                    <image
                                        wx:if="{{item.isImage}}"
                                        class="file-image"
                                        src="{{item.url}}"
                                        mode="aspectFill"
                                    ></image>
                                    <view
                                        class="file-delete"
                                        catchtap="deleteFile"
                                        data-index="{{index}}"
                                    >
                                        <van-icon
                                            name="close"
                                            size="30rpx"
                                            color="#ff4d4f"
                                        />
                                    </view>
                                </view>
                                <view class="file-name">{{item.name}}</view>
                            </view>
                        </view>
                    </view>
                    <view class="upload-limit">最多上传3个附件</view>
                </view>

                <view class="remark-section">
                    <view class="remark-title">备注说明</view>
                    <textarea
                        class="remark-input"
                        placeholder="请输入备注说明"
                        bindinput="onRemarkInput"
                        value="{{remark}}"
                    ></textarea>
                </view>

                <view class="button-group">
                    <button
                        class="btn-save"
                        bindtap="onSave"
                    >暂存</button>
                    <button
                        class="btn-submit"
                        bindtap="onSubmit"
                    >提交</button>
                </view>
            </view>
        </view>
    </view>
</view>