<view class="container">
  <language-switcher position="top-left" showLangList="{{true}}" size="normal" style="z-index: 1001;"/>
  <!-- 自定义导航栏 -->
  <view
    class="custom-nav"
    style="padding-top: {{statusBarHeight}}px;"
  >
    <view class="nav-content">
      <!-- <view class="back-icon" bindtap="navigateBack">
        <image src="/icons/moments/back.svg" mode="aspectFit"></image>
      </view> -->
      <view class="nav-title">{{text.register_personal_information}}</view>
    </view>
  </view>

  <!-- 头像上传区域 -->
  <view class="avatar-upload">
    <button
      class="avatar-button"
      open-type="chooseAvatar"
      bindchooseavatar="onChooseAvatar"
    >
      <image
        class="avatar-image"
        src="{{avatarUrl || 'https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/uploads/proof/1751274246972_981.png'}}"
        mode="aspectFill"
      ></image>
      <view class="avatar-text">{{text.click_to_upload_avatar}}</view>
    </button>
  </view>

  <!-- 表单区域 -->
  <view class="form-section">
    <!-- 昵称 -->
    <view class="form-item">
      <text class="form-label required">{{text.name}}</text>
      <view class="input-wrapper">
        <input
          class="form-input"
          placeholder="{{text.please_enter_the_company_name}}"
          value="{{companyShortName}}"
          bindinput="onCompanyShortNameInput"
          type="nickname"
        />
      </view>
    </view>

    <!-- 用户类型 -->
    <view class="form-item">
      <text class="form-label required">{{text.please_select_user_type}}</text>
      <view class="user-type-grid">
        <view
          class="user-type-item {{userType === 1 ? 'active' : ''}}"
          bindtap="setUserType"
          data-type="1"
          hover-class="none"
        >
          <image
            class="type-icon"
            src="/icons/register/v.png"
            mode="aspectFit"
          ></image>
          <text>{{text.vehicle_supplier}}</text>
        </view>
        <view
          class="user-type-item {{userType === 3 ? 'active' : ''}}"
          bindtap="setUserType"
          data-type="3"
          hover-class="none"
        >
          <image
            class="type-icon"
            src="/icons/register/a.png"
            mode="aspectFit"
          ></image>
          <text>{{text.auto_parts_dealer}}</text>
        </view>
        <view
          class="user-type-item {{userType === 2 ? 'active' : ''}}"
          bindtap="setUserType"
          data-type="2"
          hover-class="none"
        >
          <image
            class="type-icon"
            src="/icons/register/s.png"
            mode="aspectFit"
          ></image>
          <text>{{text.service_provider}}</text>
        </view>
        <view
          class="user-type-item {{userType === 4 ? 'active' : ''}}"
          bindtap="setUserType"
          data-type="4"
          hover-class="none"
        >
          <image
            class="type-icon"
            src="/icons/register/c.png"
            mode="aspectFit"
          ></image>
          <text>{{text.buyer}}</text>
        </view>
        <view
          class="user-type-item {{userType === 5 ? 'active' : ''}}"
          bindtap="setUserType"
          data-type="5"
          hover-class="none"
        >
          <image
            class="type-icon"
            src="/icons/register/ib.png"
            mode="aspectFit"
          ></image>
          <text>{{text.international_buyer}}</text>
        </view>
        
      </view>
    </view>

    <!-- 动态表单区域 - 使用固定高度容器 -->
    <view class="dynamic-form-container">

      <!-- 主营类型 - 只在汽配商类型下显示 -->
      <view class="form-item {{userType === 3 ? '' : 'hidden'}}">
        <text class="form-label required">{{text.business_types_Multi_select}}</text>
        <view
          class="car-type-new-container"
          style="{{vehicleTypeContainerStyle}}"
        >
          <view
            class="car-type-new-item {{item.isActive ? 'active' : ''}}"
            wx:for="{{businessTypeOptions}}"
            wx:key="value"
            data-type="{{item.name}}"
            data-value="{{item.value}}"
            data-index="{{index}}"
            bindtap="toggleBusinessType"
            style="width: 56px; height: 20px; line-height: 20px; display: flex; align-items: center; justify-content: center;"
          >
            {{item.name}}
          </view>
        </view>
      </view>


      <!-- 主营品牌 - 只在车源商、汽配商和采购商类型下显示 -->
      <view class="form-item {{userType === 1 || userType === 3 || userType === 4 ? '' : 'hidden'}}">
        <text class="form-label required">{{text.primary_brands_select_multiple}}</text>
        <view
          class="dropdown-wrapper"
          bindtap="toggleBrandDropdown"
          hover-class="none"
        >
          <view class="dropdown-text {{!mainBrand ? 'dropdown-placeholder' : ''}}">
            {{mainBrand || text.select_vehicle_your_main_brands}}
          </view>
          <view class="dropdown-icon">
            <image
              src="/icons/register/dowm.png"
              mode="aspectFit"
            ></image>
          </view>
        </view>
        <!-- 下拉菜单 -->
        <block wx:if="{{showBrandDropdown}}">
          <view
            class="dropdown-overlay"
            bindtap="toggleBrandDropdown"
          ></view>
          <view class="dropdown-menu">
            <!-- 加载中提示 -->
            <view
              wx:if="{{isBrandLoading}}"
              class="loading-container"
            >
              <view class="loading-spinner"></view>
              <text class="loading-text">品牌加载中...</text>
            </view>
            <!-- 品牌列表 -->
            <block wx:else>
              <view
                wx:if="{{brandOptions.length === 0}}"
                class="empty-message"
              >
                暂无品牌数据
              </view>
              <view
                class="dropdown-item {{item.isActive ? 'active' : ''}}"
                wx:for="{{brandOptions}}"
                wx:key="value"
                bindtap="selectBrand"
                data-name="{{item.name}}"
                data-value="{{item.value}}"
                data-index="{{index}}"
              >
                {{item.name}}
                <view
                  wx:if="{{item.isActive}}"
                  class="dropdown-item-check"
                >✓</view>
              </view>
            </block>
          </view>
        </block>
      </view>



      <!-- 主营配件类目 - 只在汽配商类型下显示 -->
      <view class="form-item {{userType === 3 ? '' : 'hidden'}}">
        <text class="form-label required">{{text.main_parts_category}}</text>
        <view
          class="dropdown-wrapper"
          bindtap="togglePartsDropdown"
          hover-class="none"
        >
          <view class="dropdown-text {{!mainPartsCategory ? 'dropdown-placeholder' : ''}}">
            {{mainPartsCategory || text.select_your_main_parts_category}}
          </view>
          <view class="dropdown-icon">
            <image
              src="/icons/register/dowm.png"
              mode="aspectFit"
            ></image>
          </view>
        </view>
        <!-- 修改为一级多选下拉菜单 -->
        <block wx:if="{{showPartsDropdown}}">
          <view
            class="dropdown-overlay"
            bindtap="togglePartsDropdown"
          ></view>
          <view class="dropdown-menu">
            <!-- 加载中提示 -->
            <view
              wx:if="{{isPartsLoading}}"
              class="loading-container"
            >
              <view class="loading-spinner"></view>
              <text class="loading-text">配件类目加载中...</text>
            </view>
            <!-- 配件类目列表 -->
            <block wx:else>
              <view
                wx:if="{{partsCategories.length === 0}}"
                class="empty-message"
              >
                暂无配件类目数据
              </view>
              <view
                class="dropdown-item {{item.isActive ? 'active' : ''}}"
                wx:for="{{partsCategories}}"
                wx:key="id"
                bindtap="selectPartCategory"
                data-id="{{item.id}}"
                data-name="{{item.name}}"
                data-index="{{index}}"
              >
                {{item.name}}
                <view
                  wx:if="{{item.isActive}}"
                  class="dropdown-item-check"
                >✓</view>
              </view>
            </block>
          </view>
        </block>
      </view>

      <!-- 主营业务 - 只在服务商类型下显示 -->
      <view class="form-item {{userType === 2 ? '' : 'hidden'}}">
        <text class="form-label required">{{text.core_business_select_multiple}}</text>
        <view
          class="dropdown-wrapper"
          bindtap="toggleBusinessDropdown"
          hover-class="none"
        >
          <view class="dropdown-text {{!mainBusiness ? 'dropdown-placeholder' : ''}}">
            {{mainBusiness || text.select_your_business_type}}
          </view>
          <view class="dropdown-icon">
            <image
              src="/icons/register/dowm.png"
              mode="aspectFit"
            ></image>
          </view>
        </view>
        <!-- 下拉菜单 -->
        <block wx:if="{{showBusinessDropdown}}">
          <view
            class="dropdown-overlay"
            bindtap="toggleBusinessDropdown"
          ></view>
          <view class="dropdown-menu">
            <view
              class="dropdown-item {{item.isActive ? 'active' : ''}}"
              wx:for="{{businessOptions}}"
              wx:key="value"
              bindtap="selectBusiness"
              data-name="{{item.name}}"
              data-value="{{item.value}}"
              data-index="{{index}}"
            >
              {{item.name}}
              <view
                wx:if="{{item.isActive}}"
                class="dropdown-item-check"
              >✓</view>
            </view>
          </view>
        </block>
      </view>

      <!-- 国家地区选择 - 只在采购商类型下显示 -->
      <view class="form-item {{userType === 4 || userType === 5 ? '' : 'hidden'}}">
        <text class="form-label required">{{text.sales_markets_multi_select}}</text>
        <view
          class="dropdown-wrapper"
          bindtap="toggleCountryRegionDropdown"
          hover-class="none"
        >
          <view class="dropdown-text {{!mainLocation ? 'dropdown-placeholder' : ''}}">
            {{mainLocation || text.select_your_country}}
          </view>
          <view class="dropdown-icon">
            <image
              src="/icons/register/dowm.png"
              mode="aspectFit"
            ></image>
          </view>
        </view>
        <!-- 国家多选下拉菜单 -->
        <block wx:if="{{showCountryRegionDropdown}}">
          <view
            class="dropdown-overlay"
            bindtap="toggleCountryRegionDropdown"
          ></view>
          <view class="dropdown-menu">
            <!-- 加载中提示 -->
            <view
              wx:if="{{isAreaLoading}}"
              class="loading-container"
            >
              <view class="loading-spinner"></view>
              <text class="loading-text">国家数据加载中...</text>
            </view>
            <!-- 国家列表 -->
            <block wx:else>
              <view
                wx:if="{{countryOptions.length === 0}}"
                class="empty-message"
              >
                暂无国家数据
              </view>
              <view
                class="dropdown-item {{item.isActive ? 'active' : ''}}"
                wx:for="{{countryOptions}}"
                wx:key="id"
                bindtap="selectCountry"
                data-name="{{item.name}}"
                data-value="{{item.id}}"
                data-index="{{index}}"
              >
                {{item.name}}
                <view
                  wx:if="{{item.isActive}}"
                  class="dropdown-item-check"
                >✓</view>
              </view>
            </block>
          </view>
        </block>
      </view>

      <!-- 主营车类型 - 只对车源商显示 -->
      <view class="form-item {{userType === 1 ? '' : 'hidden'}}">
        <text class="form-label required">{{text.vehicle_types_multi_select}}</text>


        <view
          class="dropdown-wrapper"
          bindtap="toggleVehicleTypeOptionsDropdown"
          hover-class="none"
        >
          <view class="dropdown-text {{!mainVehicleType ? 'dropdown-placeholder' : ''}}">
            {{mainVehicleType || text.select_your_main_vehicle_types}}
          </view>
          <view class="dropdown-icon">
            <image
              src="/icons/register/dowm.png"
              mode="aspectFit"
            ></image>
          </view>
        </view>
        <!-- 下拉菜单 -->
        <block wx:if="{{showVehicleTypeOptionsDropdown}}">
          <view
            class="dropdown-overlay"
            bindtap="toggleVehicleTypeOptionsDropdown"
          ></view>
          <view class="dropdown-menu">
            <!-- 列表 -->
            <block else>
              <view
                class="dropdown-item {{item.isActive ? 'active' : ''}}"
                wx:for="{{vehicleTypeOptions}}"
                wx:key="value"
                bindtap="selectVehicleType"
                data-name="{{item.name}}"
                data-value="{{item.value}}"
                data-index="{{index}}"
              >
                {{item.name}}
                <view
                  wx:if="{{item.isActive}}"
                  class="dropdown-item-check"
                >✓</view>
              </view>
            </block>
          </view>
        </block>

        <!-- <view class="car-type-new-container">        
          <block
            wx:for="{{vehicleTypeOptions}}"
            wx:key="value"
          >

            <view
              class="car-type-new-item {{item.isActive ? 'active' : ''}}"
              bindtap="toggleVehicleTypeNew"
              data-index="{{index}}"
            >
              {{item.name}}
            </view>
          </block>
        </view> -->
      </view>

      <!-- 车型类型占位 - 对其他用户类型 -->
      <view class="form-item {{userType !== 1 ? '' : 'hidden'}}">
        <text
          class="form-label"
          style="opacity: 0;"
        >占位</text>
        <view style="height: 80rpx; visibility: hidden;"></view>
      </view>
    </view>
  </view>

  <!-- 底部区域 - 不再固定 -->
  <view class="bottom-area">
    <!-- 提交按钮 -->
    <button
      class="submit-btn"
      bindtap="submitInfo"
    >完成注册</button>
  </view>
</view>