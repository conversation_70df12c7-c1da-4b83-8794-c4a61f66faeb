// pages/login/login_form.js
import api from '../../utils/api';
import util from '../../utils/util';
const langData = require('../../utils/lang-function.js');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: 0,
    username: '',
    password: '',
    showPassword: false,
    isAgree: false,
    loading: false,
    text: {}
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.updateText();
    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });
  },
  //语言刷新
  refreshLanguage(newLang){
    console.log('页面语言已切换到:', newLang);
  },
  // 更新页面文本
  updateText() {
    this.setData({
      text:{
        login:langData.t('login'),
        i_agree_to_abide_by:langData.t('i_agree_to_abide_by'),
        user_service_agreement:langData.t('user_service_agreement'),
        privacy_policy:langData.t('privacy_policy'),
        forgot_password:langData.t('forgot_password'),
        enter_password:langData.t('enter_password'),
        enter_phone_or_username:langData.t('enter_phone_or_username'),
        phone_login:langData.t('phone_login'),
        sign_up:langData.t('sign_up'),
        zhaochexia:langData.t('zhaochexia')
      }
    })
  },
  /**
   * 导航返回
   */
  navigateBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 切换协议同意状态
   */
  toggleAgree() {
    this.setData({
      isAgree: !this.data.isAgree
    });
  },

  /**
   * 切换密码可见性
   */
  togglePasswordVisibility() {
    // 添加调试信息
    console.log('忘记密码按钮被点击');

    // 跳转到找回密码页面
    wx.navigateTo({
      url: '/pages/login/forgot_password',
      success: function () {
        console.log('导航到忘记密码页面成功');
      },
      fail: function (error) {
        console.error('导航到忘记密码页面失败', error);
        // 显示错误提示
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 查看协议
   */
  viewAgreement(e) {
    const type = e.currentTarget.dataset.type;
    if (type === 'service') {
      wx.navigateTo({
        url: '/pages/agreement/service'
      });
    } else if (type === 'privacy') {
      wx.navigateTo({
        url: '/pages/agreement/privacy'
      });
    }
  },

  /**
   * 处理登录
   */
  handleLogin() {
    // 验证协议是否勾选
    if (!this.data.isAgree) {
      wx.showToast({
        title: '请先同意用户协议和隐私政策',
        icon: 'none'
      });
      return;
    }

    // 验证表单数据
    if (!this.data.username.trim()) {
      wx.showToast({
        title: '请输入用户名',
        icon: 'none'
      });
      return;
    }

    if (!this.data.password.trim()) {
      wx.showToast({
        title: '请输入密码',
        icon: 'none'
      });
      return;
    }

    // 开始登录
    this.setData({ loading: true });

    // 调用账号密码登录API
    api.user.accountLogin({
      mobile: this.data.username.trim(),
      password: this.data.password.trim()
    }).then(result => {
      this.loginSuccess(result);
    }).catch(err => {
      this.setData({ loading: false });
      wx.showToast({
        title: err.message || '登录失败，请检查账号密码',
        icon: 'none'
      });
    });
  },

  /**
   * 登录成功处理
   */
  loginSuccess(result) {
    // 保存用户信息和登录状态
    const userInfo = util.storeLoginFlags(result);

    // 显示成功提示
    wx.showToast({
      title: '登录成功',
      icon: 'success',
      duration: 1500
    });

    // 延迟跳转
    setTimeout(() => {
      if (!result.info) {
        wx.navigateTo({
          url: '/pages/info/index?phone=' + userInfo.phone
        });
      } else {
        wx.switchTab({
          url: '/pages/my/index'
        });
      }
    }, 1500);
  },

  /**
   * 跳转到手机登录页面
   */
  navigateToPhoneLogin() {
    wx.navigateTo({
      url: '/pages/login/index'
    });
  },

  /**
   * 跳转到注册页面
   */
  navigateToRegister() {
    wx.navigateTo({
      url: '/pages/login/register_form'
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})