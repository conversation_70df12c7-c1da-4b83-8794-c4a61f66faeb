Page({
  data: {
    carInfo: null,
    result: null,
    dealerList: [],
    loading: true,
    error: false
  },

  onLoad: function(options) {
    // 获取评估ID或评估数据
    const evaluateId = options.id;
    if (evaluateId) {
      this.loadEvaluateResult(evaluateId);
    } else {
      this.setData({
        loading: false,
        error: true
      });
    }
  },

  // 加载评估结果
  loadEvaluateResult: function(evaluateId) {
    const that = this;
    wx.showLoading({
      title: '加载中...',
    });

    // 模拟API请求
    setTimeout(() => {
      // 这里是模拟数据，实际开发中应该通过API获取
      const mockResult = {
        minPrice: 15.8,
        maxPrice: 17.5,
        carInfo: {
          brand: '大众',
          series: '途观L',
          year: '2020款',
          model: '380TSI 四驱旗舰版 国VI',
          mileage: '3.5万公里',
          licensedDate: '2020年03月'
        },
        dealerList: [
          {
            id: 1,
            name: '鑫广达二手车行',
            logo: '/static/images/dealer1.png',
            address: '北京市朝阳区东四环南路53号',
            phone: '13800138000'
          },
          {
            id: 2,
            name: '车易购二手车',
            logo: '/static/images/dealer2.png',
            address: '北京市海淀区西四环北路117号',
            phone: '13900139000'
          },
          {
            id: 3,
            name: '车来车往二手车',
            logo: '/static/images/dealer3.png',
            address: '北京市丰台区南四环西路128号',
            phone: '13700137000'
          }
        ]
      };

      that.setData({
        result: mockResult,
        carInfo: mockResult.carInfo,
        dealerList: mockResult.dealerList,
        loading: false
      });

      wx.hideLoading();
    }, 1500);
  },

  // 返回上一页
  navigateBack: function() {
    wx.navigateBack();
  },

  // 联系经销商
  contactDealer: function(e) {
    const dealerId = e.currentTarget.dataset.id;
    const dealer = this.data.dealerList.find(item => item.id === dealerId);
    
    if (dealer && dealer.phone) {
      wx.makePhoneCall({
        phoneNumber: dealer.phone,
        success: () => {
          console.log('拨打电话成功');
        },
        fail: (err) => {
          console.error('拨打电话失败', err);
          wx.showToast({
            title: '拨打电话失败',
            icon: 'none'
          });
        }
      });
    }
  },

  // 分享评估结果
  shareResult: function() {
    wx.showToast({
      title: '暂不支持分享功能',
      icon: 'none'
    });
  },

  // 立即卖车
  sellCar: function() {
    wx.navigateTo({
      url: '/pages/sell/index'
    });
  },

  // 用户点击右上角分享
  onShareAppMessage: function() {
    const carInfo = this.data.carInfo;
    let title = '我的爱车估价结果';
    
    if (carInfo) {
      title = `${carInfo.brand} ${carInfo.series} 估价${this.data.result.minPrice}-${this.data.result.maxPrice}万元`;
    }
    
    return {
      title: title,
      path: '/pages/index/index',
      imageUrl: '/static/images/share_bg.png'
    };
  }
}) 