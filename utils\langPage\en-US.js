//语言列表
const message = {
  'zhaochexia':'ZhaoCheXia',
  'login':'Login',
  'sign_up':'Sign up',
  'languageSwitchSuccess': 'Switched',
  'i_agree_to_abide_by':'I agree to abide by',
  'user_service_agreement':'User Service Agreement',
  'privacy_policy':'Privacy Policy',
  'please_enter_the_verification_code':'Enter code',
  'get_phone_code':'Get phone code',
  's_to_retry':'s to retry',
  'enter_phone_number':'enter phone number',
  'code_sent':'Code sent',
  'phone_already_used':'Phone already used',
  'send_failed':'Send failed',
  'invalid_number':'invalid number',
  'not_registered':'Not registered',
  'install_sms_plugin':'Install SMS Plugin',
  'send_failed_check_config':'Send failed, check config',
  'enter_password':'Enter password',
  'pass_chars':'8-16 chars',
  'forgot_password':'Forgot password',
  'enter_phone_or_username':'Enter phone or username',
  'phone_login':'Phone login',
  'please_log_in':'Please log in',
  'quick_login':'Quick login',
  'login_with_password':'Login with password',
  'confirm_new_password':'Confirm new password',
  'passwords_dont_match':'Passwords don’t match',
  'password_updated':'Password updated',
  'reset_password':'Reset password',
  'agree_to_terms_first':'Agree to terms first',
  'authorizing':'Authorizing',
  'name':'Name',
  'register_personal_information':'personal information',
  'click_to_upload_avatar':'Click to upload avatar',
  'please_enter_the_company_name':'Please enter the company name',
  'please_select_user_type':'Please select user type',
  'vehicle_supplier':'Vehicle supplier',
  'auto_parts_dealer':'Auto parts dealer',
  'service_provider':'Service provider',
  'buyer':'Buyer',
  'international_buyer':'International buyer',
  'primary_brands_select_multiple':'Primary brands (select multiple)',
  'business_types_Multi_select':'Business Types (Multi-select)',
  'vehicle_types_multi_select':'Vehicle Types (Multi-select)',
  'select_your_main_vehicle_types':'Select your main vehicle types',
  'select_vehicle_your_main_brands':'Select your vehicle \'s main brands',
  'core_business_select_multiple':'Core Business (Multi-select)',
  'select_your_business_type':'Select your business type',
  'sales_markets_multi_select':'Sales Markets (Multi-select)',
  'select_your_country':'Select your country',
  'complete_vehicles':'Complete Vehicles',
  'auto_parts':'Auto Parts',
  'vehicle_services':'Vehicle Services',
  'inspection':'Inspection',
  'flashing_reflashing':'Flashing / Reflashing',
  'export_channel':'Export Channel',
  'domestic_logistics':'Domestic Logistics',
  'international_logistics':'International Logistics',
  'finance':'Finance',
  'modification_refurbishment':'Modification & Refurbishment',
  'insurance':'Insurance',
  'extended_after_sales_service':'Extended After-Sales Service',
  'others':'Others',
  'main_parts_category':'Main Parts Category',
  'select_your_main_parts_category':'Select your main parts category',
  'used_cars':'Used Cars',
  'new_cars':'New Cars',
  'fleet_vehicles':'Fleet Vehicles',
  'construction':'construction',
  'right_hand_drive_cars':'Right-Hand Drive Cars',
  'commercial_vehicles':'Commercial Vehicles',
  'low_speed_vehicles':'Low-Speed Vehicles',
  'trucks':'Trucks',
  'buses':'Buses',
  'special_vehicle':'Special Vehicle',
  'trailers':'Trailers',
}

function trans(key){
  return message[key] || key;
}

module.exports = {
  trans
};