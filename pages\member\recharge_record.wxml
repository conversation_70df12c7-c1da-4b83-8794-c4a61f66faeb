<!--pages/member/recharge_record.wxml-->
<view class="recharge-record-container">
  <!-- 自定义导航栏 -->
  <view
    class="custom-navbar"
    style="height: {{navBarHeight}}px;"
  >
    <view
      class="status-bar"
      style="height: {{statusBarHeight}}px;"
    ></view>
    <view class="navbar-content">
      <view
        class="nav-back"
        bindtap="navigateBack"
      >
        <van-icon
          name="arrow-left"
          size="20px"
          color="#333"
        />
      </view>
      <view class="nav-title">充值记录</view>
      <view class="nav-placeholder"></view>
    </view>
  </view>

  <!-- 内容区域 -->
  <view
    class="content-area"
    style="margin-top: {{navBarHeight}}px;"
  >
    <!-- 会员信息区域 -->
    <view class="member-info">
      <view class="member-header">
        <image
          class="company-logo"
          src="{{userInfo.logo}}"
          mode="aspectFit"
        ></image>
        <view class="company-info">
          <view class="company-name">{{ userInfo.short_name }}</view>
          <view class="member-status">
            <view class="member-tag">
              <image
                class="company-logo"
                src="{{memberLevels[userInfo.level - 1].url}}"
                mode="aspectFit"
              ></image>
            </view>
            <view
              class="expire-info"
              wx:if="{{ userInfo.level > 1}}"
            >{{memberLevels[userInfo.level - 1].name}}于 {{userInfo.level_time_expire}} 过期</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 会员充值记录区域 -->
    <view class="recharge-title">我的会员充值</view>

    <!-- 充值记录列表 -->
    <view class="recharge-list">
      <block wx:if="{{rechargeRecords && rechargeRecords.length > 0}}">
        <block
          wx:for="{{rechargeRecords}}"
          wx:key="id"
        >
          <view
            class="recharge-item {{item.status === '已过期' ? 'expired-card' : ''}} {{item.trade_state === 1 ? 'payable-card' : ''}}"
            bindtap="{{item.trade_state === 1 ? 'handleRepay' : ''}}"
            data-order-no="{{item.out_trade_no}}"
          >
            <view class="item-header">
              <view class="top-row">
                <view class="member-icon">
                  <image
                    src="{{item.member_level.image}}"
                    mode="aspectFit"
                  ></image>
                </view>
                <view class="member-type">{{item.member_level.name}}</view>
                <view
                  class="countdown-badge"
                  wx:if="{{item.status === '待支付' && item.trade_state === 1 && item.countdownDisplay}}"
                >
                  <van-icon
                    name="clock-o"
                    class="countdown-icon"
                  />
                  {{item.countdownDisplay}}
                </view>
                <view
                  class="status-tag {{item.status === '使用中' ? 'active' : (item.status === '待支付' ? 'pending' : 'expired')}}"
                >
                  {{item.status}}
                </view>
              </view>
              <view class="content-container">
                <view class="package-row">{{item.body}}：¥ {{item.amount_payer}}</view>
                <view class="validity-period">有效期：{{item.expiration_date}}</view>
                <view
                  class="validity-period"
                  wx:if="{{item.status === '已完成'}}"
                >
                  开通时间：{{item.time_start}}
                </view>
                <view class="item-privileges">
                  享受无限畅播会员视频，增加多项功能次数等特权
                </view>
                <view
                  class="order-actions"
                  wx:if="{{item.trade_state === 1}}"
                >
                  <view
                    class="cancel-button"
                    catchtap="handleCancelOrder"
                    data-order-no="{{item.out_trade_no}}"
                    style="background-color: white; color: #6d7482; border: 1px solid #6d7482; width: 80px;"
                  >取消订单</view>
                  <view
                    class="pay-button"
                    catchtap="handleRepay"
                    data-order-no="{{item.out_trade_no}}"
                    style="background-color: #3b82f6; width: 80px;"
                  >立即支付</view>
                </view>
              </view>
            </view>
          </view>
        </block>
      </block>
      <block wx:else>
        <view class="empty-state">
          <view class="empty-icon">
            <!-- <image src="/icons/empty.png" mode="aspectFit"></image> -->
          </view>
          <view class="empty-text">暂无充值记录</view>
        </view>
      </block>
    </view>
  </view>
</view>