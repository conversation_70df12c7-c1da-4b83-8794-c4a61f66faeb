<!--pages/vehicle/params.wxml-->
<view class="container">
  <!-- 头部区域占位符 - 防止内容被固定顶部遮挡 -->
  <view class="header-placeholder" style="height: {{headerPlaceholderHeight || 120}}px;"></view>
  
  <!-- 固定头部区域 -->
  <view class="fixed-header">
    <!-- 顶部状态栏 -->
    <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>
    
    <!-- 导航栏 -->
    <view class="nav-bar">
      <view class="nav-back" bindtap="goBack">
        <van-icon name="arrow-left" size="20px" color="#333" />
      </view>
      <view class="nav-title">参数详情</view>
      <view class="nav-placeholder"></view>
    </view>
  </view>

 
</view>

 <!-- Tab导航卡片 -->
  <view class="tab-card">
    <view class="tab-item {{currentTab === 0 ? 'active' : ''}}" bindtap="switchTab" data-index="0">
      <text class="tab-text">车辆档案</text>
      <view class="tab-line"></view>
    </view>
    <view class="tab-item {{currentTab === 1 ? 'active' : ''}}" bindtap="switchTab" data-index="1">
      <text class="tab-text">参数配置</text>
      <view class="tab-line"></view>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="content-container">
    <!-- 车辆档案内容 -->
    <view wx:if="{{currentTab === 0}}" class="tab-content">
      <!-- 车辆信息卡片 -->
      <view class="content-card">
        <view class="card-title">车辆信息</view>
        <view wx:for="{{paramConfigs.vehicleInfo}}" wx:key="index" wx:if="{{index % 2 === 0}}" class="info-row">
          <view class="info-label">{{paramConfigs.vehicleInfo[index].label}}</view>
          <view class="info-value">{{paramConfigs.vehicleInfo[index].value}}</view>
          <view wx:if="{{paramConfigs.vehicleInfo[index + 1]}}" class="info-label">{{paramConfigs.vehicleInfo[index + 1].label}}</view>
          <view wx:if="{{paramConfigs.vehicleInfo[index + 1]}}" class="info-value">{{paramConfigs.vehicleInfo[index + 1].value}}</view>
        </view>
      </view>
      
      <!-- 车身尺寸卡片 -->
      <view class="content-card">
        <view class="card-title">车身尺寸</view>
        <view wx:for="{{paramConfigs.bodySize}}" wx:key="index" wx:if="{{index % 2 === 0}}" class="info-row">
          <view class="info-label">{{paramConfigs.bodySize[index].label}}</view>
          <view class="info-value">{{paramConfigs.bodySize[index].value}}</view>
          <view wx:if="{{paramConfigs.bodySize[index + 1]}}" class="info-label">{{paramConfigs.bodySize[index + 1].label}}</view>
          <view wx:if="{{paramConfigs.bodySize[index + 1]}}" class="info-value">{{paramConfigs.bodySize[index + 1].value}}</view>
        </view>
      </view>
    </view>
    
    <!-- 参数配置内容 -->
    <view wx:if="{{currentTab === 1}}" class="tab-content">
      <!-- 参数配置横向滑动导航 -->
      <scroll-view class="param-nav" scroll-x enable-flex show-scrollbar="{{false}}">
        <view class="param-nav-item {{paramTab === 0 ? 'active' : ''}}" bindtap="switchParamTab" data-index="0">发动机</view>
        <view class="param-nav-item {{paramTab === 1 ? 'active' : ''}}" bindtap="switchParamTab" data-index="1">电动机</view>
        <view class="param-nav-item {{paramTab === 2 ? 'active' : ''}}" bindtap="switchParamTab" data-index="2">电池/充电</view>
        <view class="param-nav-item {{paramTab === 3 ? 'active' : ''}}" bindtap="switchParamTab" data-index="3">变速箱</view>
        <view class="param-nav-item {{paramTab === 4 ? 'active' : ''}}" bindtap="switchParamTab" data-index="4">底盘转向</view>
        <view class="param-nav-item {{paramTab === 5 ? 'active' : ''}}" bindtap="switchParamTab" data-index="5">车轮制动</view>
        <view class="param-nav-item {{paramTab === 6 ? 'active' : ''}}" bindtap="switchParamTab" data-index="6">主动安全</view>
        <view class="param-nav-item {{paramTab === 7 ? 'active' : ''}}" bindtap="switchParamTab" data-index="7">被动安全</view>
        <view class="param-nav-item {{paramTab === 8 ? 'active' : ''}}" bindtap="switchParamTab" data-index="8">辅助操控</view>
        <view class="param-nav-item {{paramTab === 9 ? 'active' : ''}}" bindtap="switchParamTab" data-index="9">外部配置</view>
        <view class="param-nav-item {{paramTab === 10 ? 'active' : ''}}" bindtap="switchParamTab" data-index="10">内部配置</view>
        <view class="param-nav-item {{paramTab === 11 ? 'active' : ''}}" bindtap="switchParamTab" data-index="11">舒适/防盗配置</view>
        <view class="param-nav-item {{paramTab === 12 ? 'active' : ''}}" bindtap="switchParamTab" data-index="12">座椅配置</view>
        <view class="param-nav-item {{paramTab === 13 ? 'active' : ''}}" bindtap="switchParamTab" data-index="13">智能互联</view>
        <view class="param-nav-item {{paramTab === 14 ? 'active' : ''}}" bindtap="switchParamTab" data-index="14">影音娱乐</view>
        <view class="param-nav-item {{paramTab === 15 ? 'active' : ''}}" bindtap="switchParamTab" data-index="15">灯光配置</view>
        <view class="param-nav-item {{paramTab === 16 ? 'active' : ''}}" bindtap="switchParamTab" data-index="16">玻璃/后视镜</view>
        <view class="param-nav-item {{paramTab === 17 ? 'active' : ''}}" bindtap="switchParamTab" data-index="17">空调冰箱</view>
        <view class="param-nav-item {{paramTab === 18 ? 'active' : ''}}" bindtap="switchParamTab" data-index="18">智能化配置</view>
        <view class="param-nav-item {{paramTab === 19 ? 'active' : ''}}" bindtap="switchParamTab" data-index="19">选装包</view>
      </scroll-view>
      
      <!-- 参数内容区域 -->
      <view class="param-content">
        <!-- 发动机参数 -->
        <view class="param-panel" wx:if="{{paramTab === 0}}">
          <view class="param-row" wx:for="{{paramConfigs[0]}}" wx:key="index">
            <view class="param-label">{{item.label}}</view>
            <view class="param-value">{{item.value}}</view>
          </view>
        </view>
        
        <!-- 电动机参数 -->
        <view class="param-panel" wx:if="{{paramTab === 1}}">
          <view class="param-row" wx:for="{{paramConfigs[1]}}" wx:key="index">
            <view class="param-label">{{item.label}}</view>
            <view class="param-value">{{item.value}}</view>
          </view>
        </view>
        
        <!-- 电池/充电参数 -->
        <view class="param-panel" wx:if="{{paramTab === 2}}">
          <view class="param-row" wx:for="{{paramConfigs[2]}}" wx:key="index">
            <view class="param-label">{{item.label}}</view>
            <view class="param-value">{{item.value}}</view>
          </view>
        </view>
        
        <!-- 变速箱参数 -->
        <view class="param-panel" wx:if="{{paramTab === 3}}">
          <view class="param-row" wx:for="{{paramConfigs[3]}}" wx:key="index">
            <view class="param-label">{{item.label}}</view>
            <view class="param-value">{{item.value}}</view>
          </view>
        </view>
        
        <!-- 底盘转向参数 -->
        <view class="param-panel" wx:if="{{paramTab === 4}}">
          <view class="param-row" wx:for="{{paramConfigs[4]}}" wx:key="index">
            <view class="param-label">{{item.label}}</view>
            <view class="param-value">{{item.value}}</view>
          </view>
        </view>
        
        <!-- 车轮制动参数 -->
        <view class="param-panel" wx:if="{{paramTab === 5}}">
          <view class="param-row" wx:for="{{paramConfigs[5]}}" wx:key="index">
            <view class="param-label">{{item.label}}</view>
            <view class="param-value">{{item.value}}</view>
          </view>
        </view>
        
        <!-- 主动安全参数 -->
        <view class="param-panel" wx:if="{{paramTab === 6}}">
          <view class="param-row" wx:for="{{paramConfigs[6]}}" wx:key="index">
            <view class="param-label">{{item.label}}</view>
            <view class="param-value">{{item.value}}</view>
          </view>
        </view>
        
        <!-- 被动安全参数 -->
        <view class="param-panel" wx:if="{{paramTab === 7}}">
          <view class="param-row" wx:for="{{paramConfigs[7]}}" wx:key="index">
            <view class="param-label">{{item.label}}</view>
            <view class="param-value">{{item.value}}</view>
          </view>
        </view>
        
        <!-- 辅助操控参数 -->
        <view class="param-panel" wx:if="{{paramTab === 8}}">
          <view class="param-row" wx:for="{{paramConfigs[8]}}" wx:key="index">
            <view class="param-label">{{item.label}}</view>
            <view class="param-value">{{item.value}}</view>
          </view>
        </view>
        
        <!-- 外部配置参数 -->
        <view class="param-panel" wx:if="{{paramTab === 9}}">
          <view class="param-row" wx:for="{{paramConfigs[9]}}" wx:key="index">
            <view class="param-label">{{item.label}}</view>
            <view class="param-value">{{item.value}}</view>
          </view>
        </view>
        
        <!-- 内部配置参数 -->
        <view class="param-panel" wx:if="{{paramTab === 10}}">
          <view class="param-row" wx:for="{{paramConfigs[10]}}" wx:key="index">
            <view class="param-label">{{item.label}}</view>
            <view class="param-value">{{item.value}}</view>
          </view>
        </view>
        
        <!-- 舒适/防盗配置参数 -->
        <view class="param-panel" wx:if="{{paramTab === 11}}">
          <view class="param-row" wx:for="{{paramConfigs[11]}}" wx:key="index">
            <view class="param-label">{{item.label}}</view>
            <view class="param-value">{{item.value}}</view>
          </view>
        </view>
        
        <!-- 座椅配置参数 -->
        <view class="param-panel" wx:if="{{paramTab === 12}}">
          <view class="param-row" wx:for="{{paramConfigs[12]}}" wx:key="index">
            <view class="param-label">{{item.label}}</view>
            <view class="param-value">{{item.value}}</view>
          </view>
        </view>
        
        <!-- 智能互联参数 -->
        <view class="param-panel" wx:if="{{paramTab === 13}}">
          <view class="param-row" wx:for="{{paramConfigs[13]}}" wx:key="index">
            <view class="param-label">{{item.label}}</view>
            <view class="param-value">{{item.value}}</view>
          </view>
        </view>
        
        <!-- 影音娱乐参数 -->
        <view class="param-panel" wx:if="{{paramTab === 14}}">
          <view class="param-row" wx:for="{{paramConfigs[14]}}" wx:key="index">
            <view class="param-label">{{item.label}}</view>
            <view class="param-value">{{item.value}}</view>
          </view>
        </view>
        
        <!-- 灯光配置参数 -->
        <view class="param-panel" wx:if="{{paramTab === 15}}">
          <view class="param-row" wx:for="{{paramConfigs[15]}}" wx:key="index">
            <view class="param-label">{{item.label}}</view>
            <view class="param-value">{{item.value}}</view>
          </view>
        </view>
        
        <!-- 玻璃/后视镜参数 -->
        <view class="param-panel" wx:if="{{paramTab === 16}}">
          <view class="param-row" wx:for="{{paramConfigs[16]}}" wx:key="index">
            <view class="param-label">{{item.label}}</view>
            <view class="param-value">{{item.value}}</view>
          </view>
        </view>
        
        <!-- 空调冰箱参数 -->
        <view class="param-panel" wx:if="{{paramTab === 17}}">
          <view class="param-row" wx:for="{{paramConfigs[17]}}" wx:key="index">
            <view class="param-label">{{item.label}}</view>
            <view class="param-value">{{item.value}}</view>
          </view>
        </view>
        
        <!-- 智能化配置参数 -->
        <view class="param-panel" wx:if="{{paramTab === 18}}">
          <view class="param-row" wx:for="{{paramConfigs[18]}}" wx:key="index">
            <view class="param-label">{{item.label}}</view>
            <view class="param-value">{{item.value}}</view>
          </view>
        </view>
        
        <!-- 选装包参数 -->
        <view class="param-panel" wx:if="{{paramTab === 19}}">
          <view class="param-row" wx:for="{{paramConfigs[19]}}" wx:key="index">
            <view class="param-label">{{item.label}}</view>
            <view class="param-value">{{item.value}}</view>
          </view>
        </view>
      </view>
    </view>
  </view>