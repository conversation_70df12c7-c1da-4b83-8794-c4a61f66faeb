/* pages/my/order.wxss */
.fixed-nav{
  z-index: 101;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 101;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
}

.status-bar{
  width: 100%;
}

.title{
  text-align: center;
  position: relative;
  height: 44px;
  line-height: 44px;
}

.back{
  top: 32rpx;
  left: 32rpx;
  border-top: 5rpx solid #cccccc;
  border-left: 5rpx solid #cccccc;
  transform: rotate(-45deg);
  position: absolute;
  display: block;
  width: 22rpx;
  height: 22rpx;
  /* background-color: red; */
}