// pages/supply_chain_services/car_service_detail.js
import config from '../../config';
import share from '../../utils/share';  // 导入分享模块
import api from '../../utils/api';  // 导入API模块

Page({
  behaviors: [share], // 分享设置

  /**
   * 页面的初始数据
   */
  data: {
    //处理分享页面 统一写
    shareData: {
      title: '找车侠 - 车务服务',
      path: '/pages/supply_chain_services/car_service_detail',
      isDetailPage: true, // 标记是否详情页
    },
    COS_CONFIG: config.COS_CONFIG, // 添加腾讯云配置
    id: 0,
    // API数据字段
    province: '', // 省份
    city: '', // 城市
    company_name: '', // 公司名称
    invoice_status: '', // 开票情况
    financing_ability: '', // 垫资能力
    service_scope: '', // 服务范围
    capability: '', // 效能
    cost: '', // 费用
    additional_notes: '', // 补充说明
    contact_company: '', // 联系公司
    email: '', // 联系邮箱
    phone: '', // 联系电话
    // 骨架屏相关
    isLoading: true, // 默认加载中状态
    showLoadingAnimation: false, // 默认显示骨架屏而非加载动画
    statusBarHeight: 20, // 默认状态栏高度
    pageStyle: '',
    // 登录弹窗
    showLoginPopup: false,
    loginPopupOptions: {
      title: '登录以获取更多服务',
      buttonText: '立即登录',
      clickMaskClose: true,
      hideCloseButton: false
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { id } = options;
    if (id) {
      this.setData({ id });
    }

    // 设置初始加载状态
    this.setData({
      isLoading: true,
      showLoadingAnimation: false
    });

    // 获取系统状态栏高度
    wx.getSystemInfo({
      success: (res) => {
        this.setData({
          statusBarHeight: res.statusBarHeight
        });
        // 添加CSS变量以供样式使用
        this.setData({
          pageStyle: `--status-bar-height: ${res.statusBarHeight}px;` + this.data.pageStyle
        });
      }
    });

    // 调用API获取车务服务详情
    this.fetchCarServiceDetail(id);
  },

  /**
   * 获取车务服务详情
   */
  fetchCarServiceDetail(id) {
    if (!id) {
      this.setData({ isLoading: false });
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      return;
    }

    api.vehicleService.getInfo(id)
      .then(res => {
        // 详细记录API响应，帮助调试
        console.log('车务服务详情原始数据:', res);
        console.log('车务服务详情数据类型:', typeof res);

        // 处理不同的响应结构可能性
        let apiData = null;

        // 如果res本身就是数据对象(没有code/data结构)
        if (res && typeof res === 'object' && !res.code && !res.data) {
          console.log('情况1: 响应本身就是数据对象');
          apiData = res;
        }
        // 标准情况: {code: 0/200, data: {...}}
        else if (res && (res.code === 0 || res.code === 200 || res.code === 1)) {
          console.log('情况2: 标准响应结构 code/data');
          apiData = res.data;
        }
        // 嵌套数据情况: {code: 0/200, data: {data: {...}}}
        else if (res && res.data && res.data.data) {
          console.log('情况3: 嵌套响应结构');
          apiData = res.data.data;
        }

        console.log('提取的API数据:', apiData);

        // 如果成功提取数据
        if (apiData) {
          // 处理开票情况和垫资能力的转换
          let invoiceStatus = '不开票';
          if (apiData.invoice === 1) {
            invoiceStatus = '开票';
          }

          let financeAbility = '不垫资';
          if (apiData.finance === 1) {
            financeAbility = '垫资';
          }

          // 用于调试的详细字段映射日志
          console.log('数据映射:');
          console.log('省份:', apiData.province);
          console.log('城市:', apiData.city);
          console.log('背书公司:', apiData.company);
          console.log('联系公司:', apiData.company_name);
          console.log('服务范围:', apiData.cost_content);
          console.log('效能:', apiData.efficiency);

          // 设置数据到页面
          this.setData({
            province: apiData.province || '', // 省份
            city: apiData.city || '', // 城市
            company_name: apiData.company || '', // 背书公司
            invoice_status: invoiceStatus, // 开票情况
            financing_ability: financeAbility, // 垫资能力
            service_scope: apiData.cost_content || '', // 服务范围使用cost_content字段
            capability: apiData.efficiency || '', // 效能使用efficiency字段
            cost: apiData.cost || '', // 费用
            additional_notes: apiData.remark || '', // 补充说明使用remark字段
            contact_company: apiData.company_name || '', // 联系公司使用company_name字段
            email: apiData.email || '', // 联系邮箱
            phone: apiData.phone || '', // 联系电话
            isLoading: false
          });

          // 确认数据已设置
          console.log('数据已设置到页面:', this.data);
        } else {
          console.error('无法从API响应中提取有效数据');
          this.setData({ isLoading: false });
          wx.showToast({
            title: (res && res.msg) || '获取详情失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('获取车务服务详情失败:', err);
        this.setData({ isLoading: false });
        wx.showToast({
          title: '网络异常，请稍后重试',
          icon: 'none'
        });
      });
  },

  /**
   * 返回按钮功能
   */
  navigateBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 咨询下单方法
   */
  onConsult() {
    // 检查用户是否登录
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo) {
      // 未登录，显示登录弹窗
      this.setData({
        showLoginPopup: true
      });
      return;
    }

    // 已登录，直接打开客服聊天
    wx.openCustomerServiceChat({
      extInfo: {url: 'https://work.weixin.qq.com/kfid/kfc4fa17a0f88a6e7c8'},
      corpId: 'ww3f7ad32ebbae2192', // 企业微信ID
      success(res) {
        console.log('打开客服聊天窗口成功', res);
      },
      fail(err) {
        console.error('打开客服聊天窗口失败', err);
        wx.showToast({
          title: '打开客服聊天失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 处理登录
   */
  handleLogin() {
    // 模拟登录成功
    wx.showToast({
      title: '登录成功',
      icon: 'success',
      duration: 2000
    });

    // 关闭登录弹窗
    this.setData({
      showLoginPopup: false
    });
  },

  /**
   * 关闭登录弹窗
   */
  closeLoginPopup() {
    this.setData({
      showLoginPopup: false
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 模拟刷新
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: this.data.shareData.title,
      path: this.data.shareData.path,
      imageUrl: this.data.COS_CONFIG.url + 'wechat/assets/images/car_service_share.png'
    };
  }
})