/* pages/set/index.wxss */
page {  
  width: 100%;  
  box-sizing: border-box;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
}

.container {  
  padding: 0;  
  background-color: transparent;  
  width: 100%;  
  box-sizing: border-box;  
  min-height: 100vh;
  position: relative;
}

/* ==================== 顶部导航区域样式 ==================== */

/* 固定头部样式 */
.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
  /* 移除阴影效果 */
}

/* 顶部状态栏 */
.status-bar {
  width: 100%;
  background: transparent;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  width: 100%;
  background: transparent;
  position: relative;
  padding: 0 20rpx;
  box-sizing: border-box;
  border-bottom: none; /* 确保没有底部边框 */
}

.nav-back {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  z-index: 10;
}

.nav-title {
  position: absolute;
  left: 0;
  right: 0;
  height: 44px;
  line-height: 44px;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin: 0 auto;
  width: 60%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  z-index: 5;
}

.nav-placeholder {
  width: 44px;
  height: 44px;
  visibility: hidden;
  flex-shrink: 0;
}

/* 主内容区域 */
.main-content {
  width: 100%;
  padding: 0;
  box-sizing: border-box;
  position: relative;
}

/* 设置内容区域 */
.settings-content {
  width: 100%;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
}

.settings-group {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.setting-item {
  display: flex;
  align-items: center;
  padding: 30rpx 20rpx;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-icon {
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.icon-image {
  width: 40rpx;
  height: 40rpx;
}

.setting-text {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

.setting-arrow {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-image {
  width: 24rpx;
  height: 24rpx;
}