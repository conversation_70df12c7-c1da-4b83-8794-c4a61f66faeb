<!-- 商用车详情页 -->
<view class="detail-container">
  <!-- 自定义导航栏 -->
  <view class="custom-nav">
    <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>
    <view class="nav-content">
      <view class="nav-back" bindtap="onBackTap">
        <van-icon name="arrow-left" size="20px" color="#333" />
      </view>
      <view class="nav-title">{{commercialInfo.title ? '车辆详情' : '商用车详情'}}</view>
      <view class="nav-placeholder"></view>
    </view>
  </view>
  
  <!-- 可滚动内容区域 -->
  <scroll-view class="scrollable-content" scroll-y="true">
    <!-- 顶部图片轮播 -->
    <view class="swiper-container">
      <swiper class="image-swiper" 
              indicator-dots="{{false}}" 
              autoplay="{{true}}" 
              interval="4000" 
              duration="400"
              circular="{{true}}"
              indicator-color="rgba(255, 255, 255, 0.4)"
              indicator-active-color="#ffffff"
              bindchange="swiperChange">
        <block wx:for="{{commercialInfo.images}}" wx:key="index">
          <swiper-item>
            <image src="{{item}}" 
                  mode="aspectFill" 
                  class="swiper-image" 
                  bindtap="previewImage" 
                  data-url="{{item}}"
                  lazy-load="{{true}}"/>
          </swiper-item>
        </block>
        <view class="image-counter">{{current+1}}/{{commercialInfo.images.length}}</view>
      </swiper>
    </view>

    <!-- 车辆标题与价格 -->
    <view class="commercial-title-section">
      <view class="commercial-title">{{commercialInfo.title || '皮卡救险车'}}</view>
      <view class="info-container">
        <view class="commercial-id">车源地：{{commercialInfo.location || '广东省 大埔县'}}</view>
        <view class="commercial-id">发布于:{{commercialInfo.date || '2025-04-28'}}</view>
      </view>
      <view class="price-container">
        <view class="current-price">
          <text class="price-label">售价</text>
          <text class="price-value">{{commercialInfo.salePrice || '25.8'}}</text>
          <text class="price-unit">万元</text>
        </view>
      </view>
    </view>

    <!-- 详细参数 -->
    <view class="detail-section">
      <view class="section-title">基本信息</view>
      <view class="params-grid">
        <!-- 基本信息 -->
        <view class="param-grid-item">
          <text class="grid-label">品牌</text>
          <text class="grid-value">{{commercialInfo.brand || '--'}}</text>
        </view>
        <view class="param-grid-item">
          <text class="grid-label">车辆类型</text>
          <text class="grid-value">{{commercialInfo.type || '--'}}</text>
        </view>
        <view class="param-grid-item">
          <text class="grid-label">子类型</text>
          <text class="grid-value">{{commercialInfo.subtype || '--'}}</text>
        </view>
        <view class="param-grid-item">
          <text class="grid-label">颜色</text>
          <text class="grid-value">{{commercialInfo.color || '--'}}</text>
        </view>
        <view class="param-grid-item">
          <text class="grid-label">尺寸</text>
          <text class="grid-value">{{commercialInfo.dimensions || '--'}}</text>
        </view>
        <view class="param-grid-item">
          <text class="grid-label">长度</text>
          <text class="grid-value">{{commercialInfo.length || '--'}}mm</text>
        </view>
        
        <!-- 发动机/动力相关 -->
        <view class="param-group-title">动力参数</view>
        <view class="param-grid-item">
          <text class="grid-label">排量</text>
          <text class="grid-value">{{commercialInfo.displacement || '--'}}</text>
        </view>
        <view class="param-grid-item">
          <text class="grid-label">能源类型</text>
          <text class="grid-value">{{commercialInfo.energy_type || '--'}}</text>
        </view>
        <view class="param-grid-item">
          <text class="grid-label">牵引力</text>
          <text class="grid-value">{{commercialInfo.tractive_force || '--'}}</text>
        </view>
        <view class="param-grid-item">
          <text class="grid-label">牵引力 (KN)</text>
          <text class="grid-value">{{commercialInfo.traction_force || '--'}}</text>
        </view>
        <view class="param-grid-item">
          <text class="grid-label">最高车速</text>
          <text class="grid-value">{{commercialInfo.maximum_speed || '--'}} km/h</text>
        </view>
        <view class="param-grid-item">
          <text class="grid-label">满载最高车速</text>
          <text class="grid-value">{{commercialInfo.full_load_maximum_speed || '--'}} km/h</text>
        </view>
        <view class="param-grid-item">
          <text class="grid-label">排放标准</text>
          <text class="grid-value">{{commercialInfo.emission_standard || '--'}}</text>
        </view>
        
        <!-- 变速箱相关 -->
        <view class="param-group-title">变速箱</view>
        <view class="param-grid-item">
          <text class="grid-label">驱动方式</text>
          <text class="grid-value">{{commercialInfo.drive_mode || '--'}}</text>
        </view>
        <view class="param-grid-item">
          <text class="grid-label">变速方式</text>
          <text class="grid-value">{{commercialInfo.shift_mode || '--'}}</text>
        </view>
        <view class="param-grid-item">
          <text class="grid-label">档位数</text>
          <text class="grid-value">{{commercialInfo.number_of_gears || '--'}}</text>
        </view>
        
        <!-- 乘客相关 -->
        <view class="param-group-title">载客信息</view>
        <view class="param-grid-item">
          <text class="grid-label">座位数</text>
          <text class="grid-value">{{commercialInfo.seats || '--'}}座</text>
        </view>
        <view class="param-grid-item">
          <text class="grid-label">核定载客</text>
          <text class="grid-value">{{commercialInfo.number_of_approved_passengers || '--'}}</text>
        </view>
        <view class="param-grid-item">
          <text class="grid-label">座椅排数</text>
          <text class="grid-value">{{commercialInfo.number_of_seating_rows || '--'}}</text>
        </view>
        
        <!-- 其他规格 -->
        <view class="param-group-title">载重信息</view>
        <view class="param-grid-item">
          <text class="grid-label">载重能力</text>
          <text class="grid-value">{{commercialInfo.load_capacity || '--'}}</text>
        </view>
      </view>
    </view>
    
    <!-- 详情描述 -->
    <view class="detail-section" wx:if="{{commercialInfo.description}}">
      <view class="section-title">详情描述</view>
      <view class="detail-content">
        <!-- <text>{{commercialInfo.description || '暂无详细描述'}}</text> -->
        <rich-text nodes="{{commercialInfo.description}}" style="width: 100%;"></rich-text>
      </view>
    </view>

  </scroll-view>

  <!-- 底部固定按钮 -->
  <view class="bottom-action-bar">

    <button class="contact-btn" bindtap="contactSeller">联系商家</button>
    <button class="view-btn" bindtap="viewLocation">查看位置</button>
 <view class="collect-button" bindtap="toggleCollect">
      <view class="collect-icon">
        <van-icon name="{{isCollected ? 'star' : 'star-o'}}" size="24px" color="{{isCollected ? '#FFD700' : '#999'}}" />
      </view>
      <text class="collect-text">收藏</text>
    </view>
  </view>
</view>

