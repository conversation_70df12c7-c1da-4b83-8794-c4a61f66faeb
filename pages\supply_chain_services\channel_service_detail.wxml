<!--pages/supply_chain_services/channel_service_detail.wxml-->
<!-- 引入登录弹窗模板 -->
<import src="../../templates/loginPopup/loginPopup.wxml" />

<view
    class="container"
    style="{{ pageStyle }}"
>
    <!-- 骨架屏 - 仅在页面加载中且不显示正常加载动画时显示 -->
    <view
        class="skeleton-screen"
        wx:if="{{isLoading && !showLoadingAnimation}}"
    >
        <!-- 骨架自定义导航栏 -->
        <view class="skeleton-custom-nav">
            <view
                class="skeleton-status-bar"
                style="height: {{statusBarHeight}}px;"
            ></view>
            <view class="skeleton-nav-title"></view>
        </view>

        <!-- 骨架车辆列表 -->
        <view class="skeleton-car-list">
            <view
                class="skeleton-car-item"
                wx:for="{{6}}"
                wx:key="index"
            >
                <view class="skeleton-car-image"></view>
                <view class="skeleton-car-info">
                    <view class="skeleton-car-title"></view>
                    <view class="skeleton-car-params"></view>
                    <view class="skeleton-car-price"></view>
                </view>
            </view>
        </view>
    </view>

    <!-- 自定义导航栏 -->
    <view class="custom-nav">
        <view
            class="status-bar"
            style="height: {{statusBarHeight}}px;"
        ></view>
        <view class="nav-title">
            <view
                class="nav-back"
                bindtap="navigateBack"
            >
                <van-icon
                    name="arrow-left"
                    size="20px"
                    color="#333"
                />
            </view>
            <view class="title-text">通道服务</view>
            <view class="nav-placeholder"></view>
        </view>
    </view>

    <!-- 内容区域 - 添加top-margin以避开导航栏 -->
    <view class="content-container">
        <!-- 页面内容 -->
        <view class="service-content">
            <!-- 通道服务卡片头部 -->
            <view class="service-card financial-card">
                <view class="card-image-container">
                    <image
                        class="card-image"
                        src="{{COS_CONFIG.url}}wechat/assets/images/channel_service_long_detil.png"
                        mode="scaleToFill"
                    ></image>
                </view>
            </view>

            <!-- 通道服务详细信息 -->
            <view class="info-card">
                <view class="info-inside">
                    <view class="info-inside-row">
                        <view class="info-inside-row-title">通道费用</view>
                        <view class="info-inside-row-content">最低 {{min_cost}} - 最高 {{max_cost}}</view>
                    </view>
                    <view class="info-inside-row">
                        <view class="info-inside-row-title">服务范围</view>
                        <view class="info-inside-row-content">{{regional || '全国'}}</view>
                    </view>
                    <view class="info-inside-row">
                        <view class="info-inside-row-title">退税时长</view>
                        <view class="info-inside-row-content">{{tax_refund_time || '30天'}}</view>
                    </view>
                    <view class="info-inside-row">
                        <view class="info-inside-row-title">所需材料</view>
                        <view class="info-inside-row-content">{{materials || '本本、出口资质'}}</view>
                    </view>
                    <view class="info-inside-row">
                        <view class="info-inside-row-title">补充说明</view>
                        <view class="info-inside-row-content">{{remark || '无'}}</view>
                    </view>
                </view>
            </view>

            <!-- 联系信息标题 -->
            <view class="section-header">
                <text class="section-title">联系信息</text>
                <!-- <view class="section-line"></view>
                <view class="section-stars">
                    <text class="star">★</text>
                    <text class="star">★</text>
                    <text class="star">★</text>
                </view> -->
            </view>

            <!-- 联系信息 -->
            <view class="contact-card">
                <view class="contact-info">
                    <view class="contact-row">
                        <view class="contact-label">公司名称</view>
                        <view class="contact-value">{{company_name || '暂无数据'}}</view>
                    </view>
                    <view class="contact-row">
                        <view class="contact-label">联系邮箱</view>
                        <view class="contact-value">{{email || '暂无邮箱信息'}}</view>
                    </view>
                </view>
            </view>
        </view>
    </view>

    <!-- 底部咨询按钮 -->
    <view class="bottom-button">
        <button
            class="share-button"
            open-type="share"
        >
            <van-icon
                name="share-o"
                size="24px"
            />
            <text>分享</text>
        </button>
        <button
            class="consult-button"
            bindtap="onConsult"
        >咨询下单</button>
    </view>

    <!-- 引用登录弹窗模板 -->
    <template
        is="loginPopup"
        data="{{ showLoginPopup, loginPopupOptions }}"
    />
</view>