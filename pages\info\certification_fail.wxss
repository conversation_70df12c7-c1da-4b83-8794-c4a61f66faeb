/* pages/info/certification_fail.wxss */

/* 页面样式 */
page {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: fixed;
  -webkit-overflow-scrolling: touch;
  background-color: #F5F5F5;
}

.container {
  min-height: 100vh;
  height: 100%;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #DCECFF 75%, #F1F4F9 100%);
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  overflow: hidden;
}

/* 自定义导航栏样式 */
.custom-nav {
  width: 100%;
  background: transparent;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}

.status-bar {
  width: 100%;
}

.nav-content {
  display: flex;
  align-items: center;
  height: 44px;
  padding: 0 20rpx;
  position: relative;
}

.back-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  transition: all 0.3s ease;
}

.back-icon:active {
  opacity: 0.7;
  transform: scale(0.95);
}

.nav-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 主内容区域 */
.main-content {
  padding: 80rpx 30rpx 60rpx;
  height: calc(100vh - var(--status-bar-height, 0px) - 44px);
  overflow-y: auto;
  box-sizing: border-box;
}

/* 认证失败卡片 */
.fail-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 24rpx;
  padding: 50rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  margin-top: 40rpx;
}

/* 状态区域 */
.status-section {
  text-align: center;
  margin-bottom: 50rpx;
}

.status-icon {
  width: 140rpx;
  height: 140rpx;
  margin: 0 auto 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FDECEC;
  border-radius: 50%;
}

.status-x {
  font-size: 80rpx;
  font-weight: bold;
  color: #ff4444;
  line-height: 1;
  margin-top: -12rpx;
}

.status-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.status-subtitle {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 失败原因区域 */
.reason-section {
  background: rgba(255, 68, 68, 0.05);
  border-radius: 16rpx;
  padding: 30rpx 24rpx;
  margin-bottom: 40rpx;
  border-left: 6rpx solid #ff4444;
}

.reason-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.reason-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #ff4444;
  margin-left: 8rpx;
}

.reason-content {
  margin-bottom: 0;
}

.reason-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  line-height: 1.5;
}

.reason-item:last-child {
  margin-bottom: 0;
}

.reason-label {
  color: #666;
  font-weight: 500;
  min-width: 120rpx;
  flex-shrink: 0;
}

.reason-value {
  color: #333;
  flex: 1;
}

/* 温馨提示文字 */
.tips-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  text-align: center;
  margin: 0;
  padding: 0;
}

/* 客服信息卡片 */
.service-card {
  background: rgba(18, 150, 219, 0.05);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-top: 40rpx;
  border-left: 6rpx solid #1296db;
}

.service-header {
  display: flex;
  align-items: flex-start;
}

.service-title {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-left: 8rpx;
  flex: 1;
}

/* 操作按钮区域 */
.action-section {
  margin-top: 60rpx;
  padding: 0 10rpx;
}

.primary-btn,
.secondary-btn {
  width: 100% !important;
  height: 96rpx !important;
  line-height: 96rpx !important;
  font-size: 32rpx !important;
  font-weight: 500 !important;
  border: none !important;
  margin-bottom: 20rpx;
  border-radius: 24rpx !important;
  letter-spacing: 2rpx !important;
  box-shadow: none !important;
}

.primary-btn {
  background-color: #4080ff !important;
  color: #fff !important;
}

.primary-btn:active {
  opacity: 0.8;
}

.secondary-btn {
  background-color: #fff !important;
  color: #666 !important;
  border: 2rpx solid #e0e0e0 !important;
}

.secondary-btn:active {
  background-color: #f5f5f5 !important;
}

/* 按钮禁用状态 */
.primary-btn[disabled],
.secondary-btn[disabled] {
  background-color: #cccccc !important;
  color: #ffffff !important;
  opacity: 0.7;
}

/* 按钮文字样式 */
.primary-btn::after,
.secondary-btn::after {
  border: none !important;
}

/* 响应式适配 */
@media (max-height: 600px) {
  .status-section {
    padding: 40rpx 0;
    margin-bottom: 40rpx;
  }

  .main-content {
    padding: 30rpx 30rpx 40rpx;
  }

  .action-section {
    margin-top: 40rpx;
  }
}