/* pages/supply_chain_services/channel_service.wxss */

/* 页面容器 */
.container {
    min-height: 100vh;
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    position: relative;
}

/* 渐变背景 */
.gradient-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
    background-attachment: fixed;
    /* 固定背景，避免滚动时背景移动 */
    z-index: -1;
}

/* 统一头部背景 */
.unified-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 990;
    background-image: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
    background-size: 400% 400%;
    background-position: 0% 0%;
    box-shadow: none;
    /* 移除阴影，避免与内容区域产生分界线 */
}

/* 自定义导航栏 */
.custom-navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    /* 增大z-index值 */
    display: flex;
    flex-direction: column;
    background: transparent;
    /* 改为透明，使用统一头部背景 */
}

.status-bar {
    width: 100%;
}

.navbar-content {
    display: flex;
    height: 44px;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
}

.nav-back {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 44px;
    z-index: 5;
    margin-left: -8rpx;
}

.nav-title {
    flex: 1;
    text-align: center;
    font-size: 34rpx;
    font-weight: 500;
    color: #333;
}

.nav-placeholder {
    width: 40px;
    height: 44px;
}

/* 搜索栏样式 */
.search-bar {
    padding: 20rpx 20rpx;
    padding-bottom: 0rpx;
    background: transparent;
    /* 改为透明，使用统一头部背景 */
    width: 100%;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    /* 比导航栏低一级 */
    border-top: none;
    /* 确保没有顶部边框 */
}

/* 调整搜索输入框的背景，使其半透明 */
.search-input-wrapper {
    flex: 1;
    display: flex;
    height: 70rpx;
    box-sizing: border-box;
    border-radius: 16rpx;
    overflow: hidden;
    background-color: rgba(255, 255, 255, 0.8);
    /* 提高不透明度使输入框更明显 */
    align-items: center;
    position: relative;
    /* 移除右边距，使搜索框更长 */
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    border: 2rpx solid #1C82F5;
    transition: all 0.2s ease-in-out;
}

.search-icon {
    margin-left: 20rpx;
    color: #999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-input {
    flex: 1;
    height: 100%;
    padding: 0 110rpx 0 20rpx;
    font-size: 28rpx;
    background-color: transparent;
    border: none;
    color: #333;
}

.search-btn {
    height: 56rpx;
    line-height: 56rpx;
    background: linear-gradient(to right, #1C82F5, #27AFF5);
    color: #fff;
    font-size: 28rpx;
    text-align: center;
    padding: 0 30rpx;
    border-radius: 8rpx;
    position: absolute;
    right: 8rpx;
    top: 50%;
    transform: translateY(-50%);
    box-shadow: 0 2rpx 8rpx rgba(56, 136, 255, 0.3);
    transition: all 0.2s ease-in-out;
}

.search-btn:active {
    background: linear-gradient(to right, #1973dd, #24a0e5);
    box-shadow: 0 1rpx 3rpx rgba(56, 136, 255, 0.2);
    transform: translateY(-50%) scale(0.98);
}

/* 内容区域 */
.content {
    width: 100%;
    padding: 0;
    box-sizing: border-box;
    position: relative;
    background: transparent;
    border-top: none;
    /* 确保没有顶部边框 */
    margin-top: 0;
    /* 由WXML中的style控制，确保为0 */
}

/* 服务列表区域 */
.service-list {
    padding: 10rpx 0;
    /* 移除左右内边距，只保留上下内边距 */
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: 20rpx;
    /* 使用gap属性控制卡片间距 */
    position: relative;
    z-index: 10;
    /* 确保小于导航栏和搜索栏 */
    padding-top: 0rpx;
    /* 减小顶部内边距，从20rpx改为5rpx */
}

/* 服务卡片 */
.service-card {
    width: 100%;
    background-color: rgba(255, 255, 255, 0.75);
    /* 降低不透明度，更好融入背景 */
    border-radius: 0;
    /* 移除圆角 */
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
    /* 减淡阴影 */
    padding: 30rpx;
    box-sizing: border-box;
    transition: all 0.2s ease-in-out;
    overflow: hidden;
    position: relative;
    border: 1rpx solid rgba(240, 240, 240, 0.2);
    /* 进一步减淡边框颜色 */
    /* 恢复边框 */
    z-index: 1;
    /* 确保小于导航栏和搜索栏 */
    margin-top: 5rpx;
    /* 轻微顶部边距 */
    backdrop-filter: blur(2px);
    /* 轻微模糊效果 */
}

.service-card:first-child {
    margin-top: 0rpx;
    /* 减小第一个卡片的顶部外边距，从15rpx改为5rpx */
}

.service-card:active {
    background-color: rgba(255, 255, 255, 0.95);
    transform: scale(0.98);
    /* 恢复点击缩放效果 */
}

.service-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16rpx;
}

.company-name {
    font-size: 30rpx;
    font-weight: 500;
    color: #333;
    flex: 1;
}

.card-arrow {
    padding: 6rpx;
}

.service-card-info {
    display: flex;
    flex-direction: column;
    gap: 14rpx;
}

.info-row {
    display: flex;
    justify-content: flex-start;
}

.info-row .info-item:first-child {
    margin-right: 140rpx;
}

.info-item {
    font-size: 24rpx;
    color: #6B7280;
}

.qualification-tag,
.years-tag,
.coverage-tag {
    font-size: 24rpx;
    padding: 4rpx 16rpx;
    border-radius: 6rpx;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.qualification-tag {
    background-color: #E1F2FF;
    color: #1C82F5;
}

.years-tag {
    background-color: #E7F9E7;
    color: #52C652;
}

.coverage-tag {
    background-color: #FFF3E8;
    color: #FF9500;
}

.fee-info {
    font-size: 26rpx;
    color: #6B7280;
    margin-top: 6rpx;
}

/* 添加值的样式 */
.fee-value {
    color: #3D3D3D;
    margin-left: 10rpx;
}

/* 加载更多 */
.loading-more {
    text-align: center;
    padding: 30rpx 0;
    color: #999;
    font-size: 26rpx;
}

/* 空状态 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 0;
}

.empty-icon {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 20rpx;
    opacity: 0.7;
}

.empty-text {
    color: #999;
    font-size: 28rpx;
}

/* 背景过渡区域 */
.transition-area {
    position: fixed;
    width: 100%;
    height: 0;
    /* 减小高度为0，原来是20rpx */
    background: linear-gradient(to bottom, rgba(220, 236, 255, 0.5), transparent);
    z-index: 980;
}