// index.js
import { areaList } from '@vant/area-data';
import api from '../../utils/api';  // 确保导入 api
import share from '../../utils/share';  // 确保导入 分享
import util from '../../utils/util';  // 导入util工具类
import config from '../../config';  // 导入配置文件

Page({
  behaviors: [share], //分享设置
  data: {
    //处理分享页面 统一写
    shareData: {
      title: '找车侠',
      isDetailPage: false, // 标记是否详情页
    },

    city: '广州',
    activePriceRange: '',
    activeCarType: '',
    recommendCars: [],  // 改为空数组，等待接口数据填充
    showAreaPopup: false,
    areaList: {
      province_list: areaList.province_list,
      city_list: areaList.city_list
    },
    currentCityFullName: '',
    currentPage: 1,
    pageSize: 10,
    hasMoreData: true,
    isLoading: false,
    scrollTop: 0,
    navHeight: 0, // 导航栏高度
    statusBarHeight: 20, // 默认状态栏高度
    headerContentHeight: 0, // 头部内容区域高度
    navContentHeight: 0, // 快捷导航高度
    COS_CONFIG: config.COS_CONFIG, // 添加腾讯云配置
    isNavFixed: false, // 快捷导航是否固定，替换原来的isHeaderHidden
    lastScrollTop: 0, // 记录上一次滚动位置
    scrollThreshold: 50, // 滚动阈值，超过此值才触发隐藏/显示
    navFixedThreshold: 200, // 快捷导航固定位置的阈值，将在onLoad时计算，设置一个默认值
    topSpace: 0, // 添加顶部空间计算，用于搜索框外边距
    pageLoading: true, // 添加页面加载状态，用于控制骨架屏显示
    // 热门品牌数据
    hotBrands: [
      {
        id: 1,
        name: '奔驰',
        icon: 'https://p3.dcarimg.com/img/motor-mis-img/84c01ab4bb1f55a4809781b3a16586ff~80x0.webp'
      },
      {
        id: 2,
        name: '宝马',
        icon: 'https://p3.dcarimg.com/img/motor-mis-img/4867710a834bd648ba55797ba5e37f14~80x0.webp'
      },
      {
        id: 3,
        name: '奥迪',
        icon: 'https://p3.dcarimg.com/img/motor-mis-img/62946ba030f3589e083d8d3e98a595eb~80x0.webp'
      },
      {
        id: 4,
        name: '保时捷',
        icon: 'https://p3.dcarimg.com/img/tos-cn-i-dcdx/d809a071a391474c8067edeb637c328f~80x0.webp'
      },
      {
        id: 5,
        name: '雷克萨斯',
        icon: 'https://p3.dcarimg.com/img/motor-mis-img/b43814293efa6db10b762da4d351bfe7~80x0.webp'
      }
    ],
    banners: [], // 清空静态数据，改为空数组等待API数据填充
    topContainerHeight: 570, // 调整默认头部高度，考虑状态栏高度
    navFixedProgress: 0, // 导航固定进度值，用于平滑过渡
  },

  async onLoad() {
    // 初始显示骨架屏
    this.setData({
      pageLoading: true
    });

    // 获取系统状态栏高度
    wx.getSystemInfo({
      success: (res) => {
        // 导航栏高度 = 状态栏 + 44px（标题栏）
        const navHeight = res.statusBarHeight + 44;

        this.setData({
          statusBarHeight: res.statusBarHeight,
          navHeight: navHeight,
          // 设置搜索栏的顶部外边距等于导航栏高度，避免被遮挡
          topSpace: navHeight
        });
        // console.log('状态栏高度：', res.statusBarHeight);
        // console.log('导航栏总高度：', navHeight);
      }
    });

    // 设置地区数据
    this.setData({
      areaList: {
        province_list: areaList.province_list,
        city_list: areaList.city_list
      }
    });

    try {
      // 并行请求数据以提高加载速度
      await Promise.all([
        this.getBrandList(),
        this.getBanners(),
        this.getRecommendCars()
      ]);

      // 全部数据加载完成后，隐藏骨架屏
      this.hideSkeletonAfterDelay();
    } catch (error) {
      console.error('数据加载失败:', error);
      // 即使数据加载失败，也要在一定时间后隐藏骨架屏
      this.hideSkeletonAfterDelay();
    }
  },

  // 增加一个延迟隐藏骨架屏的方法
  hideSkeletonAfterDelay() {
    // 至少显示骨架屏800ms，避免闪烁
    const minSkeletonTime = 800;
    const loadStartTime = Date.now();
    const timeElapsed = Date.now() - loadStartTime;

    const delay = Math.max(0, minSkeletonTime - timeElapsed);

    setTimeout(() => {
      this.setData({ pageLoading: false });

      // 添加卡片的动画效果
      this.addCardAnimations();
    }, delay);
  },

  // 添加卡片动画效果
  addCardAnimations() {
    const cardCount = this.data.recommendCars.length;
    const animations = [];

    for (let i = 0; i < cardCount; i++) {
      const animation = wx.createAnimation({
        duration: 300,
        timingFunction: 'ease',
        delay: i * 50 // 每张卡片依次延迟显示
      });

      // 设置初始状态和最终状态
      animation.opacity(0).translateY(15).step({ duration: 0 });
      animation.opacity(1).translateY(0).step({ duration: 300 });

      animations.push(animation.export());
    }

    // 更新卡片数据，添加动画
    const updatedCars = this.data.recommendCars.map((car, index) => {
      return {
        ...car,
        animation: animations[index]
      };
    });

    this.setData({
      recommendCars: updatedCars
    });
  },

  onReady() {
    // 页面初始化完成后计算高度
    this.calculateHeaderHeight();

    // 计算导航占位高度
    this.calculateNavPlaceholderHeight();

    // 监听页面尺寸变化
    wx.onWindowResize(() => {
      this.calculateHeaderHeight();
      this.calculateNavPlaceholderHeight();
    });
  },

  // 计算头部高度
  calculateHeaderHeight() {
    // console.log('开始计算头部高度...');
    // 获取系统信息，计算状态栏高度
    wx.getSystemInfo({
      success: (res) => {
        const statusBarHeight = res.statusBarHeight;

        // 导航栏高度 = 状态栏 + 44px（标题栏）
        const navHeight = statusBarHeight + 44;

        // 添加延时确保所有元素渲染完成
        setTimeout(() => {
          const query = wx.createSelectorQuery();

          // 获取头部内容区域高度
          query.select('.top-container').boundingClientRect();

          // 获取快捷导航高度
          query.select('.quick-nav-container').boundingClientRect();

          query.exec((res) => {
            if (res && res[0] && res[1]) {
              // 获取头部内容区域高度
              const headerHeight = res[0].height;
              // 获取快捷导航高度
              const navHeight = res[1].height;
              // 计算导航固定阈值 - 当滚动到这个位置时，导航应该固定
              const navFixedThreshold = res[0].top + headerHeight - (statusBarHeight + 44);

              // console.log('头部内容高度:', headerHeight);
              // console.log('导航高度:', navHeight);
              // console.log('导航固定阈值:', navFixedThreshold);

              // 确保计算的阈值是有效的（大于0）
              if (navFixedThreshold > 0) {
                this.setData({
                  navHeight: navHeight,
                  headerContentHeight: headerHeight,
                  navContentHeight: navHeight,
                  navFixedThreshold: navFixedThreshold
                });
              } else {
                // console.error('计算的导航固定阈值无效:', navFixedThreshold);
                // 设置一个默认阈值
                this.setData({
                  navHeight: navHeight,
                  headerContentHeight: headerHeight,
                  navContentHeight: navHeight,
                  navFixedThreshold: 300 // 默认值
                });
              }
            } else {
              // console.error('未能获取到页面元素高度:', res);
            }
          });
        }, 500); // 增加延时，确保元素完全渲染
      }
    });
  },

  // 获取品牌列表
  async getBrandList() {
    try {
      const result = await api.car.getBrandList();

      // 检查 result 是否是数组
      if (Array.isArray(result)) {
        // 转换数据格式
        const brandList = result.map(brand => {
          // 检查每个品牌对象的结构
          return {
            text: brand.brand_name,
            value: brand.id
          };
        });

        // 在列表开头添加"全部"选项
        brandList.unshift({ text: '品牌', value: 0 });

        // 过滤出有logo的品牌
        const brandsWithLogo = result.filter(brand => brand.logo);

        // 如果有效logo的品牌数量不足5个，则使用所有有效logo的品牌
        const validBrandsCount = Math.min(brandsWithLogo.length, 5);

        // 随机抽取有logo的品牌
        const shuffledBrands = this.shuffleArray([...brandsWithLogo]);
        const hotBrands = shuffledBrands.slice(0, validBrandsCount).map((brand, index) => ({
          id: brand.id,
          name: brand.brand_name,
          icon: brand.logo
        }));

        // 使用 Promise 包装 setData
        return new Promise((resolve) => {
          this.setData({
            brandOptions: brandList,
            brandText: '品牌',
            brandValue: 0,
            hotBrands: hotBrands
          }, () => {
            // 验证数据是否成功设置
            const currentData = this.data.brandOptions;
            resolve(brandList);
          });
        });
      } else {
        console.error('API 返回的数据格式不正确:', result);
        throw new Error('品牌数据格式不正确');
      }
    } catch (error) {
      console.error('获取品牌列表失败:', error);
      wx.showToast({
        title: '获取品牌列表失败',
        icon: 'none',
        duration: 2000
      });
      throw error;
    }
  },

  // 获取推荐车辆
  async getRecommendCars(page = 1) {
    // 如果不是首页数据，直接返回（禁止加载更多）
    if (page > 1) {
      return;
    }

    // 设置加载状态
    if (!this.data.isLoading) {
      this.setData({ isLoading: true });
    }

    try {
      const result = await api.car.getCarList({
        page: 1, // 始终只加载第一页
        list_rows: this.data.pageSize
      });

      if (result && result.data) {
        const carListData = Array.isArray(result.data) ? result.data : [];

        // 使用公共函数格式化车辆数据
        const formattedCars = this.formatCarData(carListData);

        // 更新数据，并将hasMoreData设为false，禁止加载更多
        this.setData({
          recommendCars: formattedCars,
          currentPage: 1,
          hasMoreData: false, // 设置为false，不再加载更多
          isLoading: false
        });
      }
    } catch (error) {
      console.error('获取推荐车辆失败:', error);
      if (page === 1) {
        wx.stopPullDownRefresh();
      }
      this.setData({ isLoading: false });
    }
  },

  // 加载更多车辆
  loadMoreCars() {
    // 禁用加载更多功能
    return;

    // 原有代码注释掉
    /*
    if (!this.data.isLoading && this.data.hasMoreData) {
      // 已在onReachBottom中设置了isLoading，这里直接加载数据
      this.getRecommendCars(this.data.currentPage + 1);
    }
    */
  },

  // 下拉刷新
  onPullDownRefresh() {
    // 简化刷新实现，仅重新加载第一页数据
    this.setData({
      currentPage: 1,
      isLoading: true
    }, async () => {
      try {
        // 提供轻微振动反馈
        wx.vibrateShort({ type: 'medium' });

        // 直接加载数据
        await this.getRecommendCars(1);
      } catch (error) {
        console.error('刷新数据失败:', error);
      } finally {
        wx.stopPullDownRefresh();
        this.setData({ isLoading: false });
      }
    });
  },

  // 上拉加载更多
  onReachBottom() {
    // 禁用加载更多功能
    return;

    // 原有代码注释掉
    /* 
    // 添加安全检查确保页面已经完全准备好
    if (!this.data.isLoading && this.data.hasMoreData && this.data.recommendCars.length > 0) {
      // 设置加载状态
      this.setData({ isLoading: true }, () => {
        // 添加延时，使加载动画更加明显，增强用户体验
        setTimeout(() => {
          this.loadMoreCars();
        }, 300);
      });
    }
    */
  },

  // 数组随机打乱方法
  shuffleArray(array) {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  },

  onShow() {
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 0
      })
    }

    // 重置导航状态，确保从其他页面返回时恢复初始状态
    this.setData({
      isNavFixed: false,
      lastScrollTop: 0
    });

    // 强制重置滚动位置到顶部
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 0  // 立即滚动，无动画
    });

    // 页面显示时重新计算高度，以应对可能的显示变化
    setTimeout(() => {
      this.calculateHeaderHeight();
      this.calculateNavPlaceholderHeight();
    }, 200);  // 增加延时，确保页面元素完全渲染
  },

  onCityTap() {
    this.setData({
      showAreaPopup: true
    });
  },

  onCloseAreaPopup() {
    this.setData({
      showAreaPopup: false
    });
  },

  onConfirmArea(e) {
    const { values } = e.detail;
    // 只取省市两级
    const province = values[0]?.name || '';
    const city = values[1]?.name || '';

    // 构建完整的地区名称（省 市）
    const fullCityName = city ? `${province} ${city}` : province;

    // 更新显示的城市名称（只显示市名）
    const displayCity = (city || province).replace(/市$/, '');

    this.setData({
      city: displayCity,
      showAreaPopup: false,
      currentCityFullName: fullCityName
    });
  },

  // 处理搜索输入
  onSearchInput(e) {
    this.searchValue = e.detail.value;
  },

  // 搜索事件处理
  onSearch(e) {
    const searchValue = e.detail.value ? e.detail.value : this.searchValue;
    // 保存搜索关键词和城市信息到本地存储
    wx.setStorageSync('searchFilter', {
      keyword: searchValue,
      city: this.data.city,
      fullCityName: this.data.currentCityFullName,
      from: 'index'
    });

    // 跳转到购车页面
    wx.switchTab({
      url: '/pages/buy/index'
    });
  },

  onQuickNavTap: function (event) {
    const type = event.currentTarget.dataset.type;

    if (type === 'foreign_logistics') {
      wx.navigateTo({
        url: '/pages/foreign_logistics/foreign_logistics'
      });
      return; // 阻止后续跳转逻辑
    }

    if (type === 'domestic_trailer') {
      wx.navigateTo({
        url: '/pages/domestic_trailer/domestic_trailer'
      });
      return; // 阻止后续跳转逻辑
    }

    if (type === 'testing_services') {
      wx.navigateTo({
        url: '/pages/testing_services/index'
      });
      return; // 阻止后续跳转逻辑
    }

    if (type === 'flashing_machine') {
      wx.navigateTo({
        url: '/pages/flashing_machine/flashing_machine'
      });
      return; // 阻止后续跳转逻辑
    }

    if (type === 'luxury') {
      wx.navigateTo({
        url: '/pages/train_operation/index'
      });
      return; // 阻止后续跳转逻辑
    }

    if (type === 'OverseasExtendedWarranty') {
      wx.showToast({
        title: '海外延保功能开发中',
        icon: 'none'
      });
      return; // 阻止后续跳转逻辑
    }

    if (type === 'sedan') {
      wx.showToast({
        title: '采购信息功能开发中',
        icon: 'none'
      });
      return; // 阻止后续跳转逻辑
    }

    if (type === 'electric') {
      // wx.navigateTo({
      //   url: '/pages/brand/brand'
      // });
      wx.showToast({
        title: '更多功能开发中',
        icon: 'none'
      });
      return; // 阻止后续跳转逻辑
    }

    // 特殊处理二手车类型，直接跳转到找车页面
    if (type === 'used') {
      // 跳转到找车页面
      wx.switchTab({
        url: '/pages/buy/index'
      });
      return; // 提前返回，不执行后面的代码
    }

    if (type === 'FinancialServices') {
      wx.navigateTo({
        url: '/pages/supply_chain_services/index'
      });
      // wx.showToast({
      //   title: '供应链功能开发中',
      //   icon: 'none'
      // });
      return; // 阻止后续跳转逻辑
    }

    // 保存筛选条件到本地存储（原有逻辑保持不变）
    wx.setStorageSync('carFilter', {
      type: type,
      from: 'index'
    });
    const url = `/pages/vehicle/index?title=${type}`
    // console.log(url)
    wx.navigateTo({
      url: url
    });
  },

  onQuickNavTapCommercial: function (event) {
    const type_id = event.currentTarget.dataset.type;
    const type = 'commercial';

    // 根据不同的类型设置对应的标题
    let typeName = '';
    switch (type_id) {
      case '1':
        typeName = '卡车';
        break;
      case '2':
        typeName = '客车';
        break;
      case '3':
        typeName = '专用车';
        break;
      case '4':
        typeName = '挂车';
        break;
      default:
        typeName = '商用车';
    }

    // 保存筛选条件到本地存储
    wx.setStorageSync('carFilter', {
      type: type,
      from: 'index'
    });

    const url = `/pages/commercial/index?title=${type}&type_id=${type_id}&typeName=${typeName}`;
    // console.log(url);
    wx.navigateTo({
      url: url
    });
  },

  onCarTap(e) {
    const { id } = e.currentTarget.dataset;

    // 检查登录状态
    const userInfo = util.getUserInfo();
    if (!userInfo || !userInfo.app_id) {
      // 未登录，直接跳转到登录页面，并传递车辆ID作为重定向参数
      wx.navigateTo({
        url: `/pages/login/index?redirect=/pages/buy/detail&id=${id}`
      });
      return;
    }

    // 已登录，直接跳转到详情页
    wx.navigateTo({
      url: `/pages/buy/detail?id=${id}`
    });
  },

  // 处理点击品牌
  onBrandTap(e) {
    const brandId = e.currentTarget.dataset.id;
    const brandName = e.currentTarget.dataset.name;
    // console.log('点击了品牌:', brandName, brandId);

    // 可以跳转到品牌详情页或搜索结果页
    wx.navigateTo({
      url: `/pages/search/search?brand=${brandName}`
    });
  },

  onMoreHot() {
    wx.navigateTo({
      url: '/pages/car-list/index?type=hot'
    })
  },

  onMoreBrands() {
    wx.navigateTo({
      url: '/pages/brand-list/index'
    })
  },

  onMoreDeals() {
    wx.navigateTo({
      url: '/pages/car-list/index?type=deal'
    })
  },

  onMoreNew() {
    wx.navigateTo({
      url: '/pages/vehicle/list/index?type=new'
    });
  },

  // 点击"更多品牌"
  onMoreCarsBrand() {
    wx.navigateTo({
      url: '/pages/brand/brand'
    });
  },

  // 点击"更多好车"
  onMoreCarsTap() {
    // wx.switchTab({
    //   url: '/pages/buy/index'
    // });
  },

  onPriceRangeTap(e) {
    const range = e.currentTarget.dataset.range;
    this.setData({
      activePriceRange: range
    });
    // console.log('选择价格范围:', range);
    // 根据价格范围跳转或筛选
    wx.navigateTo({
      url: `/pages/buy/index?priceRange=${range}`
    });
  },

  onCarTypeTap(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      activeCarType: type
    });
    // console.log('选择车型:', type);
    // 根据车型跳转或筛选
    wx.navigateTo({
      url: `/pages/buy/index?carType=${type}`
    });
  },

  // 选择全国
  onSelectAllCity() {
    this.setData({
      city: '全国',
      showAreaPopup: false,
      currentCityFullName: ''
    });
  },

  // 获取轮播图数据
  async getBanners() {
    try {
      // console.log('开始获取轮播图数据');
      const result = await api.car.getBannerList();
      // console.log('轮播图接口返回数据:', JSON.stringify(result));

      // 如果是直接返回数据而不是包含在data中
      if (result && Array.isArray(result) && typeof result[0] === 'string') {
        const formattedBanners = result.map((url, index) => ({
          id: index + 1,
          image: url,
          link: ''
        }));

        // console.log('直接数组数据格式化后:', JSON.stringify(formattedBanners));
        this.setData({
          banners: formattedBanners
        });
        return;
      }

      // 标准返回格式处理
      if (result && result.data) {
        // 如果返回的是字符串数组（直接是URL列表）
        if (Array.isArray(result.data) && typeof result.data[0] === 'string') {
          const formattedBanners = result.data.map((url, index) => ({
            id: index + 1,
            image: url,
            link: ''
          }));

          // console.log('处理后的轮播图数据:', JSON.stringify(formattedBanners));

          // 更新轮播图数据
          this.setData({
            banners: formattedBanners
          });
        } else {
          // 如果是对象数组，保留原来的处理逻辑
          const bannerData = Array.isArray(result.data) ? result.data : [];

          const formattedBanners = bannerData.map(item => ({
            id: item.id || Math.floor(Math.random() * 10000),
            image: item.image || item.img_url || item.url,
            link: item.link || item.url || ''
          }));

          // console.log('处理后的轮播图数据(对象数组):', JSON.stringify(formattedBanners));

          this.setData({
            banners: formattedBanners
          });
        }
      } else {
        console.error('轮播图接口返回数据格式不正确:', JSON.stringify(result));
        // 设置静态数据作为备用
        this.setData({
          banners: [
            {
              id: 1,
              image: 'https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/wechat/assets/images/轮播图.png',
              link: ''
            }
          ]
        });
      }
    } catch (error) {
      console.error('获取轮播图失败详情：', error);
      // 加载失败时设置静态图片
      this.setData({
        banners: [
          {
            id: 1,
            image: 'https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/wechat/assets/images/轮播图.png',
            link: ''
          }
        ]
      });
    }
  },

  onPageScroll(e) {
    // 获取滚动位置
    let scrollTop = e.scrollTop;
    if (e.detail && e.detail.scrollTop !== undefined) {
      scrollTop = e.detail.scrollTop;
    }

    // 如果阈值不正确，先计算高度
    if (this.data.navFixedThreshold <= 0) {
      // console.log('导航阈值未正确计算, 重新计算中');
      this.calculateHeaderHeight();
      return;
    }

    // 计算导航固定的进度值 (0-1之间)
    const transitionZone = 50; // 过渡区域大小，可以调整以改变过渡效果的平滑度
    const transitionStart = this.data.navFixedThreshold - transitionZone; // 开始过渡的位置

    // 计算过渡进度
    let progress = 0;
    if (scrollTop <= transitionStart) {
      progress = 0; // 还未到过渡区域
    } else if (scrollTop >= this.data.navFixedThreshold) {
      progress = 1; // 已经完全过渡
    } else {
      // 在过渡区域内，计算0-1之间的进度值
      progress = (scrollTop - transitionStart) / transitionZone;
    }

    // 判断是否需要固定导航，但不影响顶部搜索区域的显示
    const shouldBeFixed = progress > 0.5;

    // 只有状态发生变化时才更新，避免不必要的重渲染
    if (shouldBeFixed !== this.data.isNavFixed || Math.abs(progress - this.data.navFixedProgress) > 0.01) {
      this.setData({
        isNavFixed: shouldBeFixed,
        navFixedProgress: progress
      }, () => {
        if (shouldBeFixed && !this.navPlaceholderCalculated) {
          this.calculateNavPlaceholderHeight();
          this.navPlaceholderCalculated = true;
        }
      });
    }

    // 更新上一次滚动位置
    this.data.lastScrollTop = scrollTop;
  },

  // 计算导航占位高度的方法
  calculateNavPlaceholderHeight() {
    // 无论是否固定，都先获取快捷导航的高度
    const query = wx.createSelectorQuery();
    query.select('.quick-nav-container').boundingClientRect();
    query.exec((res) => {
      if (res && res[0]) {
        const navHeight = res[0].height;
        // 减少一点高度，避免出现多余的空白
        const adjustedHeight = Math.max(navHeight - 5, 0);
        this.setData({
          navContentHeight: adjustedHeight
        });
        // console.log('导航占位高度:', adjustedHeight);
      }
    });
  },

  // 将车辆数据转换为展示格式的公共函数
  formatCarData(carListData) {
    return carListData.map((car, index) => ({
      id: car.id,
      badge: index % 3 === 0 ? '热门' : (index % 3 === 1 ? '新到' : '推荐'),
      title: car.ui_vehicle_name || car.title_desc,
      year: car.first_registration_time ? car.first_registration_time.substring(0, 4) : '',
      mileage: (car.mileage || 0).toString(),
      price: car.sell_price || '0',
      originalPrice: car.official_price || '0',
      image: car.main_url || (car.image_urls ? car.image_urls.split(',')[0] : ''),
      location: car.vehicle_source_location || '',
      date: car.create_time || new Date().toISOString().substring(0, 10),
      // 添加动画延迟属性，使卡片按顺序出现
      animationDelay: (index % 6) * 0.05
    }));
  }
})
