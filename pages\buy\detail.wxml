<view class="container">
  <!-- 引入登录弹窗模板 -->
  <import src="/templates/loginPopup/loginPopup.wxml" />

  <!-- 使用登录弹窗模板 -->
  <template
    is="loginPopup"
    data="{{showLoginPopup, loginPopupOptions}}"
  ></template>

  <!-- 自定义导航栏 -->
  <view class="custom-nav">
    <view
      class="status-bar"
      style="height: {{statusBarHeight}}px;"
    ></view>
    <view class="nav-content">
      <view
        class="back-icon"
        bindtap="navigateBack"
      >
        <image src="/icons/moments/back.svg"></image>
      </view>
      <view class="nav-title">车辆详情</view>
    </view>
  </view>

  <!-- 导航栏占位符 -->
  <view
    class="nav-placeholder"
    style="height: {{statusBarHeight + 44}}px;"
  ></view>

  <!-- 添加加载状态 -->
  <block wx:if="{{loading}}">
    <view class="loading-container">
      <view class="loading"></view>
      <text>加载中...</text>
    </view>
  </block>

  <block wx:else>
    <!-- 主要信息展示区 -->
    <view class="main-info-container">
      <!-- 顶部轮播图 -->
      <view class="swiper-container">
        <swiper
          class="car-image"
          autoplay="{{carImages.length > 1}}"
          interval="4000"
          duration="500"
          circular="{{carImages.length > 1}}"
          bindchange="swiperChange"
        >
          <block wx:if="{{carImages.length > 0}}">
            <swiper-item
              wx:for="{{carImages}}"
              wx:key="index"
            >
              <image
                src="{{item}}"
                mode="aspectFill"
                data-url="{{item}}"
                bindtap="previewImage"
                data-index="{{index}}"
                binderror="handleImageError"
                lazy-load="true"
              />
            </swiper-item>
          </block>
          <block wx:else>
            <swiper-item>
              <image
                src="/assets/images/default-car.png"
                mode="aspectFill"
              />
            </swiper-item>
          </block>
        </swiper>

        <!-- 自定义数字指示器 -->
        <view
          class="swiper-indicator"
          wx:if="{{carImages.length > 1}}"
        >
          {{currentSwiperIndex + 1}} / {{carImages.length}}
        </view>
      </view>

      <!-- 车辆标题 -->
      <view class="car-title">{{carTitle}}</view>

      <!-- 车源信息行 -->
      <view class="car-source-row">
        <view class="source-item source-location">车源地：{{location}}</view>
        <view class="source-item source-transfer">过户次数：{{transferCount}}次</view>
      </view>

      <!-- 价格信息行 -->
      <view class="price-row">
        <view class="price-date">{{publishDate}}</view>
        <view class="price-left">
          <view class="price-info-row">
            <view class="price-official">新车指导价：{{official_price}}万元</view>
            <view class="price-main">{{price}}万元</view>
          </view>
        </view>
      </view>
    </view>



    <!-- 车辆信息卡片 -->
    <view class="car-info-container">
      <!-- 档案部分 -->
      <view class="info-card">
        <view class="section-title">车辆档案</view>
        <view class="info-grid">
          <view class="info-item">
            <image
              class="info-icon"
              src="https://zhaochexia-**********.cos.ap-guangzhou.myqcloud.com/wechat/assets/icons/license-plate.png"
              mode="aspectFit"
            ></image>
            <view class="info-content">
              <view class="info-label">上牌时间</view>
              <view class="info-value">{{firstRegister}}</view>
            </view>
          </view>
          <view class="info-item">
            <image
              class="info-icon"
              src="https://zhaochexia-**********.cos.ap-guangzhou.myqcloud.com/wechat/assets/icons/mileage.png"
              mode="aspectFit"
            ></image>
            <view class="info-content">
              <view class="info-label">表显里程</view>
              <view class="info-value">{{mileage}}万公里</view>
            </view>
          </view>
          <view class="info-item">
            <image
              class="info-icon"
              src="https://zhaochexia-**********.cos.ap-guangzhou.myqcloud.com/wechat/assets/icons/color.png"
              mode="aspectFit"
            ></image>
            <view class="info-content">
              <view class="info-label">车身颜色</view>
              <view class="info-value">{{color}}</view>
            </view>
          </view>
          <view class="info-item">
            <image
              class="info-icon"
              src="https://zhaochexia-**********.cos.ap-guangzhou.myqcloud.com/wechat/assets/icons/vin.png"
              mode="aspectFit"
            ></image>
            <view class="info-content">
              <view class="info-label">VIN号</view>
              <view class="info-value">{{carId}}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 参数部分 -->
      <view class="info-card">
        <view class="section-title">
          <text>车辆参数</text>
          <view
            class="detail-config"
            bindtap="navigateToDetailConfig"
          >详细参数配置 ></view>
        </view>
        <view class="params-row">
          <view class="param-item">
            <image
              class="info-icon"
              src="https://zhaochexia-**********.cos.ap-guangzhou.myqcloud.com/wechat/assets/icons/emission.png"
              mode="aspectFit"
            ></image>
            <view class="info-content">
              <view class="info-label">排放标准</view>
              <view class="info-value">{{emission}}</view>
            </view>
          </view>
          <view class="param-item">
            <image
              class="info-icon"
              src="https://zhaochexia-**********.cos.ap-guangzhou.myqcloud.com/wechat/assets/icons/engine.png"
              mode="aspectFit"
            ></image>
            <view class="info-content">
              <view class="info-label">排量</view>
              <view class="info-value">{{engine}}</view>
            </view>
          </view>
          <view class="param-item">
            <image
              class="info-icon"
              src="https://zhaochexia-**********.cos.ap-guangzhou.myqcloud.com/wechat/assets/icons/transmission.png"
              mode="aspectFit"
            ></image>
            <view class="info-content">
              <view class="info-label">变速箱</view>
              <view class="info-value">{{transmission}}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 检测报告卡片 -->
      <view class="info-card">
        <view class="section-title">检测报告</view>

        <!-- 维保记录 -->
        <view class="report-item">
          <view class="report-left">
            <image
              class="report-icon"
              src="https://zhaochexia-**********.cos.ap-guangzhou.myqcloud.com/wechat/assets/icons/document.png"
              mode="aspectFit"
            ></image>
            <view class="report-text">维保记录</view>
          </view>
          <view
            class="report-link"
            bindtap="viewMaintenanceRecord"
          >点击查看</view>
        </view>

        <!-- 出险记录 -->
        <view class="report-item">
          <view class="report-left">
            <image
              class="report-icon"
              src="https://zhaochexia-**********.cos.ap-guangzhou.myqcloud.com/wechat/assets/icons/document.png"
              mode="aspectFit"
            ></image>
            <view class="report-text">出险记录</view>
          </view>
          <view
            class="report-link"
            bindtap="viewInsuranceRecord"
          >点击查看</view>
        </view>

        <!-- 第三方检测报告 -->
        <view class="report-item">
          <view class="report-left">
            <image
              class="report-icon"
              src="https://zhaochexia-**********.cos.ap-guangzhou.myqcloud.com/wechat/assets/icons/document.png"
              mode="aspectFit"
            ></image>
            <view class="report-text">第三方检测报告</view>
          </view>
          <view
            class="report-link"
            bindtap="viewThirdPartyReport"
          >点击查看</view>
        </view>
      </view>
    </view>

    <!-- 商家信息卡片 -->
    <view class="car-info-container">
      <view
        class="info-card merchant-card"
        bindtap="viewMerchantDetail"
      >
        <view class="section-title">商家信息</view>
        <view class="merchant-info">
          <view class="merchant-logo">
            <image
              src="{{merchantInfo.logo || 'https://zhaochexia-**********.cos.ap-guangzhou.myqcloud.com/uploads/proof/1751273082786_745.png'}}"
              mode="aspectFit"
            ></image>
          </view>
          <view class="merchant-details">
            <view class="merchant-name">{{merchantInfo.company_name || '暂无商家信息'}}</view>
            <view class="merchant-address">地址: {{merchantInfo.address || '暂无地址信息'}}</view>
            <view class="merchant-stats">
              <text class="merchant-rating">⭐ {{merchantInfo.score || '4.9'}}</text>
              <text class="merchant-stock">在售 {{merchantInfo.car_count || 0}} 辆</text>
            </view>
          </view>
          <view class="merchant-contact">
            <button class="verify-btn">进入店铺</button>
          </view>
        </view>
      </view>
    </view>
  </block>

  <!-- 车辆推荐区域 -->
  <view
    class="recommend-section"
    wx:if="{{recommendCars.length > 0}}"
  >
    <view class="recommend-header">
      <text>猜你喜欢</text>
      <view
        class="more-cars"
        bindtap="onMoreCarsTap"
      >查看更多 ></view>
    </view>
    <view class="recommend-grid">
      <!-- 推荐车辆卡片 -->
      <view
        class="recommend-card"
        wx:for="{{recommendCars}}"
        wx:key="id"
        bindtap="onCarTap"
        data-id="{{item.id}}"
      >
        <!-- 卡片主体 -->
        <view class="recommend-card-body">
          <!-- 车辆图片 -->
          <image
            class="recommend-car-image"
            src="{{item.image}}"
            mode="aspectFill"
          ></image>

          <!-- 热销/新到标签 -->
          <view class="recommend-tag-label {{index % 2 === 0 ? 'hot' : 'new'}}">
            {{index % 2 === 0 ? '热销' : '新到'}}
          </view>

          <!-- 车辆标题 -->
          <view class="recommend-car-title">
            {{item.title}}
          </view>

          <!-- 车辆参数 -->
          <view class="recommend-car-params">
            <text>{{item.year}}年 | {{item.mileage}}万公里</text>
          </view>

          <!-- 价格信息 -->
          <view class="recommend-price-info">
            <text class="recommend-new-price">新车指导价{{item.originalPrice}}万元</text>
            <text class="recommend-sale-price">{{item.price}}万元</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部固定操作栏 -->
  <view class="bottom-action-bar">
    <view class="action-buttons-container">
      <view
        class="action-btn-contact"
        bindtap="contactSeller"
      >
        联系商家
      </view>
      <view
        class="action-btn-manual"
        bindtap="downloadManual"
      >
        下载车辆手册
      </view>
    </view>
    <view
      class="action-btn-collect"
      bindtap="toggleCollect"
    >
      <image
        class="collect-icon"
        src="{{isCollected ? 'https://zhaochexia-**********.cos.ap-guangzhou.myqcloud.com/uploads/proof/1751273929799_524.svg' : 'https://zhaochexia-**********.cos.ap-guangzhou.myqcloud.com/uploads/proof/1751273844218_454.svg'}}"
      ></image>
      <text>收藏</text>
    </view>
  </view>
</view>