<!--components/language-switcher/language-switcher.wxml-->
<view class="lang-switcher {{position}} {{size}}" catchtap="stopPropagation" >
  <view class="lang-trigger" bindtap="toggleDropdown" wx:if="{{showLangList}}">
    <text class="lang-flag">{{currentLangInfo.flag}}</text>
    <text class="lang-name">{{currentLangInfo.name}}</text>
    <view class="dropdown-arrow {{showDropdown ? 'up' : 'down' }}">▼</view>
  </view>

  <view class="dropdown-menu {{showDropdown ? 'show' : ''}}">
    <view 
      wx:for="{{langList}}"
      wx:key="code"
      class="dropdown-item {{currentLang == item.code ? 'active' : ''}}"
      bindtap="selectLang"
      data-lang="{{item.code}}"
    >
      <text class="lang-flag">{{item.flag}}</text>
      <text class="lang-name">{{item.name}}</text>
      <text wx:if="{{ currentLang == item.code }}" class="lang-check">✓</text>
    </view>
  </view>
</view>