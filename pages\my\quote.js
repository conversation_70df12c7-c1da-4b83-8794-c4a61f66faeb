Page({
  data: {
    currentStep: 1, // 当前步骤
    steps: ['车辆采购', '国内运费和车务', '通道', '出口', '垫资账税'],
    customer: {
      name: '李华',
      phone: '13758456257'
    },
    staff: {
      name: '王科',
      phone: '13746546456'
    },
    date: '2025-4-2',
    car: {
      name: '请选择车型'
    },
    // 能源类型和汇率字段
    energyTypes: ['油车', '电车'], // 能源类型选项
    energyTypeIndex: 0, // 默认选择油车
    energyType: '', // 能源类型
    exchangeRate: '', // 美元汇率

    // 车辆采购表单字段
    appearance: '', // 外观
    interior: '', // 内饰
    vehiclePrice: '', // 车辆指导价
    priceRange: '', // 车价下浮
    deletionFee: '', // 刷机费
    depositRatio: '', // 预付比例

    // 国内运费字段
    pickupFee: '', // 采购运费
    portFee: '', // 上牌点市内运费
    customsFee: '', // 口岸运费

    // 车务服务字段
    licenseFee: '', // 上牌费用
    insurance: '', // 保险费用
    transfer: '', // 过户费用
    mortgage: '', // 背户

    // 通道字段
    exportPermit: '', // 出口许可
    originCert: '', // 原产地证
    channelFee: '', // 退税通道
    registration: '', // 注销
    inspection: '', // 检验报告

    // 出口字段
    portCharges: '', // 港杂费
    packingFee: '', // 打包证明
    declarationFee: '', // 报关费
    exportGoods: '', // 出口物流
    exportInsurance: '', // 出口保险

    // 垫资账税字段
    fundingRatio: '', // 垫资比例
    taxRate: '', // 垫资利息
    fundingDays: '', // 垫资时间
    taxDays: '', // 垫税时间
    remarks: '', // 备注

    // 计算结果
    results: {
      // 车辆采购相关
      cg_zdj: 0,    // 厂家指导价
      cg_cjxf: 0,   // 市场下浮
      cg_fpj: 0,    // 发票价
      cg_lcj: 0,    // 裸车价
      cg_gzs: 0,    // 购置税
      cg_sj: 0,     // 刷机费
      cb_cg: 0,     // 采购成本
      cb_cgyfk: 0,  // 采购预付款
      cb_cgdk: 0,   // 采购垫款

      // 运费相关
      yf_cg: 0,     // 采购运费
      yf_sn: 0,     // 市内运费
      yf_ka: 0,     // 口岸运费
      cb_yf: 0,     // 运费合计

      // 车务相关
      cw_sp: 0,     // 上牌费
      cw_bx: 0,     // 保险费
      cw_gh: 0,     // 过户费
      cw_bh: 0,     // 背户费
      cb_bh: 0,     // 背户费用合计

      // 通道相关
      td_xk: 0,     // 出口许可
      td_ycdz: 0,   // 原产地证
      td_ts: 0,     // 退税通道
      td_zx: 0,     // 注销
      td_jcbg: 0,   // 检测报告
      cb_td: 0,     // 通道费用合计

      // 出口相关
      ck_gzf: 0,    // 港杂费
      ck_dbzm: 0,   // 打包证明
      ck_bgf: 0,    // 报关费
      ck_ckwl: 0,   // 出口物流
      ck_ckbx: 0,   // 出口保险
      cb_ck: 0,     // 出口费用合计

      // 费用小计
      cb_xj: 0,     // 小计
      cb_gn: 0,     // 国内总杂费

      // 总成本相关
      zcb: 0,       // 总成本
      zcb_shj: 0,   // 税后价
      zcb_myshj: 0, // 美元税后价
      zcb_ts: 0,    // 退税金额

      // 垫资相关
      dk_bl: 0,     // 垫资比例
      dk_lx: 0,     // 垫资利息
      dk_cgsj: 0,   // 垫资时间
      dk_tssj: 0,   // 垫税时间
      dk_ts: 0,     // 垫税成本
      dk_cg: 0,     // 采购垫款成本
      zcb_dk: 0,    // 垫资总成本
      zcb_mydk: 0   // 美元垫资成本
    },

    price: '9.78万',
    exterior: '蓝',
    quantity: 1,
    freight: 'xxx',
    otherFees: 'xxx',
    subtotal: 'xxx',
    vehicleFee: '',
    vehicleLargeAmount: '',
    statusBarHeight: 0,
    exportFee: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });
  },

  onShow: function () {
    // 页面显示时，获取当前页面数据
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]

    // 如果有选中的客户数据，则更新
    if (currentPage.data.customer && currentPage.data.customer.name) {
      this.setData({
        customer: currentPage.data.customer
      })
    }

    // 如果有选中的报价人数据，则更新
    if (currentPage.data.staff && currentPage.data.staff.name) {
      this.setData({
        staff: currentPage.data.staff
      })
    }
  },

  /**
   * 导航到其他页面
   */
  navigateTo: function (e) {
    const url = e.currentTarget.dataset.url;
    wx.navigateTo({
      url: url
    });
  },

  /**
   * 日期选择器改变事件
   */
  bindDateChange: function (e) {
    this.setData({
      date: e.detail.value
    });
  },

  /**
   * 表单输入事件
   */
  inputChange: function (e) {
    const field = e.currentTarget.dataset.field;
    let value = e.detail.value;

    // 不再在输入时进行格式化，允许自由编辑包括删除
    const data = {};
    data[field] = value;

    this.setData(data);
  },

  /**
   * 输入框失去焦点时的处理
   */
  inputBlur: function (e) {
    const field = e.currentTarget.dataset.field;
    let value = e.detail.value;

    // 只在有值且为数字字段时进行格式化
    if (this.isNumberField(field) && value !== '') {
      value = this.formatInputNumber(value);

      // 更新格式化后的值
      const data = {};
      data[field] = value;
      this.setData(data);
    }
  },

  /**
   * 判断是否为数字字段
   */
  isNumberField: function (field) {
    // 所有需要数字格式化的字段名列表
    const numberFields = [
      'exchangeRate', 'vehiclePrice', 'priceRange', 'deletionFee',
      'depositRatio', 'pickupFee', 'portFee', 'customsFee',
      'licenseFee', 'insurance', 'transfer', 'mortgage',
      'exportPermit', 'originCert', 'channelFee', 'registration',
      'inspection', 'portCharges', 'packingFee', 'declarationFee',
      'exportGoods', 'exportInsurance', 'fundingRatio', 'taxRate',
      'fundingDays', 'taxDays'
    ];
    return numberFields.includes(field);
  },

  /**
   * 格式化输入的数字
   */
  formatInputNumber: function (value) {
    // 特殊情况处理：检查是否为不完整的小数格式（如"."或末尾有小数点的数字）
    if (value === '.' || value === '0.' || /^\d+\.$/.test(value)) {
      return value; // 直接返回原值，保留小数点
    }

    const num = Number(value);
    if (isNaN(num)) return value;

    // 检查是否为整数
    if (Number.isInteger(num)) {
      return num.toString();
    } else {
      // 如果是小数，保留两位小数
      return num.toFixed(6);
    }
  },

  /**
   * 直接切换到指定步骤
   */
  switchStep: function (e) {
    const step = parseInt(e.currentTarget.dataset.step);
    this.setData({
      currentStep: step
    });
  },

  /**
   * 下一步
   */
  nextStep: function () {
    let { currentStep } = this.data;

    // 在步骤1时，验证基本信息
    if (currentStep === 1) {
      if (!this.data.customer.name) {
        wx.showToast({
          title: '请选择客户',
          icon: 'none'
        });
        return;
      }

      if (!this.data.staff.name) {
        wx.showToast({
          title: '请选择报价人',
          icon: 'none'
        });
        return;
      }

      if (!this.data.car.name) {
        wx.showToast({
          title: '请选择车辆',
          icon: 'none'
        });
        return;
      }
    }

    if (currentStep < 5) {
      this.setData({
        currentStep: currentStep + 1
      });
    }
  },

  /**
   * 上一步
   */
  prevStep: function () {
    let { currentStep } = this.data;
    if (currentStep > 1) {
      this.setData({
        currentStep: currentStep - 1
      });
    }
  },

  /**
   * 计算所有结果
   */
  calculateResults: function () {
    const results = this.data.results;
    const data = this.data;

    // 格式化数值：仅转换为数字类型，不做任何舍入处理，保持原始输入值
    const formatNumber = (num) => {
      return Number(num);
    };

    // 保存输入值到结果中，并格式化输入值
    results.cg_zdj = formatNumber(data.vehiclePrice);
    results.cg_cjxf = formatNumber(data.priceRange);
    results.cg_sj = formatNumber(data.deletionFee);
    results.yf_cg = formatNumber(data.pickupFee);
    results.yf_sn = formatNumber(data.portFee);
    results.yf_ka = formatNumber(data.customsFee);
    results.cw_sp = formatNumber(data.licenseFee);
    results.cw_bx = formatNumber(data.insurance);
    results.cw_gh = formatNumber(data.transfer);
    results.cw_bh = formatNumber(data.mortgage);
    results.td_xk = formatNumber(data.exportPermit);
    results.td_ycdz = formatNumber(data.originCert);
    results.td_ts = formatNumber(data.channelFee);
    results.td_zx = formatNumber(data.registration);
    results.td_jcbg = formatNumber(data.inspection);
    results.ck_gzf = formatNumber(data.portCharges);
    results.ck_dbzm = formatNumber(data.packingFee);
    results.ck_bgf = formatNumber(data.declarationFee);
    results.ck_ckwl = formatNumber(data.exportGoods);
    results.ck_ckbx = formatNumber(data.exportInsurance);
    results.dk_bl = formatNumber(data.fundingRatio);
    results.dk_lx = formatNumber(data.taxRate);
    results.dk_cgsj = formatNumber(data.fundingDays);
    results.dk_tssj = formatNumber(data.taxDays);

    // 发票价 = 指导价 - 下浮
    results.cg_fpj = formatNumber(results.cg_zdj - results.cg_cjxf);

    // 裸车价 = 发票价 / 1.13
    results.cg_lcj = formatNumber(results.cg_fpj / 1.13);

    // 购置税计算
    results.cg_gzs = formatNumber(results.cg_lcj * 0.1);
    // 电车购置税优惠
    if (data.energyType === '电车') {
      if (results.cg_gzs < 30000) {
        results.cg_gzs = 0;
      } else {
        results.cg_gzs = formatNumber(results.cg_gzs - 30000);
      }
    }

    // 采购成本相关计算
    results.cb_cg = formatNumber(results.cg_fpj + results.cg_gzs * 1.15);
    results.cb_cgyfk = formatNumber(results.cb_cg * results.dk_bl);
    results.cb_cgdk = formatNumber(results.cb_cg - results.cb_cgyfk);

    // 费用合计计算
    results.cb_yf = formatNumber(results.yf_cg + results.yf_sn + results.yf_ka);
    results.cb_bh = formatNumber(results.cw_sp + results.cw_bx + results.cw_gh + results.cw_bh);
    results.cb_td = formatNumber(results.td_xk + results.td_ycdz + results.td_ts + results.td_zx + results.td_jcbg);
    results.cb_xj = formatNumber(results.cb_yf + results.cb_bh + results.cb_td);
    results.cb_gn = formatNumber(results.cb_xj * (1 + 1.15 * 0.15));

    // 出口费用计算
    results.cb_ck = formatNumber(results.ck_gzf + results.ck_dbzm + results.ck_bgf + results.ck_ckwl + results.ck_ckbx);

    // 总成本计算
    results.zcb = formatNumber(results.cb_cg + results.cb_gn + results.cb_ck);
    results.zcb_shj = formatNumber(results.zcb / 1.13);
    results.zcb_myshj = formatNumber(results.zcb_shj / data.exchangeRate);
    results.zcb_ts = formatNumber(results.zcb - results.zcb_shj);

    // 垫资成本计算
    results.dk_ts = formatNumber(results.zcb_ts * results.dk_lx * results.dk_tssj);
    results.dk_cg = formatNumber(results.cb_cgdk * results.dk_lx * results.dk_cgsj);
    results.zcb_dk = formatNumber(results.dk_ts + results.dk_cg);
    results.zcb_mydk = formatNumber(results.zcb_dk / data.exchangeRate);


    console.log('results>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>!!!!!!', results);
    // 更新结果
    this.setData({
      results: results
    });

    // 打印结果到控制台
    // console.log('计算结果:', results);

    return results;
  },

  /**
   * 提交表单
   */
  submitForm: function () {
    // 验证是否选择了车辆
    if (!this.data.car.name) {
      wx.showToast({
        title: '请选择车辆',
        icon: 'none'
      });
      return;
    }

    // 验证必填字段
    const requiredFields = {
      // 步骤1：车辆采购
      exchangeRate: '美元汇率',
      appearance: '外观颜色',
      interior: '内饰颜色',
      vehiclePrice: '车辆指导价',
      priceRange: '车价下浮',
      deletionFee: '刷机费',
      depositRatio: '预付比例',

      // 步骤2：国内运费和车务
      pickupFee: '采购运费',
      portFee: '上牌运费',
      customsFee: '口岸运费',
      licenseFee: '上牌费用',
      insurance: '保险费用',
      transfer: '过户费用',

      // 步骤3：通道
      exportPermit: '出口许可',
      originCert: '原产地证',
      channelFee: '退税通道',
      registration: '注销',
      inspection: '检验报告',

      // 步骤4：出口
      portCharges: '港杂费',
      packingFee: '打包证明',
      declarationFee: '报关费',
      exportGoods: '出口物流',
      exportInsurance: '出口保险',

      // 步骤5：垫资账税
      fundingRatio: '垫资比例',
      taxRate: '垫资利息',
      fundingDays: '垫资时间',
      taxDays: '垫税时间'
    };

    // 检查必填字段
    for (const field in requiredFields) {
      if (!this.data[field]) {
        wx.showToast({
          title: `请填写${requiredFields[field]}`,
          icon: 'none'
        });

        // 确定该字段在哪个步骤
        let stepForField = 1;
        if (field === 'exchangeRate' || field.match(/^(appearance|interior|vehiclePrice|priceRange|deletionFee|depositRatio)$/)) {
          stepForField = 1;
        } else if (field.match(/^(pickupFee|portFee|customsFee|licenseFee|insurance|transfer)$/)) {
          stepForField = 2;
        } else if (field.match(/^(exportPermit|originCert|channelFee|registration|inspection)$/)) {
          stepForField = 3;
        } else if (field.match(/^(portCharges|packingFee|declarationFee|exportGoods|exportInsurance)$/)) {
          stepForField = 4;
        } else if (field.match(/^(fundingRatio|taxRate|fundingDays|taxDays)$/)) {
          stepForField = 5;
        }

        // 如果当前不在该字段所在的步骤，则导航到对应步骤
        if (this.data.currentStep !== stepForField) {
          this.setData({
            currentStep: stepForField
          });
        }

        return;
      }
    }

    // 计算结果
    const calculatedResults = this.calculateResults();

    // 创建包含车型名称和计算结果的JSON
    const finalResult = {
      carName: this.data.car.name,
      appearance: this.data.appearance,
      interior: this.data.interior,
      energyType: this.data.energyType,
      ...calculatedResults
    };

    // 打印最终结果到控制台
    // console.log('最终报价结果:', JSON.stringify(finalResult, null, 2));

    // 提交数据到服务器
    wx.showLoading({
      title: '生成中...',
    });

    // 引入API和util
    const api = require('../../utils/api').default;
    const util = require('../../utils/util');

    // 准备提交数据
    const submitData = {
      app_id: util.getAppId(), // 固定参数
      level: util.getLevel(), // 固定参数
      content: JSON.stringify(finalResult) // 将finalResult转换为字符串
    };

    // 调用接口提交数据
    api.quote.addQuote(submitData).then(res => {
      wx.hideLoading(); // 先隐藏加载提示
      // console.log('报价单提交成功，返回数据:', res); // 添加日志以查看实际返回数据

      // 检查返回的code，处理失败情况
      if (res && res.code === 1) {
        // 处理失败情况，显示错误信息
        wx.showToast({
          title: res.msg || '提交失败',
          icon: 'none',
          duration: 2000
        });
        return; // 直接返回，不进行后续跳转
      }

      try {
        // 直接提取ID - 处理简单的 {id: "17"} 结构
        let quoteId = null;

        // 处理不同的返回格式
        if (res && res.id) {
          // 直接返回 {id: "17"} 的情况
          quoteId = res.id;
        } else if (res && res.code === 0 && res.data) {
          // 标准返回格式 {code: 0, data: {id: "17"}, msg: "添加成功"}
          if (typeof res.data === 'object' && res.data.id) {
            quoteId = res.data.id;
          } else if (typeof res.data === 'string' || typeof res.data === 'number') {
            quoteId = res.data.toString();
          }
        }

        if (quoteId) {
          // 获取到ID，显示成功提示
          wx.showToast({
            title: '报价单生成成功',
            icon: 'success',
            duration: 1500,
            mask: true
          });

          // console.log('准备跳转到报价单详情，ID:', quoteId);

          // 延迟执行跳转，确保Toast显示完成
          setTimeout(() => {
            wx.navigateTo({
              url: '/pages/my/quote_record?id=' + quoteId,
              success: function () {
                console.log('成功跳转到详情页');
              },
              fail: function (err) {
                console.error('跳转到详情页失败:', err);

                // 保存ID到本地存储
                wx.setStorageSync('last_quote_id', quoteId);

                // 跳转失败时尝试返回到我的页面
                wx.switchTab({
                  url: '/pages/my/index'
                });
              }
            });
          }, 1000);
        } else {
          // 没有获取到ID
          console.error('未能从响应中获取ID:', res);
          wx.showToast({
            title: '提交成功，但获取报价单ID失败',
            icon: 'none',
            duration: 2000
          });

          // 没有ID也跳转回我的页面
          setTimeout(() => {
            wx.switchTab({
              url: '/pages/my/index'
            });
          }, 1500);
        }
      } catch (error) {
        console.error('处理响应数据出错:', error);
        wx.showToast({
          title: '提交处理失败',
          icon: 'none',
          duration: 2000
        });

        // 发生错误时也返回我的页面
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/my/index'
          });
        }, 1500);
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('报价单提交失败:', err);

      wx.showToast({
        title: '网络请求失败，请重试',
        icon: 'none',
        duration: 2000
      });

      // 网络请求失败时，延迟返回首页
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/my/index'
        });
      }, 1500);
    });
  },

  // 返回按钮点击事件
  navigateBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  // 能源类型选择器改变事件
  energyTypeChange(e) {
    this.setData({
      energyTypeIndex: e.detail.value,
      energyType: this.data.energyTypes[e.detail.value]
    });
  }
})
