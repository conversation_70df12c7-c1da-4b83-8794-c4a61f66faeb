<view
  class="login-mask"
  wx:if="{{showMask}}"
></view>
<language-switcher position="top-left" showLangList="{{true}}" size="normal" />
<view
  class="container"
  style="--status-bar-height: {{statusBarHeight}}px;"
>
  <!-- 自定义导航栏 -->
  <view
    class="custom-nav"
    style="padding-top: {{statusBarHeight}}px;"
  >
    <view class="nav-content">
      <view
        class="back-icon"
        bindtap="navigateBack"
      >
        <van-icon
          name="arrow-left"
          size="20px"
          color="#333"
        />
      </view>
      <view class="nav-title">{{text.zhaochexia}}</view>
    </view>
  </view>

  <!-- 登录内容区域 -->
  <view
    class="main-content"
    style="padding-top: {{statusBarHeight + 44}}px;"
  >
    <!-- 用户头像区域 -->
    <view class="avatar-container">
      <image
        class="avatar"
        src="https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/uploads/proof/1751274246972_981.png"
        mode="aspectFill"
      ></image>
    </view>

    <!-- 提示文字 -->
    <view class="login-tip">{{text.please_log_in}}</view>

    <!-- 登录按钮 -->
    <button
      class="login-btn"
      open-type="{{isAgree && hasOpenid ? 'getPhoneNumber' : ''}}"
      bindgetphonenumber="getPhoneNumber"
      bindtap="preCheckLogin"
      loading="{{loading}}"
    >
      <text>{{text.quick_login}}</text>
    </button>

    <!-- 账号密码登录按钮 -->
    <button
      class="account-login-btn"
      bindtap="navigateToAccountLogin"
    >
      <text>{{text.login_with_password}}</text>
    </button>

    <!-- 协议区域 -->
    <view class="agreement-container">
      <checkbox
        class="checkbox"
        checked="{{isAgree}}"
        bindtap="toggleAgree"
      ></checkbox>
      <view class="agreement-text">
        {{text.i_agree_to_abide_by}}<text
          class="link"
          bindtap="viewAgreement"
          data-type="service"
        >《{{text.user_service_agreement}}》</text>、<text
          class="link"
          bindtap="viewAgreement"
          data-type="privacy"
        >《{{text.privacy_policy}}》</text>
      </view>
    </view>
  </view>
</view>