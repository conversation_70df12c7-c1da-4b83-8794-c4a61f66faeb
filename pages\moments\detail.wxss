/* 引入登录弹窗样式 */
@import "/templates/loginPopup/loginPopup.wxss";

/* 整体容器样式 */
.detail-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx; /* 为底部评论栏留出空间 */
}

/* 自定义导航栏样式 */
.custom-nav {
  width: 100%;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}

.status-bar {
  width: 100%;
}

.nav-content {
  height: 44px;
  display: flex;
  align-items: center;
  position: relative;
}

.back-icon {
  position: absolute;
  left: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx;
}

.back-icon image {
  width: 40rpx;
  height: 40rpx;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

/* 导航栏占位符 */
.nav-placeholder {
  width: 100%;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100rpx;
  margin-top: 100rpx; /* 减小上边距，因为已有导航栏占位 */
}

.loading-text {
  font-size: 28rpx;
  color: #999;
  margin-top: 20rpx;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.error-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.error-action {
  font-size: 28rpx;
  color: #ffc62c;
  padding: 10rpx 30rpx;
  border: 1px solid #ffc62c;
  border-radius: 30rpx;
}

/* 帖子详情 */
.post-detail {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.05);
}

/* 用户信息 */
.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.user-avatar {
  width: 84rpx;
  height: 84rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  background-color: #eee;
  border: 1rpx solid #eaeaea;
}

.user-meta {
  flex: 1;
}

.user-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 6rpx;
}

.post-time {
  font-size: 24rpx;
  color: #999;
}

/* 浏览次数 */
.views-item {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999;
  margin: 15rpx 0;
}

.views-item image {
  width: 28rpx;
  height: 28rpx;
  margin-right: 6rpx;
}

/* 正文内容 */
.post-content {
  margin-bottom: 24rpx;
}

.post-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 14rpx;
}

.subtitle{
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle-row {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 10rpx;
}

.subtitle-row .subtitle {
  margin-right: 30rpx;
  flex: 1;
}

.post-desc {
  font-size: 32rpx;
  color: #333;
  line-height: 1.7;
  margin-bottom: 24rpx;
  word-break: break-all;
}

/* 图片样式 */
.post-images {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 24rpx;
}

.image-item {
  width: calc((100% - 20rpx) / 3);
  height: 220rpx;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
  border-radius: 8rpx;
  overflow: hidden;
  position: relative;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08);
}

.image-item:nth-child(3n) {
  margin-right: 0;
}

.image-item.single-image {
  width: 75%;
  height: 420rpx;
  border-radius: 12rpx;
}

.image-item image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.image-item:active image {
  transform: scale(1.03);
}

/* 高亮显示当前选中的图片 */
.image-item.selected {
  border: 3rpx solid #ffc62c;
}

/* 标签和互动区域 */
.post-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #eee;
}

.post-tags {
  display: flex;
  flex-wrap: wrap;
}

.tag {
  font-size: 24rpx;
  color: #666;
  margin-right: 15rpx;
  padding: 8rpx 16rpx;
  background-color: #f5f5f5;
  border-radius: 6rpx;
  margin-bottom: 10rpx;
  border: 1rpx solid #eee;
}

/* 内容与评论之间的分隔区域 */
.content-separator {
  height: 20rpx;
  background-color: #f7f7f7;
  margin: 0 -30rpx; /* 使分隔线延伸到容器边缘 */
  border-top: 1rpx solid #eee;
  border-bottom: 1rpx solid #eee;
}

/* 评论总数 */
.comment-count {
  padding: 22rpx 0;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  border-bottom: 1rpx solid #eee;
}

/* 评论列表 */
.comment-list {
  padding: 20rpx 0;
}

.comment-item {
  margin-bottom: 30rpx;
  position: relative;
  border-bottom: 1rpx solid #f0f0f0;
  padding-bottom: 30rpx;
}

.comment-item:last-child {
  margin-bottom: 10rpx;
  border-bottom: none;
}

/* 评论用户信息 */
.comment-user {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.comment-avatar {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  margin-right: 15rpx;
  background-color: #eee;
}

.comment-meta {
  flex: 1;
}

.comment-username {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
}

.comment-time {
  font-size: 24rpx;
  color: #999;
}

/* 评论内容 */
.comment-content {
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 20rpx;
  word-break: break-all;
  margin-left: 85rpx; /* 改为margin-left与回复区域保持一致 */
  width: calc(100% - 105rpx); /* 与回复区域一致的宽度设置 */
  box-sizing: border-box;
}

/* 点赞和评论按钮容器 */
.action-buttons-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 15rpx;
}

.like-btn, .comment-btn {
  display: flex;
  align-items: center;
  margin-left: 30rpx;
}

.like-btn image, .comment-btn image {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}

.like-btn text, .comment-btn text {
  font-size: 24rpx;
  color: #999;
}

.like-btn.liked text {
  color: #ffc62c;
}

/* 新的对话框样式 */
.conversation-box {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #f8f8f8; /* 调整为更浅的灰色背景 */
  border-radius: 12rpx; /* 圆角值 */
  margin-bottom: 15rpx;
  margin-left: 85rpx; /* 保持左对齐 */
  width: calc(100% - 105rpx); /* 调整宽度，确保有足够的右侧间距 */
  box-sizing: border-box;
  box-shadow: none; /* 移除阴影，保持扁平设计 */
}

.reply-item {
  margin-bottom: 10rpx;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.reply-item:last-child {
  margin-bottom: 0;
}

.reply-line {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.reply-username {
  color: #576b95;
  font-weight: 500;
  margin-right: 0;
}

.reply-target {
  color: #576b95;
  font-weight: 500;
  margin: 0 5rpx;
}

/* 无评论提示 */
.no-comment {
  padding: 40rpx 0;
  text-align: center;
  font-size: 28rpx;
  color: #999;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 20rpx 0;
  color: #ffc62c;
  font-size: 26rpx;
}

/* 底部评论栏 */
.comment-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top: 1rpx solid #eee;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.comment-input-container {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 36rpx;
  padding: 0 20rpx;
  height: 72rpx;
  margin-right: 20rpx;
  border: 1rpx solid #eaeaea;
}

.comment-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.comment-input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
}

.cancel-reply {
  font-size: 24rpx;
  color: #ffc62c;
  margin-left: 10rpx;
  padding: 0 10rpx;
}

.action-buttons {
  display: flex;
  align-items: center;
}

.action-button {
  display: flex;
  align-items: center;
  margin-left: 25rpx;
}

.action-button.liked {
  color: #ffc62c;
}

.action-button image {
  width: 40rpx;
  height: 40rpx;
}

.action-button text {
  font-size: 24rpx;
  color: #666;
  margin-left: 6rpx;
}

/* .action-button.liked text {
  color: #ffc62c;
} */

/* 高亮选中的边框 */
.highlight-border {
  border: 3rpx solid #ffc62c;
}

/* 评论底部区域（时间和回复按钮） */
.comment-footer {
  display: flex;
  align-items: center;
  padding-left: 79rpx;
  margin-top: 8rpx;
}

.comment-footer .comment-time {
  font-size: 22rpx;
  color: #999;
  margin-right: 20rpx;
}

.comment-footer .comment-action {
  font-size: 24rpx;
  color: #576b95;
  margin-left: 0;
}

/* 回复底部样式调整 */
.reply-item .comment-footer {
  padding-left: 63rpx;
  margin-top: 6rpx;
}

/* 调整评论内容样式，移除旧的左边距 */
.comment-content {
  padding-left: 0;
}

/* 分享按钮样式 */
.share-button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  margin-left: 25rpx;
  line-height: normal;
  border-radius: 0;
  outline: none;
  box-sizing: content-box;
}

.share-button::after {
  display: none;
} 