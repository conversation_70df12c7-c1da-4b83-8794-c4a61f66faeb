/* 页面整体样式 */
.favorite-page {
  min-height: 100vh;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* 自定义导航栏样式 */
.custom-nav {
  width: 100%;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}

.status-bar {
  width: 100%;
}

.nav-content {
  height: 44px;
  display: flex;
  align-items: center;
  position: relative;
}

.back-icon {
  position: absolute;
  left: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx;
}

.back-icon image {
  width: 40rpx;
  height: 40rpx;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.edit-btn {
  position: absolute;
  right: 30rpx;
  font-size: 28rpx;
  padding: 10rpx;
  display: flex;
  align-items: center;
  z-index: 10;
}

.edit-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

/* 车辆列表容器 */
.car-list-container {
  padding-top: 180rpx;
  flex: 1;
  width: 100%;
}

.car-list {
  padding: 24rpx;
  margin-top: 50rpx;
}

/* 车辆项样式 */
.car-item {
  position: relative;
  display: flex;
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  padding: 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.car-item.edit-mode {
  padding-left: 80rpx;
}

/* 选择框样式 */
.select-checkbox {
  position: absolute;
  left: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  transition: opacity 0.3s;
  z-index: 5;
}

.select-checkbox.show {
  opacity: 1;
}

.checkbox {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 2rpx solid #D0D0D0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
}

.checkbox.checked {
  background-color: #4285F4;
  border-color: #4285F4;
}

/* 车辆图片容器 */
.car-image-container {
  position: relative;
  width: 210rpx;
  height: 160rpx;
  border-radius: 8rpx;
  overflow: hidden;
  flex-shrink: 0;
}

.car-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.status-badge {
  position: absolute;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 10rpx;
  border-bottom-right-radius: 8rpx;
}

/* 收藏图标 */
.favorite-icon {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 36rpx;
  height: 36rpx;
  z-index: 2;
}

.favorite-icon image {
  width: 100%;
  height: 100%;
}

/* 车辆信息 */
.car-info {
  flex: 1;
  margin-left: 24rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
}

.car-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #3D3D3D;
  margin-bottom: 12rpx;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.car-details {
  display: flex;
  font-size: 20rpx;
  color: #ABB4BF;
  margin-bottom: 16rpx;
  align-items: center;
}

.car-year {
  margin-right: 20rpx;
  position: relative;
}

.car-year::after {
  content: '';
  position: absolute;
  right: -10rpx;
  top: 50%;
  width: 1px;
  height: 14rpx;
  background-color: #e0e0e0;
  transform: translateY(-50%);
}

.car-mileage {
  position: relative;
}

.car-price {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 10rpx;
}

.price-value {
  font-size: 24rpx;
  font-weight: bold;
  color: #2563EB;
  margin-right: 12rpx;
}

.origin-price {
  font-size: 20rpx;
  color: #ABB4BF;
  text-decoration: line-through;
}

/* 选择框 */
.select-box {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  opacity: 0;
  transition: opacity 0.3s;
}

.select-box.show {
  opacity: 1;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 186rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  transform: translateY(100%);
  transition: transform 0.3s;
  z-index: 99;
  padding-bottom: 50rpx;
}

.bottom-bar.show {
  transform: translateY(0);
}

.select-all {
  display: flex;
  align-items: center;
}

.select-all text {
  font-size: 28rpx;
  color: #333;
  margin-left: 12rpx;
}

.delete-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #3B82F6;
  color: #fff;
  font-size: 28rpx;
  padding: 12rpx 30rpx;
  border-radius: 8rpx;
  width: 324rpx;
  height: 48rpx;
}

.delete-count {
  margin-left: 4rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 160rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.browse-btn {
  background-color: #3478f6;
  color: #fff;
  font-size: 28rpx;
  padding: 16rpx 40rpx;
  border-radius: 8rpx;
}

/* 加载更多样式 */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
  color: #999;
  font-size: 24rpx;
}

.loading-indicator {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
  border: 3rpx solid #f3f3f3;
  border-top: 3rpx solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 没有更多数据样式 */
.no-more-data {
  text-align: center;
  padding: 30rpx 0;
  color: #999;
  font-size: 24rpx;
}
