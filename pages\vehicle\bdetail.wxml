<view class="container">
  <!-- 加载中状态 -->
  <view
    class="loading-container"
    wx:if="{{isLoading}}"
  >
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 头部区域占位符 - 防止内容被固定顶部遮挡 -->
  <view class="header-placeholder"></view>

  <!-- 固定头部区域 -->
  <view class="fixed-header">
    <!-- 顶部状态栏 -->
    <view
      class="status-bar"
      style="height: {{statusBarHeight}}px;"
    ></view>

    <!-- 导航栏 -->
    <view class="nav-bar">
      <view
        class="nav-back"
        bindtap="goBack"
      >
        <van-icon
          name="arrow-left"
          size="20px"
          color="#333"
        />
      </view>
      <view class="nav-title">批量车详情</view>
      <view class="nav-placeholder"></view>
    </view>
  </view>

  <!-- 内容区域 - 仅在数据加载完成后显示 -->
  <block wx:if="{{!isLoading && vehicleDetail}}">
    <!-- 轮播图部分 -->
    <view class="swiper-container">
      <swiper
        class="swiper"
        indicator-dots="{{false}}"
        autoplay="{{true}}"
        interval="{{3000}}"
        duration="{{500}}"
        circular="{{true}}"
        bindchange="swiperChange"
        current="{{current}}"
      >
        <block
          wx:for="{{vehicleDetail.image_urls}}"
          wx:key="index"
        >
          <swiper-item>
            <image
              src="{{item}}"
              mode="aspectFill"
              class="swiper-image"
              bindtap="previewImage"
              data-url="{{item}}"
              data-index="{{index}}"
              binderror="handleImageError"
              bindload="handleImageLoad"
            />
            <image
              wx:if="{{imagesLoadError[index]}}"
              src="/assets/image-placeholder.png"
              mode="aspectFill"
              class="swiper-image placeholder-image"
            />
            <view
              class="image-loading"
              wx:if="{{!imagesLoaded[index]}}"
            >
              <view class="loading-spinner"></view>
            </view>
          </swiper-item>
        </block>
      </swiper>

      <!-- 自定义数字指示器 -->
      <view
        class="swiper-indicator"
        wx:if="{{vehicleDetail.image_urls.length > 0}}"
      >
        {{current + 1}} / {{vehicleDetail.image_urls.length}}
      </view>
    </view>

    <!-- 车辆基本信息 -->
    <view class="info-section">
      <view class="vehicle-title">{{vehicleDetail.ui_vehicle_name || vehicleDetail.vehicle_name}}</view>
      <view class="price-row">
        <text class="official-price">官方指导价{{vehicleDetail.official_price || '25.8'}}万元</text>
        <text class="batch-price">批量价{{vehicleDetail.pack_price}}万元</text>
      </view>

      <!-- 基本信息卡片 - 改为简洁的两列布局 -->
      <view class="info-grid">
        <view class="info-row">
          <view class="info-item">
            <text class="info-label">车辆性质</text>
            <text class="info-value">{{natureText}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">批次数量</text>
            <text class="info-value">{{vehicleDetail.sell_number}}辆</text>
          </view>
        </view>

        <view class="info-row">
          <view class="info-item">
            <text class="info-label">车源地</text>
            <text class="info-value">{{vehicleDetail.vehicle_source_location}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">截止日期</text>
            <text class="info-value">{{deadlineDate}}</text>
          </view>
        </view>

        <view class="info-row">
          <view class="info-item">
            <text class="info-label">发票情况</text>
            <text class="info-value">{{vehicleDetail.is_invoice ? '可开发票' : '不可开发票'}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">出口情况</text>
            <text class="info-value">{{vehicleDetail.is_export ? '可出口' : '不可出口'}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 批量车详情列表 -->
    <view class="section-title">批量车明细 ({{vehicleDetail.details.length}}辆)</view>
    <view class="batch-list">
      <view
        class="batch-item"
        wx:for="{{vehicleDetail.details}}"
        wx:key="id"
      >
        <view class="batch-header">
          <text class="batch-number">VIN码: {{item.vim_code || 'LGasdasd'}}</text>
        </view>

        <view class="batch-info-simple">
          <view class="batch-info-row">
            <text class="batch-info-label">公里数:</text>
            <text class="batch-info-value">{{item.large_mileage || '29.0'}}公里</text>
          </view>
          <view class="batch-info-row">
            <text class="batch-info-label">官方指导价:</text>
            <text class="batch-info-value">{{vehicleDetail.official_price || '40.08'}}万元</text>
          </view>
          <view class="batch-info-row">
            <text class="batch-info-label">售价:</text>
            <text class="batch-info-value price-highlight">{{item.retail_price || '10.9'}}万元</text>
          </view>
        </view>

        <view class="batch-buttons">
          <button
            class="batch-button custom-button"
            bindtap="showConfigInfo"
            data-index="{{index}}"
          >配置说明</button>
          <button
            class="batch-button custom-button"
            bindtap="showTimeInfo"
            data-index="{{index}}"
          >时间说明</button>
        </view>
      </view>
    </view>
  </block>

  <!-- 数据为空时显示 -->
  <view
    class="empty-container"
    wx:if="{{!isLoading && !vehicleDetail}}"
  >
    <text class="empty-text">暂无车辆数据</text>
  </view>

  <!-- 配置说明弹窗 -->
  <van-popup
    show="{{ showConfigPopup }}"
    bind:close="closeConfigPopup"
    round
    position="bottom"
    custom-style="height: 60%;"
  >
    <view class="popup-container">
      <view class="popup-header">
        <text class="popup-title">配置说明</text>
        <view
          class="popup-close"
          bindtap="closeConfigPopup"
        >×</view>
      </view>
      <view class="popup-content">
        <view class="popup-item">
          <text class="popup-label">外观:</text>
          <text class="popup-value">{{currentDetail.appearance || '未知'}}</text>
        </view>
        <view class="popup-item">
          <text class="popup-label">内饰:</text>
          <text class="popup-value">{{currentDetail.interior || '未知'}}</text>
        </view>
        <view class="popup-item">
          <text class="popup-label">选装:</text>
          <text class="popup-value">{{currentDetail.optional || '无'}}</text>
        </view>
        <view class="popup-item">
          <text class="popup-label">车架号:</text>
          <text class="popup-value">{{currentDetail.vim_code || '未知'}}</text>
        </view>
        <view class="popup-item">
          <text class="popup-label">车牌号:</text>
          <text class="popup-value">{{currentDetail.license_plate || '未知'}}</text>
        </view>
        <view class="popup-item">
          <text class="popup-label">来源地:</text>
          <text class="popup-value">{{currentDetail.source_location || '未知'}}</text>
        </view>
      </view>
    </view>
  </van-popup>

  <!-- 时间说明弹窗 -->
  <van-popup
    show="{{ showTimePopup }}"
    bind:close="closeTimePopup"
    round
    position="bottom"
    custom-style="height: 60%;"
  >
    <view class="popup-container">
      <view class="popup-header">
        <text class="popup-title">时间说明</text>
        <view
          class="popup-close"
          bindtap="closeTimePopup"
        >×</view>
      </view>
      <view class="popup-content">
        <view class="popup-item">
          <text class="popup-label">年审到期:</text>
          <text class="popup-value">{{formatDate(currentDetail.annual_review_expiring_date) || '未知'}}</text>
        </view>
        <view class="popup-item">
          <text class="popup-label">上牌日期:</text>
          <text class="popup-value">{{formatDate(currentDetail.register_date) || '未知'}}</text>
        </view>
        <view class="popup-item">
          <text class="popup-label">交强险到期:</text>
          <text class="popup-value">{{formatDate(currentDetail.compulsory_insurance_expiring_date) || '未知'}}</text>
        </view>
      </view>
    </view>
  </van-popup>

  <!-- 底部操作栏 -->
  <view class="footer-action-bar">
    <view
      class="contact-merchant-button"
      bindtap="contactMerchant"
    >
      <text>联系商家</text>
    </view>
    <view
      class="collect-button"
      bindtap="toggleCollect"
    >
      <view class="collect-icon">
        <van-icon
          name="{{isCollected ? 'star' : 'star-o'}}"
          size="24px"
          color="{{isCollected ? '#FFD700' : '#999'}}"
        />
      </view>
      <text class="collect-text">收藏</text>
    </view>
  </view>

  <!-- 安全区域占位 - 适配iPhone底部 -->
  <view class="safe-area-bottom"></view>
</view>