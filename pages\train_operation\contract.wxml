<!--pages/train_operation/contract.wxml-->
<view
    class="container"
    style="--status-bar-height: {{statusBarHeight}}px;"
>
    <!-- 自定义导航栏 -->
    <view
        class="custom-nav"
        style="padding-top: {{statusBarHeight}}px;"
    >
        <view class="nav-content">
            <view
                class="back-icon"
                bindtap="navigateBack"
            >
                <van-icon
                    name="arrow-left"
                    size="20px"
                    color="#333"
                />
            </view>
            <view class="nav-title">上传合同</view>
        </view>
    </view>

    <!-- 内容区域 -->
    <view
        class="main-content"
        style="padding-top: {{statusBarHeight + 44}}px;"
    >
        <view class="contract-content">
            <view class="upload-card">
                <view class="card-title">上传合同附件</view>

                <view class="upload-section">
                    <view class="upload-title">外贸公司与海外客户签订的合同附件</view>
                    <view class="upload-area">
                        <view
                            class="upload-button"
                            bindtap="uploadFile"
                            data-type="foreign"
                        >
                            <van-icon
                                name="plus"
                                size="24px"
                                color="#999"
                            />
                        </view>
                        <!-- 文件卡片列表 -->
                        <view
                            class="file-cards"
                            wx:if="{{foreignFiles.length > 0}}"
                        >
                            <view
                                class="file-card"
                                wx:for="{{foreignFiles}}"
                                wx:key="index"
                            >
                                <view class="file-card-content">
                                    <!-- 根据文件类型显示不同内容 -->
                                    <view
                                        class="file-icon"
                                        wx:if="{{!item.isImage}}"
                                    >
                                        <van-icon
                                            name="description"
                                            size="40rpx"
                                            color="#3B82F6"
                                        />
                                    </view>
                                    <!-- 图片缩略图 -->
                                    <image
                                        wx:if="{{item.isImage}}"
                                        class="file-image"
                                        src="{{item.url}}"
                                        mode="aspectFill"
                                    ></image>
                                    <view
                                        class="file-delete"
                                        catchtap="deleteFile"
                                        data-type="foreign"
                                        data-index="{{index}}"
                                    >
                                        <van-icon
                                            name="close"
                                            size="30rpx"
                                            color="#ff4d4f"
                                        />
                                    </view>
                                </view>
                                <view class="file-name">{{item.name}}</view>
                            </view>
                        </view>
                    </view>
                    <view class="upload-limit">最多上传3个附件</view>
                </view>

                <view class="upload-section">
                    <view class="upload-title">外贸公司跟国内出口企业签订的合同附件</view>
                    <view class="upload-area">
                        <view
                            class="upload-button"
                            bindtap="uploadFile"
                            data-type="domestic"
                        >
                            <van-icon
                                name="plus"
                                size="24px"
                                color="#999"
                            />
                        </view>
                        <!-- 文件卡片列表 -->
                        <view
                            class="file-cards"
                            wx:if="{{domesticFiles.length > 0}}"
                        >
                            <view
                                class="file-card"
                                wx:for="{{domesticFiles}}"
                                wx:key="index"
                            >
                                <view class="file-card-content">
                                    <!-- 根据文件类型显示不同内容 -->
                                    <view
                                        class="file-icon"
                                        wx:if="{{!item.isImage}}"
                                    >
                                        <van-icon
                                            name="description"
                                            size="40rpx"
                                            color="#3B82F6"
                                        />
                                    </view>
                                    <!-- 图片缩略图 -->
                                    <image
                                        wx:if="{{item.isImage}}"
                                        class="file-image"
                                        src="{{item.url}}"
                                        mode="aspectFill"
                                    ></image>
                                    <view
                                        class="file-delete"
                                        catchtap="deleteFile"
                                        data-type="domestic"
                                        data-index="{{index}}"
                                    >
                                        <van-icon
                                            name="close"
                                            size="30rpx"
                                            color="#ff4d4f"
                                        />
                                    </view>
                                </view>
                                <view class="file-name">{{item.name}}</view>
                            </view>
                        </view>
                    </view>
                    <view class="upload-limit">最多上传3个附件</view>
                </view>

                <view class="upload-section">
                    <view class="upload-title">国内出口企业跟背户公司签订的合同附件</view>
                    <view class="upload-area">
                        <view
                            class="upload-button"
                            bindtap="uploadFile"
                            data-type="export"
                        >
                            <van-icon
                                name="plus"
                                size="24px"
                                color="#999"
                            />
                        </view>
                        <!-- 文件卡片列表 -->
                        <view
                            class="file-cards"
                            wx:if="{{exportFiles.length > 0}}"
                        >
                            <view
                                class="file-card"
                                wx:for="{{exportFiles}}"
                                wx:key="index"
                            >
                                <view class="file-card-content">
                                    <!-- 根据文件类型显示不同内容 -->
                                    <view
                                        class="file-icon"
                                        wx:if="{{!item.isImage}}"
                                    >
                                        <van-icon
                                            name="description"
                                            size="40rpx"
                                            color="#3B82F6"
                                        />
                                    </view>
                                    <!-- 图片缩略图 -->
                                    <image
                                        wx:if="{{item.isImage}}"
                                        class="file-image"
                                        src="{{item.url}}"
                                        mode="aspectFill"
                                    ></image>
                                    <view
                                        class="file-delete"
                                        catchtap="deleteFile"
                                        data-type="export"
                                        data-index="{{index}}"
                                    >
                                        <van-icon
                                            name="close"
                                            size="30rpx"
                                            color="#ff4d4f"
                                        />
                                    </view>
                                </view>
                                <view class="file-name">{{item.name}}</view>
                            </view>
                        </view>
                    </view>
                    <view class="upload-limit">最多上传3个附件</view>
                </view>

                <view class="upload-section">
                    <view class="upload-title">背户公司跟4S店签订的合同附件</view>
                    <view class="upload-area">
                        <view
                            class="upload-button"
                            bindtap="uploadFile"
                            data-type="dealer"
                        >
                            <van-icon
                                name="plus"
                                size="24px"
                                color="#999"
                            />
                        </view>
                        <!-- 文件卡片列表 -->
                        <view
                            class="file-cards"
                            wx:if="{{dealerFiles.length > 0}}"
                        >
                            <view
                                class="file-card"
                                wx:for="{{dealerFiles}}"
                                wx:key="index"
                            >
                                <view class="file-card-content">
                                    <!-- 根据文件类型显示不同内容 -->
                                    <view
                                        class="file-icon"
                                        wx:if="{{!item.isImage}}"
                                    >
                                        <van-icon
                                            name="description"
                                            size="40rpx"
                                            color="#3B82F6"
                                        />
                                    </view>
                                    <!-- 图片缩略图 -->
                                    <image
                                        wx:if="{{item.isImage}}"
                                        class="file-image"
                                        src="{{item.url}}"
                                        mode="aspectFill"
                                    ></image>
                                    <view
                                        class="file-delete"
                                        catchtap="deleteFile"
                                        data-type="dealer"
                                        data-index="{{index}}"
                                    >
                                        <van-icon
                                            name="close"
                                            size="30rpx"
                                            color="#ff4d4f"
                                        />
                                    </view>
                                </view>
                                <view class="file-name">{{item.name}}</view>
                            </view>
                        </view>
                    </view>
                    <view class="upload-limit">最多上传3个附件</view>
                </view>

                <view class="remark-section">
                    <view class="remark-title">备注说明<text style="color: #ff4d4f;">*</text></view>
                    <textarea
                        class="remark-input"
                        placeholder="请输入备注说明"
                        bindinput="onRemarkInput"
                        value="{{remark}}"
                    ></textarea>
                </view>

                <view class="button-group">
                    <button
                        class="btn-save"
                        bindtap="onSave"
                    >暂存</button>
                    <button
                        class="btn-submit"
                        bindtap="onSubmit"
                    >提交</button>
                </view>
            </view>
        </view>
    </view>
</view>