/* pages/article/draft.wxss */
/* 复用 index.wxss 基础样式 */
page {
  padding: 0;
  margin: 0;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
}

/* 自定义导航栏样式 */
.custom-nav {
  width: 100%;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}

.status-bar {
  width: 100%;
}

.nav-content {
  height: 44px;
  display: flex;
  align-items: center;
  position: relative;
}

.back-icon {
  position: absolute;
  left: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx;
}

.back-icon image {
  width: 40rpx;
  height: 40rpx;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.container {
  padding: 0;
  margin: 0;
  width: 100%;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  display: flex;
  flex-direction: column;
}

/* 遮罩层 */
.mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 50;
}

/* 导航栏样式 */
.nav-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 100;
  border-bottom: 1rpx solid #f0f0f0;
}

.nav-left {
  width: 60rpx;
}

.nav-right {
  display: flex;
  justify-content: flex-end;
  width: 120rpx;
}

.more-icon,
.share-icon {
  font-size: 28rpx;
  margin-left: 20rpx;
}

/* 草稿管理标题 */
.draft-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background: transparent;
  width: 100%;
  box-sizing: border-box;
}

.draft-count {
  font-size: 32rpx;
  color: #333;
  font-weight: normal;
}

.manage-btn {
  font-size: 28rpx;
  color: #666;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
}

/* 草稿内容区域 */
.draft-content {
  width: 100%;
  padding-bottom: 40rpx;
  background: transparent;
}

.draft-item {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  flex-direction: row;
  position: relative;
}

.draft-info {
  flex: 1;
  padding-bottom: 30rpx;
}

/* 管理模式下的样式 */
.manage-mode {
  padding-left: 20rpx;
}

/* 选择框样式 */
.select-box {
  display: flex;
  align-items: center;
  padding-right: 20rpx;
  margin-top: 10rpx;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #ddd;
  position: relative;
}

.checkbox.checked {
  background-color: #1aabf8;
  border-color: #1aabf8;
}

.checkbox.checked::after {
  content: '';
  position: absolute;
  width: 20rpx;
  height: 10rpx;
  border-left: 4rpx solid white;
  border-bottom: 4rpx solid white;
  top: 10rpx;
  left: 8rpx;
  transform: rotate(-45deg);
}

.vehicle-name {
  font-size: 34rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
  line-height: 1.3;
}

.vehicle-type {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.dots {
  font-size: 28rpx;
  color: #999;
  margin-top: 10rpx;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 20rpx;
}

.edit-btn,
.publish-btn {
  padding: 12rpx 40rpx;
  font-size: 28rpx;
  border-radius: 8rpx;
  margin-left: 20rpx;
}

.edit-btn {
  background-color: #eee;
  color: #666;
}

.publish-btn {
  background-color: #1aabf8;
  color: #fff;
}

/* 单个删除按钮样式 */
.action-buttons .delete-btn {
  padding: 12rpx 40rpx;
  font-size: 28rpx;
  border-radius: 8rpx;
  margin-left: 20rpx;
  background-color: #ff4d4f;
  color: #fff;
}

.no-more {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 30rpx 0;
}

/* 空提示样式 */
.empty-tips {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 100rpx 0;
}

/* 底部操作栏 */
.footer-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100rpx;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  box-sizing: border-box;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.select-all-box {
  display: flex;
  align-items: center;
}

.select-all-box text {
  font-size: 28rpx;
  color: #333;
  margin-left: 15rpx;
}

.delete-btn {
  padding: 12rpx 40rpx;
  background-color: #f5f5f5;
  color: #999;
  font-size: 28rpx;
  border-radius: 30rpx;
}

.delete-btn.active {
  background-color: #ff4d4f;
  color: white;
}