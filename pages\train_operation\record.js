// pages/train_operation/record.js
import api from '../../utils/api';

Page({

  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: wx.getSystemInfoSync().statusBarHeight || 20, // 默认值20
    isIOS: false, // 是否是iOS设备
    recordList: [], // 初始化为空数组，将通过API获取
    chewu_orderid: '', // 车务订单ID
    loading: false, // 是否正在加载数据
    error: '' // 错误信息
  },

  /**
   * 返回上一页
   */
  navigateBack() {
    wx.navigateBack();
  },

  /**
   * 获取合同变更记录
   */
  getContractChangeLog() {
    if (!this.data.chewu_orderid) {
      this.setData({
        error: '缺少订单ID'
      });
      return;
    }

    this.setData({ loading: true, error: '' });

    // 调用API获取变更记录
    api.car.getContractChangeLog({
      chewu_orderid: this.data.chewu_orderid
    })
      .then(res => {
        console.log('变更记录数据:', res);

        let recordData = [];
        if (res && res.data && Array.isArray(res.data)) {
          // 如果API返回标准格式（如{code: 1, data: [...]}）
          recordData = res.data;
        } else if (res && Array.isArray(res)) {
          // 如果API直接返回数组
          recordData = res;
        }

        // 映射API返回的字段名到模板期望的字段名
        const mappedRecordData = recordData.map(item => ({
          operationTime: item.operateTime, // 映射operateTime -> operationTime
          changeMatter: item.changeItem,   // 映射changeItem -> changeMatter
          changeReason: item.changeReason, // 保持不变
          operator: item.operator          // 保持不变
        }));

        // 更新页面数据
        this.setData({
          recordList: mappedRecordData,
          loading: false
        });
      })
      .catch(err => {
        console.error('获取变更记录失败:', err);
        this.setData({
          error: '获取变更记录失败，请稍后再试',
          loading: false
        });
      });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();

    // 更新状态栏高度和设备类型
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight,
      isIOS: systemInfo.system.toLowerCase().includes('ios'),
      chewu_orderid: options.chewu_orderid || ''
    }, () => {
      // 在设置完chewu_orderid后获取变更记录
      this.getContractChangeLog();
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 下拉刷新重新获取数据
    this.getContractChangeLog();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})