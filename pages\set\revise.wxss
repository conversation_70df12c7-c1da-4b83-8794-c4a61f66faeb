/* pages/set/revise.wxss */
page {  
  width: 100%;  
  box-sizing: border-box;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
}

.container {  
  padding: 0;  
  background-color: transparent;  
  width: 100%;  
  box-sizing: border-box;  
  min-height: 100vh;
  position: relative;
}

/* ==================== 顶部导航区域样式 ==================== */

/* 固定头部样式 */
.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
}

/* 顶部状态栏 */
.status-bar {
  width: 100%;
  background: transparent;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  width: 100%;
  background: transparent;
  position: relative;
  padding: 0 20rpx;
  box-sizing: border-box;
  border-bottom: none;
}

.nav-back {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  z-index: 10;
}

.nav-title {
  position: absolute;
  left: 0;
  right: 0;
  height: 44px;
  line-height: 44px;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin: 0 auto;
  width: 60%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  z-index: 5;
}

.nav-placeholder {
  width: 44px;
  height: 44px;
  visibility: hidden;
  flex-shrink: 0;
}

/* 主内容区域 */
.main-content {
  width: 100%;
  padding: 0;
  box-sizing: border-box;
  position: relative;
}

/* 独立头像区域 */
.avatar-standalone {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10rpx 0;
}

.avatar-standalone .avatar-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  border: 2rpx solid #fff;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.avatar-standalone .avatar-btn {
  font-size: 28rpx;
  color: #878787;
  padding: 10rpx 20rpx;
}

/* 用户信息区域 */
.user-info-section {
  margin: 30rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 头像居中区域 */
.avatar-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50rpx 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.avatar-center .avatar-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  margin-bottom: 20rpx;
}

.avatar-btn {
  font-size: 28rpx;
  color: #e63132;
  padding: 10rpx 20rpx;
}

/* 头像区域 */
.avatar-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.avatar-wrapper {
  display: flex;
  align-items: center;
}

.avatar-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.avatar-arrow {
  margin-left: 10rpx;
}

/* 信息项样式 */
.info-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.info-value {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #666;
}

.info-value text {
  margin-right: 10rpx;
}

/* 头像按钮样式 */
.avatar-button {
  padding: 0;
  margin: 0;
  background: transparent;
  border: none;
  line-height: normal;
  border-radius: 50%;
  overflow: hidden;
  width: 160rpx;
  height: 160rpx;
}

.avatar-button::after {
  border: none;
}

.avatar-standalone .avatar-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  border: 2rpx solid #fff;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}