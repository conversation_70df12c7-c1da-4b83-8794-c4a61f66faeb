/* pages/login/register_form.wxss */

/* 页面样式 */
page {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: fixed;
    -webkit-overflow-scrolling: touch;
    background-color: #F5F5F5;
}

/* 容器样式 */
.container {
    min-height: 100vh;
    height: 100%;
    background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    overflow: hidden;
}

/* 自定义导航栏样式 */
.custom-nav {
    width: 100%;
    background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
}

.status-bar {
    width: 100%;
}

.nav-content {
    height: 44px;
    display: flex;
    align-items: center;
    position: relative;
}

.back-icon {
    position: absolute;
    left: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10rpx;
}

.nav-title {
    flex: 1;
    text-align: center;
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
}

/* 主内容区域 */
.main-content {
    position: relative;
    z-index: 10;
    margin-top: 0;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    padding: 20rpx 40rpx;
    background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

/* 注册表单样式 */
.register-form {
    width: 100%;
    max-width: 654rpx;
    margin-bottom: 40rpx;
}

/* 输入框样式 */
.input-item {
    display: flex;
    align-items: center;
    height: 88rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    padding: 0 24rpx;
    position: relative;
}

.input-icon {
    margin-right: 20rpx;
    display: flex;
    align-items: center;
}

.input-field {
    flex: 1;
    height: 88rpx;
    line-height: 88rpx;
    font-size: 28rpx;
    color: #333;
}

.placeholder {
    color: #999999;
    font-size: 28rpx;
}

/* 验证码按钮样式修改 */
.verify-code-btn {
    min-width: 120px;
    height: 36px;
    line-height: 36px;
    font-size: 14px;
    color: #3D7EFF;
    background: transparent;
    padding: 0 10px;
    text-align: center;
    border: none;
    box-shadow: none;
    outline: none;
    border-radius: 0;
    box-sizing: border-box;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 10;
}

/* 重置button的默认样式 */
.verify-code-btn::after {
    display: none;
}

/* 禁用状态 */
.disabled-btn {
    color: #999 !important;
    opacity: 0.8;
}

/* 协议区域 */
.agreement-container {
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;
    width: 654rpx;
    padding: 0 10rpx;
}

.checkbox {
    transform: scale(0.7);
    margin-right: 8rpx;
}

.agreement-text {
    font-family: "Source Han Sans", sans-serif;
    font-size: 24rpx;
    font-weight: normal;
    line-height: normal;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    letter-spacing: normal;
    color: #6B7280;
}

.link {
    color: #3B82F6;
}

/* 注册按钮 */
.register-btn {
    width: 654rpx;
    height: 80rpx;
    line-height: 80rpx;
    background-color: #3B82F6;
    color: #fff;
    font-size: 28rpx;
    border-radius: 16rpx;
    margin-bottom: 30rpx;
    font-weight: normal;
    box-sizing: border-box;
    padding: 0;
}

.register-btn::after {
    border: none;
}

/* 禁用状态的注册按钮 */
.register-btn-disabled {
    background-color: #cccccc !important;
    color: #ffffff !important;
    opacity: 0.7;
}

/* 成功提示窗口样式 */
.success-modal-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
}

.success-modal {
    width: 235px;
    height: 257px;
    background: linear-gradient(140deg, #FFECE8 12%, #DCE5F3 45%, #F8EFED 100%, #EFF3F9 100%);
    border-radius: 8px;
    overflow: hidden;
    padding: 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.success-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: #333;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 16px;
}

.success-icon image {
    width: 60px;
    height: 60px;
}

.success-title {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
}

.success-desc {
    font-size: 14px;
    color: #666;
    margin-bottom: 24px;
    text-align: center;
}

.go-info-btn {
    width: 100%;
    height: 40px;
    line-height: 40px;
    background-color: #3B82F6;
    color: #fff;
    font-size: 16px;
    border-radius: 8px;
    margin: 0;
    padding: 0;
    border: none;
}

.go-info-btn::after {
    border: none;
}

/* iOS底部安全区域适配 */
@supports (padding-bottom: constant(safe-area-inset-bottom)) {
    .container {
        padding-bottom: constant(safe-area-inset-bottom);
    }
}

@supports (padding-bottom: env(safe-area-inset-bottom)) {
    .container {
        padding-bottom: env(safe-area-inset-bottom);
    }
}

/* 验证码错误提示容器 */
.error-container {
    width: 100%;
    margin: 0 0 20rpx 0;
    padding: 0;
    min-height: 70rpx;
    box-sizing: border-box;
}

/* 错误提示样式 */
.error-message {
    width: 100%;
    box-sizing: border-box;
    padding: 10rpx 24rpx;
    margin: 0;
    border-radius: 8rpx;
    font-size: 24rpx;
    display: flex;
    align-items: center;
    animation: fadeIn 0.3s ease-in-out;
    min-height: 50rpx;
}

.error-message[hidden] {
    display: block;
    visibility: hidden;
    opacity: 0;
    color: transparent;
    background-color: transparent;
    height: auto;
    padding: 10rpx 24rpx;
    border: none;
    pointer-events: none;
}

.error-message van-icon {
    margin-right: 8rpx;
    flex-shrink: 0;
}

.error-message text {
    line-height: 1.4;
}

/* 错误类型样式 */
.error {
    color: #e74c3c;
    background-color: rgba(231, 76, 60, 0.1);
}

.warning {
    color: #f39c12;
    background-color: rgba(243, 156, 18, 0.1);
}

.info {
    color: #3498db;
    background-color: rgba(52, 152, 219, 0.1);
}

.success {
    color: #2ecc71;
    background-color: rgba(46, 204, 113, 0.1);
}

/* 渐入动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-5rpx);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}