// pages/brand/brand.js

import api from '../../utils/api';
Page({
  /**
   * 页面的初始数据
   */
  data: {
    hotBrands: [
      {
        id: 3,
        name: '奔驰',
        icon: 'https://p3.dcarimg.com/img/motor-mis-img/84c01ab4bb1f55a4809781b3a16586ff~80x0.webp'
      },
      {
        id: 4,
        name: '宝马',
        icon: 'https://p3.dcarimg.com/img/motor-mis-img/4867710a834bd648ba55797ba5e37f14~80x0.webp'
      },
      {
        id: 2,
        name: '奥迪',
        icon: 'https://p3.dcarimg.com/img/motor-mis-img/62946ba030f3589e083d8d3e98a595eb~80x0.webp'
      },
      {
        id: 20,
        name: '保时捷',
        icon: 'https://p3.dcarimg.com/img/tos-cn-i-dcdx/d809a071a391474c8067edeb637c328f~80x0.webp'
      },
      {
        id: 22,
        name: '雷克萨斯',
        icon: 'https://p3.dcarimg.com/img/motor-mis-img/b43814293efa6db10b762da4d351bfe7~80x0.webp'
      }
    ],
    brandList: []
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    wx.setNavigationBarTitle({
      title: '更多品牌'
    });
    this.getBrandList();
    this.getRecommendBrandList();
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 获取推荐
  async getRecommendBrandList() {
    const res = await api.car.getRecommendBrandList();

     // 直接处理数据，不判断code
     const brands = Array.isArray(res) ? res : (res.data || []);
     const processedBrands = brands.map(item => ({
         id: item.id,
         name: item.brand_name,
         icon: item.logo // 直接使用接口返回的letter字段
     }));
    this.setData({
        hotBrands : processedBrands
    })
  },
  // 获取品牌列表
  async getBrandList() {
    try {
        const res = await api.car.getBrandList();
        // console.log('接口返回数据:', res);

        // 直接处理数据，不判断code
        const brands = Array.isArray(res) ? res : (res.data || []);
        // console.log('原始品牌数据:', brands);

        const processedBrands = brands.map(item => ({
            id: item.id,
            name: item.brand_name,
            letter: item.letter, // 直接使用接口返回的letter字段
            logo: item.logo // 直接使用接口返回的letter字段
        }));
        // console.log('处理后的品牌数据:', processedBrands);

        const groupedBrands = this.groupBrandsByLetter(processedBrands);
        // console.log('分组后的数据:', groupedBrands);

        this.setData({ brandList: groupedBrands }, () => {
            console.log('设置到data后的brandList:', this.data.brandList);
        });
    } catch (error) {
        console.error('获取品牌列表失败', error);
        wx.showToast({
            title: '获取品牌列表失败',
            icon: 'none'
        });
    }
},

// 按首字母分组品牌
groupBrandsByLetter(brands) {
    // console.log('开始分组处理:', brands);
    const groups = {};
    brands.forEach(brand => {
        const letter = brand.letter;
        if (!groups[letter]) {
            groups[letter] = [];
        }
        groups[letter].push(brand);
    });
    // console.log('分组结果:', groups);

    // 转换为数组格式
    const result = Object.keys(groups).sort().map(letter => ({
        letter,
        list: groups[letter]
    }));
    // console.log('最终分组数组:', result);
    return result;
},
// 品牌选择事件
onBrandTap(e){
    const id = e.currentTarget.dataset.id;
    const name = e.currentTarget.dataset.name;
    var brandData = getApp().globalData.brandData;
    brandData.b_id = id;
    brandData.b_name = name;
    wx.switchTab({
      url: `/pages/buy/index`, // 确保参数格式正确
    });
},
// 品牌选择事件
onBrandSelect(e) {
    const { id, name } = e.currentTarget.dataset; // 获取 data-id 和 data-name
    var brandData = getApp().globalData.brandData;
    brandData.b_id = id;
    brandData.b_name = name;
    wx.switchTab({
      url: `/pages/buy/index`, // 确保参数格式正确
    });
  }
})