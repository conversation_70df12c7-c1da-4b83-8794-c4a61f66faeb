// pages/supply_chain_services/channel_service_detail.js
import config from '../../config';
import share from '../../utils/share';  // 导入分享模块
import api from '../../utils/api';  // 导入API模块

Page({
  behaviors: [share], // 分享设置

  /**
   * 页面的初始数据
   */
  data: {
    //处理分享页面 统一写
    shareData: {
      title: '找车侠 - 通道服务',
      path: '/pages/supply_chain_services/channel_service_detail',
      isDetailPage: true, // 标记是否详情页
    },
    COS_CONFIG: config.COS_CONFIG, // 添加腾讯云配置
    id: 0,
    // 详情数据
    company_name: '',
    channel_type: '',
    efficiency: '',
    cost: '',
    transport_type: '',
    regional: '',
    condition: '',
    materials: '',
    remark: '',
    email: '',
    phone: '',
    // 骨架屏相关
    isLoading: true, // 默认加载中状态
    showLoadingAnimation: false, // 默认显示骨架屏而非加载动画
    statusBarHeight: 20, // 默认状态栏高度
    pageStyle: '',
    // 登录弹窗
    showLoginPopup: false,
    loginPopupOptions: {
      title: '登录以获取更多服务',
      buttonText: '立即登录',
      clickMaskClose: true,
      hideCloseButton: false
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { id } = options;
    if (id) {
      this.setData({ id });
      // 加载详情数据
      this.getChannelDetail(id);
    }

    // 获取系统状态栏高度
    wx.getSystemInfo({
      success: (res) => {
        this.setData({
          statusBarHeight: res.statusBarHeight
        });
        // 添加CSS变量以供样式使用
        this.setData({
          pageStyle: `--status-bar-height: ${res.statusBarHeight}px;` + this.data.pageStyle
        });
      }
    });
  },

  /**
   * 获取通道服务详情
   */
  getChannelDetail(id) {
    this.setData({
      isLoading: true,
      showLoadingAnimation: false
    });

    // 添加选项，确保返回完整的响应数据
    const options = {
      toast: false, // 不显示默认toast，我们需要自己处理
      loading: false // 不显示默认loading，我们需要自己处理
    };

    api.channelService.getInfo(id, null, options).then(response => {
      console.log('API返回数据:', response);

      // response可能是完整的带code的对象，也可能是直接的数据对象
      const detail = response.data || response;

      if (detail) {
        console.log('详情数据:', detail);

        // 设置数据到页面
        this.setData({
          isLoading: false,
          // 设置关键字段，确保有默认值
          min_cost: detail.min_cost || '',
          max_cost: detail.max_cost || '',
          regional: detail.regional || '',
          tax_refund_time: detail.tax_refund_time || '',
          materials: detail.materials || '',
          remark: detail.remark || '无',
          company_name: detail.company_name || '',
          email: detail.email || '',
          // 其他原始字段
          ...detail
        });

        // 更新分享标题
        this.setData({
          'shareData.title': `找车侠 - ${detail.company_name || '通道服务'}`,
          'shareData.path': `/pages/supply_chain_services/channel_service_detail?id=${id}`
        });
      } else {
        // 数据为空
        console.error('获取的详情数据为空');
        wx.showToast({
          title: '未找到相关服务',
          icon: 'none'
        });
        this.setData({
          isLoading: false
        });
      }
    }).catch(err => {
      // 请求失败
      console.error('获取通道服务详情失败:', err);
      wx.showToast({
        title: '获取详情失败',
        icon: 'none'
      });
      this.setData({
        isLoading: false
      });
    });
  },

  /**
   * 返回按钮功能
   */
  navigateBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 咨询下单方法
   */
  onConsult() {
    // 检查用户是否登录
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo) {
      // 未登录，显示登录弹窗
      this.setData({
        showLoginPopup: true
      });
      return;
    }

    // 已登录，直接打开客服聊天
    wx.openCustomerServiceChat({
      extInfo: {url: 'https://work.weixin.qq.com/kfid/kfc4fa17a0f88a6e7c8'},
      corpId: 'ww3f7ad32ebbae2192', // 企业微信ID
      success(res) {
        console.log('打开客服聊天窗口成功', res);
      },
      fail(err) {
        console.error('打开客服聊天窗口失败', err);
        wx.showToast({
          title: '打开客服聊天失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 处理登录
   */
  handleLogin() {
    // 模拟登录成功
    wx.showToast({
      title: '登录成功',
      icon: 'success',
      duration: 2000
    });

    // 关闭登录弹窗
    this.setData({
      showLoginPopup: false
    });
  },

  /**
   * 关闭登录弹窗
   */
  closeLoginPopup() {
    this.setData({
      showLoginPopup: false
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 刷新数据
    if (this.data.id) {
      this.getChannelDetail(this.data.id);
    }
    wx.stopPullDownRefresh();
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: this.data.shareData.title,
      path: `${this.data.shareData.path}?id=${this.data.id}`,
      imageUrl: this.data.COS_CONFIG.url + 'wechat/assets/images/channel_service_share.png'
    };
  }
})