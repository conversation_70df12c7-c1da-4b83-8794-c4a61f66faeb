<view class="container">
  <!-- 自定义导航栏 -->
  <view class="custom-nav">
    <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>
    <view class="nav-content">
      <view class="back-icon" bindtap="navigateBack">
        <image src="/icons/moments/back.svg"></image>
      </view>
      <view class="nav-title">报价单</view>
    </view>
  </view>

  <!-- <view class="page-header">
    <view class="back-icon" bindtap="onBackTap">
      <view class="arrow-left"></view>
    </view>
    <view class="page-title">报价单</view>
  </view> -->

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 主内容 -->
  <view class="content-container" wx:else>
    <view class="section-title first-section">车辆采购</view>

    <view class="quote-card">
      <view class="car-title">{{quoteData.carTitle}}</view>
      
      <view class="quote-item">
        <view class="item-label">厂家指导价</view>
        <view class="item-value">{{quoteData.guidancePrice}} 万元</view>
      </view>
      
      <view class="quote-item">
        <view class="item-label">市场下浮</view>
        <view class="item-value">{{quoteData.marketDiscount}} 万元</view>
      </view>
      
      <view class="quote-item">
        <view class="item-label">购置税</view>
        <view class="item-value">{{quoteData.purchaseTax}} 万元</view>
      </view>
      
      <view class="quote-item">
        <view class="item-label">购车成本</view>
        <view class="item-value">{{quoteData.purchaseCost}} 万元</view>
      </view>
      
      <view class="quote-item">
        <view class="item-label">购车预付款</view>
        <view class="item-value">{{quoteData.downPayment}} 万元</view>
      </view>
      
      <view class="quote-item">
        <view class="item-label">购车垫款</view>
        <view class="item-value">{{quoteData.advancePayment}} 万元</view>
      </view>
    </view>
    
    <view class="section-title">车务</view>
    
    <view class="quote-card">
      <view class="quote-item">
        <view class="item-label">国内总杂费</view>
        <view class="item-value">{{quoteData.domesticFees}} 万元</view>
      </view>
    </view>
    
    <view class="section-title">出口物流</view>
    
    <view class="quote-card">
      <view class="quote-item">
        <view class="item-label">出口费用合计</view>
        <view class="item-value">{{quoteData.exportFees}} 万元</view>
      </view>
    </view>
    
    <view class="section-title">垫资账税</view>
    
    <view class="quote-card">
      <view class="quote-item">
        <view class="item-label">服务成本</view>
        <view class="item-value">{{quoteData.serviceCost}} 万元</view>
      </view>
      
      <view class="quote-item">
        <view class="item-label">税后价</view>
        <view class="item-value">{{quoteData.afterTaxPrice}} 万元</view>
      </view>
      
      <view class="quote-item">
        <view class="item-label">退税金额</view>
        <view class="item-value">{{quoteData.taxRefund}} 万元</view>
      </view>
      
      <view class="quote-item">
        <view class="item-label">垫资总成本</view>
        <view class="item-value">{{quoteData.totalAdvanceCost}} 万元</view>
      </view>
    </view>
  </view>
  
  <view class="share-button" bindtap="onShareButtonTap">分享报价单</view>
  
  <!-- 更新canvas标签，支持2D API -->
  <canvas type="2d" id="shareCanvas" class="share-canvas"></canvas>
</view>
