<!-- 引入登录弹窗模板 -->
<import src="../../templates/loginPopup/loginPopup.wxml" />

<view
  class="container"
  style="{{ pageStyle }}"
>
  <!-- 自定义导航栏 -->
  <view class="custom-nav">
    <view
      class="status-bar"
      style="height: {{statusBarHeight}}px;"
    ></view>
    <view class="nav-title">
      <view
        class="nav-back"
        bindtap="navigateBack"
      >
        <van-icon
          name="arrow-left"
          size="20px"
          color="#333"
        />
      </view>
      <view class="title-text">金融服务</view>
      <view class="nav-placeholder"></view>
    </view>
  </view>

  <!-- 内容区域 - 添加top-margin以避开导航栏 -->
  <view class="content-container">
    <!-- 页面内容 -->
    <view class="service-content">
      <!-- 金融服务卡片头部 -->
      <view class="service-card financial-card">
        <view class="card-image-container">
          <image
            class="card-image"
            src="{{COS_CONFIG.url}}wechat/assets/images/financial_services_long_detil.png"
            mode="scaleToFill"
          ></image>
        </view>
      </view>

      <!-- 金融服务详细信息 -->
      <view class="info-card">
        <view class="info-inside">
          <view class="info-inside-row">
            <view class="info-inside-row-title">垫资利息</view>
            <view class="info-inside-row-content">
              <view><text>最低利率：{{min_rate}}</text></view>
              <view><text>最高利率：{{max_rate}}</text></view>
            </view>
          </view>
          <view class="info-inside-row">
            <view class="info-inside-row-title">垫资范围</view>
            <view class="info-inside-row-content">{{restricted_area}}</view>
          </view>
          <view class="info-inside-row">
            <view class="info-inside-row-title">办理手续费</view>
            <view class="info-inside-row-content">{{cost}}</view>
          </view>
          <view class="info-inside-row">
            <view class="info-inside-row-title">是否监管货物</view>
            <view class="info-inside-row-content">{{regional}}</view>
          </view>
          <view class="info-inside-row">
            <view class="info-inside-row-title">服务范围</view>
            <view class="info-inside-row-content">{{regional}}</view>
          </view>
          <view class="info-inside-row">
            <view class="info-inside-row-title">申请条件</view>
            <view class="info-inside-row-content">{{condition}}</view>
          </view>
          <view class="info-inside-row">
            <view class="info-inside-row-title">申请材料</view>
            <view class="info-inside-row-content">{{materials}}</view>
          </view>
          <view class="info-inside-row">
            <view class="info-inside-row-title">其他补充</view>
            <view class="info-inside-row-content">{{remark}}</view>
          </view>
        </view>
      </view>

      <!-- 联系信息标题 -->
      <view class="section-header">
        <text class="section-title">联系信息</text>
        <!-- <view class="section-line"></view>
        <view class="section-stars">
          <text class="star">★</text>
          <text class="star">★</text>
          <text class="star">★</text>
        </view> -->
      </view>

      <!-- 联系信息 -->
      <view class="contact-card">
        <view class="contact-info">
          <view class="contact-row">
            <view class="contact-label">公司名称</view>
            <view class="contact-value">{{company_name}}</view>
          </view>
          <view
            class="contact-row"
            wx:if="{{phone}}"
          >
            <view class="contact-label">联系方式</view>
            <view class="contact-value">{{phone}}</view>
          </view>
          <view
            class="contact-row"
            wx:if="{{email}}"
          >
            <view class="contact-label">联系邮箱</view>
            <view class="contact-value">{{email}}</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部咨询按钮 -->
  <view class="bottom-button">
    <button
      class="share-button"
      open-type="share"
    >
      <van-icon
        name="share-o"
        size="24px"
      />
      <text>分享</text>
    </button>
    <button
      class="consult-button"
      bindtap="onConsult"
    >咨询下单</button>
  </view>

  <!-- 引用登录弹窗模板 -->
  <template
    is="loginPopup"
    data="{{ showLoginPopup, loginPopupOptions }}"
  />
</view>