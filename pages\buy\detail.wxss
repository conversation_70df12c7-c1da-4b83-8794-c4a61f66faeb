@import "/templates/loginPopup/loginPopup.wxss";

page {
  margin: 0;
  padding: 0;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  overflow-x: hidden;
  width: 100%;
  box-sizing: border-box;
}

.container {
  min-height: 100vh;
  padding-bottom: 125rpx;
  margin: 0;
  padding-top: 0;
  overflow-x: hidden;
  width: 100%;
  box-sizing: border-box;
  background: transparent;
}

/* 自定义导航栏样式 */
.custom-nav {
  width: 100%;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}

.status-bar {
  width: 100%;
}

.nav-content {
  height: 44px;
  display: flex;
  align-items: center;
  position: relative;
}

.back-icon {
  position: absolute;
  left: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx;
}

.back-icon image {
  width: 40rpx;
  height: 40rpx;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

/* 导航栏占位符 */
.nav-placeholder {
  width: 100%;
}

/* 主要信息容器 */
.main-info-container {
  width: 100%;
  margin: 0 0 0 0;
  background: #fff;
  border-radius: 0;
  box-shadow: 0 3rpx 10rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

/* 轮播图容器 */
.swiper-container {
  width: 100%;
  height: 500rpx;
  position: relative;
  margin: 0;
  padding: 0;
}

/* 顶部图片区域 */
.car-image {
  width: 100%;
  height: 500rpx;
  position: relative;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.car-image image {
  width: 100%;
  height: 100%;
  display: block;
}

/* 自定义数字指示器 */
.swiper-indicator {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 30rpx;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  z-index: 99;
}

/* 车辆标题 */
.car-title {
  padding: 25rpx 30rpx 15rpx;
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
  line-height: 1.5;
}

/* 车源信息行 */
.car-source-row {
  display: flex;
  padding: 5rpx 30rpx 20rpx;
  justify-content: space-between;
}

.source-item {
  font-size: 28rpx;
  color: #666;
}

.source-location {
  flex: 1;
}

.source-transfer {
  padding-right: 30rpx;
}

/* 价格信息行 */
.price-row {
  display: flex;
  align-items: flex-end;
  padding: 20rpx 30rpx 40rpx;
  position: relative;
  width: 100%;
  box-sizing: border-box;
  justify-content: space-between;
}

.price-left {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.price-info-row {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.price-main {
  font-size: 48rpx;
  font-weight: 500;
  color: #3B82F6;
  margin-left: 15rpx;
  white-space: nowrap;
  line-height: 48rpx;
  text-align: right;
}

.price-official {
  font-size: 24rpx;
  font-weight: 350;
  line-height: 48rpx;
  color: #6B7280;
  white-space: nowrap;
  text-align: right;
  text-decoration: line-through;
}

.price-date {
  font-size: 24rpx;
  font-weight: 350;
  line-height: 24rpx;
  color: #6B7280;
  white-space: nowrap;
  padding-right: 0;
}

/* 车辆信息容器 */
.car-info-container {
  width: 94%;
  margin: 0 auto;
  margin-top: 20rpx;
  border-radius: 15rpx;
  background: transparent;
  position: relative;
  z-index: 2;
  padding: 0;
  box-shadow: none;
}

/* 第一个卡片容器要有顶部间距 */
.car-info-container:first-of-type {
  margin-top: 20rpx;
}

/* 最后一个卡片容器要有底部间距 */
.car-info-container:last-of-type {
  margin-bottom: 20rpx;
}

/* 信息卡片 */
.info-card {
  background: #fff;
  border-radius: 15rpx;
  box-shadow: 0 3rpx 10rpx rgba(0, 0, 0, 0.08);
  padding: 0 0;
  margin-bottom: 20rpx;
  width: 100%;
}

/* 最后一个卡片不需要底部间距 */
.info-card:last-child {
  margin-bottom: 0;
}

/* 网格布局 */
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  padding: 20rpx 30rpx;
}

/* 单个信息项 */
.info-item {
  display: flex;
  align-items: flex-start;
  padding: 10rpx 0;
}

/* 信息头部 - 图标和标签在一行 */
.info-header {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

/* 图标样式 */
.info-icon {
  width: 18px;
  height: 13.49px;
  margin-right: 20rpx;
  flex-shrink: 0;
  position: relative;
  top: 6rpx;
}

/* 信息标签 */
.info-label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

/* 信息值 */
.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 信息内容容器 */
.info-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

/* 信息区块 */
.info-section {
  margin-bottom: 10rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 详细参数配置按钮 */
.detail-config {
  font-size: 24rpx;
  color: #666;
  font-weight: normal;
}

/* 信息行 */
.info-row {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;
  margin: 0 30rpx;
  position: relative;
}

/* 信息列 */
.info-col {
  flex: 1;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 检测报告卡片 */
.report-card {
  width: 94%;
  margin: 20rpx auto;
  padding: 30rpx;
  background: #fff;
  border-radius: 15rpx;
  box-shadow: 0 3rpx 10rpx rgba(0, 0, 0, 0.08);
  border: none;
}

.report-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-left: 30rpx;
}

.report-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.report-item:last-child {
  border-bottom: none;
}

.report-left {
  display: flex;
  align-items: center;
}

.report-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 15rpx;
}

.report-text {
  font-size: 28rpx;
  color: #333;
}

.report-link {
  font-size: 26rpx;
  color: #3B82F6;
  background-color: #EFF6FF;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100%;
}

.loading {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #eee;
  border-top: 4rpx solid #3B82F6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 修改图片操作按钮的位置，使其处于轮播图上方 */
.image-actions {
  position: fixed;
  right: 30rpx;
  bottom: calc(100% - 630rpx);
  display: flex;
  gap: 24rpx;
  z-index: 10;
}

.action-icon {
  width: 60rpx;
  height: 60rpx;
  padding: 12rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.15);
}

/* 添加单独的按钮样式 - 右上角查参数按钮 */
.price-action-btn {
  position: absolute;
  right: 80rpx;
  top: 35rpx;
  background: #f8a54a;
  color: #fff;
  font-size: 25rpx;
  padding: 6rpx 16rpx;
  border-radius: 24rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 调整信息列样式 */
.info-value.long-text {
  font-size: 28rpx;
  letter-spacing: 0;
  word-break: break-all;
}

/* 修改底部按钮区域，避免与TabBar重叠 */
.action-bar {
  position: fixed;
  bottom: 20rpx; /* 固定位置，避免使用calc函数可能的兼容性问题 */
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  background: transparent;
  box-shadow: none;
  z-index: 100;
}

.contact-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background: linear-gradient(to right, #f56c6c, #ff8a8a);
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  border: none;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(245, 108, 108, 0.3);
}

.contact-btn::after {
  border: none;
}

/* 参数行 - 三个参数并排显示 */
.params-row {
  display: flex;
  justify-content: center;
  padding: 20rpx 30rpx;
}

/* 参数项 */
.param-item {
  display: flex;
  align-items: flex-start;
  width: 30%;
  margin: 0 1.5%;
}

/* 商家信息卡片 */
.merchant-info {
  display: flex;
  align-items: flex-start;
  padding: 15rpx 30rpx 30rpx;
}

.merchant-logo {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.merchant-logo image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: #f5f5f5;
}

.merchant-details {
  flex: 1;
}

.merchant-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 5rpx;
  line-height: 1.4;
}

.merchant-address {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 5rpx;
  line-height: 1.4;
}

.merchant-stats {
  display: flex;
  align-items: center;
  margin-top: 5rpx;
}

.merchant-rating {
  font-size: 28rpx;
  color: #FF6634;
  margin-right: 20rpx;
}

.merchant-stock {
  font-size: 28rpx;
  color: #666;
}

.merchant-contact {
  margin-left: auto;
  display: flex;
  align-items: center;
}

.verify-btn {
  font-size: 24rpx;
  background-color: #E5F1FE;
  color: #3A84FF;
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  line-height: 1.5;
  font-weight: normal;
  min-height: unset;
  margin: 0;
}

.verify-btn::after {
  border: none;
}

/* 推荐车辆区域样式 */
.recommend-section {
  width: 100%;
  padding: 20rpx 20rpx;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  padding-bottom: 170rpx;
  box-sizing: border-box;
}

.recommend-header {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-left: 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.recommend-header .more-cars {
  font-size: 24rpx;
  color: #999999;
  font-weight: normal;
  padding-right: 10rpx;
}

/* 使用网格布局确保两列显示 */
.recommend-grid {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(2, calc(50% - 10rpx)); /* 每列宽度减去间距的一半 */
  gap: 20rpx; /* 行和列之间的间距 */
  justify-content: space-between; /* 确保两列之间有足够空间 */
  box-sizing: border-box;
}

.recommend-card {
  background-color: #fff;
  border-radius: 10rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  width: 100%;
  box-sizing: border-box;
  height: auto;
  display: flex;
  flex-direction: column;
}

.recommend-card-body {
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.recommend-car-image {
  width: 100%;
  height: 200rpx;
  object-fit: cover;
}

.recommend-tag-label {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  font-size: 22rpx;
  color: #fff;
}

.recommend-tag-label.hot {
  background-color: #ff6f00;
}

.recommend-tag-label.new {
  background-color: #1296db;
}

.recommend-car-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  padding: 10rpx 12rpx;
  word-wrap: break-word;
  word-break: break-all;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: visible;
  min-height: 76rpx;
}

.recommend-car-params {
  font-size: 22rpx;
  color: #666;
  padding: 0 12rpx;
}

.recommend-price-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 12rpx 15rpx;
}

.recommend-new-price {
  font-size: 20rpx;
  color: #999;
  text-decoration: line-through;
}

.recommend-sale-price {
  font-size: 28rpx;
  color: #3b82f6;
  font-weight: 500;
}

/* 底部固定操作栏 */
.bottom-action-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 110rpx;
  display: flex;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.action-buttons-container {
  display: flex;
  flex: 1;
  padding: 10rpx 20rpx 10rpx;
  gap: 20rpx;
  margin-top: 6rpx;
}

.action-btn-collect {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 100%;
  font-size: 24rpx;
  color: #666;
  background-color: #fff;
  margin-top: 2rpx;
}

.collect-icon {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 6rpx;
}

.action-btn-manual {
  flex: 1;
  background-color: #4080FF;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
}

.action-btn-contact {
  flex: 1;
  background-color: #FF5A00;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
}

/* 添加底部内边距，避免内容被底部操作栏遮挡 */
.recommend-section {
  padding-bottom: 170rpx;
}
