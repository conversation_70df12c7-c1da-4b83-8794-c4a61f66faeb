.staff-select-container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  padding: 20rpx 0;
  display: flex;
  justify-content: flex-end;
}

.add-btn {
  background: #1989fa;
  color: #fff;
  padding: 10rpx 30rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.staff-list {
  margin-top: 20rpx;
}

.staff-item {
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}

.staff-item.selected {
  background: #e6f7ff;
  border: 1rpx solid #1989fa;
}

.select-circle {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  margin-right: 20rpx;
  box-sizing: border-box;
}

.select-circle.selected {
  border: 2rpx solid #1989fa;
  background-color: #1989fa;
  position: relative;
}

.select-circle.selected::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20rpx;
  height: 20rpx;
  background-color: white;
  border-radius: 50%;
}

.staff-info {
  flex: 1;
}

.name {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.phone, .email {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 6rpx;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.delete-btn, .edit-btn {
  font-size: 26rpx;
  padding: 6rpx 20rpx;
  border-radius: 6rpx;
}

.delete-btn {
  color: #ff4d4f;
  border: 1rpx solid #ff4d4f;
}

.edit-btn {
  color: #1989fa;
  border: 1rpx solid #1989fa;
}

.bottom-btn {
  position: fixed;
  bottom: 40rpx;
  left: 20rpx;
  right: 20rpx;
  background: #1989fa;
  color: #fff;
  text-align: center;
  padding: 20rpx 0;
  border-radius: 8rpx;
  font-size: 32rpx;
} 