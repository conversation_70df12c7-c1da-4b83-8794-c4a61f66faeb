Page({
    data: {
        staffList: [
            {
                name: '李华',
                phone: '13758456257',
                email: '<EMAIL>'
            }
        ],
        selectedIndex: -1,
        type: 'staff' // 默认是选择报价人
    },

    onLoad(options) {
        // 获取选择类型，是客户还是报价人
        if (options.type) {
            this.setData({
                type: options.type
            });
            // 根据类型设置页面标题
            wx.setNavigationBarTitle({
                title: options.type === 'customer' ? '选择客户' : '选择报价人'
            });
        }
    },

    onAdd() {
        // 跳转到新增页面时传递类型参数
        wx.navigateTo({
            url: `/pages/staff/add?type=${this.data.type}`
        })
    },

    // 添加新用户的方法，供add页面调用
    addNewUser(user) {
        const staffList = this.data.staffList;
        staffList.push(user);
        this.setData({
            staffList
        });

        wx.showToast({
            title: '添加成功',
            icon: 'success'
        });
    },

    // 更新用户的方法，供edit页面调用
    updateUser(index, user) {
        const staffList = this.data.staffList;
        staffList[index] = user;
        this.setData({
            staffList
        });

        wx.showToast({
            title: '更新成功',
            icon: 'success'
        });
    },

    onSelect(e) {
        const index = e.currentTarget.dataset.index
        this.setData({
            selectedIndex: index
        })
    },

    onDelete(e) {
        const index = e.currentTarget.dataset.index
        wx.showModal({
            title: '提示',
            content: '确定要删除该用户吗？',
            success: (res) => {
                if (res.confirm) {
                    const staffList = this.data.staffList
                    staffList.splice(index, 1)
                    this.setData({
                        staffList
                    })

                    wx.showToast({
                        title: '删除成功',
                        icon: 'success'
                    });
                }
            }
        })
    },

    onEdit(e) {
        const index = e.currentTarget.dataset.index
        wx.navigateTo({
            url: `/pages/staff/edit?index=${index}`
        })
    },

    onConfirm() {
        if (this.data.selectedIndex === -1) {
            wx.showToast({
                title: '请选择用户',
                icon: 'none'
            })
            return
        }

        const selectedStaff = this.data.staffList[this.data.selectedIndex]
        // 将选中的用户信息返回到上一页
        const pages = getCurrentPages()
        const prevPage = pages[pages.length - 2]

        // 根据类型设置不同的数据
        if (this.data.type === 'customer') {
            prevPage.setData({
                customer: selectedStaff
            });
        } else {
            prevPage.setData({
                staff: selectedStaff
            });
        }

        wx.navigateBack()
    }
}) 