// pages/article/index.js
import api from '../../utils/api';
import util from '../../utils/util';

/**
 * 安全解析日期字符串，兼容iOS
 * @param {string} dateString - 日期字符串，如 "2025-06-03 16:18:06"
 * @return {Date} 解析后的Date对象
 */
function safeParseDateString(dateString) {
  if (!dateString) return new Date();

  // 处理 "yyyy-MM-dd HH:mm:ss" 格式
  if (typeof dateString === 'string' && dateString.indexOf('-') > 0 && dateString.indexOf(':') > 0) {
    // 将 "yyyy-MM-dd HH:mm:ss" 转换为 "yyyy/MM/dd HH:mm:ss"
    // 这种格式在iOS设备上是支持的
    return new Date(dateString.replace(/-/g, '/'));
  }

  // 其他格式或已经是Date对象，直接返回
  return new Date(dateString);
}

Page({

  /**
   * 页面的初始数据
   */
  data: {
    articleList: [],
    page: 1,
    pageSize: 15,
    total: 0,
    noMore: false,
    canEdit: true, // 是否可以编辑帖子
    hasOpenMenu: false,
    isLoading: false, // 是否正在加载
    isEditMode: false, // 是否处于编辑模式
    showSelectButtons: false, // 是否显示选择按钮
    allSelected: false, // 是否全选
    selectedCount: 0, // 选中的项目数量
    // 自定义导航栏相关数据
    statusBarHeight: wx.getSystemInfoSync()['statusBarHeight'],
    menuButtonHeight: wx.getMenuButtonBoundingClientRect().height,
    navBarHeight: wx.getSystemInfoSync()['statusBarHeight'] + wx.getMenuButtonBoundingClientRect().height + (wx.getMenuButtonBoundingClientRect().top - wx.getSystemInfoSync()['statusBarHeight']) * 2,
    // 选项卡数据
    activeTab: 0, // 当前激活的选项卡，0:我的帖子，1:收到的点赞，2:收到的评论
    likesList: [], // 收到的点赞列表

    // 评论回复相关数据
    showCommentInput: false, // 是否显示评论输入框
    commentValue: '', // 评论内容
    commentFocus: false, // 评论输入框是否聚焦
    commentPlaceholder: '说点什么...', // 评论输入框占位文本
    replyToComment: null, // 当前回复的评论信息
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 如果有指定默认选项卡，则设置
    if (options.tab) {
      this.setData({
        activeTab: parseInt(options.tab) || 0
      });
    }
    this.getArticleList();
  },

  /**
   * 返回上一页
   */
  navigateBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 切换选项卡
   */
  switchTab(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    if (this.data.activeTab === index) return;

    this.setData({
      activeTab: index,
      page: 1,
      noMore: false
    });

    if (index === 0) {
      this.setData({ articleList: [] });
      this.getArticleList(); // 获取我的帖子
    } else if (index === 1) {
      // 先清空点赞列表并显示加载状态
      this.setData({
        likesList: [],
        isLoading: true
      });
      this.getLikes(); // 获取收到的点赞
    } else if (index === 2) {
      this.getComments(); // 获取收到的评论
    }
  },

  /**
   * 获取收到的点赞列表
   */
  getLikes() {
    const app_id = util.getAppId();
    this.setData({ isLoading: true });

    console.log('开始获取点赞列表, app_id:', app_id);

    // 创建模拟数据以确保显示效果
    const mockLikeData = [];

    // 调用API获取收到的点赞列表
    api.moments.getLikeList({
      app_id: app_id,
      page: 1,
      page_size: this.data.pageSize
    }).then(res => {
      console.log('获取点赞列表响应:', res);

      // 处理直接返回数组的情况
      let dataArray = res;

      // 如果返回的是标准格式 {code:0, msg:'成功', data:[...]}
      if (res && typeof res === 'object' && !Array.isArray(res) && res.data) {
        // 检查标准响应格式
        if (res.code !== 0) {
          console.log('接口返回错误码:', res.code, res.msg);
          // 使用模拟数据替代空数据
          this.setData({
            likesList: mockLikeData,
            page: 1,
            noMore: true,
            isLoading: false
          });
          return;
        }
        dataArray = res.data;
      }

      // 确保dataArray是数组
      if (!Array.isArray(dataArray)) {
        console.log('返回数据格式不是数组:', dataArray);
        // 使用模拟数据替代
        this.setData({
          likesList: mockLikeData,
          page: 1,
          noMore: true,
          isLoading: false
        });
        return;
      }

      console.log('处理的数据数组:', dataArray);

      if (dataArray.length === 0) {
        console.log('返回的数据为空数组');
        // 使用模拟数据替代空数据
        this.setData({
          likesList: mockLikeData,
          page: 1,
          noMore: true,
          isLoading: false
        });
        return;
      }

      // 处理接口返回的数据
      const processedLikes = dataArray.map(item => {
        try {
          // 处理日期格式 - 日期字符串格式: "2025-05-22 11:19:33"
          const createDate = safeParseDateString(item.like_time);
          console.log('原始日期:', item.like_time, '解析后日期对象:', createDate);

          const year = createDate.getFullYear();
          const month = String(createDate.getMonth() + 1).padStart(2, '0');
          const day = String(createDate.getDate()).padStart(2, '0');

          // 转换为前端模板所需的数据格式
          return {
            id: item.post_id + '_' + item.app_id, // 生成唯一ID
            post_id: item.post_id, // 保存原始帖子ID
            user: {
              id: item.app_id,
              name: item.short_name || item.company_name || '匿名用户',
              avatar: item.avatar || '/images/default-avatar.png'
            },
            article: {
              id: item.post_id,
              title: item.post_title || '无标题',
              type: '商机', // 默认类型
              views: 0 // 默认值
            },
            create_time: item.like_time,
            dateStr: `${year}/${month}/${day}`,
            dateTimeStr: this.formatLikeTime(item.like_time),
            dateSortKey: `${year}${month}${day}`
          };
        } catch (error) {
          console.error('处理点赞项目时出错:', error, item);
          return null;
        }
      }).filter(item => item !== null); // 过滤掉处理失败的项

      console.log('处理后的点赞列表项数:', processedLikes.length);
      if (processedLikes.length === 0) {
        console.log('处理后没有有效的点赞数据');
        this.setData({
          likesList: [],
          page: 1,
          noMore: true,
          isLoading: false
        });
        return;
      }

      // 按日期分组处理
      const dateGroups = {};

      // 对处理后的点赞按日期分组
      processedLikes.forEach(item => {
        const dateKey = item.dateSortKey;
        if (!dateGroups[dateKey]) {
          dateGroups[dateKey] = [];
        }
        dateGroups[dateKey].push(item);
      });

      // 重组点赞列表，并标记每组中的第一个
      const result = [];
      Object.keys(dateGroups).sort().reverse().forEach(dateKey => {
        const group = dateGroups[dateKey];
        // 标记每组的第一个点赞显示日期
        for (let i = 0; i < group.length; i++) {
          group[i].showDateLabel = (i === 0);
          result.push(group[i]);
        }
      });

      console.log('最终处理后的点赞列表:', result);

      // 使用直接赋值而不是this.setData对数据进行检查
      const finalData = {
        likesList: result,
        page: 1,
        noMore: processedLikes.length < this.data.pageSize,
        isLoading: false
      };

      console.log('设置到页面的数据:', finalData);

      this.setData(finalData);

      // 额外检查设置后的数据
      setTimeout(() => {
        console.log('页面当前likesList数据:', this.data.likesList);
      }, 200);
    }).catch(err => {
      console.error('获取点赞列表失败', err);
      this.setData({
        likesList: [],
        page: 1,
        noMore: true,
        isLoading: false
      });
      wx.showToast({
        title: '获取点赞列表失败，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 获取收到的评论列表
   */
  getComments() {
    const app_id = util.getAppId();
    this.setData({ isLoading: true });

    console.log('开始获取评论列表, app_id:', app_id);

    // 调用API获取收到的评论列表
    api.moments.getCommentList({
      app_id: app_id,
      page: 1,
      page_size: this.data.pageSize
    }).then(res => {
      console.log('获取评论列表响应:', res);

      // 处理直接返回数组的情况
      let commentData = [];

      // 如果返回的是标准格式 {code:0, msg:'成功', data:[...]}
      if (res && typeof res === 'object' && res.code === 0 && res.data) {
        commentData = res.data;
      } else if (Array.isArray(res)) {
        commentData = res;
      }

      // 确保commentData是数组
      if (!Array.isArray(commentData) || commentData.length === 0) {
        console.log('返回的评论数据为空或不是数组');
        this.setData({
          commentsList: [],
          page: 1,
          noMore: true,
          isLoading: false
        });
        return;
      }

      // 处理接口返回的数据
      const processedComments = commentData.map(item => {
        try {
          // 处理日期格式 - 日期字符串格式: "2024-05-22 11:19:33"
          const commentDate = safeParseDateString(item.comment_time);

          // 获取当前日期和评论日期
          const now = new Date();
          const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime();
          const yesterday = new Date(today - 24 * 60 * 60 * 1000).getTime();
          const commentDay = new Date(commentDate.getFullYear(), commentDate.getMonth(), commentDate.getDate()).getTime();

          // 格式化时间为 HH:MM
          const hours = String(commentDate.getHours()).padStart(2, '0');
          const minutes = String(commentDate.getMinutes()).padStart(2, '0');
          const timeStr = `${hours}:${minutes}`;

          // 格式化日期文本
          let dateDisplay;
          if (commentDay === today) {
            dateDisplay = `今天 ${timeStr}`;
          } else if (commentDay === yesterday) {
            dateDisplay = `昨天 ${timeStr}`;
          } else {
            const year = commentDate.getFullYear();
            const month = String(commentDate.getMonth() + 1).padStart(2, '0');
            const day = String(commentDate.getDate()).padStart(2, '0');
            dateDisplay = `${year}/${month}/${day}`;
          }

          // 添加日期排序键，用于分组
          const year = commentDate.getFullYear();
          const month = String(commentDate.getMonth() + 1).padStart(2, '0');
          const day = String(commentDate.getDate()).padStart(2, '0');
          const dateSortKey = `${year}${month}${day}`;

          // 转换为前端模板所需的数据格式
          return {
            id: item.id || item.post_id + '_' + item.app_id, // 生成唯一ID
            comment_id: item.comment_id || item.id, // 保存原始comment_id用于回复
            user: {
              id: item.app_id,
              name: item.short_name || item.company_name || '匿名用户',
              avatar: item.avatar || '/images/default-avatar.png',
              isOfficial: false // 默认非官方账号
            },
            type: 'comment', // 默认为评论帖子
            content: item.content || '无内容',
            targetUser: null, // 默认无目标用户
            article: {
              id: item.post_id,
              title: item.post_title || '无标题'
            },
            create_time: item.comment_time,
            dateDisplay: dateDisplay,
            actionText: '评论了你的帖子',
            dateSortKey: dateSortKey,
            replies: item.replies || [],
            showReplies: false // 默认折叠回复列表
          };
        } catch (error) {
          console.error('处理评论项目时出错:', error, item);
          return null;
        }
      }).filter(item => item !== null); // 过滤掉处理失败的项

      console.log('处理后的评论列表项数:', processedComments.length);
      if (processedComments.length === 0) {
        console.log('处理后没有有效的评论数据');
        this.setData({
          commentsList: [],
          page: 1,
          noMore: true,
          isLoading: false
        });
        return;
      }

      // 按日期分组处理
      const dateGroups = {};

      // 对处理后的评论按日期分组
      processedComments.forEach(item => {
        const dateKey = item.dateSortKey;
        if (!dateGroups[dateKey]) {
          dateGroups[dateKey] = [];
        }
        dateGroups[dateKey].push(item);
      });

      // 重组评论列表，并标记每组中的第一个
      const result = [];
      Object.keys(dateGroups).sort().reverse().forEach(dateKey => {
        const group = dateGroups[dateKey];
        // 标记每组的第一个评论显示日期
        for (let i = 0; i < group.length; i++) {
          group[i].showDateLabel = (i === 0);
          result.push(group[i]);
        }
      });

      console.log('最终处理后的评论列表:', result);

      this.setData({
        commentsList: result,
        page: 1,
        noMore: true, // 目前先设为true，后续可以根据实际情况调整
        isLoading: false
      });

    }).catch(err => {
      console.error('获取评论列表失败', err);
      this.setData({
        commentsList: [],
        page: 1,
        noMore: true,
        isLoading: false
      });
      wx.showToast({
        title: '获取评论列表失败，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 设置自定义导航栏
   */
  onShow() {
    // wx.setNavigationBarTitle({
    //   title: '我的帖子'
    // });

    // 检查是否需要刷新列表数据
    const app = getApp();
    if (app.globalData && app.globalData.needRefreshArticleList) {
      // console.log('检测到需要刷新列表数据');
      // 重置标记
      app.globalData.needRefreshArticleList = false;
      // 刷新数据
      this.refreshData();
    }
  },

  /**
   * 处理日期标识分组显示
   * 相同日期的帖子只在第一个显示日期标识
   */
  processArticleListDates(articleList) {
    if (!articleList || articleList.length === 0) return [];

    // 按日期对帖子进行分组
    const dateGroups = {};

    // 先对每个帖子处理日期信息
    const processedList = articleList.map(item => this.formatDateInfo(item));

    // 对处理后的帖子按日期分组
    processedList.forEach(item => {
      const dateKey = item.dateSortKey; // 使用排序键作为分组依据
      if (!dateGroups[dateKey]) {
        dateGroups[dateKey] = [];
      }
      dateGroups[dateKey].push(item);
    });

    // 重组帖子列表，并标记每组中的第一个
    const result = [];
    Object.keys(dateGroups).sort().reverse().forEach(dateKey => {
      const group = dateGroups[dateKey];
      // 标记每组的第一个帖子显示日期
      for (let i = 0; i < group.length; i++) {
        group[i].showDateLabel = (i === 0);
        result.push(group[i]);
      }
    });

    return result;
  },

  /**
   * 格式化日期标签
   */
  formatDateInfo(item) {
    // 获取当前日期
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime();

    // 处理日期和发布状态
    const createDate = safeParseDateString(item.create_time);
    const createDateStart = new Date(createDate.getFullYear(), createDate.getMonth(), createDate.getDate()).getTime();

    // 格式化日期
    const year = createDate.getFullYear();
    const month = String(createDate.getMonth() + 1).padStart(2, '0');
    const day = String(createDate.getDate()).padStart(2, '0');

    // 保存完整日期格式供其他地方使用
    item.fullDate = `${year}/${month}/${day}`;

    // 添加日期排序键，用于分组
    item.dateSortKey = `${year}${month}${day}`;

    // 判断是否为今天
    if (createDateStart === today) {
      item.dateLabel = '今日';
    } else {
      item.dateLabel = item.fullDate;
    }

    return item;
  },

  /**
   * 获取帖子列表
   */
  async getArticleList(isLoadMore = false) {
    try {
      // console.log('开始获取帖子列表, 当前页码:', isLoadMore ? this.data.page : 1);
      this.setData({ isLoading: true });

      // 构建请求参数
      const requestParams = {
        app_id: util.getAppId(), // 固定参数
        page: isLoadMore ? this.data.page : 1,
        list_rows: this.data.pageSize,
        type: 'user' // 固定参数
      };

      // console.log('请求参数:', JSON.stringify(requestParams));

      // 调用API获取数据
      const result = await api.moments.getList(requestParams);

      // console.log('API返回数据结构:', Object.keys(result));
      // console.log('获取到的帖子数量:', result.data ? result.data.length : 0);

      if (result) {
        // 确保从正确的数据结构中获取数据
        let articleData = result.data || [];

        // 格式化数据，确保所有必要的字段都存在
        articleData = articleData.map(item => {
          // 确保图片字段是数组
          let purchase_ref_files = item.purchase_ref_files;
          if (purchase_ref_files) {
            if (typeof purchase_ref_files === 'string') {
              try {
                purchase_ref_files = JSON.parse(purchase_ref_files);
              } catch (e) {
                // 如果无法解析为JSON，尝试作为逗号分隔的字符串处理
                purchase_ref_files = purchase_ref_files.split(',').filter(url => url.trim() !== '');
              }
            }
          } else {
            purchase_ref_files = [];
          }

          // 创建处理后的对象
          const processedItem = {
            ...item,
            purchase_ref_files,
            // 确保各字段有默认值
            avatar: item.avatar,
            short_name: item.short_name || '用户' + item.id,
            likes: item.likes,
            views: item.views, // 临时随机值，实际应该使用后端数据
            comments: item.comments,
            likeds: item.likeds || false,
            ui_vehicle_name: item.ui_vehicle_name || '未知车型',
            used_type_name: item.used_type_name || '二手车',
            price: item.price || '面议',
            showActions: false // 默认不显示操作菜单
          };

          // 处理日期标记
          return this.formatDateInfo(processedItem);
        });

        if (isLoadMore) {
          // 加载更多
          const newList = [...this.data.articleList, ...articleData];
          // 处理日期显示
          const processedList = this.processArticleListDates(newList);

          this.setData({
            articleList: processedList,
            page: result.current_page,
            total: result.total,
            noMore: result.current_page >= result.last_page,
            isLoading: false
          });
        } else {
          // 首次加载或刷新
          // 处理日期显示
          const processedList = this.processArticleListDates(articleData);

          this.setData({
            articleList: processedList,
            page: result.current_page,
            total: result.total,
            noMore: result.current_page >= result.last_page,
            isLoading: false
          });
        }
      } else {
        this.setData({ isLoading: false });
        wx.showToast({
          title: '获取数据失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('获取帖子列表失败:', error);
      this.setData({ isLoading: false });
      wx.showToast({
        title: '获取数据失败',
        icon: 'none'
      });
    }
  },


  /**
   * 点赞/取消点赞
   */
  toggleLike(e) {
    const { id, index } = e.currentTarget.dataset;
    const articleList = this.data.articleList;
    const isLiked = articleList[index].likeds;

    // 更新点赞状态和数量
    articleList[index].likeds = !isLiked;
    articleList[index].likes = isLiked ? articleList[index].likes - 1 : articleList[index].likes + 1;

    this.setData({
      articleList
    });

    // 模拟发送请求
    // console.log(`${isLiked ? '取消点赞' : '点赞'} 帖子ID:`, id);
    wx.showToast({
      title: isLiked ? '已取消点赞' : '已点赞',
      icon: 'none'
    });
  },

  /**
   * 点击删除按钮
   */
  onDeleteTap(e) {
    const id = e.currentTarget.dataset.id;
    // console.log('准备删除帖子，ID:', id);

    // 显示确认对话框
    wx.showModal({
      title: '提示',
      content: '确定要删除该帖子吗？',
      success: (res) => {
        if (res.confirm) {
          // console.log('用户确认删除');
          // 显示加载提示
          wx.showLoading({
            title: '正在删除...',
            mask: true
          });

          // 调用删除接口
          api.moments.deleteMyPost({ id: id })
            .then(res => {
              // console.log('删除接口返回数据:', res);
              wx.hideLoading();

              if (res && res.code === 0) {
                // console.log('删除成功');
                wx.showToast({
                  title: '删除成功',
                  icon: 'success',
                  duration: 1500
                });

                // 使用刷新方法重新加载数据
                // console.log('删除成功，刷新页面数据');
                setTimeout(() => {
                  this.refreshData();
                }, 500);
              } else {
                // console.log('删除接口返回错误:', res);
                wx.showToast({
                  title: res?.msg || '删除失败',
                  icon: 'none'
                });
              }
            })
            .catch(err => {
              wx.hideLoading();
              console.error('删除帖子请求失败:', err);
              wx.showToast({
                title: '删除失败，请稍后重试',
                icon: 'none'
              });
            });
        } else {
          console.log('用户取消删除');
        }
      }
    });
  },

  /**
   * 切换编辑模式
   */
  onEditTap() {
    // 切换编辑模式
    const isEditMode = !this.data.isEditMode;

    // 重置选择状态
    let updatedData = {
      isEditMode: isEditMode,
      showSelectButtons: isEditMode,
      allSelected: false,
      selectedCount: 0
    };

    // 根据当前选项卡更新对应列表的选择状态
    if (this.data.activeTab === 0) {
      const updatedArticles = this.data.articleList.map(item => {
        return { ...item, selected: false };
      });
      updatedData.articleList = updatedArticles;
    } else if (this.data.activeTab === 1) {
      const updatedLikes = this.data.likesList.map(item => {
        return { ...item, selected: false };
      });
      updatedData.likesList = updatedLikes;
    } else if (this.data.activeTab === 2) {
      const updatedComments = this.data.commentsList.map(item => {
        return { ...item, selected: false };
      });
      updatedData.commentsList = updatedComments;
    }

    this.setData(updatedData);
  },

  /**
   * 处理项目选择状态切换
   */
  toggleSelectItem(e) {
    const id = e.currentTarget.dataset.id;
    const index = e.currentTarget.dataset.index;
    const tab = parseInt(e.currentTarget.dataset.tab);

    // 确保当前标签页与点击的项目所在标签页一致
    if (tab !== this.data.activeTab) return;

    let dataKey, list;

    // 根据当前标签页确定要修改的数据
    if (tab === 0) {
      dataKey = 'articleList';
      list = [...this.data.articleList];
    } else if (tab === 1) {
      dataKey = 'likesList';
      list = [...this.data.likesList];
    } else if (tab === 2) {
      dataKey = 'commentsList';
      list = [...this.data.commentsList];
    }

    // 切换选中状态
    if (list && list[index]) {
      list[index].selected = !list[index].selected;

      // 计算选中数量
      const selectedCount = list.filter(item => item.selected).length;

      // 更新数据
      const updateData = {
        [dataKey]: list,
        selectedCount: selectedCount,
        allSelected: selectedCount === list.length && list.length > 0
      };

      this.setData(updateData);
    }
  },

  /**
   * 全选/取消全选
   */
  toggleSelectAll() {
    let dataKey, list;

    // 根据当前标签页确定要修改的数据
    if (this.data.activeTab === 0) {
      dataKey = 'articleList';
      list = [...this.data.articleList];
    } else if (this.data.activeTab === 1) {
      dataKey = 'likesList';
      list = [...this.data.likesList];
    } else if (this.data.activeTab === 2) {
      dataKey = 'commentsList';
      list = [...this.data.commentsList];
    }

    if (!list || list.length === 0) return;

    // 切换全选状态
    const newSelectState = !this.data.allSelected;

    // 更新列表中所有项目的选择状态
    list.forEach(item => {
      item.selected = newSelectState;
    });

    // 更新数据
    this.setData({
      [dataKey]: list,
      allSelected: newSelectState,
      selectedCount: newSelectState ? list.length : 0
    });
  },

  /**
   * 删除选中的项目
   */
  deleteSelectedItems() {
    let dataKey, list, idList = [];

    // 根据当前标签页确定要修改的数据
    if (this.data.activeTab === 0) {
      dataKey = 'articleList';
      list = [...this.data.articleList];
      idList = list.filter(item => item.selected).map(item => item.id);
    } else if (this.data.activeTab === 1) {
      dataKey = 'likesList';
      list = [...this.data.likesList];
      idList = list.filter(item => item.selected).map(item => item.id);
    } else if (this.data.activeTab === 2) {
      dataKey = 'commentsList';
      list = [...this.data.commentsList];
      idList = list.filter(item => item.selected).map(item => item.id);
    }

    if (idList.length === 0) {
      wx.showToast({
        title: '请选择要删除的项目',
        icon: 'none'
      });
      return;
    }

    // 显示确认对话框
    wx.showModal({
      title: '确认删除',
      content: `确定要删除选中的${idList.length}个项目吗？`,
      confirmColor: '#3a88ff',
      success: (res) => {
        if (res.confirm) {
          // 用户点击确定，调用对应的删除接口
          this.callDeleteAPI(idList, dataKey, list);
        }
      }
    });
  },

  /**
   * 根据当前标签页调用对应的删除接口
   */
  async callDeleteAPI(idList, dataKey, list) {
    try {
      console.log('开始删除操作，标签页:', this.data.activeTab, '要删除的ID列表:', idList);

      // 显示加载提示
      wx.showLoading({
        title: '删除中...',
        mask: true
      });

      let deletePromise;
      let deleteParams;

      // 根据当前标签页调用不同的删除接口
      if (this.data.activeTab === 0) {
        // 删除我的帖子 - 支持批量删除
        if (idList.length === 1) {
          // 单个删除
          deleteParams = { id: idList[0] };
          console.log('删除我的帖子(单个)，参数:', deleteParams);
          deletePromise = api.moments.deleteMyPost(deleteParams);
        } else {
          // 批量删除
          deleteParams = { ids: idList };
          console.log('删除我的帖子(批量)，参数:', deleteParams);
          deletePromise = api.moments.deleteMyPost(deleteParams);
        }
      } else if (this.data.activeTab === 1) {
        // 删除点赞记录 - 支持批量删除
        const postIds = idList.map(id => {
          // 从id中提取post_id (格式: post_id_app_id)
          return id.split('_')[0];
        });

        console.log('点赞记录原始ID:', idList, '提取的post_ids:', postIds);

        if (postIds.length === 1) {
          // 单个删除
          deleteParams = {
            app_id: util.getAppId(),
            post_id: postIds[0]
          };
          console.log('删除点赞记录(单个)，参数:', deleteParams);
          deletePromise = api.moments.deleteLike(deleteParams);
        } else {
          // 批量删除
          deleteParams = {
            app_id: util.getAppId(),
            post_ids: postIds
          };
          console.log('删除点赞记录(批量)，参数:', deleteParams);
          deletePromise = api.moments.deleteLike(deleteParams);
        }
      } else if (this.data.activeTab === 2) {
        // 删除评论记录 - 支持批量删除
        const commentIds = idList.map(id => {
          // 查找对应的评论获取comment_id
          const comment = list.find(item => item.id === id);
          return comment ? (comment.comment_id || id) : id;
        });

        console.log('评论记录原始ID:', idList, '提取的comment_ids:', commentIds);

        if (commentIds.length === 1) {
          // 单个删除
          deleteParams = {
            comment_id: commentIds[0]
          };
          console.log('删除评论记录(单个)，参数:', deleteParams);
          deletePromise = api.moments.deleteComment(deleteParams);
        } else {
          // 批量删除
          deleteParams = {
            comment_ids: commentIds
          };
          console.log('删除评论记录(批量)，参数:', deleteParams);
          deletePromise = api.moments.deleteComment(deleteParams);
        }
      }

      // 执行删除请求
      const result = await deletePromise;

      console.log('删除接口返回结果:', result);

      wx.hideLoading();

      // 检查删除结果
      if (result && (result.code === 0 || result.success)) {
        wx.showToast({
          title: `删除成功`,
          icon: 'success'
        });

        // 延迟刷新，让用户看到删除结果提示
        setTimeout(() => {
          this.refreshCurrentTabData();
        }, 1000);
      } else {
        console.warn('删除失败，接口返回:', result);
        wx.showToast({
          title: result?.msg || '删除失败，请稍后重试',
          icon: 'none'
        });
      }

    } catch (error) {
      wx.hideLoading();
      console.error('删除操作失败:', error);
      wx.showToast({
        title: '删除失败，请稍后重试',
        icon: 'none'
      });
    }
  },

  /**
   * 刷新当前标签页的数据
   */
  refreshCurrentTabData() {
    // 退出编辑模式
    this.setData({
      isEditMode: false,
      showSelectButtons: false,
      allSelected: false,
      selectedCount: 0
    });

    // 根据当前标签页刷新对应数据
    if (this.data.activeTab === 0) {
      this.setData({
        articleList: [],
        page: 1,
        noMore: false
      });
      this.getArticleList();
    } else if (this.data.activeTab === 1) {
      this.setData({
        likesList: [],
        page: 1,
        noMore: false
      });
      this.getLikes();
    } else if (this.data.activeTab === 2) {
      this.setData({
        commentsList: [],
        page: 1,
        noMore: false
      });
      this.getComments();
    }
  },

  /**
   * 编辑文章（不同于编辑模式切换）
   */
  onEditArticleTap(e) {
    e.stopPropagation(); // 阻止事件冒泡
    const id = e.currentTarget.dataset.id;

    wx.navigateTo({
      url: `/pages/publish/edit?id=${id}`
    });
  },

  /**
   * 切换显示更多操作菜单
   */
  toggleMoreActions(e) {
    const index = e.currentTarget.dataset.index;
    const articleList = this.data.articleList;

    // 关闭所有其他打开的菜单
    articleList.forEach((item, idx) => {
      if (idx !== index && item.showActions) {
        item.showActions = false;
      }
    });

    // 切换当前菜单状态
    articleList[index].showActions = !articleList[index].showActions;

    // 更新是否有打开的菜单状态
    const hasOpenMenu = articleList.some(item => item.showActions);

    this.setData({
      articleList,
      hasOpenMenu
    });
  },

  /**
   * 关闭所有菜单
   */
  closeAllMenus() {
    const articleList = this.data.articleList;

    // 关闭所有菜单
    articleList.forEach(item => {
      item.showActions = false;
    });

    this.setData({
      articleList,
      hasOpenMenu: false
    });
  },

  /**
   * 跳转到草稿箱页面
   */
  goDraftBox() {
    wx.navigateTo({
      url: '/pages/article/draft',
      success: () => {
        console.log('跳转到草稿箱');
      },
      fail: (err) => {
        console.error('跳转草稿箱失败', err);
        wx.showToast({
          title: '页面不存在',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 点击分享按钮
   */
  onShareTap(e) {
    const index = e.currentTarget.dataset.index;
    const item = this.data.articleList[index];

    wx.showActionSheet({
      itemList: ['分享到朋友圈', '分享给朋友', '复制链接'],
      success: (res) => {
        // console.log('分享帖子:', item.id, '，选项:', res.tapIndex);
        wx.showToast({
          title: '分享成功',
          icon: 'success'
        });
      }
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (this.data.noMore || this.data.isLoading) return;

    this.setData({
      page: this.data.page + 1
    });

    // 根据当前激活的选项卡加载不同的内容
    if (this.data.activeTab === 0) {
      // 加载我的帖子
      this.getArticleList(true);
    } else if (this.data.activeTab === 1) {
      // 加载收到的点赞
      this.loadMoreLikes();
    } else if (this.data.activeTab === 2) {
      // 加载收到的评论
      this.loadMoreComments();
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage(e) {
    const item = e.target ? this.data.articleList[e.target.dataset.index] : null;
    if (item) {
      return {
        title: item.ui_vehicle_name,
        path: `/pages/article/detail?id=${item.id}`
      };
    }
    return {
      title: '我的帖子',
      path: '/pages/article/index'
    };
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // console.log('下拉刷新触发');
    this.refreshData();
  },

  /**
   * 强制刷新页面数据
   */
  refreshData() {
    // console.log('执行强制刷新');
    // 重置页面数据
    this.setData({
      page: 1,
      noMore: false,
      isLoading: true
    }, () => {
      // 根据当前激活的选项卡获取新数据
      let refreshPromise;

      if (this.data.activeTab === 0) {
        // 重置文章列表
        this.setData({ articleList: [] });
        refreshPromise = this.getArticleList();
      } else if (this.data.activeTab === 1) {
        // 重置点赞列表
        this.setData({ likesList: [] });
        // 获取点赞列表
        refreshPromise = new Promise((resolve) => {
          this.getLikes();
          resolve();
        });
      } else if (this.data.activeTab === 2) {
        // 重置评论列表
        this.setData({ commentsList: [] });
        refreshPromise = new Promise((resolve) => {
          this.getComments();
          resolve();
        });
      }

      // 完成刷新后停止下拉动画
      if (refreshPromise) {
        refreshPromise.then(() => {
          wx.stopPullDownRefresh();
        }).catch(() => {
          wx.stopPullDownRefresh();
        });
      } else {
        wx.stopPullDownRefresh();
      }
    });
  },

  /**
   * 跳转到文章详情页
   */
  goToArticleDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/moments/detail?id=${id}`
    });
  },

  /**
   * 跳转到询盘帖子详情页
   */
  goToMomentDetail(e) {
    const { id, status } = e.currentTarget.dataset;
    if (status == 1) {
      // 改为允许导航到编辑页面，与status === 3的处理方式相同
      wx.navigateTo({
        url: `/pages/moments/release?id=${id}`
      });
      return;
    } else if (status == 3 || status == 0) {
      wx.navigateTo({
        url: `/pages/moments/release?id=${id}`
      });
      return;
    }
    wx.navigateTo({
      url: `/pages/moments/detail?id=${id}`
    });
  },

  /**
 * 回复评论
 */
  onReplyComment(e) {
    // 获取评论ID
    const id = e.currentTarget.dataset.id;

    // 在当前页面找到对应的评论
    const comment = this.data.commentsList.find(item => item.id == id);
    if (!comment) {
      wx.showToast({
        title: '评论信息不存在',
        icon: 'none'
      });
      return;
    }

    // 检查登录状态
    const res = util.checkLogin();
    if (!res) return false;

    // 设置评论框状态
    this.setData({
      showCommentInput: true,
      commentFocus: true,
      commentPlaceholder: `回复 ${comment.user.name || '用户'}：`,
      replyToComment: {
        id: id,                           // 用于查找评论的ID
        comment_id: comment.comment_id,   // 用于API请求的comment_id
        user: comment.user,
        article: comment.article
      },
      commentValue: ''
    });
  },

  /**
   * 展开/折叠回复列表
   */
  toggleReplies(e) {
    const commentId = e.currentTarget.dataset.id;
    const commentsList = this.data.commentsList;

    // 找到当前评论并切换其展开状态
    const updatedList = commentsList.map(item => {
      if (item.id == commentId) {
        return {
          ...item,
          showReplies: !item.showReplies
        };
      }
      return item;
    });

    this.setData({
      commentsList: updatedList
    });
  },

  /**
   * 取消回复
   */
  cancelReply() {
    this.setData({
      showCommentInput: false,
      commentFocus: false,
      commentPlaceholder: '说点什么...',
      replyToComment: null,
      commentValue: ''
    });
  },

  /**
   * 评论输入事件
   */
  onCommentInput(e) {
    this.setData({
      commentValue: e.detail.value
    });
  },

  /**
   * 提交评论
   */
  async submitComment() {
    if (!this.data.commentValue.trim()) {
      wx.showToast({
        title: '请输入评论内容',
        icon: 'none'
      });
      return;
    }

    // 检查登录状态
    const res = util.checkLogin()
    if (!res) return false;

    try {
      // 构建评论参数
      const params = {
        content: this.data.commentValue,
        app_id: res.app_id,
        is_comment_on_post: 2,
        comment_id: this.data.replyToComment.comment_id || this.data.replyToComment.id
      };

      // 从已保存的replyToComment中获取文章ID
      if (this.data.replyToComment && this.data.replyToComment.article && this.data.replyToComment.article.id) {
        params.vehicle_opportunity_id = this.data.replyToComment.article.id;
      } else {
        // 再尝试从评论列表中查找
        const comment = this.data.commentsList.find(item => item.id == this.data.replyToComment.id);
        if (comment && comment.article && comment.article.id) {
          params.vehicle_opportunity_id = comment.article.id;
        } else {
          wx.showToast({
            title: '无法找到相关文章信息',
            icon: 'none'
          });
          wx.hideLoading();
          return;
        }
      }

      wx.showLoading({
        title: '提交中...',
        mask: true
      });

      // 调试输出参数
      console.log('发送评论参数:', params);

      // 调用添加评论API
      const result = await api.moments.addComment(params);
      console.log('评论返回结果:', result);

      wx.hideLoading();
      wx.showToast({
        title: '回复成功',
        icon: 'success'
      });

      // 清空输入框并隐藏
      this.setData({
        commentValue: '',
        showCommentInput: false,
        replyToComment: null,
        commentPlaceholder: '说点什么...'
      });

      // 重新加载评论列表以显示新回复
      this.getComments();

    } catch (error) {
      wx.hideLoading();
      console.error('提交评论失败:', error);
      wx.showToast({
        title: '评论失败，请稍后重试',
        icon: 'none'
      });
    }
  },

  /**
   * 加载更多点赞数据
   */
  loadMoreLikes() {
    const app_id = util.getAppId();
    this.setData({ isLoading: true });

    // 调用API获取收到的点赞列表，传入页码
    api.moments.getLikeList({
      app_id: app_id,
      page: this.data.page,
      page_size: this.data.pageSize
    }).then(res => {
      console.log('加载更多点赞响应:', res);

      // 处理直接返回数组的情况
      let dataArray = res;

      // 如果返回的是标准格式 {code:0, msg:'成功', data:[...]}
      if (res && typeof res === 'object' && !Array.isArray(res) && res.data) {
        // 检查标准响应格式
        if (res.code !== 0) {
          console.log('接口返回错误码:', res.code, res.msg);
          this.setData({
            noMore: true,
            isLoading: false
          });
          return;
        }
        dataArray = res.data;
      }

      // 确保dataArray是数组
      if (!Array.isArray(dataArray) || dataArray.length === 0) {
        console.log('返回的数据为空或不是数组');
        this.setData({
          noMore: true,
          isLoading: false
        });
        return;
      }

      // 处理接口返回的数据
      const processedLikes = dataArray.map(item => {
        try {
          // 处理日期格式 - 日期字符串格式: "2025-05-22 11:19:33"
          const now = new Date();
          const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime();
          const yesterday = new Date(today - 24 * 60 * 60 * 1000).getTime();

          // 将 "yyyy-MM-dd HH:mm:ss" 转换为 "yyyy/MM/dd HH:mm:ss" 以兼容iOS
          const likeDate = new Date(item.like_time.replace(/-/g, '/'));
          const year = likeDate.getFullYear();
          const month = String(likeDate.getMonth() + 1).padStart(2, '0');
          const day = String(likeDate.getDate()).padStart(2, '0');

          // 转换为前端模板所需的数据格式
          return {
            id: item.post_id + '_' + item.app_id, // 生成唯一ID
            user: {
              id: item.app_id,
              name: item.short_name || item.company_name || '匿名用户',
              avatar: item.avatar || '/images/default-avatar.png'
            },
            article: {
              id: item.post_id,
              title: item.post_title || '无标题',
              type: '商机', // 默认类型
              views: 0 // 默认值
            },
            create_time: item.like_time,
            dateStr: `${year}/${month}/${day}`,
            dateTimeStr: this.formatLikeTime(item.like_time),
            dateSortKey: `${year}${month}${day}`
          };
        } catch (error) {
          console.error('处理点赞项目时出错:', error, item);
          return null;
        }
      }).filter(item => item !== null);

      // 合并现有数据和新数据
      const currentLikes = this.data.likesList || [];
      const allLikes = [...currentLikes, ...processedLikes];

      // 按日期分组处理
      const dateGroups = {};

      // 对处理后的点赞按日期分组
      allLikes.forEach(item => {
        const dateKey = item.dateSortKey;
        if (!dateGroups[dateKey]) {
          dateGroups[dateKey] = [];
        }
        dateGroups[dateKey].push(item);
      });

      // 重组点赞列表，并标记每组中的第一个
      const result = [];
      Object.keys(dateGroups).sort().reverse().forEach(dateKey => {
        const group = dateGroups[dateKey];
        // 标记每组的第一个点赞显示日期
        for (let i = 0; i < group.length; i++) {
          group[i].showDateLabel = (i === 0);
          result.push(group[i]);
        }
      });

      console.log('加载更多后的点赞列表:', result);

      this.setData({
        likesList: result,
        noMore: processedLikes.length < this.data.pageSize,
        isLoading: false
      });
    }).catch(err => {
      console.error('加载更多点赞失败', err);
      this.setData({
        noMore: true,
        isLoading: false
      });
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 格式化点赞时间
   */
  formatLikeTime(dateString) {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime();
    const yesterday = new Date(today - 24 * 60 * 60 * 1000).getTime();

    const likeDate = safeParseDateString(dateString);
    const likeDay = new Date(likeDate.getFullYear(), likeDate.getMonth(), likeDate.getDate()).getTime();

    // 格式化时间为 HH:MM
    const hours = String(likeDate.getHours()).padStart(2, '0');
    const minutes = String(likeDate.getMinutes()).padStart(2, '0');
    const timeStr = `${hours}:${minutes}`;

    // 格式化日期文本
    if (likeDay === today) {
      return `今天 ${timeStr}`;
    } else if (likeDay === yesterday) {
      return `昨天 ${timeStr}`;
    } else {
      const year = likeDate.getFullYear();
      const month = String(likeDate.getMonth() + 1).padStart(2, '0');
      const day = String(likeDate.getDate()).padStart(2, '0');
      return `${month}/${day} ${timeStr}`;
    }
  },

  // 获取点赞列表数据
  fetchLikesList: function () {
    wx.showLoading({
      title: '加载中...',
    });

    // 创建模拟数据以确保显示效果
    const mockLikeData = [{
      id: 'mock-1',
      post_id: '104',
      user: {
        avatar: 'https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/uploads/proof/1751272635627_786.png',
        name: '车友车行'
      },
      dateTimeStr: '昨天 12:55',
      article: {
        id: '104',
        title: '比亚迪 秦PLUS 2023 冠军版 DM-i 120KM领先型'
      }
    }];

    // 首先尝试获取真实数据
    api.moments.getLikeList()
      .then(res => {
        wx.hideLoading();
        if (res && res.data && res.data.list && res.data.list.length > 0) {
          this.setData({
            likesList: this.processList(res.data.list),
            noMore: res.data.list.length < 10
          });
        } else {
          // 如果没有真实数据，使用模拟数据
          this.setData({
            likesList: mockLikeData,
            noMore: true
          });
        }
      })
      .catch(err => {
        console.log('获取点赞列表失败', err);
        wx.hideLoading();
        // 发生错误时显示模拟数据
        this.setData({
          likesList: mockLikeData,
          noMore: true
        });
      });
  },

  /**
   * 加载更多评论数据
   */
  loadMoreComments() {
    const app_id = util.getAppId();
    this.setData({ isLoading: true });

    // 调用API获取收到的评论列表，传入页码
    api.moments.getCommentList({
      app_id: app_id,
      page: this.data.page,
      page_size: this.data.pageSize
    }).then(res => {
      console.log('加载更多评论响应:', res);

      // 处理直接返回数组的情况
      let commentData = res;

      // 如果返回的是标准格式 {code:0, msg:'成功', data:[...]}
      if (res && typeof res === 'object' && res.code === 0 && res.data) {
        commentData = res.data;
      } else if (Array.isArray(res)) {
        commentData = res;
      }

      // 确保commentData是数组
      if (!Array.isArray(commentData) || commentData.length === 0) {
        console.log('返回的评论数据为空或不是数组');
        this.setData({
          commentsList: [],
          page: 1,
          noMore: true,
          isLoading: false
        });
        return;
      }

      // 处理接口返回的数据
      const processedComments = commentData.map(item => {
        try {
          // 处理日期格式 - 日期字符串格式: "2024-05-22 11:19:33"
          const commentDate = safeParseDateString(item.comment_time);

          // 获取当前日期和评论日期
          const now = new Date();
          const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime();
          const yesterday = new Date(today - 24 * 60 * 60 * 1000).getTime();
          const commentDay = new Date(commentDate.getFullYear(), commentDate.getMonth(), commentDate.getDate()).getTime();

          // 格式化时间为 HH:MM
          const hours = String(commentDate.getHours()).padStart(2, '0');
          const minutes = String(commentDate.getMinutes()).padStart(2, '0');
          const timeStr = `${hours}:${minutes}`;

          // 格式化日期文本
          let dateDisplay;
          if (commentDay === today) {
            dateDisplay = `今天 ${timeStr}`;
          } else if (commentDay === yesterday) {
            dateDisplay = `昨天 ${timeStr}`;
          } else {
            const year = commentDate.getFullYear();
            const month = String(commentDate.getMonth() + 1).padStart(2, '0');
            const day = String(commentDate.getDate()).padStart(2, '0');
            dateDisplay = `${year}/${month}/${day}`;
          }

          // 添加日期排序键，用于分组
          const year = commentDate.getFullYear();
          const month = String(commentDate.getMonth() + 1).padStart(2, '0');
          const day = String(commentDate.getDate()).padStart(2, '0');
          const dateSortKey = `${year}${month}${day}`;

          // 转换为前端模板所需的数据格式
          return {
            id: item.id || item.post_id + '_' + item.app_id, // 生成唯一ID
            user: {
              id: item.app_id,
              name: item.short_name || item.company_name || '匿名用户',
              avatar: item.avatar || '/images/default-avatar.png',
              isOfficial: false // 默认非官方账号
            },
            type: 'comment', // 默认为评论帖子
            content: item.content || '无内容',
            targetUser: null, // 默认无目标用户
            article: {
              id: item.post_id,
              title: item.post_title || '无标题'
            },
            create_time: item.comment_time,
            dateDisplay: dateDisplay,
            actionText: '评论了你的帖子',
            dateSortKey: dateSortKey
          };
        } catch (error) {
          console.error('处理评论项目时出错:', error, item);
          return null;
        }
      }).filter(item => item !== null);

      console.log('处理后的评论列表项数:', processedComments.length);
      if (processedComments.length === 0) {
        console.log('处理后没有有效的评论数据');
        this.setData({
          commentsList: [],
          page: 1,
          noMore: true,
          isLoading: false
        });
        return;
      }

      // 合并现有数据和新数据
      const currentComments = this.data.commentsList || [];
      const allComments = [...currentComments, ...processedComments];

      // 按日期分组处理
      const dateGroups = {};

      // 对处理后的评论按日期分组
      allComments.forEach(item => {
        const dateKey = item.dateSortKey;
        if (!dateGroups[dateKey]) {
          dateGroups[dateKey] = [];
        }
        dateGroups[dateKey].push(item);
      });

      // 重组评论列表，并标记每组中的第一个
      const result = [];
      Object.keys(dateGroups).sort().reverse().forEach(dateKey => {
        const group = dateGroups[dateKey];
        // 标记每组的第一个评论显示日期
        for (let i = 0; i < group.length; i++) {
          group[i].showDateLabel = (i === 0);
          result.push(group[i]);
        }
      });

      console.log('加载更多后的评论列表:', result);

      this.setData({
        commentsList: result,
        noMore: processedComments.length < this.data.pageSize,
        isLoading: false
      });
    }).catch(err => {
      console.error('加载更多评论失败', err);
      this.setData({
        noMore: true,
        isLoading: false
      });
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 模拟删除项目（已废弃，保留作为备用）
   */
  simulateDeleteItems(idList, dataKey, list) {
    // 显示加载提示
    wx.showLoading({
      title: '删除中...',
      mask: true
    });

    // 模拟网络请求延迟
    setTimeout(() => {
      // 过滤掉选中的项目
      const filteredList = list.filter(item => !item.selected);

      // 更新数据
      this.setData({
        [dataKey]: filteredList,
        selectedCount: 0,
        allSelected: false
      });

      wx.hideLoading();
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });
    }, 1000);
  },

  /**
   * 安全解析日期字符串，兼容iOS
   * @param {string} dateString - 日期字符串，如 "2025-06-03 16:18:06"
   * @return {Date} 解析后的Date对象
   */
  parseDate(dateString) {
    if (!dateString) return new Date();

    // 处理 "yyyy-MM-dd HH:mm:ss" 格式
    if (typeof dateString === 'string' && dateString.indexOf('-') > 0 && dateString.indexOf(':') > 0) {
      // 将 "yyyy-MM-dd HH:mm:ss" 转换为 "yyyy/MM/dd HH:mm:ss"
      // 这种格式在iOS设备上是支持的
      return new Date(dateString.replace(/-/g, '/'));
    }

    // 其他格式或已经是Date对象，直接返回
    return new Date(dateString);
  },
})