/* pages/article/index.wxss */
page {
  padding: 0;
  margin: 0;
  background-image: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
  background-attachment: fixed;
  /* 固定背景，避免滚动时背景移动 */
  background-size: cover;
  /* 确保背景覆盖整个页面 */
  overflow-x: hidden;
  min-height: 100vh;
  /* 确保最小高度为视口高度 */
  box-sizing: border-box;
}

.container {
  padding: 0;
  margin: 0;
  width: 100%;
  background-color: transparent;
  /* 确保容器背景透明，显示页面背景 */
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  /* 确保最小高度为视口高度 */
}

/* 顶部区域容器 */
.top-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 99;
  background: transparent;
  /* 使背景透明 */
}

/* 渐变背景层 - 修改为与页面相同的渐变并固定位置 */
.gradient-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
  z-index: -1;
  background-attachment: fixed;
  /* 固定背景，确保与页面背景一致 */
  background-position: top;
  /* 从顶部开始 */
  background-size: cover;
}

/* 自定义导航栏样式 */
.custom-navbar {
  position: fixed;
  /* 固定在顶部 */
  top: 0;
  left: 0;
  width: 100%;
  display: flex;
  align-items: center;
  /* 垂直居中对齐子元素 */
  justify-content: center;
  /* 水平居中标题 */
  background-image: none;
  /* 移除背景，使用父容器的渐变背景 */
  z-index: 1000;
  /* 确保在最上层 */
  box-sizing: border-box;
  box-shadow: none;
  /* 移除任何阴影 */
}

.nav-back {
  position: absolute;
  left: 20rpx;
  /* 调整返回按钮位置 */
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  z-index: 10;
}

.nav-title {
  color: #333;
  /* 更新标题颜色 */
  font-size: 34rpx;
  /* 标题字号 */
  font-weight: bold;
  text-align: center;
  position: absolute;
  left: 0;
  right: 0;
  margin: 0 auto;
  width: 60%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  z-index: 5;
}

/* 选项卡导航样式 */
.tab-nav {
  position: fixed;
  width: 100%;
  height: 44px;
  display: flex;
  background-image: none;
  /* 移除背景，使用父容器的渐变背景 */
  border-bottom: none;
  z-index: 100;
  box-shadow: none;
  /* 移除阴影 */
  margin-top: 0;
  /* 恢复正常边距 */
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  font-size: 28rpx;
  color: #666;
  padding: 0 8rpx;
}

.tab-item.active {
  color: #3a88ff;
  font-weight: 500;
}

.tab-line {
  position: absolute;
  bottom: 0;
  width: 40rpx;
  height: 4rpx;
  background-color: #3a88ff;
  border-radius: 2rpx;
}

.tab-edit {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 0 20rpx;
}

.tab-edit .edit-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 4rpx;
}

.tab-edit text {
  font-size: 24rpx;
  color: #666;
}

.mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: transparent;
  z-index: 50;
}

/* 文章列表项相关样式 */
.article-list {
  width: 100%;
  padding: 0 30rpx;
  box-sizing: border-box;
  padding-bottom: 100rpx;
  /* 添加底部内边距，确保内容不贴底 */
  background: transparent;
  /* 确保列表背景透明 */
}

.article-item {
  background-color: #fff;
  border-radius: 16rpx;
  margin: 24rpx 0;
  padding: 20rpx 30rpx 30rpx 30rpx;
  /* 减小上边距为20rpx，其他边距保持30rpx */
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  /* 增强阴影效果 */
  position: relative;
  /* 确保定位上下文 */
  overflow: visible;
  /* 允许内容溢出 */
  transition: all 0.3s ease;
  /* 添加过渡效果 */
}

.article-item:active {
  transform: scale(0.98);
  /* 点击时的缩放效果 */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 日期标记样式 */
.date-tag {
  margin: 16rpx 4rpx -6rpx 4rpx;
  /* 略微调整左右边距，使对齐更精确 */
  padding-left: 4rpx;
  font-size: 24rpx;
  color: #333;
  font-weight: 400;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
  /* 添加文本阴影增强可读性 */
}

.user-info {
  display: none;
  /* 隐藏用户信息区域 */
}

.article-content {
  padding: 0;
  margin-top: 15rpx;
  /* 减小内容区域的上边距 */
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

/* 信息行样式 */
.info-row {
  display: flex;
  flex-wrap: nowrap;
  /* 防止换行 */
  margin-bottom: 8rpx;
  justify-content: space-between;
  /* 两端分布 */
}

.info-left,
.info-right {
  display: flex;
}

.info-left {
  width: 40%;
  /* 左侧区域宽度 */
}

.info-right {
  width: 45%;
  /* 右侧区域宽度 */
}

.info-item {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.right-item {
  margin-right: 0;
  margin-left: 60rpx;
  /* 固定与左侧元素的距离 */
  min-width: 200rpx;
  /* 设置最小宽度使对齐效果更明显 */
}

/* 阅读量样式 */
.view-count {
  font-size: 24rpx;
  color: #999;
  display: flex;
  align-items: center;
}

.view-count image {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

/* 底部区域样式 */
.bottom-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
}

/* 发布状态标签样式 */
.publish-tag {
  width: 100rpx;
  height: 35rpx;
  background-color: #fff;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
  /* 确保标签始终靠右对齐 */
  /* box-shadow: 0 2rpx 6rpx rgba(64, 128, 255, 0.3); */

}

.blue {
  border: 1rpx solid #4080ff;
  color: #4080ff;
}

.yellow {
  color: #F6CB86;
  border: 1rpx solid #F6CB86;
}

.red {
  color: #FF5C25;
  border: 1rpx solid #FF5C25;
}

.gray {
  color: #999999;
  border: 1rpx solid #999999;
}

/* 底部日期样式 */
.bottom-date {
  position: absolute;
  right: 30rpx;
  bottom: 30rpx;
  font-size: 24rpx;
  color: #999;
}

.image-container {
  margin: 20rpx 0;
  width: 100%;
}

/* 水平滚动视图样式 */
.image-scroll {
  white-space: nowrap;
  width: 100%;
}

.image-list {
  display: inline-flex;
  padding: 5rpx 0;
}

.article-image {
  width: 230rpx;
  height: 230rpx;
  border-radius: 8rpx;
  margin-right: 10rpx;
  background-color: #f0f0f0;
  flex-shrink: 0;
}

/* 标签样式 */
.tags {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20rpx;
  margin-bottom: 15rpx;
}

.tag {
  font-size: 24rpx;
  color: #666;
  margin-right: 20rpx;
  background-color: #f5f5f5;
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
}

/* Moments风格的底部操作区 */
.post-footer {
  position: relative;
  margin-top: 20rpx;
  padding-top: 10rpx;
  z-index: 50;
}

.post-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  padding-bottom: 10rpx;
}

.left-stats {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex: 1;
}

.stat-item {
  display: flex;
  align-items: center;
  margin-right: 40rpx;
  background: transparent;
  padding: 0;
  line-height: normal;
  border-radius: 0;
  font-size: inherit;
  max-width: 120rpx;
  overflow: hidden;
}

.share-button {
  display: flex;
  align-items: center;
  margin-right: 40rpx;
  background: transparent;
  padding: 0;
  line-height: normal;
  border-radius: 0;
  font-size: inherit;
}

.share-button::after {
  border: none;
}

.stat-item image {
  width: 40rpx;
  height: 40rpx;
  margin-right: 8rpx;
}

.stat-item text {
  font-size: 26rpx;
  color: #666;
}

.stat-item.liked text {
  color: #ff6b6b;
}

/* 操作按钮样式 */
.operations {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  display: flex;
  gap: 20rpx;
  z-index: 90;
}

.delete-btn,
.edit-btn {
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.5);
}

.delete-btn {
  background-color: rgba(255, 70, 70, 0.7);
}

.edit-btn {
  background-color: rgba(58, 136, 255, 0.7);
}

.no-more {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 30rpx 0;
}

/* 固定底部的草稿箱按钮样式 */
.draft-box-btn {
  position: fixed;
  right: 30rpx;
  bottom: 80rpx;
  width: 100rpx;
  height: 100rpx;
  background-color: #29a0ff;
  /* 修改为明亮的蓝色背景 */
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(41, 160, 255, 0.4);
  /* 匹配蓝色的阴影 */
  z-index: 100;
}

.draft-box-btn image {
  width: 42rpx;
  height: 42rpx;
  margin-bottom: 2rpx;
  filter: brightness(0) invert(1);
  /* 将图标变为白色 */
}

.draft-box-btn text {
  font-size: 22rpx;
  color: #fff;
  /* 文字颜色改为白色 */
}

/* ================ 收到的点赞列表样式 ================ */
.likes-list {
  width: 100%;
  padding: 0 30rpx;
  box-sizing: border-box;
  padding-bottom: 100rpx;
  /* 添加底部内边距，确保内容不贴底 */
  background: transparent;
  /* 确保列表背景透明 */
}

.like-item {
  background-color: #fff;
  border-radius: 16rpx;
  margin: 24rpx 0;
  padding: 20rpx 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

/* 点赞头部布局 */
.like-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8rpx;
}

/* 用户信息样式 */
.like-user-info {
  display: flex;
  align-items: flex-start;
}

.like-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  background-color: #f44336;
  /* 红色背景，与图片中的红色头像一致 */
}

.like-user-details {
  display: flex;
  flex-direction: column;
}

.like-user-name {
  font-size: 28rpx;
  color: #000;
  font-weight: 500;
  margin-bottom: 4rpx;
}

/* 点赞信息样式 */
.like-info {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.like-action {
  font-size: 24rpx;
  color: #666;
  margin-right: 8rpx;
}

.like-time {
  font-size: 24rpx;
  color: #999;
}

/* 被点赞的帖子内容样式 */
.liked-article {
  padding: 0;
  margin-top: 16rpx;
}

.liked-title-card {
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 18rpx 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-top: 4rpx;
}

.liked-title {
  font-size: 30rpx;
  color: #333;
  line-height: 1.4;
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.liked-dots {
  color: #999;
  font-size: 28rpx;
  margin-left: 8rpx;
  flex-shrink: 0;
}

/* 删除旧的不再使用的样式 */
.liked-title::after {
  display: none;
}

/* 评论列表占位样式 */
.placeholder-notice {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  margin-top: 100rpx;
}

/* ================ 收到的评论列表样式 ================ */
.comments-list {
  width: 100%;
  padding: 0 30rpx;
  box-sizing: border-box;
  padding-bottom: 100rpx;
  background: transparent;
}

.comment-item {
  background-color: #fff;
  border-radius: 16rpx;
  margin: 24rpx 0;
  padding: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

/* 评论用户信息样式 */
.comment-user {
  display: flex;
  margin-bottom: 16rpx;
}

.comment-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 16rpx;
  background-color: #f44336;
  /* 红色背景，图片中显示的是红色头像 */
}

.official-avatar {
  border: 2rpx solid #f44336;
}

.comment-user-info {
  flex: 1;
}

.comment-user-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.comment-action-row {
  display: flex;
  align-items: center;
}

.comment-action {
  font-size: 24rpx;
  color: #666;
}

.comment-time {
  font-size: 24rpx;
  color: #999;
  margin-left: 16rpx;
}

/* 评论内容样式 */
.comment-content {
  margin-bottom: 16rpx;
}

.comment-target {
  font-size: 26rpx;
  color: #3a88ff;
  margin-bottom: 6rpx;
}

.comment-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  word-break: break-all;
}

/* 回复列表样式 */
.replies-container {
  margin: 16rpx 0;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  overflow: hidden;
}

.replies-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx;
  font-size: 26rpx;
  color: #666;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.replies-list {
  padding: 8rpx 16rpx;
}

.reply-item {
  padding: 12rpx 0;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.03);
}

.reply-item:last-child {
  border-bottom: none;
}

.reply-user {
  font-size: 24rpx;
  color: #3a88ff;
  margin-bottom: 4rpx;
  font-weight: 500;
}

.reply-content {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 4rpx;
  word-break: break-all;
  line-height: 1.4;
}

.reply-time {
  font-size: 22rpx;
  color: #999;
}

/* 评论所属的帖子样式 */
.comment-article {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 16rpx;
  margin: 12rpx 0 20rpx 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.article-title {
  flex: 1;
  font-size: 26rpx;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.article-more {
  font-size: 24rpx;
  color: #999;
  margin-left: 12rpx;
}

/* 回复按钮样式 */
.reply-btn {
  display: flex;
  align-items: center;
  padding: 6rpx 0;
}

.reply-btn image {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

.reply-btn text {
  font-size: 26rpx;
  color: #666;
}

/* 无评论提示 */
.empty-notice {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  margin-top: 100rpx;
}

/* 评论输入框样式 */
.comment-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.comment-input-container {
  display: flex;
  align-items: center;
  width: 100%;
  height: 80rpx;
  background-color: #f8f9fa;
  border-radius: 40rpx;
  padding: 0 20rpx;
}

.comment-icon {
  width: 44rpx;
  height: 44rpx;
  margin-right: 20rpx;
}

.comment-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
  background: transparent;
}

.cancel-reply {
  margin-left: 20rpx;
  font-size: 28rpx;
  color: #666;
  padding: 0 20rpx;
}

/* 底部留白 */
.bottom-space {
  height: 140rpx;
  width: 100%;
}

/* 选择框样式调整 */
.select-checkbox {
  position: absolute;
  left: 15rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 40rpx;
  height: 40rpx;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox-inner {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 2rpx solid #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

.checkbox-inner.selected {
  background-color: #3a88ff;
  border-color: #3a88ff;
}

/* 特定列表项的选择框位置调整 */
.article-item .select-checkbox {
  left: 15rpx;
  top: 60rpx;
  transform: none;
}

.like-item .select-checkbox {
  left: 15rpx;
  top: 40rpx;
  transform: none;
}

.comment-item .select-checkbox {
  left: 15rpx;
  top: 40rpx;
  transform: none;
}

/* 编辑模式下的内容样式 */
.article-content.edit-mode {
  margin-left: 60rpx;
}

.article-item .article-content {
  transition: margin-left 0.3s ease;
}

/* 底部操作栏样式 */
.bottom-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  box-sizing: border-box;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 200;
}

.select-all-btn {
  display: flex;
  align-items: center;
}

.select-all-checkbox {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.select-all-btn text {
  font-size: 28rpx;
  color: #333;
}

.delete-selected-btn {
  height: 70rpx;
  line-height: 70rpx;
  padding: 0 40rpx;
  background-color: #3a88ff;
  color: #fff;
  font-size: 30rpx;
  border-radius: 35rpx;
}

/* 给点赞和评论列表项添加左边距，为选择框留出空间 */
.like-item,
.comment-item {
  position: relative;
  padding-left: 30rpx;
}

/* 编辑模式下的样式调整 */
.like-user-info,
.comment-user,
.comment-content,
.replies-container,
.comment-article,
.reply-btn {
  transition: margin-left 0.3s ease;
}

.like-item .select-checkbox,
.comment-item .select-checkbox {
  left: 20rpx;
}

/* 底部留白，防止内容被底部操作栏遮挡 */
.article-list,
.likes-list,
.comments-list {
  padding-bottom: 120rpx;
  /* 增加底部内边距 */
}

/* 在编辑模式下隐藏草稿箱按钮 */
.draft-box-btn {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* 左下角标签样式 */
.corner-tag {
  position: fixed;
  left: 30rpx;
  bottom: 140rpx;
  background-color: #4080ff;
  color: #fff;
  font-size: 28rpx;
  padding: 14rpx 26rpx;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  z-index: 100;
}

/* 点赞和评论列表编辑模式下的样式 */
.like-item .like-user-info,
.like-item .liked-article,
.comment-item .comment-user,
.comment-item .comment-content,
.comment-item .replies-container,
.comment-item .comment-article,
.comment-item .reply-btn {
  transition: margin-left 0.3s ease;
}

/* 编辑模式下的内容左边距 */
.isEditMode .like-item .like-user-info,
.isEditMode .like-item .liked-article,
.isEditMode .comment-item .comment-user,
.isEditMode .comment-item .comment-content,
.isEditMode .comment-item .replies-container,
.isEditMode .comment-item .comment-article,
.isEditMode .comment-item .reply-btn {
  margin-left: 50rpx;
}

/* 编辑模式下的选择框位置调整 */
.like-item .select-checkbox,
.comment-item .select-checkbox {
  left: 20rpx;
  top: 30rpx;
  transform: translateY(0);
}

/* 点赞和评论列表编辑模式下的样式 */
.like-item .like-header,
.like-item .liked-article,
.comment-item .comment-user,
.comment-item .comment-content,
.comment-item .replies-container,
.comment-item .comment-article,
.comment-item .reply-btn {
  transition: padding-left 0.3s ease;
}

/* 编辑模式下的内容左边距 */
.isEditMode .like-item .like-header,
.isEditMode .like-item .liked-article,
.isEditMode .comment-item .comment-user,
.isEditMode .comment-item .comment-content,
.isEditMode .comment-item .replies-container,
.isEditMode .comment-item .comment-article,
.isEditMode .comment-item .reply-btn {
  padding-left: 40rpx;
}

/* 编辑模式下重置边距，采用内边距方案 */
.isEditMode .like-item,
.isEditMode .comment-item {
  padding-left: 60rpx;
}