// pages/car/series.js
import api from '../../utils/api'

Page({
    data: {
        brandId: '', // 品牌ID
        brandName: '', // 品牌名称
        seriesList: [], // 车系列表
        loading: true
    },

    onLoad(options) {
        const { brandId, brandName } = options

        if (!brandId) {
            wx.showToast({
                title: '品牌参数错误',
                icon: 'none'
            })
            setTimeout(() => {
                wx.navigateBack()
            }, 1500)
            return
        }

        this.setData({
            brandId,
            brandName
        })

        // 获取车系列表
        this.getSeriesList(brandId)
    },

    // 获取车系列表
    async getSeriesList(brandId) {
        try {
            this.setData({ loading: true })
            // console.log('获取车系列表，品牌ID:', brandId)

            const res = await api.car.getSeriesList({ brand_id: brandId })
            // console.log('车系接口返回数据:', res)

            const series = Array.isArray(res) ? res : (res.data || [])
            // console.log('原始车系数据:', series)

            const processedSeries = series.map(item => ({
                id: item.series_id,
                name: item.series_name,
                letter: item.series_name ? item.series_name.charAt(0).toUpperCase() : '#'
            }))
            // console.log('处理后的车系数据:', processedSeries)

            const groupedSeries = this.groupSeriesByLetter(processedSeries)
            // console.log('分组后的数据:', groupedSeries)

            this.setData({
                seriesList: groupedSeries,
                loading: false
            }, () => {
                // console.log('设置到data后的seriesList:', this.data.seriesList)
                // 添加调试代码
                console.log('当前页面数据:', this.data)
            })
        } catch (error) {
            console.error('获取车系列表失败', error)
            wx.showToast({
                title: '获取车系列表失败',
                icon: 'none'
            })
        }
    },

    // 按首字母分组车系
    groupSeriesByLetter(series) {
        // console.log('开始分组处理:', series)
        const groups = {}
        series.forEach(item => {
            const letter = item.letter
            if (!groups[letter]) {
                groups[letter] = []
            }
            groups[letter].push(item)
        })
        // console.log('分组结果:', groups)

        // 转换为数组格式
        const result = Object.keys(groups).sort().map(letter => ({
            letter,
            list: groups[letter]
        }))
        // console.log('最终分组数组:', result)
        return result
    },

    // 车系选择事件
    onSelectSeries(e) {
        const { id, name } = e.currentTarget.dataset;
        // 跳转到车型选择页面
        wx.navigateTo({
            url: `/pages/car/model?seriesId=${id}&seriesName=${name}&brandName=${this.data.brandName}`
        });
    },

    // 返回上一页
    onBack() {
        wx.navigateBack()
    }
}) 