/* 页面容器 */
.quote-container {
  min-height: 100vh;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

/* 自定义导航栏样式 */
.custom-nav {
  width: 100%;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}

.status-bar {
  width: 100%;
}

.nav-content {
  height: 44px;
  display: flex;
  align-items: center;
  position: relative;
}

.back-icon {
  position: absolute;
  left: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx;
}

.back-icon image {
  width: 40rpx;
  height: 40rpx;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

/* 导航栏占位符 */
.nav-placeholder {
  width: 100%;
}

/* 步骤条样式 */
.step-container {
  margin: 20rpx;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx 20rpx 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid #3B82F6;
  position: relative;
}

.step-list {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  position: relative;
}

/* 步骤连接线 - 底层灰色线 */
.step-list::before {
  content: '';
  position: absolute;
  top: 12rpx;
  left: 12%;
  right: 12%;
  height: 2rpx;
  background-color: #cccccc;
  z-index: 1;
}

/* 步骤连接线 - 进度蓝色线 */
.step-list::after {
  content: '';
  position: absolute;
  top: 12rpx;
  left: 12%;
  width: calc((100% - 24%) * (var(--current-step, 1) - 1) / 4);
  height: 2rpx;
  background-color: #4080ff;
  z-index: 2;
  transition: width 0.3s;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  width: 20%;
  z-index: 3;
}

.step-circle {
  font-size: 24rpx;
  color: #cccccc;
  line-height: 24rpx;
  background-color: #fff;
  position: relative;
}

.step-text {
  font-size: 22rpx;
  color: #999;
  text-align: center;
  margin-top: 10rpx;
  width: 100%;
  white-space: nowrap;
}

/* 激活状态样式 */
.step-item.active .step-circle {
  color: #4080ff;
}

.step-item.active .step-text {
  color: #333333;
}

/* 内容容器 */
.content-container {
  padding-bottom: 150rpx;
}

/* 区块标题样式 */
.section-header {
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 单一卡片样式 - 车辆采购 */
.vehicle-card {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  padding: 0;
  overflow: hidden;
}

/* 车型选择样式 */
.car-select {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  font-size: 28rpx;
}

/* 卡片内分隔线 */
.card-divider {
  height: 1rpx;
  background-color: #f0f0f0;
  margin: 0 20rpx;
}

/* 表单项样式 */
.vehicle-card .form-item {
  display: flex;
  padding: 20rpx 30rpx;
  align-items: center;
}

.vehicle-card .label {
  width: 180rpx;
  font-size: 28rpx;
  color: #333;
}

.vehicle-card .input-box {
  flex: 1;
  font-size: 28rpx;
  height: 60rpx;
  color: #333;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  border: none;
}

/* 区块内容样式 */
.section-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  padding: 24rpx 30rpx;
  font-size: 28rpx;
  border-bottom: 1rpx solid #eee;
  margin: 20rpx 20rpx 0;
  border-radius: 10rpx;
}

/* 车辆信息表单样式 */
.vehicle-form {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 10rpx;
  padding: 10rpx 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.vehicle-form .form-item {
  display: flex;
  padding: 20rpx 0;
  align-items: center;
  border-bottom: 1rpx solid #f5f5f5;
}

.vehicle-form .form-item:last-child {
  border-bottom: none;
}

.vehicle-form .label {
  width: 180rpx;
  font-size: 28rpx;
  color: #333;
}

.vehicle-form .input-box {
  flex: 1;
  font-size: 28rpx;
  height: 60rpx;
  color: #333;
  background-color: transparent;
  border-radius: 8rpx;
  padding: 0 20rpx;
  border: none;
}

/* 箭头样式 */
.arrow {
  width: 16rpx;
  height: 16rpx;
  border-top: 2rpx solid #666666;
  border-right: 2rpx solid #666666;
  transform: rotate(45deg);
}

.arrow-down {
  width: 16rpx;
  height: 16rpx;
  border-top: 2rpx solid #666666;
  border-right: 2rpx solid #666666;
  transform: rotate(135deg);
  margin-left: 10rpx;
}

/* 日期选择区域 */
.date-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  padding: 24rpx 30rpx;
  font-size: 28rpx;
  border-bottom: 1rpx solid #eee;
}

.picker-content {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
  border-radius: 4rpx;
  position: relative;
  padding-right: 200rpx;
}

.picker-content text {
  font-size: 28rpx;
  color: #333;
  margin-left: 20rpx;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.arrow-container {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
}

.picker-content .arrow {
  width: 20rpx;
  height: 20rpx;
  border: solid #666666;
  border-width: 0 2rpx 2rpx 0;
  transform: rotate(45deg);
}

/* 表单区域 */
.price-form, .tax-form {
  background-color: #fff;
  padding: 20rpx 30rpx;
}

.form-item {
  display: flex;
  padding: 20rpx 0;
  align-items: center;
}

.label {
  width: 160rpx;
  font-size: 28rpx;
  color: #333;
}

.input-box {
  flex: 1;
  font-size: 28rpx;
  height: 80rpx;
  color: #333;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 0 30rpx;
  border: none;
}

.form-row {
  display: flex;
}

.form-row .form-item {
  flex: 1;
  padding-right: 20rpx;
}

.form-row .form-item:last-child {
  padding-right: 0;
}

/* 底部导航按钮 */
.bottom-buttons {
  position: fixed;
  bottom: 40rpx;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 30rpx;
  box-sizing: border-box;
  z-index: 100;
}

.prev-btn, .next-btn, .submit-btn, .placeholder-btn {
  width: 45%;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  border-radius: 12rpx;
}

.prev-btn {
  color: #fff;
  background-color: #FF6A00;
}

.prev-btn.active {
  color: #fff;
  background-color: #FF6A00;
}

.prev-btn.disabled {
  color: #999;
  background-color: #f5f5f5;
}

.next-btn {
  color: #fff;
  background-color: #3C7CF9;
}

.submit-btn {
  color: #fff;
  background-color: #3C7CF9;
}

.placeholder-btn {
  opacity: 0;
}

/* 文本域样式 */
.remark-item {
  align-items: flex-start;
  padding-top: 30rpx;
  padding-bottom: 30rpx;
}

.textarea-box {
  flex: 1;
  font-size: 28rpx;
  height: 200rpx;
  width: 100%;
  color: #333;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 20rpx;
  border: none;
}

.select-box {
  position: relative;
  background-color: #f8f8f8;
  border-radius: 4rpx;
  height: 80rpx;
  flex: 1;
  display: flex;
  align-items: center;
}

/* input placeholder样式 */
.input-box::placeholder {
  color: #999;
}

.vehicle-form .input-box::placeholder {
  color: #999;
}

.vehicle-card .input-box::placeholder {
  color: #999;
}
