<!-- 引入登录弹窗模板 -->
<import src="../../templates/loginPopup/loginPopup.wxml" />

<view
  class="container"
  style="{{ pageStyle }}"
>
  <!-- 骨架屏 - 仅在页面加载中且不显示正常加载动画时显示 -->
  <view
    class="skeleton-screen"
    wx:if="{{isLoading && !showLoadingAnimation}}"
  >
    <!-- 骨架自定义导航栏 -->
    <view class="skeleton-custom-nav">
      <view
        class="skeleton-status-bar"
        style="height: {{statusBarHeight}}px;"
      ></view>
      <view class="skeleton-nav-title"></view>
    </view>

    <!-- 骨架车辆列表 -->
    <view class="skeleton-car-list">
      <view
        class="skeleton-car-item"
        wx:for="{{6}}"
        wx:key="index"
      >
        <view class="skeleton-car-image"></view>
        <view class="skeleton-car-info">
          <view class="skeleton-car-title"></view>
          <view class="skeleton-car-params"></view>
          <view class="skeleton-car-price"></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 顶部区域：固定高度容器，用于容纳搜索框或筛选标题 -->
  <view class="fixed-header">
    <!-- 自定义导航栏 -->
       <!-- 自定义导航栏 -->
       <view class="custom-nav">
      <view
        class="status-bar"
        style="height: {{statusBarHeight}}px;"
      ></view>
    <view class="nav-title">
      <view style="display: flex;">
        <view bindtap="navigateBack" style="margin-left:10rpx;">
        <van-icon
        name="arrow-left"
        size="20px"
        color="#333"
      />
      </view>
      <view style="text-align: center;width: 90%;">刷机服务详情</view>
      </view>
    </view>
    </view>
    <view
      class="top-container"
      style="height: {{ topContainerHeight }}px;"
    >
    </view>
  </view>

  <view class="car-list">
    <view class="info-out">
      <view class="info-inside">
        <view class="info-inside-row"> 
          <view class="info-inside-row-title">品牌</view>
          <view>{{brand_name}}</view>
        </view>
        <view class="info-inside-row"> 
          <view class="info-inside-row-title">车系</view>
          <view>{{series_name}}</view>
        </view>
        <view class="info-inside-row"> 
          <view class="info-inside-row-title">车型</view>
          <view>{{ui_vehicle_name}}</view>
        </view>
        <view class="info-inside-row"> 
          <view class="info-inside-row-title">可刷语言</view>
          <view>{{languageText}}</view>
        </view>
        <view class="info-inside-row"> 
          <view class="info-inside-row-title">远程刷机</view>
          <view>{{onlineText}}</view>
        </view>
        <view class="info-inside-row"> 
          <view class="info-inside-row-title">刷机时间</view>
          <view>{{taketime}}</view>
        </view>
        <view class="info-inside-row"> 
          <view class="info-inside-row-title">费用</view>
          <view>{{cost}}</view>
        </view>
        <view class="info-inside-row"> 
          <view class="info-inside-row-title">备注</view>
          <view>{{remark}}</view>
        </view>
      </view>
    </view>
  </view>
  <!-- 联系信息标题 -->
<view class="section-title">
  <view class="section-title-title">联系信息</view>
</view>

<!-- 联系信息卡片 -->
<view class="car-list car-list-2">
    <view class="info-out">
      <view class="info-inside">
        <view class="info-inside-row"> 
          <view class="info-inside-row-title">公司名称</view>
          <view>{{company_name}}</view>
        </view>
        <view class="info-inside-row"> 
          <view class="info-inside-row-title">联系方式</view>
          <view>{{phone}}</view>
        </view>
        <view class="info-inside-row"> 
          <view class="info-inside-row-title">联系邮箱</view>
          <view>{{email}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 引用登录弹窗模板 -->
  <template
    is="loginPopup"
    data="{{ showLoginPopup, loginPopupOptions }}"
  />
</view>