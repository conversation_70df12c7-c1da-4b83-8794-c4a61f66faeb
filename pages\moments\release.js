// pages/moments/release.js
import { areaList } from '@vant/area-data';
import api from '../../utils/api';
import cosUpload from '../../utils/cos-upload';
import config from '../../config';
import util from '../../utils/util';

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 品牌选项
    brands: [],
    brandSelected: '',
    brandId: '', // 存储选中品牌ID
    showBrandPicker: false,

    // 标题
    title: '',

    // 车系选项
    series: [],
    seriesSelected: '',
    seriesId: '', // 存储选中车系ID
    showSeriesPicker: false,

    // 车型选项
    models: [],
    modelSelected: '',
    vehicle_id: '', // 存储选中车型ID
    showModelPicker: false,

    // 截止日期
    deadline: '',

    // 新旧类型
    isNew: true,

    // 过户次数
    transferCount: '',

    // 目标价格
    targetPrice: '',

    // 采购数量
    purchaseCount: '',

    // 是否带保险
    hasInsurance: true,

    // 是否带发票
    hasInvoice: true,
    invoiceType: '0', // 发票类型：无需发票、普通发票、增值税专用发票
    showInvoicePicker: false,
    invoiceName: "请选择发票类型",

    // 交付地点
    areaList: areaList,
    provinces: [], // 省份列表
    cities: {}, // 城市列表，按省份ID分组
    provinceSelected: '', // 选中的省份
    provinceCode: '', // 选中的省份代码
    citySelected: '', // 选中的城市
    cityCode: '', // 选中的城市代码
    locationSelected: '', // 完整的地址（省市组合）
    showProvincePicker: false,
    showCityPicker: false,

    // 配置说明
    description: '',
    descriptionLength: 0,

    // 采购证明图片
    proofImages: [],

    // 新增：多图上传
    multiImages: [], // 多图上传图片列表
    multiImageUrls: [], // 多图上传的相对路径

    statusBarHeight: 20, // 默认状态栏高度

    // 驳回原因相关
    rejectReason: '', // 原始驳回原因
    rejectType: '', // 问题类型
    rejectDetail: '', // 详细原因
    hasRejectReason: false, // 是否有驳回原因

    // 审核状态
    isReviewing: false, // 是否处于审核中状态
    isWithdrawn: false, // 是否已撤回重新编辑
  },

  // 添加滚动标记变量（移到Page对象的直接属性）
  _isScrolling: false,
  _scrollTimer: null,

  // 品牌选择器相关方法
  showBrandPicker() {
    // console.log('显示品牌选择器');
    this.hideAllPickers();

    // 获取点击元素的位置信息
    this.getPickerPositionBySelector('[data-picker-id="brand-picker"]', 'brand', () => {
      this.setData({
        showBrandPicker: true
      });
    });
  },

  selectBrand(e) {
    // console.log('选择品牌事件:', e);
    const value = e.currentTarget.dataset.value;
    const brandId = e.currentTarget.dataset.id;
    // console.log('选择的品牌:', value, brandId);

    // 立即更新状态
    this.setData({
      brandSelected: value,
      brandId: brandId,
      showBrandPicker: false,
      // 清空车系和车型选择
      seriesSelected: '',
      seriesId: '',
      modelSelected: '',
      vehicle_id: '',
      series: [],
      models: []
    });

    // 获取对应的车系列表
    this.getSeriesList(brandId);
  },

  // 车系选择器相关方法
  showSeriesPicker() {
    if (!this.data.brandSelected) {
      wx.showToast({
        title: '请先选择品牌',
        icon: 'none'
      });
      return;
    }
    this.hideAllPickers();

    // 获取点击元素的位置信息
    this.getPickerPositionBySelector('[data-picker-id="series-picker"]', 'series', () => {
      this.setData({
        showSeriesPicker: true
      });
    });
  },

  selectSeries(e) {
    // console.log('选择车系事件:', e);
    const value = e.currentTarget.dataset.value;
    const seriesId = e.currentTarget.dataset.id;
    // console.log('选择的车系:', value, seriesId);

    // 确保seriesId不为undefined
    if (!seriesId) {
      wx.showToast({
        title: '车系数据异常',
        icon: 'none'
      });
      return;
    }

    this.setData({
      seriesSelected: value,
      seriesId: seriesId,
      showSeriesPicker: false,
      // 清空车型选择
      modelSelected: '',
      vehicle_id: '',
      models: []
    });
    // 获取对应的车型列表
    this.getVehicleList(seriesId);
  },

  // 车型选择器相关方法
  showModelPicker() {
    if (!this.data.seriesSelected) {
      wx.showToast({
        title: '请先选择车系',
        icon: 'none'
      });
      return;
    }
    this.hideAllPickers();

    // 获取点击元素的位置信息
    this.getPickerPositionBySelector('[data-picker-id="model-picker"]', 'model', () => {
      this.setData({
        showModelPicker: true
      });
    });
  },

  selectModel(e) {
    // console.log('选择车型事件:', e);
    const value = e.currentTarget.dataset.value;
    const vehicle_id = e.currentTarget.dataset.id;
    // console.log('选择的车型:', value, vehicle_id);

    // 确保vehicle_id不为undefined
    if (!vehicle_id) {
      wx.showToast({
        title: '车型数据异常',
        icon: 'none'
      });
      return;
    }

    this.setData({
      modelSelected: value,
      vehicle_id: vehicle_id,
      showModelPicker: false
    });
  },

  // 解析地区数据
  parseAreaData() {
    const provinces = [];
    const cities = {};

    // 提取省份数据
    Object.keys(this.data.areaList.province_list).forEach(provinceCode => {
      provinces.push({
        code: provinceCode,
        name: this.data.areaList.province_list[provinceCode]
      });
    });

    // 提取城市数据
    Object.keys(this.data.areaList.city_list).forEach(cityCode => {
      const provinceCode = cityCode.substring(0, 2) + '0000';
      if (!cities[provinceCode]) {
        cities[provinceCode] = [];
      }
      cities[provinceCode].push({
        code: cityCode,
        name: this.data.areaList.city_list[cityCode]
      });
    });

    this.setData({
      provinces,
      cities
    });

    // 设置默认选中的地点（广东 广州）
    const defaultProvince = '广东';
    const defaultCity = '广州';
    const defaultProvinceCode = '44000000'; // 广东省代码

    this.setData({
      provinceSelected: defaultProvince,
      provinceCode: defaultProvinceCode,
      citySelected: defaultCity,
      locationSelected: defaultProvince + ' ' + defaultCity
    });
  },

  // 省份选择器相关方法
  showProvincePicker() {
    this.hideAllPickers();

    // 获取位置信息
    this.getPickerPositionBySelector('[data-picker-id="province-picker"]', 'province', () => {
      this.setData({
        showProvincePicker: true
      });
    });
  },

  selectProvince(e) {
    const province = e.currentTarget.dataset.value;
    const provinceCode = e.currentTarget.dataset.code;

    // 先更新选中的省份
    this.setData({
      provinceSelected: province,
      provinceCode: provinceCode,
      showProvincePicker: false
    });

    // 使用一个延时，确保省份选择器先隐藏
    setTimeout(() => {
      // 计算城市选择器的位置（使用与省份相同的位置）
      // 这样城市选择器会出现在省份选择器的位置
      this.setData({
        cityPickerTop: this.data.provincePickerTop,
        cityPickerLeft: this.data.provincePickerLeft,
        cityPickerWidth: this.data.provincePickerWidth,
        showCityPicker: true
      });

      console.log('城市选择器位置:', {
        top: this.data.cityPickerTop,
        left: this.data.cityPickerLeft,
        width: this.data.cityPickerWidth
      });
    }, 50); // 短暂延时，确保UI更新
  },

  // 城市选择器相关方法
  selectCity(e) {
    const city = e.currentTarget.dataset.value;
    const cityCode = e.currentTarget.dataset.code;
    this.setData({
      citySelected: city,
      cityCode: cityCode,
      locationSelected: this.data.provinceSelected + ' ' + city,
      showCityPicker: false
    });
  },

  // 发票类型选择器相关方法
  showInvoicePicker() {
    this.hideAllPickers();

    // 获取位置信息
    this.getPickerPositionBySelector('[data-picker-id="invoice-picker"]', 'invoice', () => {
      this.setData({
        showInvoicePicker: true
      });
    });
  },

  selectInvoiceType(e) {
    const value = e.currentTarget.dataset.value;
    console.log(value)
    // 设置对应的hasInvoice值，除了"无需发票"外都算作"有发票"
    const arr = ['无需发票', '普通发票', '增值税专用发票']
    const hasInvoice = arr[value];

    this.setData({
      invoiceType: value,
      hasInvoice: hasInvoice,
      showInvoicePicker: false,
      invoiceName: hasInvoice
    });
  },

  // 隐藏所有选择器
  hideAllPickers() {
    this.setData({
      showBrandPicker: false,
      showSeriesPicker: false,
      showModelPicker: false,
      showProvincePicker: false,
      showCityPicker: false,
      showInvoicePicker: false
    });
  },

  // 页面点击事件，隐藏所有选择器
  onPageClick() {
    // console.log('页面点击，隐藏选择器');
    // 增加判断，避免iOS下拉时误触发
    if (this._isScrolling) {
      return;
    }
    this.hideAllPickers();
  },

  // 返回上一页
  navigateBack() {
    wx.navigateBack();
  },

  // 阻止事件冒泡，防止点击选择器内部时触发页面点击事件
  stopPropagation(e) {
    console.log('阻止事件冒泡');
    // 阻止事件冒泡，在微信小程序中不需要return false，通过在wxml中使用catchtap来处理
  },

  // 取消选择
  cancelSelection() {
    // console.log('取消选择');
    this.hideAllPickers();
  },

  // 日期选择器变化事件
  bindDateChange(e) {
    this.setData({
      deadline: e.detail.value
    });
  },

  // 切换新旧类型
  toggleIsNew() {
    this.setData({
      isNew: !this.data.isNew
    });
  },

  // 处理单选按钮变化
  onNewTypeChange(e) {
    const value = e.detail.value;
    this.setData({
      isNew: value === 'new'
    });
  },

  // 输入过户次数
  inputTransferCount(e) {
    this.setData({
      transferCount: e.detail.value
    });
  },

  // 输入目标价格
  inputTargetPrice(e) {
    this.setData({
      targetPrice: e.detail.value
    });
  },

  // 输入采购数量
  inputPurchaseCount(e) {
    this.setData({
      purchaseCount: e.detail.value
    });
  },

  // 输入标题
  inputTitle(e) {
    this.setData({
      title: e.detail.value
    });
  },

  // 切换是否带保险
  toggleHasInsurance() {
    this.setData({
      hasInsurance: !this.data.hasInsurance
    });
  },

  // 处理保险单选按钮变化
  onInsuranceChange(e) {
    const value = e.detail.value;
    this.setData({
      hasInsurance: value === 'yes'
    });
  },

  // 输入配置说明
  inputDescription(e) {
    const value = e.detail.value;
    this.setData({
      description: value,
      descriptionLength: value.length
    });
  },

  // 选择图片
  chooseImage() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        if (!res.tempFilePaths || !res.tempFilePaths[0]) {
          wx.showToast({
            title: '未选择图片',
            icon: 'none'
          });
          return;
        }

        const tempFilePath = res.tempFilePaths[0];

        // 清除可能存在的loading提示
        try {
          wx.hideLoading();
        } catch (e) { }

        // 显示新的加载提示
        wx.showLoading({
          title: '上传中...',
          mask: true
        });

        // 使用计时器确保loading能显示至少1秒
        const loadingTimer = setTimeout(() => { }, 1000);

        // 上传图片到腾讯云COS
        cosUpload.uploadFile(tempFilePath, 'proof')
          .then(result => {
            // 确保loading至少显示1秒
            clearTimeout(loadingTimer);

            // 设置数据并隐藏loading
            this.setData({
              proofImages: [this.getFullFileUrl(this.getFilePathSuffix(result.url))]
            }, () => {
              // 在回调中关闭loading，确保数据设置完成
              wx.hideLoading();
              wx.showToast({
                title: '上传成功',
                icon: 'success'
              });
            });
          })
          .catch(error => {
            // 处理错误
            console.error('上传失败:', error);
            clearTimeout(loadingTimer);

            wx.hideLoading();
            wx.showToast({
              title: '上传失败',
              icon: 'none'
            });

            // 清空图片
            this.setData({
              proofImages: []
            });
          });
      },
      fail: (error) => {
        console.error('选择图片失败:', error);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 保存草稿
  saveDraft() {
    // 如果是审核中状态，禁止保存草稿
    if (this.data.isReviewing) {
      wx.showToast({
        title: '审核中，请耐心等候',
        icon: 'none'
      });
      return;
    }

    // 收集当前表单数据
    const draftData = {
      brandSelected: this.data.brandSelected,
      brandId: this.data.brandId,
      seriesSelected: this.data.seriesSelected,
      seriesId: this.data.seriesId,
      modelSelected: this.data.modelSelected,
      vehicle_id: this.data.vehicle_id,
      deadline: this.data.deadline,
      isNew: this.data.isNew,
      transferCount: this.data.transferCount,
      targetPrice: this.data.targetPrice,
      purchaseCount: this.data.purchaseCount,
      hasInsurance: this.data.hasInsurance,
      hasInvoice: this.data.hasInvoice,
      invoiceType: this.data.invoiceType,
      provinceSelected: this.data.provinceSelected,
      provinceCode: this.data.provinceCode,
      citySelected: this.data.citySelected,
      cityCode: this.data.cityCode,
      locationSelected: this.data.locationSelected,
      description: this.data.description,
      proofImages: this.data.proofImages,
      multiImages: this.data.multiImages, // 新增：保存多图上传
      multiImageUrls: this.data.multiImageUrls, // 新增：保存多图上传URL
      saveTime: new Date().getTime(),
      title: this.data.title
    };

    // 保存到本地缓存
    wx.setStorageSync('momentDraft', draftData);

    wx.showToast({
      title: '已保存草稿',
      icon: 'success'
    });
  },

  // 发布信息
  publish() {
    // 如果是审核中状态且未撤回编辑，禁止发布
    if (this.data.isReviewing && !this.data.isWithdrawn) {
      wx.showToast({
        title: '审核中，请耐心等候',
        icon: 'none'
      });
      return;
    }

    // 检查必填项
    if (!this.data.title || !this.data.brandSelected || !this.data.seriesSelected || !this.data.modelSelected ||
      !this.data.targetPrice || !this.data.purchaseCount ||
      !this.data.locationSelected || this.data.proofImages.length === 0) {
      wx.showToast({
        title: '请填写必填项',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: this.data.fromRejected || this.data.isWithdrawn ? '更新中...' : '发布中...',
    });

    // 从缓存中获取用户信息
    const userInfoStorage = wx.getStorageSync('userInfo');
    let app_id = 1; // 默认值

    if (userInfoStorage && userInfoStorage.value && userInfoStorage.value.app_id) {
      app_id = userInfoStorage.value.app_id;
    }

    // 准备发布数据
    const publishData = {
      brand_id: this.data.brandId,
      series_id: this.data.seriesId,
      used_type: this.data.isNew ? 'new' : 'used',
      vehicle_id: this.data.vehicle_id,
      due_date: this.data.deadline,
      price: this.data.targetPrice,
      quantity: this.data.purchaseCount,
      delivery_location: this.data.locationSelected,
      purchase_ref_files: this.data.proofImages.map(img => this.getFilePathSuffix(img)).join(','),
      image_urls: this.data.multiImages.map(img => this.getFilePathSuffix(img)).join(','),
      vehicle_condition: this.data.description || '',
      app_id: app_id,
      title: this.data.title,
      invoice: this.data.invoiceType,
      level: util.getLevel(), // 固定参数

    };

    // 添加过户次数（如果有）
    if (this.data.transferCount !== undefined && this.data.transferCount !== null && this.data.transferCount !== '') {
      publishData.ownership_transfers = this.data.transferCount;
    }

    // 如果是从驳回状态进入编辑或者是撤回后重新编辑，则添加文章ID并使用更新接口
    if ((this.data.fromRejected || this.data.isWithdrawn) && this.data.articleId) {
      publishData.id = this.data.articleId;
      // 添加审核状态为审核中(1)
      publishData.is_review = 1;

      // 调用更新API
      api.moments.update(publishData).then(res => {
        this.handlePublishSuccess(res, "更新成功");
      }).catch(err => {
        this.handlePublishError(err, "更新失败");
      });
    } else {
      // 调用发布API
      api.moments.publish(publishData).then(res => {
        this.handlePublishSuccess(res, "提交成功，等待审核");
      }).catch(err => {
        this.handlePublishError(err, "发布失败");
      });
    }
  },

  // 处理发布/更新成功
  handlePublishSuccess(res, message) {
    wx.hideLoading();

    // 检查返回的状态码，如果不是成功状态(code不为0)则作为错误处理
    if (res.code !== 0 && res.code !== undefined) {
      // 转为错误处理
      this.handlePublishError(res, res.msg || "发布失败");
      return;
    }

    // 如果是从驳回状态更新或从撤回状态更新，提示用户状态已变为审核中
    if ((this.data.fromRejected || this.data.isWithdrawn) && this.data.articleId) {
      message = "更新成功，状态已变为审核中";
    }

    wx.showToast({
      title: message,
      icon: 'success',
      duration: 2000,
      success: () => {
        // 设置一个全局变量标记，表示需要刷新列表数据
        getApp().globalData = getApp().globalData || {};
        getApp().globalData.needRefreshMomentsList = true;
        getApp().globalData.needRefreshArticleList = true;

        // 清除草稿数据
        wx.removeStorageSync('momentDraft');

        // 延迟跳转到"我的帖子"页面
        setTimeout(() => {
          wx.navigateTo({
            url: '/pages/article/index'
          });
        }, 2000);
      }
    });
    console.log(message + "，返回数据:", res);
  },

  // 处理发布/更新失败
  handlePublishError(err, message) {
    wx.hideLoading();

    // 获取错误信息，优先使用API返回的消息
    let errorMessage = message;
    if (err && err.msg) {
      errorMessage = err.msg;
    }

    // 特定错误消息的个性化处理
    if (errorMessage.includes('超过会员每日发布限制')) {
      errorMessage = '超过会员每日发布限制，请明天再试';
    } else if (errorMessage.includes('系统繁忙')) {
      errorMessage = '系统繁忙，请稍后再试';
    } else {
      errorMessage += '，请重试';
    }

    // 显示错误提示，但不跳转页面
    wx.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 2000
    });

    console.error("发布/更新失败:", err);
  },

  // 撤回重新编辑
  withdrawForEditing() {
    // 检查是否有文章ID
    if (!this.data.articleId) {
      wx.showToast({
        title: '帖子ID不存在',
        icon: 'none'
      });
      return;
    }

    // 显示加载提示
    wx.showLoading({
      title: '撤回中',
      mask: true
    });

    // 调用撤回接口
    api.moments.withdraw({
      id: this.data.articleId
    }).then(res => {
      wx.hideLoading();

      // 设置状态为已撤回
      this.setData({
        isWithdrawn: true,
        isReviewing: false
      });

      wx.showToast({
        title: '已撤回，可以编辑',
        icon: 'success'
      });
    }).catch(err => {
      wx.hideLoading();
      console.error('撤回帖子失败:', err);

      wx.showToast({
        title: '撤回失败，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 解析地区数据
    this.parseAreaData();
    // 获取品牌列表
    this.getBrandList();

    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });

    // 检查是否有ID参数（表示从驳回状态进入编辑页面）
    if (options.id) {
      // 保存文章ID
      this.setData({
        articleId: options.id,
        fromRejected: true
      });

      // 加载详情数据
      this.loadArticleDetail(options.id);
    } else {
      // 尝试恢复草稿
      this.loadDraft();
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 添加页面点击事件监听
    this.pageListener = () => {
      this.hideAllPickers();
    };
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  // 处理图片加载错误
  imageError(e) {
    console.error('图片加载失败:', e);
    wx.showToast({
      title: '图片加载失败，请重新上传',
      icon: 'none'
    });
    // 清空已上传的图片路径
    this.setData({
      proofImages: []
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 获取文件路径后缀
  getFilePathSuffix(path) {
    if (!path) return '';
    // 检查是否已经包含完整路径
    const baseUrl = config.COS_CONFIG.url + config.COS_CONFIG.path;
    if (path.startsWith('http')) {
      // 从URL中提取路径
      const parts = path.split(baseUrl);
      if (parts.length > 1) {
        // 确保路径以 /uploads 开头
        return '/uploads/' + parts[1];
      }
      return path;
    } else if (path.includes('uploads/')) {
      // 如果已经包含 uploads/ 但不是完整URL
      const parts = path.split('uploads/');
      return '/uploads/' + parts[1];
    } else {
      // 如果是相对路径，直接添加前缀
      return '/uploads/' + path;
    }
  },

  // 获取完整的文件URL
  getFullFileUrl(pathSuffix) {
    if (!pathSuffix) return '';
    if (pathSuffix.indexOf('https://') === 0 || pathSuffix.indexOf('http://') === 0) {
      return pathSuffix;
    }
    // 移除开头的 /uploads 如果存在
    const cleanPath = pathSuffix.startsWith('/uploads/')
      ? pathSuffix.substring(9)
      : (pathSuffix.startsWith('uploads/') ? pathSuffix.substring(8) : pathSuffix);
    return config.COS_CONFIG.url + config.COS_CONFIG.path + cleanPath;
  },

  // 获取品牌列表
  getBrandList() {
    wx.showLoading({
      title: '加载中',
    });
    api.car.getBrandList().then(res => {
      wx.hideLoading();
      // console.log('品牌数据:', res);

      // 直接处理返回的数据，无需判断code
      if (res && Array.isArray(res)) {
        // console.log('品牌数据是一个数组，长度:', res.length);
        if (res.length > 0) {
          console.log('品牌数据第一项:', res[0]);
        }

        // 设置数据到状态
        this.setData({
          brands: res
        }, () => {
          console.log('设置后的品牌数据长度:', this.data.brands.length);
        });
      } else {
        console.error('品牌数据不是数组格式:', res);
        wx.showToast({
          title: '获取品牌列表失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '获取品牌列表失败',
        icon: 'none'
      });
      console.error('获取品牌列表失败', err);
    });
  },

  // 获取车系列表
  getSeriesList(brandId) {
    if (!brandId) {
      console.error('未提供品牌ID');
      return;
    }

    // console.log('传递的品牌ID:', brandId);

    wx.showLoading({
      title: '加载中',
    });
    api.car.getSeriesList({
      brand_id: brandId  // 确保参数名称与后端API要求一致
    }).then(res => {
      wx.hideLoading();
      // console.log('车系数据:', res);

      // 直接处理返回的数据，无需判断code
      if (res && Array.isArray(res)) {
        if (res.length > 0) {
          console.log('车系数据第一项:', res[0]);
        }
        this.setData({
          series: res
        });
      } else {
        wx.showToast({
          title: '获取车系列表失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '获取车系列表失败',
        icon: 'none'
      });
      console.error('获取车系列表失败', err);
    });
  },

  // 获取车型列表
  getVehicleList(seriesId) {
    if (!seriesId) {
      console.error('未提供车系ID');
      return;
    }

    // console.log('传递的车系ID:', seriesId);

    wx.showLoading({
      title: '加载中',
    });
    api.car.getVehicleList({
      series_id: seriesId  // 确保参数名称与后端API要求一致
    }).then(res => {
      wx.hideLoading();
      // console.log('车型数据:', res);

      // 直接处理返回的数据，无需判断code
      if (res && Array.isArray(res)) {
        if (res.length > 0) {
          console.log('车型数据第一项:', res[0]);
        }
        this.setData({
          models: res
        });
      } else {
        wx.showToast({
          title: '获取车型列表失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '获取车型列表失败',
        icon: 'none'
      });
      console.error('获取车型列表失败', err);
    });
  },

  // 加载草稿数据
  loadDraft() {
    const draftData = wx.getStorageSync('momentDraft');
    if (!draftData) return;

    // 提示用户是否要恢复草稿
    wx.showModal({
      title: '提示',
      content: '检测到有未完成的草稿，是否恢复？',
      success: (res) => {
        if (res.confirm) {
          // 恢复草稿数据
          this.setData({
            title: draftData.title || '',
            brandSelected: draftData.brandSelected || '',
            brandId: draftData.brandId || '',
            seriesSelected: draftData.seriesSelected || '',
            seriesId: draftData.seriesId || '',
            modelSelected: draftData.modelSelected || '',
            vehicle_id: draftData.vehicle_id || '',
            deadline: draftData.deadline || '',
            isNew: draftData.isNew !== undefined ? draftData.isNew : true,
            transferCount: draftData.transferCount || '',
            targetPrice: draftData.targetPrice || '',
            purchaseCount: draftData.purchaseCount || '',
            hasInsurance: draftData.hasInsurance !== undefined ? draftData.hasInsurance : true,
            invoiceName: draftData.invoiceName,
            invoiceType: draftData.invoiceType,
            provinceSelected: draftData.provinceSelected || this.data.provinceSelected,
            provinceCode: draftData.provinceCode || this.data.provinceCode,
            citySelected: draftData.citySelected || this.data.citySelected,
            cityCode: draftData.cityCode || this.data.cityCode,
            locationSelected: draftData.locationSelected || this.data.locationSelected,
            description: draftData.description || '',
            descriptionLength: draftData.description ? draftData.description.length : 0,
            proofImages: draftData.proofImages || [],
            multiImages: draftData.multiImages || [], // 新增：恢复多图上传
            multiImageUrls: draftData.multiImageUrls || [] // 新增：恢复多图上传URL
          });

          // 如果有车系和车型信息，需要加载相应的数据列表
          if (draftData.brandId) {
            this.getSeriesList(draftData.brandId);
          }

          if (draftData.seriesId) {
            this.getVehicleList(draftData.seriesId);
          }
        } else {
          // 用户不想恢复，清除草稿
          wx.removeStorageSync('momentDraft');
        }
      }
    });
  },

  // 新增：选择多图
  chooseMultiImages() {
    const that = this;
    wx.chooseImage({
      count: 3 - that.data.multiImages.length, // 最多可以选择的图片张数
      sizeType: ['compressed'], // 可以指定是原图还是压缩图
      sourceType: ['album', 'camera'], // 相册和相机
      success: function (res) {
        // 处理多图上传
        const tempFilePaths = res.tempFilePaths;

        wx.showLoading({
          title: '上传中...',
          mask: true
        });

        // 上传图片
        const uploadPromises = tempFilePaths.map(filePath => {
          return cosUpload.uploadFile(filePath, 'proof');
        });

        Promise.all(uploadPromises)
          .then(results => {
            const newImages = results.map(result =>
              that.getFullFileUrl(that.getFilePathSuffix(result.url))
            );
            const newUrls = results.map(result =>
              that.getFilePathSuffix(result.url)
            );
            that.setData({
              multiImages: [...that.data.multiImages, ...newImages],
              multiImageUrls: [...that.data.multiImageUrls, ...newUrls]
            });
            wx.hideLoading();
            wx.showToast({
              title: '上传成功',
              icon: 'success'
            });
          })
          .catch(error => {
            console.error('上传失败:', error);
            wx.hideLoading();
            wx.showToast({
              title: '图片上传失败',
              icon: 'none'
            });
          });
      }
    });
  },

  // 新增：删除多图中的某张图片
  deleteMultiImage(e) {
    const index = e.currentTarget.dataset.index;
    const multiImages = this.data.multiImages;
    const multiImageUrls = this.data.multiImageUrls;
    multiImages.splice(index, 1);
    multiImageUrls.splice(index, 1);
    this.setData({
      multiImages: multiImages,
      multiImageUrls: multiImageUrls
    });
  },

  // 新增：处理多图加载错误
  multiImageError(e) {
    console.error('多图加载失败:', e);
    const index = e.currentTarget.dataset.index;
    if (index !== undefined) {
      // 如果有索引，只移除对应图片
      const multiImages = [...this.data.multiImages];
      const multiImageUrls = [...this.data.multiImageUrls];
      multiImages.splice(index, 1);
      multiImageUrls.splice(index, 1);
      this.setData({
        multiImages: multiImages,
        multiImageUrls: multiImageUrls
      });
      wx.showToast({
        title: '图片加载失败，已移除',
        icon: 'none'
      });
    }
  },

  /**
   * 加载文章详情
   */
  loadArticleDetail(id) {
    wx.showLoading({
      title: '加载中',
      mask: true
    });

    // 先获取文章详情
    api.moments.getDetail({
      id: id,
      app_id: util.getAppId()
    }).then(detail => {
      console.log('获取驳回文章详情:', detail);

      if (!detail) {
        throw new Error('获取详情数据为空');
      }

      // 处理驳回原因
      let rejectType = '';
      let rejectDetail = '';
      let hasRejectReason = false;
      let isReviewing = false;

      // 判断是否为审核中状态
      if (detail.is_review == 1) {
        isReviewing = true;
        hasRejectReason = true;
        rejectType = '审核不通过';
        rejectDetail = '正在审核中，请耐心等候...';
      } else if (detail.reason && detail.is_review == 3) {
        hasRejectReason = true;
        // 同时支持中文冒号"："和英文冒号":"作为分隔符
        let reasonParts;
        if (detail.reason.includes('：')) {
          reasonParts = detail.reason.split('：');
        } else if (detail.reason.includes(':')) {
          reasonParts = detail.reason.split(':');
        } else {
          reasonParts = [detail.reason]; // 没有分隔符的情况
        }

        if (reasonParts.length >= 2) {
          rejectType = reasonParts[0];
          rejectDetail = reasonParts.slice(1).join(reasonParts[0].includes('：') ? '：' : ':'); // 合并可能包含多个分隔符的后续部分
        } else {
          // 如果没有分隔符，则整个内容作为详细原因
          rejectType = '审核不通过';
          rejectDetail = detail.reason;
        }
      }

      // 设置基本信息（不依赖品牌和车系的字段）
      this.setData({
        title: detail.title || detail.ui_vehicle_name || '',
        brandId: detail.brand_id || '',
        seriesId: detail.series_id || '',
        modelSelected: detail.ui_vehicle_name || '',
        vehicle_id: detail.vehicle_id || '',
        isNew: detail.used_type === 'new',
        transferCount: detail.ownership_transfers || '',
        targetPrice: detail.price || '',
        purchaseCount: detail.quantity || '',
        hasInsurance: detail.has_insurance == 1,
        invoiceType: detail.invoice || '0',
        locationSelected: detail.delivery_location || '',
        description: detail.vehicle_condition || '',
        descriptionLength: (detail.vehicle_condition || '').length,
        proofImages: detail.purchase_ref_files ? this.processImageUrls(detail.purchase_ref_files) : [],
        multiImages: detail.image_urls ? this.processImageUrls(detail.image_urls) : [],
        // 设置驳回原因相关字段
        rejectReason: detail.reason || '',
        rejectType: rejectType,
        rejectDetail: rejectDetail,
        hasRejectReason: hasRejectReason,
        isReviewing: isReviewing,
        isWithdrawn: false // 初始时未撤回编辑
      });

      // 设置发票名称
      this.setInvoiceName(detail.invoice || '0');

      // 保存详情数据，后续会用到
      this.articleDetail = detail;

      // 更新加载提示
      wx.showLoading({
        title: '加载品牌数据',
        mask: true
      });

      // 获取品牌列表
      return api.car.getBrandList();
    })
      .then(brands => {
        // 保存品牌列表
        this.setData({
          brands: brands || []
        });

        // 查找对应的品牌名称
        if (this.articleDetail && this.articleDetail.brand_id && brands && brands.length > 0) {
          const brand = brands.find(item => item.id == this.articleDetail.brand_id);
          if (brand) {
            this.setData({
              brandSelected: brand.brand_name || ''
            });
            console.log('找到品牌:', brand.brand_name);
          } else {
            console.log('未找到匹配的品牌名称，brand_id:', this.articleDetail.brand_id);
          }
        }

        // 如果有品牌ID，获取车系列表
        if (this.articleDetail && this.articleDetail.brand_id) {
          // 更新加载提示
          wx.showLoading({
            title: '加载车系数据',
            mask: true
          });

          return api.car.getSeriesList({
            brand_id: this.articleDetail.brand_id
          });
        }

        return Promise.resolve([]);
      })
      .then(seriesList => {
        // 保存车系列表
        this.setData({
          series: seriesList || []
        });

        // 查找对应的车系名称
        if (this.articleDetail && this.articleDetail.series_id && seriesList && seriesList.length > 0) {
          const series = seriesList.find(item => item.series_id == this.articleDetail.series_id);
          if (series) {
            this.setData({
              seriesSelected: series.series_name || ''
            });
            console.log('找到车系:', series.series_name);
          } else {
            console.log('未找到匹配的车系名称，series_id:', this.articleDetail.series_id);
          }
        }

        // 如果有车系ID，获取车型列表
        if (this.articleDetail && this.articleDetail.series_id) {
          // 更新加载提示
          wx.showLoading({
            title: '加载车型数据',
            mask: true
          });

          return api.car.getVehicleList({
            series_id: this.articleDetail.series_id
          });
        }

        return Promise.resolve([]);
      })
      .then(vehicleList => {
        // 保存车型列表
        this.setData({
          models: vehicleList || []
        });

        // 完成所有数据加载
        wx.hideLoading();
        wx.showToast({
          title: '加载完成',
          icon: 'success'
        });
      })
      .catch(err => {
        wx.hideLoading();
        console.error('加载文章详情失败:', err);
        wx.showToast({
          title: '加载数据失败，请重试',
          icon: 'none'
        });
      });
  },

  /**
   * 处理图片URL
   */
  processImageUrls(urls) {
    if (!urls) return [];

    // 如果是字符串，尝试解析为数组
    if (typeof urls === 'string') {
      try {
        urls = JSON.parse(urls);
      } catch (e) {
        // 尝试作为逗号分隔的字符串处理
        urls = urls.split(',');
      }
    }

    // 确保是数组
    if (!Array.isArray(urls)) {
      return [];
    }

    // 处理每个URL，确保是完整路径
    return urls.map(url => this.getFullFileUrl(url));
  },

  /**
   * 设置发票名称
   */
  setInvoiceName(type) {
    let invoiceName = "请选择发票类型";
    if (type === '1') {
      invoiceName = "普通发票";
    } else if (type === '2') {
      invoiceName = "增值税专用发票";
    } else if (type === '0') {
      invoiceName = "无需发票";
    }

    this.setData({
      invoiceName: invoiceName
    });
  },

  // 处理页面滚动开始
  onPageScroll() {
    // 设置滚动标记
    this._isScrolling = true;

    // 清除之前的定时器
    if (this._scrollTimer) {
      clearTimeout(this._scrollTimer);
    }

    // 设置新的定时器，滚动结束后重置标记
    this._scrollTimer = setTimeout(() => {
      this._isScrolling = false;
    }, 150); // 150ms后认为滚动结束
  },

  // 获取选择器位置
  getPickerPosition(pickerType, callback) {
    const selector = `.form-item:nth-child(${pickerType === 'brand' ? 2 : pickerType === 'series' ? 3 : 4}) .picker-value`;
    this.getPickerPositionBySelector(selector, pickerType, callback);
  },

  // 通过选择器获取元素位置
  getPickerPositionBySelector(selector, pickerType, callback) {
    const query = wx.createSelectorQuery();
    query.select(selector).boundingClientRect();
    query.selectViewport().boundingClientRect();
    query.selectViewport().scrollOffset();
    query.exec((res) => {
      if (res && res[0]) {
        const rect = res[0]; // 选择器元素的大小和位置
        const viewport = res[1]; // 视口大小
        const scrollTop = res[2] ? res[2].scrollTop : 0;

        // 计算选择器应该显示的位置 - 确保正好在触发元素下方
        const top = rect.top + rect.height + 2; // 2px是一个小间隔
        const left = rect.left;

        // 获取系统信息以计算rpx与px的换算
        const systemInfo = wx.getSystemInfoSync();

        // 计算下拉菜单最大高度
        const maxHeight = 300; // 300rpx
        const bottomSpace = viewport.height - top;

        // 添加额外的日志
        console.log(`元素位置: ${JSON.stringify({
          rect,
          viewportHeight: viewport.height,
          scrollTop,
          calculatedTop: top,
          calculatedLeft: left
        })}`);

        // 设置位置数据，确保不超出屏幕
        this.setData({
          [`${pickerType}PickerTop`]: top,
          [`${pickerType}PickerLeft`]: Math.max(10, left), // 至少距离左边10px
          [`${pickerType}PickerWidth`]: Math.min(rect.width, viewport.width - 20) // 确保不超出屏幕宽度
        }, callback);
      } else {
        // 如果无法获取位置信息，使用默认值
        console.error(`无法获取${pickerType}选择器位置，选择器:`, selector);
        this.setData({
          [`${pickerType}PickerTop`]: 200,
          [`${pickerType}PickerLeft`]: 30,
          [`${pickerType}PickerWidth`]: wx.getSystemInfoSync().windowWidth - 60
        }, callback);
      }
    });
  },
})