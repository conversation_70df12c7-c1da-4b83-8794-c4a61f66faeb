/* pages/supply_chain_services/index.wxss */

/* 页面容器 */
.container {
    min-height: 100vh;
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    position: relative;
}

/* 渐变背景 */
.gradient-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
    background-attachment: fixed;
    /* 固定背景，避免滚动时背景移动 */
    z-index: -1;
}

/* 自定义导航栏 */
.custom-navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 100;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 100%);
    /* 移除box-shadow以消除横线效果 */
}

.status-bar {
    width: 100%;
}

.navbar-content {
    display: flex;
    height: 44px;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
}

.nav-back {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 44px;
    z-index: 5;
    margin-left: -8rpx;
}

.nav-title {
    flex: 1;
    text-align: center;
    font-size: 34rpx;
    font-weight: 500;
    color: #333;
}

.nav-placeholder {
    width: 40px;
    height: 44px;
}

/* 内容区域 */
.content {
    width: 100%;
    padding: 10rpx;
    box-sizing: border-box;
    position: relative;
}

/* 服务内容区域 */
.service-content {
    padding: 10rpx;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: 30rpx;
}

/* 服务卡片通用样式 */
.service-card {
    width: 100%;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    background-color: #fff;
    display: flex;
    flex-direction: column;
}

/* 卡片主题颜色 */
.financial-card .card-image-container {
    background-color: rgba(255, 237, 209, 0.8);
}

.channel-card .card-image-container,
.car-card .card-image-container {
    background-color: rgba(220, 236, 255, 0.8);
}

/* 卡片图片容器 */
.card-image-container {
    position: relative;
    height: 240rpx;
    display: flex;
    padding: 0;
    box-sizing: border-box;
    overflow: hidden;
    margin-bottom: 0;
    /* 确保底部没有多余边距 */
}

/* 卡片图片 */
.card-image {
    width: 100%;
    height: 100%;
    position: relative;
    object-fit: cover;
}

/* 卡片标题区域 */
.card-title {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-top: 20rpx;
}

.title-cn {
    font-size: 40rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
}

.title-en {
    font-size: 24rpx;
    color: #666;
    margin-bottom: 20rpx;
}

/* 卡片标签 */
.card-tags {
    display: flex;
    gap: 10rpx;
    margin-top: 10rpx;
}

.tag {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    font-size: 22rpx;
    color: #666;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 6rpx;
    padding: 4rpx 12rpx;
}

/* 卡片内容区域 */
.card-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 30rpx;
    padding-top: 10rpx;
    background-color: #fff;
}

.card-desc {
    display: flex;
    flex-direction: column;
}

/* 卡片标题 */
.card-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 8rpx;
}

.desc-text,
.desc-price {
    font-size: 26rpx;
    color: #666;
    margin-bottom: 6rpx;
}

.desc-price {
    color: #FF4D4F;
    font-weight: bold;
    font-size: 28rpx;
}

.desc-extra {
    font-size: 24rpx;
    color: #999;
}

/* 卡片按钮 */
.card-button {
    width: 160rpx;
    height: 60rpx;
    background-color: #1890ff;
    color: #FCFEFF;
    font-family: "Source Han Sans";
    font-size: 28rpx;
    font-weight: normal;
    line-height: normal;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    letter-spacing: 0.04em;
    border-radius: 8rpx;
}