// 修改引入方式
import { areaList } from '@vant/area-data';
// 导入 API 模块
import api from '../../utils/api';

import share from '../../utils/share';  // 确保导入 分享
import util from '../../utils/util';  // 导入util模块

Page({
    behaviors: [share], //分享设置
    data: {
        //处理分享页面 统一写
        shareData: {
            title: '找车侠',
            path: '/pages/moments/index',
            isDetailPage: false, // 标记是否详情页
        },
        searchValue: '',
        carList: [], // 改为空数组，这样就不会显示预设数据
        isDropdownOpen: false,
        pageStyle: '',
        currentDropdownIndex: null,
        currentFilter: '', // 当前选中的筛选类型
        showFilterContent: false, // 是否显示筛选内容
        topContainerHeight: 110, // 顶部容器高度，单位px
        // 添加分页相关数据
        currentPage: 1,
        pageSize: 15,
        hasMoreData: true,
        isLoading: true, // 默认为加载中状态，以显示骨架屏
        showLoadingAnimation: false, // 是否显示常规加载动画，默认不显示，使用骨架屏替代
        hideFilterBar: false,
        detailId: '',
        detailTitle: '',
        statusBarHeight: 20, // 默认状态栏高度
        forceShowSkeleton: false, // 默认不强制显示骨架屏
    },
    async onLoad() {
        // 设置页面正在加载，显示骨架屏
        this.setData({
            isLoading: true,
            showLoadingAnimation: false
        });

        // 获取顶部容器高度
        const query = wx.createSelectorQuery();
        query.select('.search-bar').boundingClientRect();
        query.exec((res) => {
            if (res && res[0]) {
                this.setData({
                    topContainerHeight: res[0].height
                });
            }
        });

        // 获取系统状态栏高度
        wx.getSystemInfo({
            success: (res) => {
                this.setData({
                    statusBarHeight: res.statusBarHeight
                });
                // console.log('状态栏高度：', res.statusBarHeight);
            }
        });

        try {
            // 获取车辆列表
            await this.getList();
        } catch (error) {
            console.error('数据加载失败:', error);
        } finally {
            // 无论是否成功，都在一定延迟后隐藏骨架屏
            setTimeout(() => {
                this.setData({
                    isLoading: false
                });
            }, 3000); // 调整为800毫秒，与原代码保持一致
        }

        // 在 onLoad 或 onReady 函数中
        wx.getSystemInfo({
            success: (res) => {
                const topContainerHeight = this.data.topContainerHeight || 50;
                const filterMenuHeight = 44; // 根据您的实际筛选菜单高度调整

                // 设置 CSS 变量
                this.setData({
                    headerStyle: `--top-container-height: ${topContainerHeight}px; --filter-menu-height: ${filterMenuHeight}px;`
                });

                // 更新页面样式
                this.setData({
                    pageStyle: this.data.pageStyle + this.data.headerStyle
                });
            }
        });
    },
    onShow() {
        // 检查是否需要刷新列表数据
        const app = getApp();
        if (app && app.globalData && app.globalData.needRefreshMomentsList) {
            // 显示骨架屏
            this.setData({
                isLoading: true,
                showLoadingAnimation: false
            });

            // 重置标记
            app.globalData.needRefreshMomentsList = false;
            // 重新加载第一页数据
            this.setData({
                currentPage: 1,
                hasMoreData: true,
                carList: [] // 清空现有数据
            });

            this.getList({ page: 1 }).finally(() => {
                // 数据加载完成后，延迟隐藏骨架屏
                setTimeout(() => {
                    this.setData({
                        isLoading: false
                    });
                }, 800);
            });
        }
    },
    // 处理搜索输入
    onSearchInput(e) {
        this.setData({
            searchValue: e.detail.value
        });
    },
    // 搜索事件
    onSearch(e) {
        // 显示骨架屏
        this.setData({
            isLoading: true,
            showLoadingAnimation: false
        });

        const keyword = typeof e.detail === 'string' ? e.detail : this.data.searchValue;
        // 更新搜索关键词到状态
        this.setData({
            searchValue: keyword || '',
            carList: [] // 清空现有列表
        });

        // 调用 getList 进行搜索
        this.getList().finally(() => {
            // 数据加载完成后，延迟隐藏骨架屏
            setTimeout(() => {
                this.setData({
                    isLoading: false
                });
            }, 800);
        });
    },
    // 点击事件
    onCarItemTap(e) {
        const id = e.currentTarget.dataset.id;

        // 获取用户信息
        const userInfo = util.getUserInfo();

        // 检查登录状态
        if (userInfo && userInfo.app_id) {
            // 用户已登录，跳转到侠友圈详情页
            wx.navigateTo({
                url: `/pages/moments/detail?id=${id}`
            });
        } else {
            // 用户未登录，直接跳转到登录页面
            wx.navigateTo({
                url: '/pages/login/index'
            });
        }
    },
    // 切换内容展开/收起状态
    toggleContent(e) {
        // 阻止事件冒泡，防止触发整个卡片的点击事件
        // e.stopPropagation();

        const id = e.currentTarget.dataset.id;
        const carList = this.data.carList;
        const index = carList.findIndex(item => item.id === id);

        if (index !== -1) {
            // 更新特定项的showFullContent状态
            const key = `carList[${index}].showFullContent`;
            this.setData({
                [key]: !carList[index].showFullContent
            });
        }
    },
    // 下拉菜单打开事件
    onDropdownOpen(e) {
        // 获取当前打开的下拉菜单索引
        const index = e.currentTarget.dataset.index;

        // 设置当前打开的下拉菜单索引
        this.setData({
            isDropdownOpen: true,
            pageStyle: 'overflow: hidden; height: 100vh;',
            currentDropdownIndex: index
        });

        // 关闭其他下拉菜单
        const dropdownItems = this.selectAllComponents('.van-dropdown-item');
        dropdownItems.forEach((item, i) => {
            if (i !== index && item.data.showPopup) {
                item.toggle(false);
            }
        });
    },
    // 下拉菜单关闭事件
    onDropdownClose() {
        this.setData({
            isDropdownOpen: false,
            pageStyle: ''
        });
    },
    // 锁定页面滚动
    lockPage() {
        // 获取页面根元素
        const pageContainer = document.querySelector('.container');
        if (pageContainer) {
            pageContainer.style.overflow = 'hidden';
            pageContainer.style.height = '100vh';
        }
    },
    // 解锁页面滚动
    unlockPage() {
        // 获取页面根元素
        const pageContainer = document.querySelector('.container');
        if (pageContainer) {
            pageContainer.style.overflow = '';
            pageContainer.style.height = '';
        }
    },
    // 阻止页面滚动
    preventScroll() {
        return false;
    },
    // 添加一个方法来处理下拉菜单的点击事件
    onDropdownItemClick(e) {
        const { index } = e.currentTarget.dataset;

        // 获取所有下拉菜单项
        const dropdownItems = this.selectAllComponents('.van-dropdown-item');

        // 如果当前有打开的下拉菜单，且不是点击的这个，则关闭它
        dropdownItems.forEach((item, i) => {
            if (i !== index && item.data.showPopup) {
                item.toggle(false);
            }
        });
    },
    // 切换筛选内容显示状态
    showFilterContent(type) {
        if (this.data.currentFilter === type && this.data.showFilterContent) {
            this.setData({
                showFilterContent: false,
                currentFilter: '',
                pageStyle: '',
                filterTitleVisible: false
            });
        } else {
            this.setData({
                currentFilter: type,
                showFilterContent: true,
                pageStyle: 'overflow: hidden;',
                filterTitleVisible: true
            }, () => {
                console.log('筛选内容已更新:', {
                    currentFilter: this.data.currentFilter,
                    showFilterContent: this.data.showFilterContent,
                    options: this.data[`${type}Options`]
                });
            });
        }
    },
    // 关闭筛选内容
    closeFilter() {
        this.setData({
            showFilterContent: false,
            currentFilter: '',
            pageStyle: '',
            filterTitleVisible: false
        });
    },
    // 统一的获取列表方法
    async getList(params = {}) {
        // 设置加载状态
        // 区分首次加载和加载更多的情况
        const currentPage = params.page || 1;

        if (currentPage === 1) {
            // 首次加载或刷新时显示骨架屏
            this.setData({
                isLoading: true,
                showLoadingAnimation: false
            });
        } else {
            // 加载更多时显示普通加载动画
            this.setData({
                isLoading: true,
                showLoadingAnimation: true
            });
        }

        try {
            // 构建请求参数，合并所有筛选条件
            const requestParams = {
                app_id: util.getAppId(),
                page: currentPage,
                list_rows: this.data.pageSize
            };

            // 添加搜索关键词
            if (this.data.searchValue) {
                requestParams.title_desc = this.data.searchValue;
            }

            // console.log('发起请求，参数:', requestParams);
            const result = await api.moments.getList(requestParams);

            if (result && result.data) {
                const carListData = Array.isArray(result.data) ? result.data : [];
                // console.log('API返回数据长度:', carListData.length);

                // 处理返回的列表
                let formattedCarList = carListData.map(item => {

                    // 计算折扣百分比
                    const salePrice = parseFloat(item.sell_price) || 0;
                    const newPrice = parseFloat(item.official_price) || 0;
                    const discount = newPrice > 0 ? (((newPrice - salePrice) / newPrice) * 100).toFixed(0) : 0;

                    // 格式化车辆信息
                    return {
                        id: item.id,
                        title: item.title || item.ui_vehicle_name,
                        price: item.price || 0,
                        quantity: item.quantity || 0,
                        used_type_name: item.used_type_name || '二手车',
                        delivery_location: item.delivery_location || '全国',
                        desc: item.vehicle_condition,
                        showFullContent: false, // 默认不展开内容
                        images: item.image_urls || [],
                        username: item.short_name || item.company_name,
                        avatar: item.avatar || '/static/images/default-avatar.png',
                        comments: item.comments || 0,
                        likes: item.likes || 0,
                        isLiked: item.likeds, // 添加点赞状态
                        time: item.create_time || '刚刚',
                        remainingDays: item.day,
                        views: item.views
                    };
                });

                // 根据是否是第一页决定是替换还是追加数据
                const newCarList = currentPage === 1
                    ? formattedCarList
                    : [...this.data.carList, ...formattedCarList];

                // 更新页面数据
                this.setData({
                    carList: newCarList,
                    currentPage: currentPage,
                    isLoading: false,
                    hasMoreData: formattedCarList.length >= this.data.pageSize // 如果返回的数据量小于页大小，表示没有更多数据
                });

                // console.log('数据加载完成，当前页:', currentPage, '数据条数:', newCarList.length);

                // 如果是第一页且没有数据，显示提示
                if (currentPage === 1 && newCarList.length === 0) {
                    wx.showToast({
                        title: '暂无数据',
                        icon: 'none',
                        duration: 2000
                    });
                }

                return formattedCarList;
            } else {
                this.setData({
                    isLoading: false,
                    hasMoreData: false
                });

                // 显示错误提示
                wx.showToast({
                    title: '获取数据失败',
                    icon: 'none',
                    duration: 2000
                });

                return [];
            }
        } catch (error) {
            console.error('获取数据失败:', error);
            this.setData({
                isLoading: false,
                hasMoreData: false
            });

            // 显示错误提示
            wx.showToast({
                title: 'API 请求失败',
                icon: 'none',
                duration: 2000
            });

            return [];
        }
    },

    // 下拉刷新
    onPullDownRefresh() {
        // 显示骨架屏
        this.setData({
            isLoading: true,
            showLoadingAnimation: false,
            carList: [] // 清空现有数据
        });

        // 重置页码
        this.setData({
            currentPage: 1,
            hasMoreData: true
        });

        // 重新加载数据
        this.getList({ page: 1 }).finally(() => {
            // 停止下拉刷新动画
            wx.stopPullDownRefresh();

            // 延迟隐藏骨架屏
            setTimeout(() => {
                this.setData({
                    isLoading: false
                });
            }, 800);
        });
    },

    // 上拉加载更多
    onReachBottom() {
        // console.log('触发上拉加载更多');
        if (this.data.hasMoreData && !this.data.isLoading) {
            this.getList({ page: this.data.currentPage + 1 });
        }
    },

    // 添加 toggleFilter 方法
    toggleFilter(e) {
        const type = e.currentTarget.dataset.type;

        // 如果点击的是筛选按钮
        if (type === 'filter') {
            this.onFilterTap();
            return;
        }

        // 处理品牌、里程和价格的点击
        if (this.data.currentFilter === type && this.data.showFilterContent) {
            // 如果当前筛选项已经打开，则关闭它
            this.setData({
                showFilterContent: false,
                currentFilter: '',
                pageStyle: '',
                filterTitleVisible: false
            });
        } else {
            // 打开选中的筛选项
            this.setData({
                currentFilter: type,
                showFilterContent: true,
                pageStyle: 'overflow: hidden;',
                filterTitleVisible: true
            });

            // 如果是品牌筛选且没有品牌数据，则获取品牌列表
            if (type === 'brand' && (!this.data.brandOptions || this.data.brandOptions.length === 0)) {
                this.getBrandList();
            }
        }
    },

    // 发帖按钮点击事件
    onPostButtonTap() {
        // 获取用户信息
        const userInfo = util.getUserInfo();

        // 检查登录状态
        if (userInfo && userInfo.app_id) {
            // 用户已登录，正常跳转到发布页面
            wx.navigateTo({
                url: '/pages/moments/release'
            });
        } else {
            // 用户未登录，直接跳转到登录页面
            wx.navigateTo({
                url: '/pages/login/index'
            });
        }
    },

    // 点赞事件
    toggleLike(e) {
        // 获取用户信息
        const userInfo = util.getUserInfo();

        // 检查登录状态
        if (userInfo && userInfo.app_id) {
            // 用户已登录，执行点赞操作
            const id = e.currentTarget.dataset.id;
            const index = e.currentTarget.dataset.index;
            const carList = this.data.carList;

            // 找到对应的项
            const item = carList[index];
            if (!item) {
                return;
            }

            // 切换点赞状态
            const isLiked = !item.isLiked;
            const likesCount = isLiked ? (parseInt(item.likes || 0) + 1) : (parseInt(item.likes || 0) - 1);

            // 更新页面显示
            this.setData({
                [`carList[${index}].isLiked`]: isLiked,
                [`carList[${index}].likes`]: likesCount >= 0 ? likesCount : 0
            });

            // 调用后端API更新点赞状态
            this.updateLikeStatus({
                'app_id': userInfo.app_id,
                'type': 1,
                'type_id': id,
                'liked': isLiked
            });
        } else {
            // 用户未登录，直接跳转到登录页面
            wx.navigateTo({
                url: '/pages/login/index'
            });
        }
    },

    // 调用API更新点赞状态
    async updateLikeStatus(params) {
        try {
            // 这里应该调用实际的API
            // 示例: api.moments.toggleLike(id, isLiked)
            const result = await api.moments.toggleLike(params)
            // console.log(`更新帖子 ${params.type_id} 点赞状态为 ${params.isLiked ? '已点赞' : '取消点赞'}`, result);
        } catch (error) {
            console.error('更新点赞状态失败:', error);
            wx.showToast({
                title: '操作失败，请重试',
                icon: 'none'
            });
        }
    },

    //todo 后续优化
    shareDetail() { },

    // 分享功能
    onShareAppMessage(res) {
        // 检查是否通过点击分享按钮触发
        if (res.from === 'button') {
            const { id, title, index } = res.target.dataset;
            const item = this.data.carList[index];

            return {
                title: title || '找车侠-好车直供',
                path: `/pages/moments/detail?id=${id}`,
                imageUrl: item && item.images && item.images.length > 0 ?
                    item.images[0] : undefined
            };
        }

        // 默认分享整个列表页
        return {
            title: this.data.shareData.title,
            path: this.data.shareData.path
        };
    }
}) 