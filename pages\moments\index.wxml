<view
  class="container"
  style="{{ pageStyle }}"
>
  <!-- 引入登录弹窗模板 -->
  <import src="/templates/loginPopup/loginPopup.wxml" />

  <!-- 使用登录弹窗模板 -->
  <template
    is="loginPopup"
    data="{{showLoginPopup, loginPopupOptions}}"
  ></template>

  <!-- 骨架屏 - 仅在页面加载中且不显示正常加载动画时显示 -->
  <view
    class="skeleton-screen"
    wx:if="{{isLoading && !showLoadingAnimation}}"
  >
    <!-- 骨架导航栏 -->
    <view class="skeleton-nav">
      <view
        class="skeleton-status-bar"
        style="height: {{statusBarHeight}}px;"
      ></view>
      <view class="skeleton-nav-title"></view>
    </view>

    <!-- 骨架搜索栏 -->
    <view class="skeleton-search-bar">
      <view class="skeleton-search-input"></view>
    </view>

    <!-- 骨架内容列表 -->
    <view class="skeleton-content">
      <!-- 骨架帖子卡片 - 循环创建多个 -->
      <view
        class="skeleton-card"
        wx:for="{{8}}"
        wx:key="index"
      >
        <!-- 骨架用户信息 -->
        <view class="skeleton-user-info">
          <view class="skeleton-avatar"></view>
          <view class="skeleton-user-meta">
            <view class="skeleton-username"></view>
            <view class="skeleton-time"></view>
          </view>
        </view>

        <!-- 骨架帖子内容 -->
        <view class="skeleton-post-content">
          <view class="skeleton-post-title"></view>
          <view class="skeleton-info-grid">
            <view class="skeleton-info-row">
              <view class="skeleton-info-item"></view>
              <view class="skeleton-info-item"></view>
            </view>
            <view class="skeleton-info-row">
              <view class="skeleton-info-item"></view>
              <view class="skeleton-info-item"></view>
            </view>
          </view>
          <view class="skeleton-post-desc"></view>
        </view>

        <!-- 骨架图片区域 -->
        <view class="skeleton-post-images">
          <view
            class="skeleton-image"
            wx:for="{{3}}"
            wx:key="imgIndex"
            wx:for-index="imgIndex"
          ></view>
        </view>

        <!-- 骨架底部操作栏 -->
        <view class="skeleton-post-actions">
          <view class="skeleton-views"></view>
          <view class="skeleton-actions-right">
            <view class="skeleton-action"></view>
            <view class="skeleton-action"></view>
            <view class="skeleton-action"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 骨架加载提示 -->
    <view class="skeleton-loading-tip">
      <view class="skeleton-loading-bar"></view>
      <view class="skeleton-loading-text"></view>
    </view>
  </view>

  <!-- 添加普通加载动画 -->
  <view
    class="loading"
    wx:if="{{ isLoading && showLoadingAnimation }}"
  >
    <text>加载中...</text>
  </view>

  <!-- 顶部区域：固定高度容器，用于容纳搜索框或筛选标题 -->
  <view class="fixed-header">
    <!-- 自定义导航栏 -->
    <view class="custom-nav">
      <view
        class="status-bar"
        style="height: {{statusBarHeight}}px;"
      ></view>
      <view class="nav-title">询盘</view>
    </view>
    <view
      class="top-container"
      style="height: {{ topContainerHeight }}px;"
    >
      <!-- 搜索区域 -->
      <view class="search-bar {{ showFilterContent ? 'hidden' : '' }}">
        <view class="search-input-wrapper">
          <icon
            type="search"
            size="14"
            class="search-icon"
          ></icon>
          <input
            class="search-input"
            placeholder="请输入品牌或车型"
            placeholder-style="color: #aaa; font-size: 26rpx;"
            value="{{ searchValue }}"
            bindinput="onSearchInput"
            bindconfirm="onSearch"
          />
          <view
            class="search-btn"
            bindtap="onSearch"
          >搜索</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 内容区域 - 添加一个占位符，高度等于固定头部的高度 -->
  <view class="header-placeholder"></view>

  <!-- 车辆列表 - 修改为新样式 -->
  <view class="car-list">
    <view
      class="car-item"
      wx:for="{{ carList }}"
      wx:key="id"
      bindtap="onCarItemTap"
      data-id="{{ item.id }}"
    >
      <!-- 用户信息区域 -->
      <view class="user-info">
        <image
          class="user-avatar"
          src="{{ item.avatar || '/static/images/default-avatar.png' }}"
        ></image>
        <view class="user-meta">
          <view class="user-name">{{ item.username || '侠友' + item.id }}</view>
          <view class="post-time">{{ item.time }}</view>
        </view>
      </view>

      <!-- 内容区域 -->
      <view class="post-content">
        <view class="post-title">{{ item.title }}</view>
        <view class="info-grid">
          <view class="info-row">
            <view class="info-item">类型:{{ item.used_type_name }}</view>
            <view class="info-item">价格:<text class="price-text">{{ item.price }}万元</text></view>
          </view>
          <view class="info-row">
            <view class="info-item">数量:{{ item.quantity }}辆</view>
            <view class="info-item">交付地:{{ item.delivery_location }}</view>
          </view>
        </view>
        <!-- 修改描述区域，添加内容收起/展开功能 -->
        <view class="post-desc {{ item.showFullContent ? 'expanded' : 'collapsed' }}">
          {{ item.desc || '热浪车新车直出，不限车型，可惠运可陆运，费用低，提前运返税，专业出口退税，会处理时间。' }}
        </view>
        <!-- 添加"全文"按钮，仅在内容较长且未展开时显示 -->
        <view
          wx:if="{{ !item.showFullContent && (item.desc && item.desc.length > 50) }}"
          class="show-more"
          catch:tap="toggleContent"
          data-id="{{ item.id }}"
        >
          全文
        </view>
      </view>

      <!-- 图片区域 -->
      <view class="post-images">
        <view
          class="image-item"
          wx:for="{{ item.images || [item.imageUrl, item.imageUrl, item.imageUrl] }}"
          wx:for-item="img"
          wx:key="index"
        >
          <image
            src="{{ img }}"
            mode="aspectFill"
          ></image>
        </view>
      </view>

      <!-- 底部操作区 -->
      <view class="post-actions">
        <view class="post-stats">
          <view class="views-item">
            <image src="/icons/moments/view.svg"></image>
            <text>浏览{{ item.views || 0 }}次</text>
          </view>
          <view class="right-actions">
            <view
              class="stat-item {{ item.isLiked ? 'liked' : '' }}"
              catch:tap="toggleLike"
              data-id="{{ item.id }}"
              data-index="{{ index }}"
            >
              <image src="{{ item.isLiked ? '/icons/moments/liked.png' : '/icons/moments/like.svg' }}"></image>
              <text>{{ item.likes}}</text>
            </view>
            <view class="stat-item">
              <image src="/icons/moments/comment.svg"></image>
              <text>{{ item.comments }}</text>
            </view>
            <view class="stat-item">
              <button
                class="share-button"
                open-type="share"
                catch:tap="shareDetail"
                data-id="{{ item.id }}"
                data-title="{{ item.title }}"
                data-index="{{ index }}"
              >
                <image src="/icons/moments/share.svg"></image>
              </button>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载中提示 -->
    <view
      class="loading"
      wx:if="{{ isLoading && showLoadingAnimation }}"
    >
      <text>加载中...</text>
    </view>
  </view>

  <!-- 添加悬浮发帖按钮 -->
  <view
    class="post-button"
    bindtap="onPostButtonTap"
  >
    <image
      src="/icons/moments/post_icon.png"
      class="post-icon"
    ></image>
  </view>
</view>