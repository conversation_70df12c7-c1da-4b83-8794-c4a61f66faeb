page {  
  overflow-x: hidden;  
  width: 100%;  
  box-sizing: border-box;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
}

.container {
  padding-bottom: calc(170rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(170rpx + env(safe-area-inset-bottom));
  background-color: transparent;
  margin: 0;
  padding-top: 0;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
  position: relative;
}

/* ==================== 顶部导航区域样式 ==================== */

/* 头部占位符 - 防止内容被固定顶部遮挡 */
.header-placeholder {
  width: 100%;
  height: calc(var(--status-bar-height, 20px) + 88rpx); /* 状态栏+导航栏 */
}

/* 固定头部样式 */
.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 顶部状态栏 */
.status-bar {
  width: 100%;
  background: transparent;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  width: 100%;
  background: transparent;
  position: relative;
  padding: 0 20rpx;
  box-sizing: border-box;
}

.nav-back {
  width: 88rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  z-index: 10;
}

.nav-title {
  position: absolute;
  left: 0;
  right: 0;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin: 0 auto;
  width: 60%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  z-index: 5;
}

.nav-placeholder {
  width: 88rpx;
  height: 88rpx;
  visibility: hidden;
  flex-shrink: 0;
}

/* 轮播图样式 */
.swiper-container {
  position: relative;
  width: 100%;
  height: 500rpx;
}

.swiper {
  width: 100%;
  height: 500rpx;
  background-color: #f5f5f5;
  margin: 0;
  padding: 0;
  position: relative;
}

.swiper-image {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: cover;
}

/* 自定义轮播图指示器样式 */
.swiper-indicator {
  position: absolute;
  bottom: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  z-index: 10;
  text-align: center;
  min-width: 100rpx;
}

/* 车辆基本信息 */
.info-section {
  padding: 20rpx 30rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
  width: 100%;
  box-sizing: border-box;
}

.vehicle-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
  color: #333;
  width: 100%;
  text-align: left;
}

.price-row {
  margin-bottom: 25rpx;
  width: 100%;
  text-align: left;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 20rpx;
}

.official-price {
  font-size: 32rpx;
  color: #6d7481;
  font-weight: bold;
}

.batch-price {
  font-size: 32rpx;
  color: #1989fa;
  font-weight: bold;
  padding: 0 60rpx;
}

/* 信息网格 - 新的两列布局 */
.info-grid {
  width: 100%;
  padding: 0;
}

.info-row {
  display: flex;
  width: 100%;
  margin-bottom: 15rpx;
  justify-content: space-between;
}

.info-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 10rpx 0;
}

.info-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 高亮值 - 用于特殊信息 */
.info-value.highlight {
  color: #1296db;
}

/* 章节标题 */
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  padding: 30rpx 30rpx 20rpx;
  color: #333;
  background-color: #fff;
  margin-top: 20rpx;
  border-bottom: 1rpx solid #eee;
  width: 100%;
  box-sizing: border-box;
  text-align: left;
}

/* 配置信息 */
.config-section, .condition-section {
  padding: 20rpx 30rpx 30rpx;
  background-color: #fff;
}

.config-text, .condition-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 批量车列表 - 新卡片样式 */
.batch-list {
  background-color: #fff;
  padding: 20rpx;
  width: 100%;
  box-sizing: border-box;
}

.batch-item {
  margin-bottom: 20rpx;
  border: 1rpx solid #eee;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.batch-header {
  padding: 20rpx;
  background-color: #f9f9f9;
  border-bottom: 1rpx solid #eee;
}

.batch-number {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.batch-info-simple {
  padding: 20rpx;
}

.batch-info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.batch-info-label {
  font-size: 26rpx;
  color: #666;
}

.batch-info-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.batch-buttons {
  display: flex;
  padding: 0 20rpx 20rpx;
}

.batch-button {
  flex: 1;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 26rpx;
  margin: 0 10rpx;
  background-color: #f5f5f5;
  color: #333;
  border: none;
  border-radius: 8rpx;
}

.custom-button {
  background-color: #eff4fc;
  color: #3d84f6;
  border: none;
  font-size: 30rpx;
  font-weight: 500;
}

.batch-button:first-child {
  margin-left: 0;
}

.batch-button:last-child {
  margin-right: 0;
}

/* 底部联系栏 */
.contact-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.contact-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-right: 40rpx;
}

.contact-item text {
  font-size: 24rpx;
  color: #666;
  margin-top: 6rpx;
}

.contact-button {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #ffc62c;
  color: #fff;
  font-size: 28rpx;
  border-radius: 40rpx;
  text-align: center;
  margin: 0;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ffc62c;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态样式 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
  background-color: #fff;
  margin-top: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 调试按钮样式 */
.debug-btn {
  position: fixed;
  bottom: 120rpx;
  right: 30rpx;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  padding: 10rpx 20rpx;
  border-radius: 10rpx;
  font-size: 24rpx;
  z-index: 999;
}

/* 图片加载状态 */
.image-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
}

/* 弹窗样式 */
.popup-container {
  padding: 30rpx;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
  margin-bottom: 30rpx;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.popup-close {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}

.popup-content {
  max-height: 800rpx;
  overflow-y: auto;
}

.popup-item {
  display: flex;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.popup-label {
  width: 180rpx;
  font-size: 28rpx;
  color: #666;
}

.popup-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 售价高亮样式 */
.price-highlight {
  color: #3b82f6;
  font-weight: bold;
}

/* 底部操作栏样式 */
.footer-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 130rpx; /* 增加容器高度，为按钮留出更多空间 */
  background-color: #fff;
  display: flex;
  align-items: center;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
  padding: 0 30rpx 20rpx 30rpx; /* 增加底部内边距 */
}

.collect-button {
  width: 120rpx;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-left: 30rpx;
  padding-bottom: 20rpx; /* 增加底部内边距 */
}

.collect-icon {
  margin-bottom: 6rpx;
}

.collect-text {
  font-size: 24rpx;
  color: #666;
}

.contact-merchant-button {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #4080ff;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 18rpx;
  text-align: center;
  margin-bottom: 20rpx;
}

/* 安全区域占位符 - 适配iPhone底部 */
.safe-area-bottom {
  width: 100%;
  height: constant(safe-area-inset-bottom);
  height: env(safe-area-inset-bottom);
  background-color: transparent;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 99;
}
