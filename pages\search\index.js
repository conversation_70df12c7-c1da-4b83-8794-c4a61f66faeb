Page({
    data: {
        keyword: '',
        searchResults: []
    },
    onLoad(options) {
        if (options.keyword) {
            const keyword = decodeURIComponent(options.keyword)
            this.setData({
                keyword
            })
            this.searchCars(keyword)
        }
    },

    onInput(e) {
        this.setData({
            keyword: e.detail.value
        })
    },

    onSearch() {
        this.searchCars(this.data.keyword)
    },

    searchCars(keyword) {
        // 这里应该是实际的搜索逻辑
        // 模拟一些搜索结果
        const results = [
            { id: 1, name: '奔驰 C级 2022款', price: '31.98', image: '/assets/images/car1.jpg' },
            { id: 2, name: '宝马 3系 2023款', price: '29.39', image: '/assets/images/car2.jpg' }
        ].filter(car => car.name.indexOf(keyword) !== -1)

        this.setData({
            searchResults: results
        })
    }
}) 