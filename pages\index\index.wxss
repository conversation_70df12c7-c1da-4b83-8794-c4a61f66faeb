/**index.wxss**/
/* 引入登录弹窗样式 */
@import "../../templates/loginPopup/loginPopup.wxss";

page {
  background: linear-gradient(#DCECFF, #F1F4F9, #FDFCFB);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  scroll-behavior: smooth;
}

.container {
  background-color: transparent;
  min-height: 100vh;
  width: 100%;
  padding: 0;
  box-sizing: border-box;
}

/* 固定导航栏样式 */
.fixed-nav {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 90;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
}

.status-bar {
  width: 100%;
}

.nav-title {
  height: 44px;
  line-height: 44px;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  position: relative;
}

/* 滚动头部区域样式 */
.scroll-header {
  width: 100%;
  z-index: 30;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  transition: transform 0.3s ease-in-out;
  display: block;
  opacity: 1;
  position: relative;
  overflow: visible;
  padding-top: 0;
  /* margin-top值通过JS动态设置，以适应不同机型 */
}

/* 快捷导航样式 */
.quick-nav-container {
  width: 100%;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  box-sizing: border-box;
  margin-bottom: 0;
  padding: 10px 0;
  z-index: 9;
  transition: all 0.3s ease;
  position: relative;
  z-index: 20;
  background-color: #fff;
  will-change: transform, position;
}

/* 固定状态的快捷导航样式 */
.fixed-nav-container {
  position: fixed;
  left: 0;
  right: 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 100;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  padding: 5rpx 0;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
  }

  to {
    transform: translateY(0);
  }
}

/* 移除或注释掉原有的nav-moved-up类 */
/* .nav-moved-up {
  position: fixed;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
} */

/* 顶部容器样式 */
.top-container {
  width: 100%;
  box-sizing: border-box;
  z-index: 1001;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  margin-top: 0;
  padding-top: 0;
  display: block;
}

/* 导航占位元素 - 平滑过渡，修复显示问题 */
.nav-placeholder {
  width: 100%;
  box-sizing: border-box;
  background: transparent;
  /* 改为透明背景 */
  display: block;
  transition: opacity 0.3s ease;
  opacity: 0;
  overflow: hidden;
  height: 0 !important;
  /* 确保未激活时不占用空间 */
  margin: 0;
  padding: 0;
  position: relative;
  line-height: 0;
  font-size: 0;
}

.nav-placeholder-visible {
  opacity: 1;
  height: auto !important;
  /* 使用auto而非固定值 */
}

/* 头部占位符，防止内容被固定头部遮挡 */
.header-placeholder {
  width: 100%;
  box-sizing: border-box;
  transition: height 0.3s ease;
}

/* 顶部搜索容器 - 水平布局 */
.top-search-container {
  display: flex;
  align-items: center;
  padding: 10rpx 24rpx 20rpx;
  /* 减少顶部内边距，增加底部内边距 */
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  width: 100%;
  box-sizing: border-box;
  transition: all 0.3s ease;
  margin-top: 0;
  opacity: 1;
  position: relative;
  z-index: 10;
}

/* 内联LOGO样式 */
.inline-logo {
  width: 140rpx;
  height: 60rpx;
  flex-shrink: 0;
}

/* 搜索区域 */
.search-section {
  display: flex;
  background: #fff;
  border-radius: 20rpx;
  padding: 0;
  width: 100%;
  border: 1px solid #C1DFFF;
  align-items: center;
  box-sizing: border-box;
  overflow: visible;
}

.city-selector {
  display: flex;
  align-items: center;
  min-width: 100rpx;
  color: #333;
  font-size: 28rpx;
  font-weight: 500;
  padding: 15rpx 20rpx;
}

.city-selector text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100rpx;
}

.arrow-down {
  width: 0;
  height: 0;
  border-left: 8rpx solid transparent;
  border-right: 8rpx solid transparent;
  border-top: 8rpx solid #333;
  margin-left: 8rpx;
}

.search-input-container {
  flex: 1;
  display: flex;
  align-items: center;
  padding-left: 20rpx;
  border-left: 1px solid #e5e5e5;
  height: 70rpx;
}

.search-input-container icon {
  margin-right: 10rpx;
  flex-shrink: 0;
}

.search-input-container input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  min-width: 0;
  height: 40rpx;
  line-height: 60rpx;
}

/* 修改搜索按钮样式 */
.search-btn {
  background-color: #25a6f5;
  color: #fff;
  font-size: 28rpx;
  height: 60rpx;
  padding: 0 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
  margin: 8rpx 10rpx;
}

input::placeholder {
  color: #999;
  font-size: 26rpx;
}

.banner-container {
  padding: 0 24rpx;
  /* 12px */
  box-sizing: border-box;
  display: flex;
  justify-content: center;
}

.banner-swiper {
  width: 702rpx;
  /* 351px */
  height: 220rpx;
  /* 110px */
  border-radius: 10rpx;
  overflow: hidden;
  /* 确保内容不超出圆角边界 */
}

.banner-image {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
  /* 保持与swiper相同的圆角 */
}

/* 确保swiper-item也有圆角 */
.banner-swiper .wx-swiper-item {
  border-radius: 10rpx;
  overflow: hidden;
}

.quick-nav-swiper {
  width: 100%;
  height: 360rpx;
  /* 减少高度 */
  position: relative;
  margin-bottom: 10rpx;
}

/* 轮播指示点样式 */
.banner-swiper .wx-swiper-dots {
  margin-bottom: 10rpx;
  /* 为轮播图指示点添加底部间距 */
}

.banner-swiper .wx-swiper-dot {
  width: 16rpx !important;
  height: 16rpx !important;
  margin: 0 8rpx !important;
  border-radius: 50%;
}

/* 快捷导航指示点样式 */
.wx-swiper-dots {
  position: relative;
  bottom: 10rpx !important;
  /* 减少底部间距 */
}

.wx-swiper-dots.wx-swiper-dots-horizontal {
  margin-bottom: 0;
  padding-bottom: 0;
}

/* 添加指示点大小和间距的控制 */
.quick-nav-swiper .wx-swiper-dot {
  width: 16rpx !important;
  height: 16rpx !important;
  margin: 0 12rpx !important;
  border-radius: 50%;
}

.quick-nav-page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding-top: 10rpx;
  padding-bottom: 0;
  /* 移除底部内边距 */
  /* padding-left: 12rpx; */
  /* 12px */
  /* padding-right: 12rpx; */
  /* 12px */
  background: transparent;
  box-sizing: border-box;
}

.quick-nav-row {
  display: flex;
  /* justify-content: center; */
  justify-content: left;
  /* 居中对齐 */
  width: 100%;
  padding: 0;
  box-sizing: border-box;
  margin-bottom: 20rpx;
  /* 10px */
}

.quick-nav-row:last-child {
  margin-bottom: 5rpx;
  /* 大幅减少底部间距 */
}

.quick-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 108rpx;
  /* 与图标宽度相同 */
  margin: 0 24rpx;
  /* 修改为44rpx的一半，每边22rpx */
  box-sizing: border-box;
}

.quick-nav-icon {
  width: 94rpx;
  /* 48px */
  height: 94rpx;
  /* 48px */
  margin-bottom: 8rpx;
  /* 4px */
  /* 保持方形图标 */
  border-radius: 0;
  overflow: hidden;
  background-color: transparent;
}

.quick-nav-text {
  font-size: 24rpx;
  color: #333;
  text-align: center;
  width: 120rpx;
  /* 增加宽度以适应四个字 */
  height: 40rpx;
  /* 20px */
  line-height: 22rpx;
  /* 调整行高与高度一致 */
  white-space: nowrap;
  /* 防止文字换行 */
  overflow: hidden;
  /* 隐藏溢出的文本 */
  text-overflow: ellipsis;
  /* 文本溢出时显示省略号 */
  display: block;
  /* 确保文字正常显示 */
}

/* 价格导航 */
.price-nav {
  display: flex;
  justify-content: space-between;
  margin: 0 0 0 0;
  background: #fff;
  border-radius: 0;
  width: 100%;
  padding: 20rpx;
  padding-left: 40rpx;
  padding-right: 40rpx;
  box-sizing: border-box;
}

.price-btn {
  text-align: center;
  font-size: 26rpx;
  color: #333;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  padding: 12rpx 24rpx;
  margin: 0 10rpx;
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.price-btn:active {
  background-color: #e6f7ff;
  color: #1890ff;
  box-shadow: 0 0 8rpx rgba(24, 144, 255, 0.2);
}

/* 车型导航 */
.car-type-nav {
  display: flex;
  justify-content: space-between;
  margin: 0 0 0 0;
  background: #fff;
  border-radius: 0;
  width: 100%;
  padding: 20rpx;
  padding-left: 40rpx;
  padding-right: 40rpx;
  box-sizing: border-box;
}

.car-type-btn {
  text-align: center;
  font-size: 26rpx;
  color: #333;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  padding: 12rpx 24rpx;
  margin: 0 10rpx;
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.car-type-btn:active {
  background-color: #e6f7ff;
  color: #1890ff;
  box-shadow: 0 0 8rpx rgba(24, 144, 255, 0.2);
}

/* 添加选中状态的样式 */
.price-btn.active,
.car-type-btn.active {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 1px solid #1890ff;
}

/* 品牌导航 */
.brand-nav {
  display: flex;
  justify-content: space-between;
  margin: 0 0 20rpx 0;
  background: #fff;
  border-radius: 0;
  width: 100%;
  padding: 20rpx;
  box-sizing: border-box;
}

.brand-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.brand-item image {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}

.brand-item text {
  font-size: 24rpx;
  color: #333;
}

/* 推荐车辆 */
.section {
  background: #fff;
  margin: 0 0 20rpx 0;
  border-radius: 0;
  width: 100%;
  padding: 20rpx;
  box-sizing: border-box;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.recommend-car-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.car-image-container {
  position: relative;
  width: 220rpx;
  height: 160rpx;
  margin-right: 20rpx;
}

.car-image {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}

.car-video-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.car-video-icon image {
  width: 30rpx;
  height: 30rpx;
}

.car-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.car-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.car-details {
  display: flex;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.car-year {
  margin-right: 20rpx;
}

.car-price {
  margin-bottom: 10rpx;
}

.price-value {
  color: #f44;
  font-size: 32rpx;
  font-weight: bold;
  margin-right: 20rpx;
}

.original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}

.car-tags {
  display: flex;
  margin-bottom: 10rpx;
}

.tag {
  font-size: 20rpx;
  padding: 4rpx 10rpx;
  margin-right: 10rpx;
  border-radius: 6rpx;
}

.car-report {
  color: #4b89dc;
  background: rgba(75, 137, 220, 0.1);
  border: 1px solid #4b89dc;
}

.three-year {
  color: #4caf50;
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid #4caf50;
}

.certification {
  color: #ff9800;
  background: rgba(255, 152, 0, 0.1);
  border: 1px solid #ff9800;
}

.car-location {
  display: flex;
  justify-content: space-between;
  font-size: 22rpx;
  color: #999;
}

/* 通用区块样式 */
.section {
  margin: 20rpx 0 0 0;
  background: #fff;
  width: 100%;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eee;
  width: 100%;
  box-sizing: border-box;
}

.section-title-wrap {
  display: flex;
  align-items: center;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 4rpx;
  height: 30rpx;
  width: 6rpx;
  background: #1296db;
  border-radius: 3rpx;
}

.more-link {
  font-size: 24rpx;
  color: #999;
}

/* 热门车型 */
.hot-cars {
  padding: 20rpx;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 100%;
  box-sizing: border-box;
}

.hot-car-item {
  width: 48%;
  margin-bottom: 20rpx;
  background: #fff;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.hot-car-image {
  width: 100%;
  height: 200rpx;
}

.hot-car-info {
  padding: 16rpx;
}

.hot-car-name {
  font-size: 26rpx;
  color: #333;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.hot-car-price-wrap {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10rpx;
}

.hot-car-price {
  font-size: 28rpx;
  color: #f60;
  font-weight: bold;
}

.hot-car-tag {
  font-size: 20rpx;
  color: #fff;
  background: #f60;
  padding: 2rpx 10rpx;
  border-radius: 4rpx;
}

/* 品牌列表 */
.brand-list {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx 10rpx;
  width: 100%;
  box-sizing: border-box;
}

.brand-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}

.brand-logo {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: #f9f9f9;
  padding: 10rpx;
  margin-bottom: 10rpx;
}

.brand-name {
  font-size: 24rpx;
  color: #666;
}

/* 特惠车源 */
.deal-cars {
  padding: 20rpx;
  width: 100%;
  box-sizing: border-box;
}

.deal-car-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  width: 100%;
}

.deal-car-item:last-child {
  border-bottom: none;
}

.deal-car-image {
  width: 200rpx;
  height: 150rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.deal-car-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.deal-car-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.deal-car-price-wrap {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.deal-car-price {
  font-size: 30rpx;
  color: #f60;
  font-weight: bold;
  margin-right: 10rpx;
}

.deal-car-original {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}

.deal-car-tags {
  display: flex;
}

.deal-car-tag {
  font-size: 22rpx;
  color: #999;
  background: #f5f5f5;
  padding: 4rpx 10rpx;
  border-radius: 4rpx;
  margin-right: 10rpx;
}

/* 最新上架 */
.new-cars {
  padding: 20rpx;
  width: 100%;
  box-sizing: border-box;
}

.new-car-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  width: 100%;
}

.new-car-item:last-child {
  border-bottom: none;
}

.new-car-image {
  width: 200rpx;
  height: 150rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.new-car-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.new-car-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.new-car-price {
  font-size: 30rpx;
  color: #f60;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.new-car-desc {
  font-size: 24rpx;
  color: #999;
}

/* 添加服务信息样式 */
.service-info {
  width: 100%;
  padding: 20rpx 30rpx;
  background: #fff;
  text-align: center;
  font-size: 26rpx;
  color: #333;
  border-bottom: 1px solid #eee;
  box-sizing: border-box;
  margin-bottom: 1px;
}

/* 为您推荐的样式 - 与我的页面保持一致 */
.recommend-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  padding: 10rpx 0;
}

.recommend-list {
  background-color: #fff;
}

.recommend-card {
  background-color: #fff;
  border-radius: 10rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  width: 100%;
  box-sizing: border-box;
  height: auto;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease, opacity 0.3s ease;
  animation: cardAppear 0.5s ease forwards;
}

.recommend-card-body {
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 卡片左右布局 */
.car-layout {
  display: flex;
  margin-bottom: 15rpx;
}

/* 左侧图片 */
.car-thumb {
  width: 200rpx;
  height: 150rpx;
  border-radius: 6rpx;
  margin-right: 15rpx;
  flex-shrink: 0;
}

/* 右侧内容 */
.car-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 标题行 */
.car-title-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.car-badge {
  display: inline-block;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  background-color: #ffb800;
  color: #fff;
  border-radius: 4rpx;
  margin-right: 10rpx;
}

.car-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 车辆详情 */
.car-detail,
.car-location-info {
  font-size: 22rpx;
  color: #888;
  margin-bottom: 8rpx;
}

/* 价格行 */
.car-price-row {
  margin-top: 5rpx;
}

.car-price {
  font-size: 28rpx;
  font-weight: bold;
  color: #ff4d4f;
  margin-right: 10rpx;
}

.car-original-price {
  font-size: 22rpx;
  color: #999;
}

/* 底部标签 */
.car-tags-row {
  display: flex;
  flex-wrap: wrap;
}

.car-tag {
  display: inline-block;
  font-size: 20rpx;
  padding: 2rpx 10rpx;
  border-radius: 4rpx;
  margin-right: 10rpx;
  margin-bottom: 6rpx;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  border: 1px solid #1890ff;
}

/* 推荐区域样式 - 修复与导航栏之间的空白 */
.recommend-section {
  width: 100%;
  padding: 0 20rpx 120rpx 20rpx;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  box-sizing: border-box;
  transition: transform 0.3s ease;
  will-change: transform;
  margin-top: 0;
  /* 确保没有额外的上边距 */
  padding-top: 5rpx;
  /* 添加极小的上边距 */
}

/* 导航占位元素 - 平滑过渡，修复显示问题 */
.nav-placeholder {
  width: 100%;
  box-sizing: border-box;
  background: transparent;
  /* 改为透明背景 */
  display: block;
  transition: opacity 0.3s ease;
  opacity: 0;
  overflow: hidden;
  height: 0 !important;
  /* 确保未激活时不占用空间 */
  margin: 0;
  padding: 0;
  position: relative;
  line-height: 0;
  font-size: 0;
}

.nav-placeholder-visible {
  opacity: 1;
  height: auto !important;
  /* 使用auto而非固定值 */
}

/* 热门品牌区域 - 调整上边距 */
.hot-brands-section {
  background-color: #fff;
  margin: 5rpx 0 10rpx 0;
  /* 进一步减小上边距 */
  padding: 15rpx 24rpx 20rpx 24rpx;
  /* 减小顶部内边距 */
  border-radius: 10rpx;
  box-sizing: border-box;
}

/* 推荐头部调整上边距 */
.recommend-header {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin: 5rpx 0 15rpx 0;
  /* 进一步减小上边距 */
  padding-left: 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.recommend-header .more-cars {
  font-size: 24rpx;
  color: #999999;
  font-weight: normal;
  background-color: transparent;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
}

/* 使用网格布局确保两列显示 */
.recommend-grid {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(2, calc(50% - 10rpx));
  /* 每列宽度减去间距的一半 */
  gap: 20rpx;
  /* 行和列之间的间距 */
  justify-content: space-between;
  /* 确保两列之间有足够空间 */
  box-sizing: border-box;
}

.recommend-car-image {
  width: 100%;
  height: 200rpx;
  object-fit: cover;
}

.recommend-tag-label {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  font-size: 22rpx;
  color: #fff;
}

.recommend-tag-label.hot {
  background-color: #ff6f00;
}

.recommend-tag-label.new {
  background-color: #1296db;
}

.recommend-car-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  padding: 10rpx 12rpx;
  word-wrap: break-word;
  word-break: break-all;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: visible;
  min-height: 76rpx;
}

.recommend-car-params {
  font-size: 22rpx;
  color: #666;
  padding: 0 12rpx;
}

.recommend-price-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 12rpx 15rpx;
}

.recommend-new-price {
  font-size: 20rpx;
  color: #999;
  text-decoration: line-through;
}

.recommend-sale-price {
  font-size: 28rpx;
  color: #2B68F6;
  font-weight: 500;
}

/* 加载更多提示 */
.loading-more,
.no-more-data {
  text-align: center;
  padding: 20rpx 0;
  font-size: 24rpx;
  color: #999;
  width: 100%;
  clear: both;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  opacity: 0;
  transition: opacity 0.3s ease;
  animation: fadeIn 0.3s ease forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.loading-icon {
  width: 30rpx;
  height: 30rpx;
  margin-right: 10rpx;
  border: 2px solid #f0f0f0;
  border-top: 2px solid #1296db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.all-city {
  padding: 20rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1px solid #eee;
}

.all-city:active {
  background-color: #f5f5f5;
}

/* 热门品牌区域 */
.hot-brands-section {
  background-color: #fff;
  margin: 0 0 10rpx 0;
  padding: 20rpx 24rpx;
  border-radius: 10rpx;
  box-sizing: border-box;
}

.hot-brands-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.hot-brands-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.hot-brands-more {
  font-size: 24rpx;
  color: #999;
}

.hot-brands-list {
  display: flex;
  justify-content: space-between;
  flex-wrap: nowrap;
  overflow-x: auto;
  white-space: nowrap;
  padding: 5rpx 0;
  /* 减小内边距 */
  margin-left: 30rpx;
}

.hot-brand-item {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  margin-right: 45rpx;
  transition: transform 0.2s ease;
}

.hot-brand-item:active {
  transform: scale(0.95);
}

.hot-brand-icon {
  width: 64rpx;
  /* 32px */
  height: 64rpx;
  /* 32px */
  margin-bottom: 8rpx;
  /* 4px */
}

.hot-brand-name {
  font-size: 24rpx;
  height: 32rpx;
  /* 16px */
  line-height: 32rpx;
  color: #333;
}

/* 新增内容容器样式 */
.content-container {
  width: 100%;
  box-sizing: border-box;
  position: relative;
  z-index: 5;
}

/* 添加页面级别的卡片动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.recommend-card {
  animation: fadeInUp 0.3s ease-out;
  animation-fill-mode: both;
}

/* 添加骨架屏淡出、内容淡入效果 */
.content-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

/* 添加隐藏样式类，使元素不可见但保持占位 */
.invisible-item {
  opacity: 0;
  pointer-events: none;
}