/* pages/information/index.wxss */

.container {
  min-height: 100vh;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* 自定义导航栏样式 */
.custom-nav {
  width: 100%;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}

.status-bar {
  width: 100%;
}

.nav-content {
  height: 44px;
  display: flex;
  align-items: center;
  position: relative;
}

.back-icon {
  position: absolute;
  left: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx;
}

.back-icon image {
  width: 40rpx;
  height: 40rpx;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

/* 页面内容 */
.page-content {
  padding-top: calc(var(--nav-height) + 20rpx);
  box-sizing: border-box;
  width: 100%;
  border-top: none;
  box-shadow: none;
}

/* 商家信息卡片 */
.merchant-card {
  margin: 5rpx 20rpx 20rpx;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.05);
  padding: 0;
  overflow: hidden;
}

/* 商家头部信息 */
.merchant-header {
  display: flex;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.merchant-logo {
  width: 174rpx;
  height: 154rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.merchant-basic-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.merchant-name {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.merchant-phone {
  font-size: 28rpx;
  color: #666;
}

/* 商家详细信息 */
.merchant-detail-info {
  padding: 20rpx 30rpx;
}
.merchant-name,.merchant-phone{
  font-size:28rpx
}
.merchant-time {
  font-size: 22rpx;
  color: #666;
  margin-top: 30rpx;
}

.merchant-address-container {
  display: flex;
  align-items: flex-start;
}

.address-icon {
  width: 30rpx;
  height: 30rpx;
  margin-right: 10rpx;
  flex-shrink: 0;
}

.location-text {
  font-size: 28rpx;
  color: #666;
  margin-right: 8rpx;
}

.merchant-address {
  font-size: 28rpx;
  color: #666;
  flex: 1;
}

/* 联系按钮区域 */
.contact-buttons {
  display: flex;
  padding: 30rpx;
  gap: 20rpx;
}

.contact-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: 500;
  padding: 0;
}

.call-btn {
  background-color: #ff6b00;
  color: white;
}

.navigate-btn {
  background-color: #4080ff;
  color: white;
}

/* 标签页样式 */
.tab-container {
  background-color: #fff;
  margin: 10rpx 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.tab-bar {
  display: flex;
  border-bottom: 1rpx solid #eee;
}

.tab-item {
  padding: 24rpx 0;
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #4080ff;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background-color: #4080ff;
  border-radius: 3rpx;
}

/* 车辆列表 */
.car-list {
  padding: 20rpx 30rpx;
}

.car-item {
  display: flex;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eee;
}

.car-item:last-child {
  border-bottom: none;
}

.car-image {
  width: 240rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.car-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.car-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 15rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.car-params {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.car-address {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 15rpx;
}

.car-price {
  font-size: 32rpx;
  display: flex;
  align-items: center;
}

.guide-price {
  font-size: 22rpx;
  color: #999;
  text-decoration: line-through;
  margin-right: 15rpx;
}

.sale-price {
  font-size: 32rpx;
  color: #4080ff;
  font-weight: 500;
  margin-left: auto;
}

/* 加载中样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(0, 0, 0, 0.1);
  border-top-color: #4080ff;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}