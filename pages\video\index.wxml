<!--pages/video/index.wxml-->
<view class="video-container">
  <!-- 自定义导航栏 -->
  <view
    class="custom-navbar"
    style="height: {{navBarHeight}}px;"
  >
    <view
      class="status-bar"
      style="height: {{statusBarHeight}}px;"
    ></view>
    <view class="navbar-content">
      <view
        class="nav-back"
        bindtap="navigateBack"
      >
        <van-icon
          name="arrow-left"
          size="20px"
          color="#333"
        />
      </view>
      <view class="nav-title">视频中心</view>
      <view class="nav-placeholder"></view>
    </view>
  </view>

  <!-- Channel Info -->
  <view class="channel-info">
    <view class="channel-avatar" wx:if="{{video_cate == 0}}">
      <image
        src="{{merchantInfo.avatar}}"
        mode="aspectFill"
      ></image>
    </view>
    <view class="channel-details">
      <view class="channel-name" wx:if="{{video_cate == 0}}">
        {{merchantInfo.short_name || " "}}
      </view>
      <!-- 视频合集标题 -->
      <view class="channel-name" wx:if="{{video_cate == 3}}">
        {{collectionsDetail.collection_name || " "}}
      </view>

      <view class="channel-company" wx:if="{{video_cate == 0}}">
        <text style="font-size: 26rpx;">{{merchantInfo.company_name}}</text>
        <image
          class="auth-icon"
          src="/icons/video/auth.png"
          mode="aspectFit"
        ></image>
        <!-- <text class="verify-icon iconfont icon-verified"></text> -->
      </view>
    </view>
  </view>
  <view class="channel-description" wx:if="{{video_cate == 0}}">
    {{merchantInfo.introduce}}
  </view>
  <view class="channel-description" wx:if="{{video_cate == 3}}">
    {{collectionsDetail.remark}}
  </view>

  <view class="video-count">共{{totalVideos}}条视频</view>

  <!-- 新的Tab导航 - 使用固定宽度和偏移量 -->
  <view class="tabs-container">
    <view class="tab-container">
      <view class="tab-wrapper">
        <view
          class="tab {{activeTab === 'all' ? 'active' : ''}}"
          bindtap="switchTab"
          data-tab="all"
        >全部视频</view>
        <view wx:if="{{video_cate == 0}}"
          class="tab {{activeTab === 'live' ? 'active' : ''}}"
          bindtap="switchTab"
          data-tab="live"
        >直播回放</view>
        <view wx:if="{{video_cate == 0}}"
          class="tab {{activeTab === 'collections' ? 'active' : ''}}"
          bindtap="switchTab"
          data-tab="collections"
        >视频合集</view>
        <!-- 使用内联样式控制下划线位置 -->
        <view
          class="tab-line"
          style="left: {{activeTab === 'all' ? '18rpx' : (activeTab === 'live' ? '285rpx' : (activeTab === 'collections' ? '560rpx' : '18 rpx'))}}"
        ></view>
      </view>
    </view>
    <view class="tabs-divider"></view>
  </view>

  <!-- Video Grid -->
  <view class="video-grid">
    <block wx:if="{{!isEmpty}}">
      <block
        wx:for="{{videoList}}"
        wx:key="id"
      >
      
        <view
          class="video-card {{item.type === 'member' && userInfo.level === 1 && item.payState == 0 ? 'locked' : ''}}"
          bindtap="watchVideo"
          data-id="{{item.id}}"
          data-price ="{{item.price}}"
          data-title ="{{item.title}}"
          data-paystate ="{{item.payState}}"
          data-video_cate ="{{item.video_cate}}"
        >
          <view class="video-thumbnail">
            <image
              src="{{item.coverUrl}}"
              mode="aspectFill"
            ></image>
            <!-- <view class="series-tag" wx:if="{{item.seriesTag}}">{{item.seriesTag}}</view> -->
            <!-- <view class="view-count-badge">
              <image
                src="/icons/video/{{item.isLiked}}.png"
                class="like-icon"
                mode="aspectFit"
              ></image>
              <text>{{item.likes}}</text>
            </view> -->
            <view
              class="video-badge"
              wx:if="{{item.type === 'free'}}"
            >免费</view>
            <view
              class="video-badge member"
              wx:if="{{item.type === 'member'}}"
            >会员</view>

            <view
              class="lock-icon"
              wx:if="{{item.type === 'member' && userInfo.level === 1 && item.payState == 0}}"
            >
              <image
                src="/icons/video/lock.png"
                mode="aspectFit"
              ></image>
            </view>
          </view>
          <view class="video-title">{{item.title}}</view>
          <view class="video-info">
            <text class="update-time">{{item.updateTime}}</text>
          </view>
        </view>
      </block>
    </block>

    <!-- 空数据提示 -->
    <view
      class="empty-container"
      wx:if="{{isEmpty}}"
    >
      <!-- <image class="empty-image" src="/icons/video/empty.png" mode="aspectFit"></image> -->
      <van-icon
        name="video-o"
        size="80rpx"
        color="#999"
      />
      <view class="empty-text">暂无视频数据</view>
    </view>

    <!-- 加载更多提示 -->
    <view
      class="loading-more"
      wx:if="{{!isEmpty && loading && videoList.length > 0}}"
    >
      <view class="loading-dot"></view>
      <view class="loading-dot"></view>
      <view class="loading-dot"></view>
      <text>加载中...</text>
    </view>

    <!-- 没有更多数据提示 -->
    <view
      class="no-more"
      wx:if="{{!isEmpty && !hasMore && videoList.length > 0}}"
    >
      <text>— 已经到底了 —</text>
    </view>
  </view>
</view>

<!-- 会员弹窗 -->
<view
  class="member-dialog-mask"
  wx:if="{{showMemberDialog}}"
  bindtap="closeMemberDialog"
>
  <view
    class="member-dialog"
    catchtap="stopPropagation"
  >
    <view class="member-dialog-content">
      <view class="close-btn" bindtap="closeMemberDialog">×</view>
      <view class="member-dialog-title">立即开通会员</view>
      <view class="member-dialog-subtitle">获得以下5项VIP权益</view>

      <!-- 钻石图标 -->
      <view class="member-icon-container">
        <image
          class="member-diamond-icon"
          src="https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/wechat/icons/zs.png"
          mode="aspectFit"
        ></image>
      </view>

      <!-- 首次开通优惠标签 -->
      <view class="first-open-discount">
        <view class="discount-line"></view>
        <text class="star-icon">★</text>
        <text class="discount-text">首次开通优惠</text>
        <text class="star-icon">★</text>
        <view class="discount-line"></view>
      </view>

      <!-- 会员权益列表 -->
      <view class="member-benefits">
        <view class="benefit-item">会员视频</view>
        <view class="benefit-item">报价单生成次数</view>
        <view class="benefit-item">询盘发布次数</view>
      </view>

      <!-- 按钮区域 -->
      <view class="member-dialog-buttons">
        <!-- <button
          class="btn-cancel"
          bindtap="Pay"
        >付款观看</button> -->
        <button
          class="btn-cancel"
          bindtap="closeMemberDialog"
        >暂不开通</button>
        <button
          class="btn-confirm"
          bindtap="goToMemberPage"
        >开通会员</button>
      </view>
    </view>
  </view>
</view>