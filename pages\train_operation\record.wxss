/* pages/train_operation/record.wxss */
page {
    width: 100%;
    height: 100%;
    overflow: hidden;
    /* 禁止页面级滚动 */
    position: fixed;
    /* 固定页面，防止滚动 */
    -webkit-overflow-scrolling: touch;
    /* 提高iOS滚动体验 */
}

.container {
    min-height: 100vh;
    height: 100%;
    background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    overflow: hidden;
    /* 禁止容器滚动 */
}

/* 自定义导航栏样式 */
.custom-nav {
    width: 100%;
    background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    /* 降低z-index以避免干扰下拉刷新 */
}

.status-bar {
    width: 100%;
}

.nav-content {
    height: 44px;
    display: flex;
    align-items: center;
    position: relative;
}

.back-icon {
    position: absolute;
    left: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10rpx;
}

.back-icon image {
    width: 40rpx;
    height: 40rpx;
}

.nav-title {
    flex: 1;
    text-align: center;
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
}

/* 主内容区域 */
.main-content {
    /* 移除固定padding-top，改为在WXML中动态计算 */
    padding-left: 0;
    padding-right: 0;
    position: relative;
    z-index: 10;
    margin-top: 0;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    overflow: hidden;
    /* 禁止内容区域滚动 */
}

/* 加载中容器 */
.loading-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 30rpx;
}

/* 加载动画 */
.loading-spinner {
    width: 80rpx;
    height: 80rpx;
    border: 8rpx solid rgba(0, 0, 0, 0.1);
    border-top-color: #3B82F6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20rpx;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* 加载文本 */
.loading-text {
    color: #666;
    font-size: 28rpx;
}

/* 错误容器 */
.error-container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30rpx;
}

/* 错误文本 */
.error-text {
    color: #E53E3E;
    font-size: 28rpx;
    text-align: center;
}

/* 空数据容器 */
.empty-container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30rpx;
}

/* 空数据文本 */
.empty-text {
    color: #666;
    font-size: 28rpx;
    text-align: center;
}

/* 记录滚动区域 */
.record-scroll {
    height: 100%;
    width: 100%;
    box-sizing: border-box;
    padding: 30rpx;
}

/* 记录列表 */
.record-list {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 30rpx;
}

/* 记录项 */
.record-item {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 30rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    gap: 20rpx;
}

/* 记录行 */
.record-row {
    display: flex;
    flex-direction: row;
}

/* 记录标签 */
.record-label {
    width: 150rpx;
    color: #999;
    font-size: 28rpx;
    flex-shrink: 0;
}

/* 记录值 */
.record-value {
    flex: 1;
    color: #333;
    font-size: 28rpx;
    line-height: 1.5;
}