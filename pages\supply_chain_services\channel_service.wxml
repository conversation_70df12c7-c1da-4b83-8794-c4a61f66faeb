<!--pages/supply_chain_services/channel_service.wxml-->
<view class="container">
    <view class="gradient-bg"></view>

    <!-- 统一头部背景 -->
    <view
        class="unified-header"
        style="height: {{navBarHeight + 70}}px;"
    >
        <!-- 自定义导航栏 -->
        <view
            class="custom-navbar"
            style="height: {{navBarHeight}}px;"
        >
            <view
                class="status-bar"
                style="height: {{statusBarHeight}}px;"
            ></view>
            <view class="navbar-content">
                <view
                    class="nav-back"
                    bindtap="goBack"
                >
                    <van-icon
                        name="arrow-left"
                        size="20px"
                        color="#333"
                    />
                </view>
                <view class="nav-title">通道服务</view>
                <view class="nav-placeholder"></view>
            </view>
        </view>

        <!-- 搜索区域 (固定在导航栏下方) -->
        <view
            class="search-bar"
            style="top: {{navBarHeight-1}}px;"
        >
            <view class="search-input-wrapper">
                <icon
                    type="search"
                    size="14"
                    class="search-icon"
                ></icon>
                <input
                    class="search-input"
                    placeholder="请输入公司名称搜索"
                    placeholder-style="color: #aaa; font-size: 26rpx;"
                    value="{{ searchValue || ''}}"
                    bindinput="onSearchInput"
                    bindconfirm="onSearch"
                />
                <view
                    class="search-btn"
                    bindtap="onSearch"
                >搜索</view>
            </view>
        </view>
    </view>

    <!-- 背景过渡区域 -->
    <view
        class="transition-area"
        style="top: {{navBarHeight + 70}}px;"
    ></view>

    <!-- 页面内容容器 -->
    <view
        class="content"
        style="margin-top: {{navBarHeight + 70}}px; padding-bottom: 30rpx;"
    >

        <view class="service-list">
            <!-- 通道服务列表内容 -->
            <view
                class="service-card"
                wx:for="{{serviceList}}"
                wx:key="id"
                bindtap="onCardTap"
                data-id="{{item.id}}"
            >
                <view class="service-card-header">
                    <view class="company-name">{{item.company_name}}</view>
                    <view class="card-arrow">
                        <van-icon
                            name="arrow"
                            size="16px"
                            color="#999"
                        />
                    </view>
                </view>
                <view class="service-card-info">
                    <view class="info-row">
                        <view class="info-item">退税时长 <text class="fee-value">{{item.tax_refund_time}}</text></view>
                        <view class="info-item">服务范围 <text class="fee-value">{{item.regional}}</text></view>
                    </view>
                    <view class="fee-info">
                        通道费用 最低<text class="fee-value">{{item.min_cost}}</text>-最高<text
                            class="fee-value">{{item.max_cost}}</text>
                    </view>
                </view>
            </view>

            <!-- 加载更多提示 -->
            <view
                class="loading-more"
                wx:if="{{serviceList.length > 0}}"
            >
                <view wx:if="{{hasMoreData && loading}}">
                    <van-loading
                        size="24px"
                        type="spinner"
                    />
                    <text style="margin-left: 10rpx;">加载中...</text>
                </view>
                <view wx:elif="{{hasMoreData}}">上拉加载更多</view>
                <view wx:else>没有更多数据了</view>
            </view>

            <!-- 空状态提示 -->
            <view
                class="empty-state"
                wx:if="{{serviceList.length === 0}}"
            >

                <view class="empty-text">暂无数据</view>
            </view>
        </view>
    </view>
</view>