.container {
  min-height: 100vh;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* 自定义导航栏样式 */
.custom-nav {
  width: 100%;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}

.status-bar {
  width: 100%;
}

.nav-content {
  height: 44px;
  display: flex;
  align-items: center;
  position: relative;
}

.back-icon {
  position: absolute;
  left: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx;
}

.back-icon image {
  width: 40rpx;
  height: 40rpx;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

/* 列表容器 */
.quote-list-container {
  padding-top: calc(var(--status-bar-height, 44px) + 54px);
  padding-bottom: 50rpx;
  width: 100%;
}

/* 日期分组 */
.date-group {
  margin-bottom: 25rpx;
  width: 100%;
}

.date-title {
  padding: 20rpx 5%;
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
  width: 90%;
}

/* 报价单项目 */
.quote-item {
  background-color: #fff;
  margin: 15rpx auto;
  border-radius: 16rpx;
  padding: 0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
  display: flex;
  align-items: flex-start;
  overflow: hidden;
  position: relative;
  width: 95%;
}

.quote-content {
  flex: 1;
  overflow: hidden;
  padding: 30rpx;
  position: relative;
}

.quote-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 40rpx;
}

.divider-line {
  height: 1px;
  background-color: #EEEEEE;
  width: 100%;
  margin-bottom: 15rpx;
}

.quote-details {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.detail-row {
  display: flex;
  justify-content: space-between;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  width: 50%;
  padding-right: 15rpx;
  box-sizing: border-box;
  margin-right: 80rpx;
}

.detail-label {
  font-size: 26rpx;
  color: #999;
  flex-shrink: 0;
  font-weight: normal;
}

.detail-value {
  font-size: 26rpx;
  color: #999;
  font-weight: 500;
  /* text-align: right;
  margin-left: 15rpx; */
}

.quote-arrow {
  position: absolute;
  right: 20rpx;
  top: 30rpx;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quote-arrow image {
  width: 24rpx;
  height: 24rpx;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 70vh;
  width: 100%;
  padding: 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100%;
  position: fixed;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  top: 0;
  left: 0;
  z-index: 900;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 5rpx solid rgba(64, 128, 255, 0.1);
  border-top: 5rpx solid #4080ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 加载更多样式 */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
  width: 100%;
}

.loading-spinner-small {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid rgba(0, 0, 0, 0.1);
  border-top-color: #1C82F5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10rpx;
}

.loading-text-small {
  font-size: 24rpx;
  color: #666;
}

/* 无更多数据 */
.no-more {
  text-align: center;
  padding: 30rpx 0;
  color: #999;
  font-size: 24rpx;
  width: 100%;
}
