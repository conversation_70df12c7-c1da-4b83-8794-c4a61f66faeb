<!--pages/info/certification_review.wxml-->
<view class="container" style="--status-bar-height: {{statusBarHeight}}px;">
  <!-- 自定义导航栏 -->
  <view
    class="custom-nav"
    style="padding-top: {{statusBarHeight}}px;"
  >
    <view class="nav-content">
      <view class="back-icon" bindtap="navigateBack">
        <van-icon
          name="arrow-left"
          size="20px"
          color="#333"
        />
      </view>
      <view class="nav-title">企业认证中</view>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="main-content" style="padding-top: {{statusBarHeight + 44}}px;">
    <!-- 审核状态卡片 -->
    <view class="review-status-card">
      <!-- 机器人图标 -->
      <view class="robot-icon">
        <image 
          class="robot-image"
          src="https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/uploads/proof/1751274246972_981.png"
          mode="aspectFit"
          lazy-load="{{true}}"
        />
      </view>

      <!-- 状态文字 -->
      <view class="status-text">正在审核中</view>

      <!-- 描述文字 -->
      <view class="status-desc">
        <view class="desc-line">查看审核进度，请点击下方卡片的关注按钮</view>
        <view class="desc-line">公众号内输入"审核"获取最新动态</view>
      </view>

      <!-- 分隔线 -->
      <view class="divider"></view>



    </view>

    <!-- 温馨提示 -->
    <view class="tips-section">
      <view class="section-title" style="padding-bottom: 10px;">温馨提示</view>
      <view class="tips-card">
        <view class="tip-item">
          <van-icon name="info-o" size="16px" color="#1296db" />
          <text>审核时间通常为1-3个工作日</text>
        </view>
        <view class="tip-item">
          <van-icon name="info-o" size="16px" color="#1296db" />
          <text>如有疑问，请联系客服</text>
        </view>
        <view class="tip-item">
          <van-icon name="info-o" size="16px" color="#1296db" />
          <text>审核结果将通过短信或站内消息通知</text>
        </view>
      </view>
    </view>

    <!-- 订阅通知卡片 -->
    <view class="subscribe-section" wx:if="{{!hasSubscribed && !hasRejectedSubscribe}}">
      <view class="section-title" style="padding-bottom: 10px;">消息通知</view>
      <view class="subscribe-card">
        <view class="subscribe-header">
          <view class="subscribe-icon">
            <van-icon name="bell" size="24px" color="#1296db" />
          </view>
          <view class="subscribe-content">
            <view class="subscribe-title">开启审核结果通知</view>
            <view class="subscribe-desc">审核完成后我们将第一时间通知您，不错过重要消息</view>
          </view>
        </view>
        <view class="subscribe-actions">
          <button class="subscribe-later-btn" bindtap="remindLater">稍后提醒</button>
          <button class="subscribe-btn" bindtap="requestSubscribeMessage">立即开启</button>
        </view>
      </view>
    </view>

    <!-- 已订阅提示 -->
    <view class="subscribe-section" wx:if="{{hasSubscribed}}">
      <view class="section-title" style="padding-bottom: 0px;">消息通知</view>
      <view class="subscribed-card">
        <view class="subscribed-content">
          <van-icon name="success" size="20px" color="#07c160" />
          <text class="subscribed-text">已开启审核结果通知</text>
        </view>
      </view>
    </view>

    <!-- 撤回审核按钮 -->
    <view class="withdraw-section">
      <button class="withdraw-btn" bindtap="withdrawReview">
        撤回审核
      </button>
    </view>
  </view>
</view>