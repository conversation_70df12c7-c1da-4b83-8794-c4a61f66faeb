<!--pages/information/index.wxml-->
<skeleton loading="{{pageLoading}}">
  <view class="container">
    <!-- 自定义导航栏 -->
    <view class="custom-nav" style="padding-top: {{statusBarHeight}}px; --status-bar-height: {{statusBarHeight}}px;">
      <view class="status-bar"></view>
      <view class="nav-content">
        <view class="back-icon" bindtap="goBack">
          <image src="/icons/moments/back.svg" mode="aspectFit"></image>
        </view>
        <view class="nav-title">商家信息</view>
      </view>
    </view>
    
    <!-- 页面内容 -->
    <view class="page-content" style="--nav-height: {{navHeight}}px;">
      <!-- 商家信息卡片 -->
      <view class="merchant-card">
        <!-- 商家基本信息 -->
        <view class="merchant-header">
          <image class="merchant-logo" src="{{merchantInfo.logo}}" mode="aspectFill"></image>
          <view class="merchant-basic-info">
            <view class="merchant-name">{{merchantInfo.company_name}}</view>
            <view class="merchant-phone">{{merchantInfo.mobile}}</view>
            <view class="merchant-time">营业时间: {{merchantInfo.time}}</view>
          </view>
        </view>
        
        <view class="merchant-detail-info">
          <view class="merchant-address-container">
            <image class="address-icon" src="/icons/infomation/address.png" mode="aspectFit"></image>
            <text class="location-text">地址:</text>
            <view class="merchant-address">{{merchantInfo.address}}</view>
          </view>
        </view>
        
        <!-- 联系按钮 -->
        <view class="contact-buttons">
          <button class="contact-btn call-btn" bindtap="callMerchant">电话联系</button>
          <button class="contact-btn navigate-btn" bindtap="navigateToMerchant">在线联系</button>
        </view>
      </view>
      
      <!-- 在售车辆选项卡 -->
      <view class="tab-container">
        <view class="section-title">在售车源</view>
        <view class="tab-bar">
          <view class="tab-item {{currentTab === 'used' ? 'active' : ''}}" data-tab="used" bindtap="switchTab">二手车</view>
          <view class="tab-item {{currentTab === 'batch' ? 'active' : ''}}" data-tab="batch" bindtap="switchTab">批量车</view>
          <view class="tab-item {{currentTab === 'new' ? 'active' : ''}}" data-tab="new" bindtap="switchTab">新车</view>
          <view class="tab-item {{currentTab === 'commercial' ? 'active' : ''}}" data-tab="commercial" bindtap="switchTab">工程车</view>
        </view>
        
        <!-- 加载中提示 -->
        <view class="loading-container" wx:if="{{isLoading && !pageLoading}}">
          <view class="loading-spinner"></view>
          <text class="loading-text">加载中...</text>
        </view>
        
        <!-- 车辆列表 -->
        <view class="car-list" wx:if="{{carList.length > 0}}">
          <view class="car-item" wx:for="{{carList}}" wx:key="id" bindtap="goToCarDetail" data-id="{{item.id}}">
            <image class="car-image" src="{{item.image}}" mode="aspectFill" lazy-load="true"></image>
            <view class="car-info">
              <view class="car-title">{{item.title}}</view>
              <view class="car-params" wx:if="{{ currentTab == 'used'}}">{{item.year}}年 | {{item.mileage}}万公里 | {{item.type}}</view>
              <view class="car-params" wx:if="{{ currentTab == 'batch'}}">{{item.nature_name}} | 出售数量:{{item.sell_number}}</view>
              <view class="car-address" wx:if="{{ currentTab == 'new'}}">国六 | {{item.address}} | {{item.publishTime}}</view>
              <view class="car-address" wx:else>{{item.address}} | {{item.publishTime}}</view>

              <view class="car-price">
                <text class="guide-price">新车指导价{{item.guidePrice}}万元</text>
                <text class="sale-price">{{item.price}}万元</text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 空状态 -->
        <view class="empty-state" wx:if="{{!isLoading && carList.length === 0}}">
          <text class="empty-text">暂无车辆信息</text>
        </view>
      </view>
    </view>
  </view>
</skeleton>