//语言列表
const message = {
  'en-US':require('./langPage/en-US'),
  'zh-CN': require('./langPage/zh-CN')
}

//支持的语言列表
const supportLang = [
  {code:'zh-CN','name':'中文',flag:'CN'},
  {code:'en-US','name':'EN',flag:'US'},
];

//获取当前语言
function getCurrentLang(){
  return wx.getStorageSync('lang') || 'zh-CN'
}

//设置语言并刷新所有页面
function setLang(lang){
  wx.setStorageSync('lang', lang);
  // 获取所有当前页面
  const pages = getCurrentPages();

  //变量所有页面，调用刷新方法
  pages.forEach(page => {
    //如果页面有refreshLanguage方法，就调用它
    if (page.refreshLanguage && typeof page.refreshLanguage === 'function') {
      try{
        page.refreshLanguage(lang);
      }catch(error){
        console.error('页面刷新失败：',error);
      }
    }

    if(page.updateText && typeof page.updateText === 'function'){
       try{
          page.updateText();
       } catch(error){
          console.error('更新页面失败：',error);
       }
    }
  });

  //切换成功弹窗
  wx.showToast({
    title: t('languageSwitchSuccess'),
    icon: 'success'
  });

  const app = getApp();
  if (app._langSwitchers) {
    app._langSwitchers.forEach(comp => {
      if (comp.initLanguageData) comp.initLanguageData();
    });
  }
}

//翻译函数
function t(key,params = {}){
  const currentLang = getCurrentLang();
  // console.log(message)
  let text =  message[currentLang].trans(key) || key;

  //替换函数
  Object.keys(params).forEach(param=>{
    text = text.replace(`{{${param}}}`, params[param]);
  });
  //调用方法
  //t('totalVideos', { count: 5 }),结果会变成：共5条视频
  return text;
}

//获取支持的语言列表
function getSupportLangs(){
  return supportLang;
}

//刷新指定页面的语言
function refreshPageLanguage(pageInstance) {
  if (pageInstance && pageInstance.refreshLanguage) {
    pageInstance.refreshLanguage(getCurrentLang());
  }
}

// 刷新当前页面所以页面语言
function refreshAllPages(){
  const pages = getCurrentPages();
  pages.forEach(page=>{
    refreshPageLanguage(page);
  });
}

module.exports = {
  message,
  supportLang,
  getCurrentLang,
  setLang,
  t,
  getSupportLangs,
  refreshPageLanguage,
  refreshAllPages
};