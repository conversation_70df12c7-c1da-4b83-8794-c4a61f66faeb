/* pages/testing_services/index.wxss */
page {
  width: 100%;
  height: 100%;
  overflow: hidden;
  /* 禁止页面级滚动 */
  position: fixed;
  /* 固定页面，防止滚动 */
  -webkit-overflow-scrolling: touch;
  /* 提高iOS滚动体验 */
}

.container {
  min-height: 100vh;
  height: 100%;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  overflow: hidden;
  /* 禁止容器滚动 */
}

/* 自定义导航栏样式 */
.custom-nav {
  width: 100%;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  /* 降低z-index以避免干扰下拉刷新 */
}

.search-input-wrapper {
  flex: 1;
  display: flex;
  height: 70rpx;
  box-sizing: border-box;
  border-radius: 16rpx;
  overflow: hidden;
  background-color: #f7f7f7;
  align-items: center;
  position: relative;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid #1C82F5;
  transition: all 0.2s ease-in-out;
  padding-left: 10rpx;
}

.search-bar {
  padding: 20rpx 30rpx;
  width: 100%;
  box-sizing: border-box;
}

.top-container {
  position: relative;
  width: 100%;
  box-sizing: border-box;
  z-index: 1001;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  overflow: hidden;
  padding: 0;
  /* 移除可能的内边距 */
  margin-bottom: 11rpx;
}

.search-icon {
  margin-left: 20rpx;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-input {
  flex: 1;
  height: 100%;
  padding: 0 110rpx 0 15rpx;
  font-size: 28rpx;
  background-color: transparent;
  border: none;
  color: #333;
}

.search-btn {
  height: 56rpx;
  line-height: 56rpx;
  background: linear-gradient(to right, #1C82F5, #27AFF5);
  color: #fff;
  font-size: 28rpx;
  text-align: center;
  padding: 0 30rpx;
  border-radius: 16rpx;
  position: absolute;
  right: 7rpx;
  top: 50%;
  transform: translateY(-50%);
  box-shadow: 0 2rpx 8rpx rgba(56, 136, 255, 0.3);
  transition: all 0.2s ease-in-out;
}

.search-btn:active {
  background: linear-gradient(to right, #1973dd, #24a0e5);
  box-shadow: 0 1rpx 3rpx rgba(56, 136, 255, 0.2);
  transform: translateY(-50%) scale(0.98);
}

.status-bar {
  width: 100%;
}

.nav-content {
  height: 44px;
  display: flex;
  align-items: center;
  position: relative;
}

.back-icon {
  position: absolute;
  left: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx;
}

.back-icon image {
  width: 40rpx;
  height: 40rpx;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

/* 主内容区域 */
.main-content {
  /* 移除固定padding-top，改为在WXML中动态计算 */
  padding-left: 0;
  padding-right: 0;
  position: relative;
  z-index: 10;
  margin-top: 0;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
  /* 禁止内容区域滚动 */
}

/* 筛选栏样式 */
.filter-bar {
  display: flex;
  height: 90rpx;
  /* 统一使用rpx单位 */
  background-color: #ffffff;
  border-bottom: 1px solid #eeeeee;
  /* 移除fixed定位 */
  width: 100%;
  z-index: 900;
  margin: 20rpx 0 0;
  box-sizing: border-box;
  /* 确保padding不会增加宽度 */
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.filter-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #333;
}

.filter-item.active {
  color: #1989fa;
}

.filter-item text {
  margin-right: 4px;
}

/* 筛选图标样式 */
.filter-icon {
  width: 24rpx;
  height: 24rpx;
  margin-left: 6rpx;
  flex-shrink: 0;
  /* 防止图标被压缩 */
}

.filter-icon.active {
  /* 当图标处于激活状态时的样式 */
  tint-color: #1989fa;
}

.iconfont {
  font-size: 12px;
  color: #999;
}

.iconfont.active {
  color: #1989fa;
}

/* 筛选面板样式 */
.filter-panel {
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 899;
  /* 确保在筛选栏下方 */
  width: 100%;
  max-height: 600rpx;
  border-bottom-left-radius: 12rpx;
  border-bottom-right-radius: 12rpx;
  /* 移除顶部边框线，增强与筛选栏的视觉连接性 */
  border-top: none;
  position: fixed;
  left: 0;
  margin-left: 0;
  /* 通过状态栏高度+导航栏高度+筛选栏高度+筛选栏margin-top来计算 */
  /* 状态栏+导航栏高度通过padding-top动态计算，筛选栏高度为90rpx，margin为20rpx */
  top: calc(var(--status-bar-height, 44px) + 44px + 110rpx);
  box-sizing: border-box;
}

/* 筛选面板内容 */
.filter-panel-content {
  max-height: 600rpx;
  padding: 20rpx 0;
  width: 100%;
  box-sizing: border-box;
}

/* 筛选标题 */
.filter-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
  padding-left: 20rpx;
  margin-top: 10rpx;
}

/* 品牌列表样式 */
.brand-list {
  display: flex;
  flex-wrap: wrap;
  padding: 0 10rpx;
  width: 100%;
  box-sizing: border-box;
}

.brand-item {
  width: 30%;
  height: 60rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 10rpx 1.5%;
  font-size: 24rpx;
  color: #333;
}

.brand-item.active {
  background-color: #1989fa;
  color: #ffffff;
}

/* 订单类型列表样式 */
.order-type-list {
  padding: 0 20rpx;
  width: 100%;
  box-sizing: border-box;
}

.order-type-item {
  height: 80rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1px solid #eeeeee;
}

.order-type-item:last-child {
  border-bottom: none;
}

.order-type-item.active {
  color: #1989fa;
}

/* 车辆列表样式 */
.vehicle-list {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  /* 防止横向滚动 */
  width: 100%;
  margin-top: 0;
  box-sizing: border-box;
  padding-left: 0;
  padding-right: 0;
  /* 确保列表可以滚动到底部，且最后一项不被底部导航条遮挡 */
  padding-bottom: calc(env(safe-area-inset-bottom) + 180rpx);
  min-height: 100vh;
}

.vehicle-item {
  position: relative;
  background-color: #ffffff;
  padding: 30rpx 0;
  margin-bottom: 20rpx;
  /* border-radius: 12rpx; */
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
}

/* 车型名称和箭头行 */
.vehicle-name-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
  padding: 0 30rpx;
}

.vehicle-name {
  flex: 1;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 10rpx;
}

.arrow {
  display: flex;
  align-items: center;
}

.arrow image {
  width: 28rpx;
  height: 20rpx;
}

.vehicle-info {
  margin-bottom: 20rpx;
  padding: 0 30rpx;
}

/* 新增三列布局样式 */
.info-header,
.info-content {
  display: flex;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.info-header {
  margin-bottom: 20rpx;
}

.info-text {
  color: #3D3D3D !important;
}

.info-header text,
.info-content text {
  flex: 1;
  font-size: 26rpx;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.info-header text:first-child,
.info-content text:first-child {
  margin-left: -70rpx;
  /* 修改负margin，防止溢出 */
  /* padding-left: 0; */
}

.info-header text {
  color: #888;
}

.info-content text {
  color: #333;
}

/* 原有行样式保留以备使用 */
.info-row {
  display: flex;
  margin-bottom: 12px;
}

.info-label {
  width: 120px;
  font-size: 13px;
  color: #888;
}

.info-value {
  flex: 1;
  font-size: 13px;
  color: #333;
}

/* 发布时间样式 */
.publish-time {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
  width: 100%;
  /* text-align: right; */
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 30rpx;
  margin-top: 20rpx;
}

/* 图标样式 */
.icon-back:before {
  content: "<";
}

.icon-down:before {
  content: "∨";
}

.icon-sort:before {
  content: "↕";
}

.icon-right:before {
  content: ">";
}

/* 空数据提示 */
.empty-tip {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}

/* 确保滚动容器正常工作 */
scroll-view {
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  width: 100%;
}

/* iOS专用修复样式 */
.ios-fix {
  width: 100%;
  overflow-x: hidden;
}

/* iOS底部安全区域适配 */
@supports (padding-bottom: constant(safe-area-inset-bottom)) {
  .container {
    padding-bottom: 0;
  }
}

@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .container {
    padding-bottom: 0;
  }
}

/* iOS边框修复 */
@media (-webkit-min-device-pixel-ratio: 2) {
  .filter-bar {
    border-bottom: 0.5px solid #eeeeee;
  }

  .order-type-item {
    border-bottom: 0.5px solid #eeeeee;
  }
}

/* 修复横向滚动问题的最终解决方案 */
.container,
page,
.main-content,
.vehicle-list,
.filter-bar,
.filter-panel {
  max-width: 100vw;
}

/* 加载更多样式 */
.loading-more {
  width: 100%;
  padding: 20rpx 0 calc(env(safe-area-inset-bottom) + 20rpx);
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
  border: 2rpx solid #f3f3f3;
  border-top: 2rpx solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.no-more,
.pull-tip {
  color: #999;
  font-size: 24rpx;
  text-align: center;
  padding: 20rpx 0;
  padding-bottom: calc(env(safe-area-inset-bottom) + 40rpx);
  margin-bottom: 60rpx;
}

/* 点击加载更多按钮 */
.load-more-btn {
  width: 60%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #f8f8f8;
  color: #666;
  font-size: 28rpx;
  border-radius: 40rpx;
  margin: 20rpx auto calc(env(safe-area-inset-bottom) + 20rpx);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}