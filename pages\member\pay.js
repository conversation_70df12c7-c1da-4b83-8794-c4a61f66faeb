// pages/member/pay.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: 20, // 默认状态栏高度
    navBarHeight: 64, // 导航栏高度
    menuButtonHeight: 32, // 菜单按钮高度
    showPaymentCard: false, // 是否显示支付状态卡片
    paymentStatus: '支付中' // 支付状态文本
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取状态栏高度和菜单按钮位置信息
    const systemInfo = wx.getSystemInfoSync();
    const menuButtonInfo = wx.getMenuButtonBoundingClientRect();

    // 计算导航栏高度
    const navBarHeight = menuButtonInfo.bottom + 6;

    this.setData({
      statusBarHeight: systemInfo.statusBarHeight,
      navBarHeight: navBarHeight,
      menuButtonHeight: menuButtonInfo.height
    });

    // 如果是从会员页跳转来的，则直接展示页面
    if (options.fromMember === 'true') {
      // 不显示支付状态卡片，直接显示支付页面
      return;
    }

    // 否则默认进入页面就显示支付状态
    this.setData({
      showPaymentCard: true,
      paymentStatus: '支付中'
    });

    // 3秒后隐藏支付状态
    setTimeout(() => {
      this.setData({
        showPaymentCard: false
      });
    }, 3000);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 处理支付
   */
  handlePayment() {
    // 显示支付状态卡片
    this.setData({
      showPaymentCard: true,
      paymentStatus: '支付中'
    });

    // 模拟支付过程，3秒后更新支付状态
    setTimeout(() => {
      this.setData({
        paymentStatus: '支付成功'
      });

      // 再等待1.5秒后隐藏支付状态卡片并返回
      setTimeout(() => {
        wx.navigateBack({
          delta: 1
        });
      }, 1500);
    }, 3000);
  }
})

/**
 * 跳转到支付页面，显示支付状态
 */
function navigateToPayWithStatus() {
  wx.navigateTo({
    url: '/pages/member/pay?showStatus=true'
  });
}

// 将函数导出，以便其他页面可以调用
module.exports = {
  navigateToPayWithStatus: navigateToPayWithStatus
}