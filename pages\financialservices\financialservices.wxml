<!-- 引入登录弹窗模板 -->
<import src="../../templates/loginPopup/loginPopup.wxml" />

<view
  class="container"
  style="{{ pageStyle }}"
>
  <!-- 骨架屏 - 仅在页面加载中且不显示正常加载动画时显示 -->
  <view
    class="skeleton-screen"
    wx:if="{{isLoading && !showLoadingAnimation}}"
  >
    <!-- 骨架自定义导航栏 -->
    <view class="skeleton-custom-nav">
      <view
        class="skeleton-status-bar"
        style="height: {{statusBarHeight}}px;"
      ></view>
      <view class="skeleton-nav-title"></view>
    </view>

    <!-- 骨架顶部容器，用于包含搜索栏 -->
    <view class="skeleton-top-container">
      <!-- 骨架搜索栏 -->
      <view class="skeleton-search-bar">
        <view class="skeleton-search-input"></view>
      </view>
    </view>

    <!-- 骨架筛选菜单 -->
    <view class="skeleton-filter-menu">
      <view class="skeleton-filter-item"></view>
      <view class="skeleton-filter-item"></view>
      <view class="skeleton-filter-item"></view>
      <view class="skeleton-filter-item"></view>
    </view>

    <!-- 骨架车辆列表 -->
    <view class="skeleton-car-list">
      <view
        class="skeleton-car-item"
        wx:for="{{6}}"
        wx:key="index"
      >
        <view class="skeleton-car-image"></view>
        <view class="skeleton-car-info">
          <view class="skeleton-car-title"></view>
          <view class="skeleton-car-params"></view>
          <view class="skeleton-car-price"></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 顶部区域：固定高度容器，用于容纳搜索框或筛选标题 -->
  <view class="fixed-header">
    <!-- 自定义导航栏 -->
    <view class="custom-nav">
      <view
        class="status-bar"
        style="height: {{statusBarHeight}}px;"
      ></view>
      <view class="nav-title">
        <view style="display: flex;">
          <view
            bindtap="navigateBack"
            style="margin-left:10rpx;"
          >
            <van-icon
              name="arrow-left"
              size="20px"
              color="#333"
            />
          </view>
          <view style="text-align: center;width: 90%;">金融服务</view>
        </view>
      </view>
    </view>
    <view
      class="top-container"
      style="height: {{ topContainerHeight }}px;"
    >
      <!-- 搜索区域 -->
      <view class="search-bar">
        <view class="search-input-wrapper">
          <icon
            type="search"
            size="14"
            class="search-icon"
          ></icon>
          <input
            class="search-input"
            placeholder="请输入服务范围或公司名称"
            placeholder-style="color: #aaa; font-size: 26rpx;"
            value="{{ searchValue || ''}}"
            bindinput="onSearchInput"
            bindconfirm="onSearch"
          />
          <view
            class="search-btn"
            bindtap="onSearch"
          >搜索</view>
        </view>
      </view>
    </view>
  </view>
  <!-- 内容区域 - 添加一个占位符，高度等于固定头部的高度 -->
  <view class="header-placeholder"></view>

  <!-- 车辆列表 -->
  <view class="car-list">
    <block
      wx:for="{{ carList }}"
      wx:key="id"
    >
      <view
        class="car-item"
        data-id="{{ item.id }}"
        bindtap="onCarItemTap"
      >

        <view class="company-card">
          <view class="company-title">
            <text class="company-title-name">{{item.company_name}}</text>
            <image
              src="/assets/images/dayuhao.png"
              style="width: 32rpx; height: 32rpx;"
            />
          </view>
          <view class="company-info">
            <view class="info-row">
              <view class="info-lable">垫资周期</view>
              <view class="info-lable">垫资范围</view>
              <view class="info-lable">办理手续费</view>
            </view>
            <view class="info-row-content">
              <view class="info-lable-content">{{item.period}}</view>
              <view class="info-lable-content">{{item.restricted_area}}</view>
              <view class="info-lable-content">{{item.cost}}</view>
            </view>
            <view class="info-row-area">
              <text class="info-row-area-title">服务范围: </text> <text
                class="info-row-area-conent">{{item.regional}}</text>
            </view>
          </view>
        </view>
      </view>
    </block>
    <view
      class="loading"
      wx:if="{{ isLoading && showLoadingAnimation }}"
    >
      <text>加载中...</text>
    </view>
    <view
      class="no-more"
      wx:if="{{ !hasMoreData && carList.length > 0 }}"
    >
      <text>没有更多数据了</text>
    </view>
    <view
      class="empty"
      wx:if="{{ !isLoading && carList.length === 0 }}"
    >
      <text>暂无车辆数据</text>
    </view>
  </view>

  <!-- 使用 Vant 的 Overlay 组件 -->
  <van-overlay
    show="{{ showAreaPopup }}"
    z-index="1001"
    bind:click="onCloseAreaPopup"
  />

  <van-popup
    show="{{ showAreaPopup }}"
    position="top"
    bind:close="onCloseAreaPopup"
    z-index="1002"
    overlay="false"
    custom-class="city-popup"
  >
    <!-- 添加全国选项和省市选择器 -->
    <view class="area-container">
      <!-- 全国选项 -->
      <view
        class="all-city"
        bindtap="onSelectAllCity"
      >
        <text>全国</text>
      </view>
      <!-- 省市选择器 -->
      <van-area
        area-list="{{ areaList }}"
        value="{{ selectedAreaCode }}"
        bind:confirm="onConfirmArea"
        bind:cancel="onCloseAreaPopup"
        custom-class="custom-area"
        columns-num="{{ 2 }}"
      />
    </view>
  </van-popup>

  <!-- 筛选弹出层 -->
  <van-popup
    show="{{ filterPopupVisible }}"
    position="right"
    custom-style="width: 100%; height: 100%;"
    bind:close="onCloseFilterPopup"
  >
    <view class="filter-popup">
      <view class="filter-popup-header">
        <view class="filter-popup-title">筛选</view>
        <view
          class="filter-popup-close"
          bindtap="onCloseFilterPopup"
        >✕</view>
      </view>

      <view class="filter-popup-content">
        <!-- 地区选择 -->
        <view class="filter-section">
          <view class="section-title">所在地</view>
          <van-area
            area-list="{{ areaList }}"
            columns-num="2"
            bind:change="onAreaChange"
            value="{{ selectedAreaCode }}"
            show-toolbar="{{ false }}"
          />
        </view>

        <!-- 车龄范围 -->
        <view class="filter-section">
          <view class="section-title">车龄</view>
          <van-slider
            value="{{ ageRange }}"
            min="0"
            max="8"
            step="2"
            range
            bind:change="onAgeChange"
          />
          <view class="slider-labels">
            <text>0年</text>
            <text>2年</text>
            <text>4年</text>
            <text>6年</text>
            <text>8年</text>
            <text>不限</text>
          </view>
        </view>

        <!-- 里程范围 -->
        <view class="filter-section">
          <view class="section-title">里程（万公里）</view>
          <van-slider
            value="{{ mileageRange }}"
            min="0"
            max="8"
            step="2"
            range
            bind:change="onMileageChange"
          />
          <view class="slider-labels">
            <text>0</text>
            <text>2</text>
            <text>4</text>
            <text>6</text>
            <text>8</text>
            <text>不限</text>
          </view>
        </view>

        <!-- 价格范围 -->
        <view class="filter-section">
          <view class="section-title">价格范围（万元）</view>
          <van-slider
            value="{{ priceRange }}"
            min="0"
            max="40"
            step="10"
            range
            bind:change="onPriceChange"
          />
          <view class="slider-labels">
            <text>0</text>
            <text>10</text>
            <text>20</text>
            <text>30</text>
            <text>40</text>
            <text>不限</text>
          </view>
        </view>
      </view>

      <!-- 底部按钮 -->
      <view class="filter-popup-footer">
        <view
          class="reset-btn"
          bindtap="onResetFilter"
        >重置</view>
        <view
          class="confirm-btn"
          bindtap="onConfirmFilter"
        >确定</view>
      </view>
    </view>
  </van-popup>

  <!-- 引用登录弹窗模板 -->
  <template
    is="loginPopup"
    data="{{ showLoginPopup, loginPopupOptions }}"
  />
</view>