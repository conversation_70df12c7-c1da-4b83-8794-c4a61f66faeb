Component({
    properties: {
        loading: {
            type: Boolean,
            value: true
        },
        bgcolor: {
            type: String,
            value: '#f2f2f2'
        },
        selector: {
            type: String,
            value: 'skeleton'
        },
        unit: {
            type: String,
            value: 'px'
        },
        animated: {
            type: Boolean,
            value: true
        },
        contentBgcolor: {
            type: String,
            value: '#f8f8f8'
        }
    },
    data: {
        firstLoading: true
    },
    attached() {
        // 页面首次加载时显示骨架屏
        this.setData({
            firstLoading: true
        });
    },
    methods: {
        // 隐藏骨架屏
        hideLoading() {
            this.setData({
                firstLoading: false
            });
        }
    }
}) 