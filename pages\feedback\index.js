import api from '../../utils/api';
import util from '../../utils/util';
import cosUpload from '../../utils/cos-upload';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: 20, // 默认状态栏高度
    selectedType: '', // 选中的问题类型
    description: '', // 问题描述
    descriptionLength: 0, // 问题描述字数
    uploadImages: [], // 上传的图片
    urls:[], // 上传图片的相对路径
    contact: '', // 联系方式
    isIOS: false, // 是否是iOS设备
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    
    // 检查是否是iOS设备
    const isIOS = systemInfo.platform === 'ios';
    
    // 设置状态栏高度并更新iOS标志
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight,
      isIOS: isIOS
    });
    
    // 为iOS设备添加自定义样式变量
    if (isIOS) {
      wx.setNavigationBarColor({
        frontColor: '#000000',
        backgroundColor: '#FFFFFF',
        animation: {
          duration: 0,
          timingFunc: 'easeIn'
        }
      });
    }
  },

  /**
   * 选择问题类型
   */
  selectType: function (e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      selectedType: type
    });
  },

  /**
   * 输入问题描述
   */
  inputDescription: function (e) {
    const value = e.detail.value;
    this.setData({
      description: value,
      descriptionLength: value.length
    });
  },

  /**
   * 输入联系方式
   */
  inputContact: function (e) {
    this.setData({
      contact: e.detail.value
    });
  },

  /**
   * 选择图片
   */
  chooseImage: function () {
    const that = this;
    wx.chooseImage({
      count: 3 - that.data.uploadImages.length, // 最多可以选择的图片张数
      sizeType: ['compressed'], // 可以指定是原图还是压缩图，默认二者都有
      sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
      success: function (res) {
        // 返回选定照片的本地文件路径列表
        const tempFilePaths = res.tempFilePaths;
        
        wx.showLoading({
          title: '上传中...',
          mask: true
        });

        // 上传图片
        const uploadPromises = tempFilePaths.map(filePath => {
          return cosUpload.uploadFile(filePath, 'feedback');
        });

        Promise.all(uploadPromises)
          .then(results => {
            const newImages = results.map(result => result.url);
            const urls = results.map(result => '/'+result.key);
            that.setData({
              uploadImages: [...that.data.uploadImages, ...newImages],
              urls:[...that.data.urls, ...urls]
            });
            wx.hideLoading();
          })
          .catch(error => {
            console.error('上传失败:', error);
            wx.hideLoading();
            wx.showToast({
              title: '图片上传失败',
              icon: 'none'
            });
          });
      }
    });
  },

  /**
   * 删除图片
   */
  deleteImage: function (e) {
    const index = e.currentTarget.dataset.index;
    const uploadImages = this.data.uploadImages;
    const urls = this.data.urls
    uploadImages.splice(index, 1);
    urls.splice(index,1);
    this.setData({
      uploadImages: uploadImages,
      urls:urls,
    });
  },

  /**
   * 提交反馈
   */
  submitFeedback: function () {
    const { selectedType, description, uploadImages, contact } = this.data;
  
    // 验证必填项
    if (!selectedType) {
      wx.showToast({
        title: '请选择问题类型',
        icon: 'none'
      });
      return;
    }

    if (!description.trim()) {
      wx.showToast({
        title: '请填写问题描述',
        icon: 'none'
      });
      return;
    }

    // 显示加载中
    wx.showLoading({
      title: '提交中...',
      mask: true
    });

    // 调用API提交反馈
    api.user.feedback({
      app_id: util.getAppId(),
      type: selectedType,
      content: description,
      image_urls: this.data.urls.join(','),
      contact: contact
    }).then(res => {
      wx.hideLoading();
      wx.showToast({
        title: '提交成功',
        icon: 'success',
        duration: 2000,
        success: () => {
          // 延迟返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 2000);
        }
      });
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '提交失败，请重试',
        icon: 'none'
      });
      console.error('提交反馈失败:', err);
    });
  },

  /**
   * 返回上一页
   */
  navigateBack: function () {
    wx.navigateBack();
  }
});
