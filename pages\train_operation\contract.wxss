/* pages/train_operation/contract.wxss */
page {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: fixed;
    -webkit-overflow-scrolling: touch;
    background-color: #f8f8f8;
}

.container {
    min-height: 100vh;
    height: 100%;
    background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    overflow: hidden;
}

/* 自定义导航栏样式 */
.custom-nav {
    width: 100%;
    background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
}

.status-bar {
    width: 100%;
}

.nav-content {
    height: 44px;
    display: flex;
    align-items: center;
    position: relative;
}

.back-icon {
    position: absolute;
    left: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10rpx;
}

.nav-title {
    flex: 1;
    text-align: center;
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
}

/* 主内容区域 */
.main-content {
    padding-left: 0;
    padding-right: 0;
    position: relative;
    z-index: 10;
    margin-top: 0;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    overflow: auto;
}

/* 合同内容区域 */
.contract-content {
    padding: 30rpx;
}

/* 上传卡片样式 */
.upload-card {
    background-color: #ffffff;
    border-radius: 16rpx;
    padding: 30rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 卡片标题 */
.card-title {
    width: 192rpx;
    height: 60rpx;
    font-family: 'Source Han Sans', sans-serif;
    font-size: 32rpx;
    font-weight: normal;
    line-height: 56rpx;
    letter-spacing: normal;
    color: #3D3D3D;
    margin-bottom: 30rpx;
}

/* 上传区块 */
.upload-section {
    padding-bottom: 30rpx;
    margin-bottom: 30rpx;
    border-bottom: 1px solid #f0f0f0;
}

.upload-section:last-of-type {
    border-bottom: none;
}

.upload-title,
.remark-title {
    width: 452rpx;
    height: 68rpx;
    font-family: 'Source Han Sans', sans-serif;
    font-size: 24rpx;
    font-weight: 350;
    line-height: 32rpx;
    display: flex;
    align-items: center;
    letter-spacing: normal;
    color: #808384;
    margin-bottom: 20rpx;
}

.upload-area {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
}

.upload-button {
    width: 80rpx;
    height: 80rpx;
    border: 1px dashed #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8rpx;
    margin-right: 20rpx;
    margin-bottom: 20rpx;
}

.upload-limit {
    font-size: 24rpx;
    color: #999;
    margin-top: 10rpx;
}

/* 文件卡片样式 */
.file-cards {
    display: flex;
    flex-wrap: wrap;
}

.file-card {
    width: 120rpx;
    margin-right: 20rpx;
    margin-bottom: 20rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.file-card-content {
    width: 100%;
    height: 120rpx;
    background-color: #f8f8f8;
    border-radius: 8rpx;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #eee;
    box-sizing: border-box;
    overflow: hidden;
}

.file-icon {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 图片文件样式 */
.file-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.file-delete {
    position: absolute;
    top: -10rpx;
    right: -10rpx;
    width: 30rpx;
    height: 30rpx;
    background-color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
    z-index: 10;
}

.file-name {
    width: 100%;
    font-size: 20rpx;
    color: #333;
    text-align: center;
    margin-top: 8rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 备注样式 */
.remark-section {
    padding-top: 20rpx;
    padding-bottom: 30rpx;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 30rpx;
}

.remark-input {
    width: 100%;
    height: 160rpx;
    border: 1px solid #eee;
    border-radius: 8rpx;
    padding: 20rpx;
    font-size: 28rpx;
    box-sizing: border-box;
}

/* 按钮组样式 */
.button-group {
    display: flex;
    justify-content: space-between;
    padding: 0 50rpx;
}

.btn-save,
.btn-submit {
    width: 210rpx;
    height: 56rpx;
    line-height: 56rpx;
    border-radius: 16rpx;
    font-size: 28rpx;
    outline: none;
}

.btn-save {
    background-color: #FFFFFF;
    color: #3B82F6;
    box-sizing: border-box;
    border: 1px solid #3B82F6;
}

.btn-submit {
    background-color: #3B82F6;
    color: #fff;
    border: none;
}

/* iOS底部安全区域适配 */
@supports (padding-bottom: constant(safe-area-inset-bottom)) {
    .container {
        padding-bottom: constant(safe-area-inset-bottom);
    }
}

@supports (padding-bottom: env(safe-area-inset-bottom)) {
    .container {
        padding-bottom: env(safe-area-inset-bottom);
    }
}