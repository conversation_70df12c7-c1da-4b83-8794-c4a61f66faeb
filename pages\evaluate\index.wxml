<view class="evaluate-container">
  <!-- 头部导航栏 -->
  <view class="nav-bar">
    <view class="nav-back" bindtap="navigateBack">
      <text class="back-icon">←</text>
    </view>
    <view class="nav-title">车价评估</view>
  </view>
  
  <!-- 表单区域 -->
  <view class="form-container">
    <!-- 品牌选择 -->
    <view class="form-item" bindtap="selectBrand">
      <view class="label">品牌</view>
      <view class="value-container">
        <text class="value">{{brandName}}</text>
        <text class="arrow">›</text>
      </view>
    </view>
    
    <!-- 车系选择 -->
    <view class="form-item" bindtap="selectSeries">
      <view class="label">车系</view>
      <view class="value-container">
        <text class="value">{{seriesName}}</text>
        <text class="arrow">›</text>
      </view>
    </view>
    
    <!-- 车型选择 -->
    <view class="form-item" bindtap="selectModel">
      <view class="label">车型</view>
      <view class="value-container">
        <text class="value">{{modelName}}</text>
        <text class="arrow">›</text>
      </view>
    </view>
    
    <!-- 车辆所在地 -->
    <view class="form-item" bindtap="selectLocation">
      <view class="label">车辆所在地</view>
      <view class="value-container">
        <text class="value">{{locationName}}</text>
        <text class="arrow">›</text>
      </view>
    </view>
    
    <!-- 上牌时间 -->
    <view class="form-item" bindtap="selectRegisterTime">
      <view class="label">上牌时间</view>
      <view class="value-container">
        <text class="value">{{registerTimeName}}</text>
        <text class="arrow">›</text>
      </view>
    </view>
    
    <!-- 行驶里程 -->
    <view class="form-item" bindtap="selectMileage">
      <view class="label">里程(公里)</view>
      <view class="value-container">
        <text class="value">{{mileageName}}</text>
        <text class="arrow">›</text>
      </view>
    </view>
  </view>
  
  <!-- 提交按钮区域 -->
  <view class="submit-container">
    <view class="agreement-container" bindtap="toggleAgreement">
      <view class="checkbox {{isAgreed ? 'checked' : ''}}">
        <text wx:if="{{isAgreed}}" class="check-icon">✓</text>
      </view>
      <view class="agreement-text">
        已同意
        <text class="link" catchtap="goToPrivacyPolicy">《隐私政策》</text>和
        <text class="link" catchtap="goToConsultation">《二手车买卖咨询》</text>，提交后将获得1-3个商家的报价服务
      </view>
    </view>
    
    <button class="submit-button" bindtap="submitForm">免费评估</button>
  </view>
  
  <!-- 上牌时间选择器弹出层 -->
  <view class="time-picker-mask" wx:if="{{showTimePicker}}" bindtap="closeTimePicker"></view>
  <view class="time-picker-container" wx:if="{{showTimePicker}}">
    <view class="time-picker-header">
      <text class="time-picker-title">上牌时间</text>
      <view class="close-icon" bindtap="closeTimePicker">×</view>
    </view>
    
    <view class="time-picker-content">
      <view class="time-picker-column">
        <picker-view indicator-style="height: 100rpx;" style="width: 80%; height: 300rpx; margin: 0 auto;" value="{{[2, 0]}}" bindchange="onYearChange">
          <picker-view-column>
            <view wx:for="{{years}}" wx:key="index" class="picker-item">{{item}}</view>
          </picker-view-column>
        </picker-view>
        <view class="picker-label">年</view>
      </view>
      
      <view class="time-picker-column">
        <picker-view indicator-style="height: 100rpx;" style="width: 80%; height: 300rpx; margin: 0 auto;" value="{{[0]}}" bindchange="onMonthChange">
          <picker-view-column>
            <view wx:for="{{months}}" wx:key="index" class="picker-item">{{item}}</view>
          </picker-view-column>
        </picker-view>
        <view class="picker-label" style="right:30%">月</view>
      </view>
    </view>
    
    <view class="time-picker-footer">
      <button class="confirm-button" bindtap="confirmTimePicker">确认</button>
    </view>
  </view>
</view> 