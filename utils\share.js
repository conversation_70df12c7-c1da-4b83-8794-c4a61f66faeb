module.exports = Behavior({
  methods: {
    onShareAppMessage() {
      const currentRoute = this.route; // 直接获取当前路径，如 "pages/index/index"
      // 从页面的 data 中获取动态分享参数
      const { shareData } = this.data;
      if(shareData?.isDetailPage){
        return {
          title: shareData?.title || '找车侠',
          path: shareData?.path || `/${currentRoute}`, // 拼接完整路径，如 "/pages/index/index"
        }
      }
      
      //详情分享
      return {
        title: shareData?.title || '找车侠',
        path: shareData?.path || '/pages/index/index',
        imageUrl: shareData?.url || ''
      };
    }
  }
});