Component({
    data: {
        selected: 0,
        color: "#999999",
        selectedColor: "#1296db",
        list: [
            {
                pagePath: "/pages/buy/index",
                text: "找车",
                iconPath: "/icons/buy.png",
                selectedIconPath: "/icons/buy-selected.png"
            },
            {
                pagePath: "/pages/my/index",
                text: "我的",
                iconPath: "/icons/my.png",
                selectedIconPath: "/icons/my-selected.png"
            }
        ]
    },
    methods: {
        switchTab(e) {
            const data = e.currentTarget.dataset
            const index = data.index
            const url = data.path

            // 如果点击的是"我的"标签
            if (index === 1) {
                // 检查是否已登录
                const userInfo = wx.getStorageSync('userInfo')

                if (!userInfo) {
                    // 未登录，直接跳转到登录页面
                    wx.navigateTo({
                        url: '/pages/login/index'
                    })
                    return
                }
            }

            // 已登录或点击的不是"我的"标签，正常切换
            wx.switchTab({
                url
            })

            this.setData({
                selected: index
            })
        }
    }
}) 