.car-list {
  background: #fff;
  border-radius: 12rpx;
  margin: 20rpx;
  overflow: hidden;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.more {
  font-size: 24rpx;
  color: #999;
}

.list {
  padding: 20rpx;
}

.car-item {
  margin-bottom: 20rpx;
}

.car-image {
  width: 100%;
  height: 300rpx;
  border-radius: 8rpx;
}

.car-info {
  padding: 10rpx 0;
  position: relative;
}

.car-name {
  font-size: 28rpx;
  color: #333;
}

.car-price {
  font-size: 32rpx;
  color: #f60;
  margin-top: 10rpx;
}

.car-tag {
  position: absolute;
  top: 10rpx;
  right: 0;
  background: #f60;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
} 