// pages/member/index.js
// 引入http模块以直接调用支付接口
import util from '../../utils/util';
import api from '../../utils/api'; // 引入API模块
const http = require('../../utils/request').default;
// 引入配置文件获取baseURL
const config = require('../../config');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: 20, // 默认状态栏高度
    navBarHeight: 64, // 导航栏高度
    menuButtonHeight: 32, // 菜单按钮高度
    currentMemberIndex: 0, // 当前会员等级索引
    userInfo: {}, // 用户信息
    expirationText: '', // 会员到期文本
    showMedalCarousel: false, // 是否显示会员勋章轮播图
    currentMedalCarouselIndex: 0, // 当前轮播图索引
    // 用于存储用户已使用的首月/首年优惠信息
    usedPromoStatus: {
      silver: {
        monthly: false, // 是否使用过白银会员首月优惠
        yearly: false   // 是否使用过白银会员首年优惠
      },
      gold: {
        monthly: false, // 是否使用过黄金会员首月优惠
        yearly: false   // 是否使用过黄金会员首年优惠
      }
    },
    memberLevels: [
      {
        name: '普通会员',
        image: 'https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/wechat/assets/images/member_badge.png',
        progress: 0,
        total: 5
      },
      {
        name: '白银会员',
        image: 'https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/wechat/assets/images/member_silver.png',
        progress: 3,
        total: 5
      },
      {
        name: '黄金会员',
        image: 'https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/wechat/assets/images/member_gold.png',
        progress: 5,
        total: 5
      }
    ],
    memberPriceCards: [
      // 普通会员价格卡片
      {
        id: 1,
        title: '月度卡',
        price: '9.9',
        originalPrice: '39.9',
        tag: '首月',
        backgroundImage: 'https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/wechat/assets/images/price_card_bg_blue.png',
        memberType: 'normal', // 普通会员
        promotionPrice: '9.9', // 首月促销价格
        regularPrice: '39.9'   // 常规价格
      },
      // 白银会员价格卡片
      {
        id: 2,
        title: '月度卡',
        price: '12.9',
        originalPrice: '39.9',
        tag: '首月',
        backgroundImage: 'https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/wechat/assets/images/price_card_bg_blue.png',
        memberType: 'silver', // 白银会员
        promotionPrice: '9.9', // 首月促销价格
        regularPrice: '12.9'   // 常规价格
      },
      {
        id: 3,
        title: '季度卡',
        price: '38.8',
        originalPrice: '120',
        tag: '特惠',
        backgroundImage: 'https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/wechat/assets/images/price_card_bg_blue.png',
        memberType: 'silver' // 白银会员
      },
      {
        id: 4,
        title: '年度卡',
        price: '99',
        originalPrice: '480',
        tag: '首年',
        backgroundImage: 'https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/wechat/assets/images/price_card_bg_blue.png',
        memberType: 'silver', // 白银会员
        promotionPrice: '99',  // 首年促销价格
        regularPrice: '118'    // 常规价格
      },
      // 黄金会员价格卡片
      {
        id: 5,
        title: '月度卡',
        price: '19.9',
        originalPrice: '39.9',
        tag: '首月',
        backgroundImage: 'https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/wechat/assets/images/price_card_bg_blue.png',
        memberType: 'gold', // 黄金会员
        promotionPrice: '13.9', // 首月促销价格
        regularPrice: '19.9'    // 常规价格
      },
      {
        id: 6,
        title: '季度卡',
        price: '46.8',
        originalPrice: '120',
        tag: '特惠',
        backgroundImage: 'https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/wechat/assets/images/price_card_bg_blue.png',
        memberType: 'gold' // 黄金会员
      },
      {
        id: 7,
        title: '年度卡',
        price: '108',
        originalPrice: '480',
        tag: '首年',
        backgroundImage: 'https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/wechat/assets/images/price_card_bg_blue.png',
        memberType: 'gold', // 黄金会员
        promotionPrice: '108', // 首年促销价格
        regularPrice: '138'    // 常规价格
      }
    ],
    currentMemberType: 'normal', // 当前选择的会员类型：normal, silver, gold
    filteredPriceCards: [], // 根据会员类型筛选的价格卡片
    isAgreed: false, // 用户是否同意协议
    currentSelectedCardIndex: 0, // 当前选中的价格卡片索引
    memberDescriptionText: '同一个账户仅可享一次优惠，请以实际支付价格为准，开通立即享受白银会员所有权益。', // 默认会员说明
    showPaymentCard: false, // 是否显示支付状态卡片
    paymentStatus: '正在生成支付订单信息', // 支付状态文本
    showMemberLimitCard: false, // 是否显示会员限制卡片
    memberLimitText: '', // 会员限制卡片动态文本
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取状态栏高度和菜单按钮位置信息
    const systemInfo = wx.getSystemInfoSync();
    const menuButtonInfo = wx.getMenuButtonBoundingClientRect();

    // 计算导航栏高度
    const navBarHeight = menuButtonInfo.bottom + 6;

    this.setData({
      statusBarHeight: systemInfo.statusBarHeight,
      navBarHeight: navBarHeight,
      menuButtonHeight: menuButtonInfo.height
    });

    // 初始化筛选价格卡片 - 默认显示白银会员
    this.filterPriceCardsByMemberType('silver');

    // 初始化会员说明文本
    this.updateMemberDescription();
  },

  /**
   * 根据会员类型筛选价格卡片
   */
  filterPriceCardsByMemberType(memberType) {
    // 获取最新的用户信息 - 仅用于调试，不再用于价格显示判断
    const userInfo = util.getUserInfo() || util.getCacheWithExpiry('userInfo') || {};

    // 克隆价格卡片数据，避免修改原始数据
    const priceCards = JSON.parse(JSON.stringify(this.data.memberPriceCards));

    // 获取促销使用状态
    const { usedPromoStatus } = this.data;

    // 只根据接口返回的促销使用状态设置价格卡片
    priceCards.forEach(card => {
      // 处理月度卡的首月价格
      if (card.title === '月度卡' && card.promotionPrice && card.regularPrice) {
        // 检查是否已使用过该会员类型的首月促销
        let hasUsedPromo = false;
        if (card.memberType === 'silver') {
          hasUsedPromo = usedPromoStatus.silver.monthly;
        } else if (card.memberType === 'gold') {
          hasUsedPromo = usedPromoStatus.gold.monthly;
        }

        // 根据促销使用情况决定价格显示
        if (hasUsedPromo) {
          // 如果已使用过该类型的首月促销，显示常规价格
          card.price = card.regularPrice;
          card.tag = ''; // 移除首月标签
        } else {
          // 否则显示促销价格
          card.price = card.promotionPrice;
          card.tag = '首月'; // 添加首月标签
        }
      }
      // 处理年度卡的首年价格
      else if (card.title === '年度卡' && card.promotionPrice && card.regularPrice) {
        // 检查是否已使用过该会员类型的首年促销
        let hasUsedYearlyPromo = false;
        if (card.memberType === 'silver') {
          hasUsedYearlyPromo = usedPromoStatus.silver.yearly;
        } else if (card.memberType === 'gold') {
          hasUsedYearlyPromo = usedPromoStatus.gold.yearly;
        }

        // 根据促销使用情况决定价格显示
        if (hasUsedYearlyPromo) {
          // 如果已使用过该类型的首年促销，显示常规价格
          card.price = card.regularPrice;
          card.tag = ''; // 移除首年标签
        } else {
          // 否则显示促销价格
          card.price = card.promotionPrice;
          card.tag = '首年'; // 添加首年标签
        }
      }
    });

    // 筛选对应会员类型的价格卡片
    const filteredCards = priceCards.filter(card => card.memberType === memberType);

    this.setData({
      filteredPriceCards: filteredCards,
      currentMemberType: memberType,
      currentSelectedCardIndex: 0 // 默认选中该会员类型的第一个价格卡片
    });
  },

  /**
   * 会员轮播图改变事件
   */
  onMemberChange(e) {
    const current = e.detail.current;
    // 根据会员等级同步更新价格卡片选择
    this.setData({
      currentMemberIndex: current
    }, () => {
      // 在状态更新后，更新会员说明文本
      this.updateMemberDescription();
    });
  },

  /**
   * 点击箭头切换到下一个会员等级
   */
  nextMember() {
    const currentIndex = this.data.currentMemberIndex;
    const nextIndex = (currentIndex + 1) % this.data.memberLevels.length;

    // 同步更新价格卡片选择
    this.setData({
      currentMemberIndex: nextIndex,
      currentSelectedCardIndex: nextIndex
    }, () => {
      // 在状态更新后，更新会员说明文本
      this.updateMemberDescription();
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 获取最新的用户信息
    const userInfo = util.getUserInfo() || util.getCacheWithExpiry('userInfo') || {};

    // 设置会员到期文本
    let expirationText = '';
    if (userInfo && userInfo.level_time_expire) {
      if (userInfo.level == 2) {
        expirationText = `白银会员于${userInfo.level_time_expire}日过期`;
      } else if (userInfo.level == 3) {
        expirationText = `黄金会员于${userInfo.level_time_expire}日过期`;
      } else if (userInfo.level == 1) {
        expirationText = `普通会员于${userInfo.level_time_expire}日过期`;
      } else {
        expirationText = `会员于${userInfo.level_time_expire}日过期`;
      }
    }

    // 根据用户会员等级设置当前会员索引
    let memberIndex = 0; // 默认为普通会员
    let memberType = 'silver'; // 默认为白银会员
    if (userInfo && userInfo.level) {
      // 映射用户等级到会员索引
      // level 1 -> 普通会员 -> memberLevels[0]
      // level 2 -> 白银会员 -> memberLevels[1]
      // level 3 -> 黄金会员 -> memberLevels[2]
      memberIndex = Math.min(Math.max(userInfo.level - 1, 0), 2);
      memberType = userInfo.level === 1 ? 'silver' : userInfo.level === 2 ? 'silver' : 'gold';
    }

    // 更新数据
    this.setData({
      userInfo: userInfo,
      expirationText: expirationText,
      currentMemberIndex: memberIndex,
      // 临时将价格卡片设置为空数组，在API响应后会更新
      filteredPriceCards: [],
      currentMemberType: memberType
    });

    // 调用接口检查用户是否已使用过首月/首年促销
    // 会在API响应后更新价格卡片
    this.checkUserPromoEligibility();

    // 在状态更新后，更新会员说明文本
    this.updateMemberDescription();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 跳转到会员充值记录页面
   */
  goToRechargeRecord() {
    wx.navigateTo({
      url: '/pages/member/recharge_record'
    });
  },

  /**
   * 跳转到自动续费管理页面
   */
  goToAutoRenew() {
    wx.showToast({
      title: '功能开发中!!',
      icon: 'none'
    });
  },

  /**
   * 显示会员勋章详情
   */
  showMedalDetails() {
    // 显示会员勋章轮播图卡片
    this.setData({
      showMedalCarousel: true,
      currentMedalCarouselIndex: this.data.currentMemberIndex
    });
  },

  /**
   * 关闭会员勋章轮播图卡片
   */
  closeMedalCarousel() {
    this.setData({
      showMedalCarousel: false
    });
  },

  /**
   * 会员勋章轮播图切换事件
   */
  onMedalSwiperChange(e) {
    const current = e.detail.current;
    this.setData({
      currentMedalCarouselIndex: current
    });
  },

  /**
   * 切换到上一个会员勋章
   */
  prevMedalCarousel() {
    const currentIndex = this.data.currentMedalCarouselIndex;
    const prevIndex = (currentIndex - 1 + this.data.memberLevels.length) % this.data.memberLevels.length;

    this.setData({
      currentMedalCarouselIndex: prevIndex
    });
  },

  /**
   * 切换到下一个会员勋章
   */
  nextMedalCarousel() {
    const currentIndex = this.data.currentMedalCarouselIndex;
    const nextIndex = (currentIndex + 1) % this.data.memberLevels.length;

    this.setData({
      currentMedalCarouselIndex: nextIndex
    });
  },

  /**
   * 协议勾选变化事件
   */
  onAgreementChange(e) {
    const isAgreed = e.detail.value === 'agree';
    this.setData({
      isAgreed: isAgreed
    });
  },

  /**
   * 确认订阅
   */
  async onConfirmSubscription() {
    if (!this.data.isAgreed) {
      wx.showToast({
        title: '请先同意会员服务协议',
        icon: 'none'
      });
      return;
    }

    // 获取最新的用户信息和当前选择的会员类型
    const userInfo = util.getUserInfo() || util.getCacheWithExpiry('userInfo');
    const selectedCard = this.data.filteredPriceCards[this.data.currentSelectedCardIndex];
    const currentMemberType = selectedCard.memberType;

    // 检查用户会员级别限制
    if (userInfo && userInfo.level) {
      // 如果用户是白银会员(level=2)，但尝试订阅黄金会员
      if ((userInfo.level == 2 || userInfo.level === "2") && currentMemberType === 'gold') {
        this.setData({
          showMemberLimitCard: true,
          memberLimitText: '您目前已开通白银会员。如需更换套餐，请等待当前套餐到期后，可更换会员套餐。'
        });
        return;
      }

      // 如果用户是黄金会员(level=3)，但尝试订阅白银会员
      if ((userInfo.level == 3 || userInfo.level === "3") && currentMemberType === 'silver') {
        this.setData({
          showMemberLimitCard: true,
          memberLimitText: '您目前已开通黄金会员。如需更换套餐，请等待当前套餐到期后，可更换会员套餐。'
        });
        return;
      }

      // 如果用户已有相同类型的会员，提示续费而不是重新开通
      if (((userInfo.level == 2 || userInfo.level === "2") && currentMemberType === 'silver') ||
        ((userInfo.level == 3 || userInfo.level === "3") && currentMemberType === 'gold')) {
        wx.showModal({
          title: '续费提示',
          content: `您当前已是${currentMemberType === 'silver' ? '白银' : '黄金'}会员，确认续费该会员？`,
          success: (res) => {
            if (res.confirm) {
              // 用户确认续费，继续支付流程
              this.continueSubscription();
            }
          }
        });
        return;
      }
    }

    // 如果没有会员级别限制，直接继续支付流程
    this.continueSubscription();
  },

  /**
   * 继续订阅流程
   */
  continueSubscription() {
    try {
      // 先检查是否有未支付订单
      const appId = util.getAppId();

      // 显示加载提示
      wx.showLoading({
        title: '正在检查订单状态',
        mask: true
      });

      // 使用直接的wx.request替代api.user.checkOrder
      wx.request({
        url: `${config.BASE_URL}wechat/user/checkTradeState`,
        method: 'GET',
        data: { app_id: appId },
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`
        },
        success: (res) => {
          wx.hideLoading();
          console.log('检查订单结果:', res.data);

          // 如果有未支付订单 (code为-1)
          if (res.data && res.data.code === -1) {
            // 获取提示消息，优先使用接口返回的msg
            const tipMessage = res.data.msg || '您有未支付的订单，点击前往支付';

            // 显示弹窗提示用户去支付
            wx.showModal({
              title: '提示',
              content: tipMessage,
              showCancel: true,
              cancelText: '取消',
              confirmText: '去支付',
              success: (res) => {
                if (res.confirm) {
                  // 用户点击去支付，跳转到充值记录页面
                  wx.navigateTo({
                    url: '/pages/member/recharge_record'
                  });
                }
              }
            });
            return;
          }

          // 如果没有未支付订单，继续支付流程
          this.proceedWithPayment();
        },
        fail: (err) => {
          wx.hideLoading();
          console.error('检查订单失败:', err);
          // 出错时也继续支付流程
          this.proceedWithPayment();
        }
      });
    } catch (error) {
      wx.hideLoading();
      console.error('订单检查或支付过程出错:', error);
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 继续处理支付流程
   */
  proceedWithPayment() {
    // 显示加载提示
    wx.showLoading({
      title: '正在获取支付信息',
      mask: true
    });

    // 获取当前选择的会员卡信息
    const selectedCard = this.data.filteredPriceCards[this.data.currentSelectedCardIndex];

    // 获取用户信息 - 仅用于调试，不再用于价格判断
    const userInfo = util.getUserInfo() || util.getCacheWithExpiry('userInfo') || {};

    // 获取促销使用状态
    const { usedPromoStatus } = this.data;

    // 确定实际支付金额
    let payAmount = selectedCard.price; // 默认使用显示价格

    // 如果是月度卡且有首月价格配置，进行额外检查确保与页面显示一致
    if (selectedCard.title === '月度卡' && selectedCard.promotionPrice && selectedCard.regularPrice) {
      // 检查是否已使用过该会员类型的首月促销
      let hasUsedPromo = false;
      if (selectedCard.memberType === 'silver') {
        hasUsedPromo = usedPromoStatus.silver.monthly;
      } else if (selectedCard.memberType === 'gold') {
        hasUsedPromo = usedPromoStatus.gold.monthly;
      }

      // 根据促销使用情况选择价格
      if (hasUsedPromo) {
        // 已使用过促销，使用常规价格
        payAmount = selectedCard.regularPrice;
      } else {
        // 未使用过促销，可以使用促销价格
        payAmount = selectedCard.promotionPrice;
      }
    }
    // 如果是年度卡且有首年价格配置，进行额外检查确保与页面显示一致
    else if (selectedCard.title === '年度卡' && selectedCard.promotionPrice && selectedCard.regularPrice) {
      // 检查是否已使用过该会员类型的首年促销
      let hasUsedYearlyPromo = false;
      if (selectedCard.memberType === 'silver') {
        hasUsedYearlyPromo = usedPromoStatus.silver.yearly;
      } else if (selectedCard.memberType === 'gold') {
        hasUsedYearlyPromo = usedPromoStatus.gold.yearly;
      }

      // 根据促销使用情况选择价格
      if (hasUsedYearlyPromo) {
        // 已使用过年度促销，使用常规价格
        payAmount = selectedCard.regularPrice;
      } else {
        // 未使用过年度促销，可以使用促销价格
        payAmount = selectedCard.promotionPrice;
      }
    }

    // 生成唯一订单号（使用时间戳+随机数）
    const timestamp = new Date().getTime();
    const randomNum = Math.floor(Math.random() * 1000);
    const outTradeNo = `MEM${timestamp}${randomNum}`;

    // 获取用户openid，确保是字符串格式
    let openid = wx.getStorageSync('openid');

    // 检查openid是否是对象，如果是则尝试获取其中的值
    if (openid && typeof openid === 'object') {
      // 尝试从对象中获取openid字段，这取决于实际存储结构
      if (openid.openid) {
        openid = openid.openid;
      } else if (openid.value) {
        openid = openid.value;
      } else {
        console.error('无法从对象中获取有效的openid:', openid);
        wx.hideLoading();
        wx.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        });
        return;
      }
    }

    // 检查openid是否有效
    if (!openid || typeof openid !== 'string' || openid === '[object Object]') {
      console.error('无效的openid:', openid);
      wx.hideLoading();
      wx.showToast({
        title: '获取用户信息失败，请重新登录',
        icon: 'none'
      });
      return;
    }

    // 构造支付请求参数
    const payParams = {
      // amount: selectedCard.price, // 注意：请确保金额是正确格式，如果需要，这里可以转换单位
      amount: payAmount, // 使用动态计算的金额
      // amount: "0.01",
      amount_total: selectedCard.originalPrice,
      app_id: util.getAppId(),
      thali_status: selectedCard.title === '月度卡' ? "1" : selectedCard.title === '季度卡' ? "2" : "3",
      body: `${selectedCard.title}套餐`,
      openid: openid, // 使用处理后的openid
      level: selectedCard.memberType === 'silver' ? "2" : selectedCard.memberType === 'gold' ? "3" : "1",
    };

    console.log('支付参数:', payParams);

    // 使用wx.request直接提交表单数据
    wx.request({
      url: `${config.BASE_URL}wechat/pay/unifiedOrder`,
      // url: `${config.BASE_URL}addons/epay/api/submit`,
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded', // 使用表单格式
        'Authorization': `Bearer ${wx.getStorageSync('token')}`
      },
      data: payParams,
      success: (res) => {
        wx.hideLoading();

        console.log('支付结果:', res);
        if (res.statusCode >= 200 && res.statusCode < 300) {
          const data = res.data;

          // 检查是否有标准的code响应
          if (data && (data.code === 1 || data.code === 0)) {
            // 获取支付参数成功，拉起微信支付
            const payData = data.data;
            this.processPayment(payData);
          }
          // 检查是否直接返回了支付参数（没有包装在data.data中）
          else if (data && data.appId && data.timeStamp && data.nonceStr && data.package && data.paySign) {
            // 直接使用返回的数据作为支付参数
            this.processPayment(data);
          }
          // 检查是否返回了其他格式的数据
          else if (data && data.data && (data.data.appId || data.data.timeStamp)) {
            // 使用data.data中的支付参数
            this.processPayment(data.data);
          }
          else {
            // 获取支付参数失败
            wx.showToast({
              title: data?.msg || '获取支付信息失败',
              icon: 'none'
            });
          }
        } else {
          // HTTP错误
          wx.showToast({
            title: `请求失败：${res.statusCode}`,
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        wx.showToast({
          title: '网络请求失败，请重试',
          icon: 'none'
        });
        console.error('请求支付参数失败', err);
      }
    });
  },

  /**
   * 处理支付参数并调用微信支付API
   */
  processPayment(payData) {
    console.log('处理支付参数:', payData);

    // 确保timeStamp是字符串类型
    const timeStamp = typeof payData.timeStamp === 'string' ?
      payData.timeStamp : String(payData.timeStamp);

    wx.requestPayment({
      timeStamp: timeStamp,
      nonceStr: payData.nonceStr,
      package: payData.package,
      signType: payData.signType || 'MD5', // 默认使用MD5
      paySign: payData.paySign,
      success: (payRes) => {
        // 支付成功
        wx.showToast({
          title: '支付成功',
          icon: 'success'
        });

        const userInfo = util.getCacheWithExpiry('userInfo');
        // 更新用户会员等级信息
        const newLevel = payData.level || (payData.data && payData.data.level);
        // 确保level值为数字类型
        const numericLevel = parseInt(newLevel) || 1; // 如果转换失败则默认为普通会员(1)

        // 替换缓存中的level值
        userInfo.level = numericLevel;
        // 直接使用接口返回的time_expire作为会员到期时间
        userInfo.level_time_expire = payData.time_expire || (payData.data && payData.data.time_expire);
        // 更新缓存
        util.setCacheWithExpiry('userInfo', userInfo);

        // 立即更新显示的会员过期文本
        let expirationText = '';
        if (userInfo.level_time_expire) {
          if (userInfo.level == 2 || numericLevel === 2) {
            expirationText = `白银会员于${userInfo.level_time_expire}日过期`;
          } else if (userInfo.level == 3 || numericLevel === 3) {
            expirationText = `黄金会员于${userInfo.level_time_expire}日过期`;
          } else if (userInfo.level == 1 || numericLevel === 1) {
            expirationText = `普通会员于${userInfo.level_time_expire}日过期`;
          } else {
            expirationText = `会员于${userInfo.level_time_expire}日过期`;
          }
        }

        // 根据新的会员等级更新会员轮播图索引
        let newMemberIndex = 0;
        if (userInfo.level == 2 || numericLevel === 2) {
          newMemberIndex = 1; // 白银会员对应的索引
        } else if (userInfo.level == 3 || numericLevel === 3) {
          newMemberIndex = 2; // 黄金会员对应的索引
        } else {
          newMemberIndex = 0; // 普通会员对应的索引
        }

        // 更新用户信息和会员等级索引
        this.setData({
          userInfo: userInfo,
          expirationText: expirationText,
          currentMemberIndex: newMemberIndex
        });

        // 也更新对应的会员类型
        let newMemberType = numericLevel === 3 ? 'gold' : 'silver';
        this.filterPriceCardsByMemberType(newMemberType);

        // 重新调用接口检查用户促销状态，确保价格卡片显示正确
        this.checkUserPromoEligibility();

        // 延迟后跳转到会员中心或其他页面
        setTimeout(() => {
          wx.redirectTo({
            url: '/pages/member/center/index'
          });
        }, 1500);
      },
      fail: (payErr) => {
        // 支付失败或取消
        console.log('支付失败', payErr);
        if (payErr.errMsg === 'requestPayment:fail cancel') {
          wx.showToast({
            title: '您已取消支付',
            icon: 'none'
          });
        } else {
          wx.showToast({
            title: '支付失败，请重试',
            icon: 'none'
          });
        }
      }
    });
  },

  /**
   * 查看会员服务协议
   */
  onViewAgreement() {
    // 跳转到协议页面
    wx.navigateTo({
      url: '/pages/agreement/member-service-agreement',
    });
  },

  /**
   * 点击价格卡片
   */
  onSelectPriceCard(e) {
    const index = e.currentTarget.dataset.index;
    // 仅更新当前选中的价格卡片索引，不改变会员类型
    this.setData({
      currentSelectedCardIndex: index
    }, () => {
      // 在状态更新后，更新会员说明文本
      this.updateMemberDescription();
    });
  },

  /**
   * 选择会员类型
   */
  onSelectMemberType(e) {
    const memberType = e.currentTarget.dataset.type;

    // 只切换会员类型，不改变会员等级索引
    // 筛选对应会员类型的价格卡片
    this.filterPriceCardsByMemberType(memberType);

    // 更新会员说明文本
    this.updateMemberDescription();
  },

  /**
   * 根据会员级别动态更新说明文本
   */
  updateMemberDescription() {
    // 获取当前会员类型文本
    let memberTypeText = this.data.currentMemberType === 'silver' ? '白银会员' : '黄金会员';

    // 固定文本模板，只替换会员类型部分
    const descriptionText = `同一个账户仅可享一次优惠，请以实际支付价格为准，开通立即享受${memberTypeText}所有权益。`;

    this.setData({
      memberDescriptionText: descriptionText
    });
  },

  /**
   * 关闭会员限制卡片
   */
  closeMemberLimitCard() {
    this.setData({
      showMemberLimitCard: false
    });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation(e) {
    // 阻止事件冒泡，防止点击内容区域时关闭卡片
    // catchtap 本身已经阻止了冒泡，不需要调用额外的方法
  },

  /**
   * 检查用户是否已使用过白银/黄金会员的首月/首年促销
   * 调用checkFirstMonthFirstYear API获取用户促销使用状态
   */
  checkUserPromoEligibility() {
    // 显示加载提示
    wx.showLoading({
      title: '加载会员信息',
      mask: true
    });

    // 获取appId参数
    const appId = util.getAppId();
    if (!appId) {
      wx.hideLoading();
      console.error('获取appId失败');
      return;
    }

    // 调用API获取用户促销使用状态
    api.user.checkFirstMonthFirstYear({ app_id: appId })
      .then(res => {
        wx.hideLoading();
        console.log('获取用户促销资格状态:', res);

        // 初始化促销使用状态对象
        const usedPromoStatus = {
          silver: {
            monthly: false,
            yearly: false
          },
          gold: {
            monthly: false,
            yearly: false
          }
        };

        // 特殊处理：如果res是空数组，表示可以享受所有优惠
        if (Array.isArray(res)) {
          console.log('用户未使用过任何促销，可享受所有优惠');
        }
        // 处理标准响应格式：有code和data字段
        else if (res && (res.code === 1 || res.code === 0)) {
          const data = res.data;

          // 如果data是空数组，表示用户没有充值过任何会员，可以享受所有优惠
          if (Array.isArray(data) && data.length === 0) {
            console.log('用户未使用过任何促销，可享受所有优惠');
          }
          // 如果data是对象格式，检查已使用的优惠
          else if (data && typeof data === 'object' && !Array.isArray(data)) {
            this.processPromotionData(data, usedPromoStatus);
          }
        }
        // 新增处理：直接返回数据对象格式，如 {2:[1]}
        else if (res && typeof res === 'object' && !Array.isArray(res)) {
          // 检查是否有2或3键，表明这可能是直接返回的数据对象
          if (res['2'] || res['3']) {
            console.log('获取用户促销资格状态格式为直接数据:', res);
            this.processPromotionData(res, usedPromoStatus);
          } else {
            console.log('获取用户促销资格状态格式未识别:', res);
          }
        }
        else {
          console.log('获取用户促销资格状态格式异常:', res);
        }

        // 更新状态
        this.setData({ usedPromoStatus }, () => {
          // 重新筛选价格卡片，应用新的促销规则
          this.filterPriceCardsByMemberType(this.data.currentMemberType);
        });
      })
      .catch(err => {
        wx.hideLoading();
        console.error('调用checkFirstMonthFirstYear接口失败:', err);

        // 发生错误时，假设用户未使用过任何促销
        const usedPromoStatus = {
          silver: {
            monthly: false,
            yearly: false
          },
          gold: {
            monthly: false,
            yearly: false
          }
        };

        // 即使出错也更新状态，确保UI正常显示
        this.setData({ usedPromoStatus }, () => {
          this.filterPriceCardsByMemberType(this.data.currentMemberType);
        });
      });
  },

  /**
   * 处理促销数据，更新usedPromoStatus对象
   */
  processPromotionData(data, usedPromoStatus) {
    // 处理白银会员(2)的数据
    if (data['2'] && Array.isArray(data['2'])) {
      // 检查是否包含月度套餐(1)
      if (data['2'].includes(1)) {
        usedPromoStatus.silver.monthly = true;
      }
      // 检查是否包含年度套餐(3)
      if (data['2'].includes(3)) {
        usedPromoStatus.silver.yearly = true;
      }
    }

    // 处理黄金会员(3)的数据
    if (data['3'] && Array.isArray(data['3'])) {
      // 检查是否包含月度套餐(1)
      if (data['3'].includes(1)) {
        usedPromoStatus.gold.monthly = true;
      }
      // 检查是否包含年度套餐(3)
      if (data['3'].includes(3)) {
        usedPromoStatus.gold.yearly = true;
      }
    }
  },
})