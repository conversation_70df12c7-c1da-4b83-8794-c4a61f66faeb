// 导入 API 模块
import api from '../../utils/api';
import util from '../../utils/util';

Page({
  data: {
    statusBarHeight: wx.getSystemInfoSync().statusBarHeight,
    isLoading: true,
    quotes: [],
    groupedQuotes: {},
    todayQuotes: [],
    hasMore: true,
    page: 1,
    pageSize: 10,
    isEmpty: false
  },

  onLoad: function (options) {
    // 设置状态栏高度
    this.setData({
      statusBarHeight: wx.getSystemInfoSync().statusBarHeight,
      isLoading: true
    });

    // 立即开始加载数据
    this.fetchQuotes();
  },

  onPullDownRefresh: function () {
    // 显示刷新状态
    wx.showLoading({
      title: '正在刷新...',
      mask: true
    });
    
    // 重置数据和状态
    this.setData({
      page: 1,
      hasMore: true,
      quotes: [],
      groupedQuotes: {},
      todayQuotes: [],
      isLoading: true
    });
    
    // 获取新数据
    this.fetchQuotes(() => {
      // 完成后停止下拉刷新动画并隐藏 loading
      wx.stopPullDownRefresh();
      wx.hideLoading();
      
      // 显示刷新结果
      wx.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1500
      });
    });
  },

  onReachBottom: function () {
    if (this.data.hasMore && !this.data.isLoading) {
      this.loadMoreQuotes();
    }
  },

  fetchQuotes: async function (callback) {
    try {
      this.setData({ isLoading: true });

      const res = await api.quote.getList({
        page: 1,
        page_size: this.data.pageSize,
        app_id: util.getAppId()
      });

      if (res && res.data) {
        // 处理数据
        this.processQuoteData(res.data);

        this.setData({
          isLoading: false,
          isEmpty: !res.data.length,
          // 重置页面状态
          page: 1,
          hasMore: res.data.length >= this.data.pageSize
        });
      } else {
        wx.showToast({
          title: '获取报价单列表失败',
          icon: 'none',
          duration: 2000
        });
        this.setData({
          isLoading: false,
          isEmpty: true
        });
      }
    } catch (error) {
      console.error('获取报价单列表失败:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'none',
        duration: 2000
      });
      this.setData({
        isLoading: false,
        isEmpty: true
      });
    } finally {
      if (callback) {
        callback();
      }
    }
  },

  loadMoreQuotes: async function () {
    if (!this.data.hasMore) return;

    try {
      this.setData({
        isLoading: true,
        page: this.data.page + 1
      });

      const res = await api.quote.getList({
        page: this.data.page,
        page_size: this.data.pageSize,
        app_id: util.getAppId()
      });

      if (res && res.data) {
        if (res.data.length === 0) {
          this.setData({
            hasMore: false,
            isLoading: false
          });
          return;
        }

        const allQuotes = [...this.data.quotes, ...res.data];
        this.processQuoteData(allQuotes);
      } else {
        wx.showToast({
          title: '获取更多数据失败',
          icon: 'none',
          duration: 2000
        });
      }
    } catch (error) {
      console.error('加载更多报价单失败:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'none',
        duration: 2000
      });
    } finally {
      this.setData({ isLoading: false });
    }
  },

  processQuoteData: function (quotes) {
    // 将所有报价按日期分组
    const groupedQuotes = {};
    const todayQuotes = [];
    const today = new Date().toLocaleDateString();

    if (!Array.isArray(quotes)) {
      console.error('processQuoteData: quotes 不是数组类型');
      this.setData({
        quotes: [],
        groupedQuotes: [],
        todayQuotes: []
      });
      return;
    }

    quotes.forEach(quote => {
      // 检查 quote 是否有效
      if (!quote || typeof quote !== 'object') {
        console.warn('无效的报价数据项');
        return;
      }

      // 获取 content，如果不存在则使用空对象
      const content = quote.content || {};

      // 处理接口返回的数据结构
      const quoteItem = {
        id: quote.id || '',
        vehicleName: content.carName || '未知车型',
        guidancePrice: content.cg_zdj || 0,
        marketDiscount: content.cg_cjxf || 0,
        domesticFees: content.cb_gn || 0,
        exportFees: content.cb_ck || 0,
        createdAt: quote.create_time || new Date().toISOString()
      };

      let quoteDate = '';
      try {
        // 使用 formatDateString 转换日期字符串为兼容 iOS 的格式
        const formattedDate = util.formatDateString(quoteItem.createdAt);
        quoteDate = new Date(formattedDate).toLocaleDateString();
      } catch (error) {
        console.warn('日期格式化失败:', error, quoteItem.createdAt);
        quoteDate = new Date().toLocaleDateString();
      }

      // 格式化价格
      quoteItem.formattedGuidancePrice = this.formatPrice(quoteItem.guidancePrice);
      quoteItem.formattedMarketDiscount = this.formatPrice(quoteItem.marketDiscount);
      quoteItem.formattedDomesticFees = this.formatPrice(quoteItem.domesticFees);
      quoteItem.formattedExportFees = this.formatPrice(quoteItem.exportFees);

      // 今天的报价放入todayQuotes
      if (quoteDate === today) {
        todayQuotes.push(quoteItem);
        return;
      }

      // 其他日期的报价按日期分组
      if (!groupedQuotes[quoteDate]) {
        groupedQuotes[quoteDate] = [];
      }
      groupedQuotes[quoteDate].push(quoteItem);
    });

    // 转换为数组格式，方便在WXML中使用
    const groupedQuotesArray = Object.keys(groupedQuotes).map(date => {
      return {
        date: date,
        quotes: groupedQuotes[date]
      };
    }).sort((a, b) => {
      try {
        return new Date(b.date) - new Date(a.date); // 按日期从新到旧排序
      } catch (error) {
        console.warn('日期排序失败:', error);
        return 0;
      }
    });

    this.setData({
      quotes: quotes,
      groupedQuotes: groupedQuotesArray,
      todayQuotes: todayQuotes
    });
  },

  formatPrice: function (price) {
    if (!price && price !== 0) return '0';

    // 检查是否为负数
    const isNegative = price.toString().startsWith('-');

    // 移除负号并转为数字
    let numPrice = parseFloat(isNegative ? price.toString().substring(1) : price);

    // 只保留整数部分
    numPrice = Math.floor(numPrice);

    // 添加千位分隔符
    let formattedPrice = numPrice.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");

    // 如果原数为负数，添加负号
    return isNegative ? '-' + formattedPrice : formattedPrice;
  },

  navigateToDetail: function (e) {
    const quoteId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/pages/my/quote_record?id=' + quoteId
    });
  },

  navigateBack: function () {
    wx.switchTab({
      url: '/pages/my/index'
    });
  },
});
