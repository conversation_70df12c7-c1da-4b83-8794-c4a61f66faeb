// components/language-switcher/language-switcher.js
const langData = require('../../utils/lang-function.js');
Component({

  /**
   * 组件的属性列表
   */
  properties: {
    position:{
      type:'String',
      value:'top-left'
    },
    showLangList:{
      type:Boolean,
      value:true,
    },
    size:{
      type:String,
      value:'normal'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    currentLang:'zh-CN',
    showDropdown:false,
    langList:[],
    currentLangInfo:{},
  },
  
  //页面生命周期
  lifetimes: {
    attached() {
      this.initLanguageData();
      // 注册到全局
      const app = getApp();
      if (!app._langSwitchers) app._langSwitchers = [];
      app._langSwitchers.push(this);
    },
    detached() {
      // 注销
      const app = getApp();
      if (app._langSwitchers) {
        app._langSwitchers = app._langSwitchers.filter(item => item !== this);
      }
    }
  },
  /**
   * 组件的方法列表
   */
  methods: {
    initLanguageData(){
      const langList = langData.getSupportLangs();
      const currentLang = langData.getCurrentLang();
      const currentLangInfo = langList.find(lang => lang.code === currentLang);
      this.setData({
        langList,
        currentLang,
        currentLangInfo,
      });
    },
    toggleDropdown(){
      this.setData({
        showDropdown: !this.data.showDropdown
      });
    },
    selectLang(e){
      const langCode = e.currentTarget.dataset.lang;
      if(langCode === this.data.currentLang){
        this.setData({
          showDropdown:false
        })
        return
      }
      //设置语音
      langData.setLang(langCode);

      //更新标签
      this.data.langList.forEach(item=>{
        if(item.code === langCode){
          this.setData({
            currentLangInfo:item,
            currentLang:langCode,
            showDropdown:false
          })
        }
      })
    },
    stopPropagation(){
      return
    }
  }
})