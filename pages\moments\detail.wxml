<view class="detail-container">
  <!-- 引入登录弹窗模板 -->
  <import src="/templates/loginPopup/loginPopup.wxml" />

  <!-- 使用登录弹窗模板 -->
  <template
    is="loginPopup"
    data="{{showLoginPopup, loginPopupOptions}}"
  ></template>

  <!-- 自定义导航栏 -->
  <view class="custom-nav">
    <view
      class="status-bar"
      style="height: {{statusBarHeight}}px;"
    ></view>
    <view class="nav-content">
      <view
        class="back-icon"
        bindtap="navigateBack"
      >
        <image src="/icons/moments/back.svg"></image>
      </view>
      <view class="nav-title">询盘详情</view>
    </view>
  </view>

  <!-- 导航栏占位，避免内容被导航栏遮挡 -->
  <view
    class="nav-placeholder"
    style="height: {{statusBarHeight + 44}}px;"
  ></view>

  <!-- 加载中 -->
  <view
    class="loading-container"
    wx:if="{{isLoading}}"
  >
    <van-loading color="#ffc62c" />
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 帖子详情 -->
  <view
    class="post-detail"
    wx:if="{{!isLoading && postDetail}}"
  >
    <!-- 用户信息 -->
    <view class="user-info">
      <image
        class="user-avatar"
        src="{{postDetail.avatar}}"
        mode="aspectFill"
      />
      <view class="user-meta">
        <view class="user-name">{{postDetail.username}}</view>
        <view class="post-time">{{postDetail.time}}</view>
      </view>
    </view>

    <!-- 正文内容 -->
    <view class="post-content">
      <view class="post-title">{{postDetail.title}}</view>
      <view class="subtitle-row">
        <view class="subtitle">类型:{{ postDetail.used_type_name }}</view>
        <view class="subtitle">价格:{{ postDetail.price }}万</view>
      </view>
      <view class="subtitle-row">
        <view class="subtitle">数量:{{ postDetail.quantity }}辆</view>
        <view class="subtitle">交付地:{{ postDetail.delivery_location }}</view>
      </view>
      <view class="subtitle-row">
        <view class="subtitle">车型:{{ postDetail.ui_vehicle_name }}</view>
      </view>
      <view class="post-desc">{{postDetail.desc}}</view>
    </view>

    <!-- 图片展示 -->
    <view
      class="post-images"
      wx:if="{{postDetail.images.length > 0}}"
    >
      <view
        class="image-item {{postDetail.images.length === 1 ? 'single-image' : ''}}"
        wx:for="{{postDetail.images}}"
        wx:key="index"
        bindtap="previewImage"
        data-index="{{index}}"
      >
        <image
          src="{{item}}"
          mode="aspectFill"
        />
      </view>
    </view>

    <!-- 浏览次数 -->
    <view class="views-item">
      <image src="/icons/moments/view.svg"></image>
      <text>浏览{{ postDetail.views || 0 }}次</text>
    </view>

    <!-- 添加空白分隔区域 -->
    <view class="content-separator"></view>

    <!-- 总评论数 -->
    <view class="comment-count">
      <text>全部评论 ({{postDetail.comments}})</text>
    </view>

    <!-- 评论列表 -->
    <view
      class="comment-list"
      wx:if="{{commentList.length > 0}}"
    >
      <view
        class="comment-item"
        wx:for="{{commentList}}"
        wx:key="id"
        wx:for-index="commentIndex"
      >
        <!-- 评论用户信息 -->
        <view
          class="comment-user"
          bindtap="replyComment"
          data-user-id="{{item.userId}}"
          data-username="{{item.username}}"
          data-comment-id="{{item.id}}"
        >
          <image
            class="comment-avatar"
            src="{{item.avatar}}"
            mode="aspectFill"
          />
          <view class="comment-meta">
            <view class="comment-username">{{item.username}}</view>
            <view class="comment-time">{{item.createTime}}</view>
          </view>
        </view>

        <!-- 评论内容 -->
        <view
          class="comment-content"
          bindtap="replyComment"
          data-user-id="{{item.userId}}"
          data-username="{{item.username}}"
          data-comment-id="{{item.id}}"
        >{{item.content}}</view>

        <!-- 回复内容区域 - 对话格式 -->
        <view
          class="conversation-box"
          wx:if="{{item.replies && item.replies.length > 0}}"
        >
          <view
            class="reply-item"
            wx:for="{{item.replies}}"
            wx:key="id"
            wx:for-item="reply"
            wx:for-index="replyIndex"
            bindtap="replyComment"
            data-user-id="{{reply.userId}}"
            data-username="{{reply.username}}"
            data-comment-id="{{item.id}}"
          >
            <view class="reply-line">
              <text class="reply-username">{{reply.username}}</text>
              <text>：{{reply.content}}</text>
            </view>
          </view>
        </view>

        <!-- 点赞和评论按钮 -->
        <view class="action-buttons-container">
          <view
            class="like-btn {{item.isLiked ? 'liked' : ''}}"
            bindtap="toggleCommentLike"
            data-index="{{commentIndex}}"
          >
            <image src="{{item.isLiked ? '/icons/moments/liked.png' : '/icons/moments/like.png'}}" />
            <text>{{item.likes}}</text>
          </view>
          <view
            class="comment-btn"
            bindtap="replyComment"
            data-user-id="{{item.userId}}"
            data-username="{{item.username}}"
            data-comment-id="{{item.id}}"
          >
            <image src="/icons/moments/comment.png" />
            <text>{{item.replies && item.replies.length ? item.replies.length : 0}}</text>
          </view>

        </view>
      </view>
    </view>

    <!-- 无评论提示 -->
    <view
      class="no-comment"
      wx:else
    >
      <text>暂无评论，快来抢沙发吧~</text>
    </view>

    <!-- 加载更多 -->
    <view
      class="load-more"
      wx:if="{{hasMoreComments && commentList.length > 0}}"
    >
      <text bindtap="loadMoreComments">加载更多评论</text>
    </view>
  </view>

  <!-- 错误提示 -->
  <view
    class="error-container"
    wx:if="{{!isLoading && !postDetail}}"
  >
    <view class="error-text">加载失败，请稍后再试</view>
    <view
      class="error-action"
      bindtap="navigateBack"
    >返回上一页</view>
  </view>

  <!-- 底部评论栏 -->
  <view class="comment-bar">
    <view class="comment-input-container">
      <image
        class="comment-icon"
        src="/icons/moments/enter_comment.png"
      />
      <input
        class="comment-input"
        placeholder="{{commentPlaceholder}}"
        value="{{commentValue}}"
        bindinput="onCommentInput"
        focus="{{commentFocus}}"
        confirm-type="send"
        bindconfirm="submitComment"
      />
      <view
        class="cancel-reply"
        bindtap="cancelReply"
        wx:if="{{replyToUser}}"
      >取消</view>
    </view>

    <view class="action-buttons">
      <view
        class="action-button {{postDetail.isLiked ? 'liked' : ''}}"
        bindtap="toggleLike"
      >
        <image src="{{postDetail.isLiked ? '/icons/moments/liked.png' : '/icons/moments/like.png'}}" />
        <text>{{postDetail.likes || 0}}</text>
      </view>
      <!-- <view class="action-button" bindtap="toggleFavorite">
        <image src="{{postDetail.isFavorite ? '/icons/favorited.png' : '/icons/favorite.png'}}" />
      </view> -->
      <button
        class="action-button share-button"
        open-type="share"
      >
        <image src="/icons/moments/share.png" />
      </button>
    </view>
  </view>
</view>