Component({
    properties: {
        city: {
            type: String,
            value: '全国'
        },
        placeholder: {
            type: String,
            value: '搜索车型'
        }
    },
    data: {
        searchValue: ''
    },
    methods: {
        onCityTap() {
            this.triggerEvent('cityTap')
        },
        onInput(e) {
            this.setData({
                searchValue: e.detail.value
            })
        },
        onConfirm(e) {
            
            const value = e.detail && e.detail.value !== undefined ? e.detail.value : this.data.searchValue
            this.triggerEvent('search', { value: value })
        }
    }
}) 