// pages/supply_chain_services/index.js
import config from '../../config'; // 导入配置文件
import api from '../../utils/api'; // 导入API模块

Page({

  /**
   * 页面的初始数据
   */
  data: {
    navBarHeight: 0, // 导航栏高度
    statusBarHeight: 0, // 状态栏高度
    COS_CONFIG: config.COS_CONFIG // 添加腾讯云配置
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setNavBarInfo();
  },

  /**
   * 设置导航栏信息
   */
  setNavBarInfo() {
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    // 获取状态栏高度
    const statusBarHeight = systemInfo.statusBarHeight;
    // 导航栏高度 = 状态栏高度 + 44(导航内容高度)
    const navBarHeight = statusBarHeight + 44;

    this.setData({
      statusBarHeight: statusBarHeight,
      navBarHeight: navBarHeight
    });
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 跳转到金融服务页面 - 修改为直接跳转到详情页
   */
  async navigateToFinancial() {
    // 显示加载提示
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    try {
      // 获取金融服务列表
      const result = await api.serviceFinance.getList({
        page: 1,
        list_rows: 50 // 获取足够多的记录以确保包含所有服务
      });

      // 检查数据是否正确返回
      if (result && result.data && Array.isArray(result.data) && result.data.length > 0) {
        // 找出ID最小的金融服务
        const minIdService = result.data.reduce((min, current) => {
          return (min.id < current.id) ? min : current;
        });

        // 关闭加载提示
        wx.hideLoading();

        // 直接跳转到详情页
        wx.navigateTo({
          url: `/pages/financialservices/financialservicesInfo?id=${minIdService.id}`
        });
      } else {
        // 数据为空的处理
        wx.hideLoading();
        wx.showToast({
          title: '暂无金融服务数据',
          icon: 'none',
          duration: 2000
        });
      }
    } catch (error) {
      // 错误处理
      console.error('获取金融服务列表失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '获取数据失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  /**
   * 跳转到通道服务页面 - 修改为直接跳转到详情页
   */
  async navigateToChannelService() {
    // 显示加载提示
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    try {
      // 获取通道服务列表
      const params = {
        page: 1,
        limit: 50 // 获取足够多的记录以确保包含所有服务
      };
      
      // 设置请求选项，禁用全局loading
      const options = {
        loading: false // 禁用request.js中的全局loading
      };

      const response = await api.channelService.getList(params, options);
      
      // 检查数据是否正确返回
      if (response && response.list && Array.isArray(response.list) && response.list.length > 0) {
        // 找出ID最小的通道服务
        const minIdService = response.list.reduce((min, current) => {
          return (min.id < current.id) ? min : current;
        });

        // 关闭加载提示
        wx.hideLoading();

        // 直接跳转到详情页
        wx.navigateTo({
          url: `/pages/supply_chain_services/channel_service_detail?id=${minIdService.id}`
        });
      } else {
        // 数据为空的处理
        wx.hideLoading();
        wx.showToast({
          title: '暂无通道服务数据',
          icon: 'none',
          duration: 2000
        });
      }
    } catch (error) {
      // 错误处理
      console.error('获取通道服务列表失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '获取数据失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  /**
   * 跳转到车务服务页面 - 修改为直接跳转到详情页
   */
  async navigateToCarService() {
    // 显示加载提示
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    try {
      // 获取车务服务列表
      const params = {
        page: 1,
        limit: 50 // 获取足够多的记录以确保包含所有服务
      };
      
      // 设置请求选项，禁用全局loading
      const options = {
        loading: false, // 禁用request.js中的全局loading
        toast: false, // 禁用默认toast，我们需要自己处理
        allowErrorCodes: [200, 0, 1] // 允许的业务状态码
      };

      const response = await api.vehicleService.getList(params, options);
      
      // 检查数据是否正确返回
      // 车务服务API返回可能有多种结构，需要适应处理
      let serviceList = [];
      
      if (response) {
        if (response.code === 200 && response.data) {
          // 标准格式：{code: 200, data: {list: [], total: 0}}
          serviceList = response.data.list || [];
        } else if (response.list) {
          // 直接返回列表格式：{list: [], total: 0}
          serviceList = response.list || [];
        } else if (Array.isArray(response)) {
          // 直接返回数组格式
          serviceList = response;
        }
      }
      
      if (serviceList.length > 0) {
        // 找出ID最小的车务服务
        const minIdService = serviceList.reduce((min, current) => {
          return (min.id < current.id) ? min : current;
        });

        // 关闭加载提示
        wx.hideLoading();

        // 直接跳转到详情页
        wx.navigateTo({
          url: `/pages/supply_chain_services/car_service_detail?id=${minIdService.id}`
        });
      } else {
        // 数据为空的处理
        wx.hideLoading();
        wx.showToast({
          title: '暂无车务服务数据',
          icon: 'none',
          duration: 2000
        });
      }
    } catch (error) {
      // 错误处理
      console.error('获取车务服务列表失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '获取数据失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})