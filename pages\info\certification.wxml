<!--可以纵向展开和收起的列表-->

<view class="container" style="--status-bar-height: {{statusBarHeight}}px;">
  <!-- 自定义导航栏 -->
  <view
    class="custom-nav"
    style="padding-top: {{statusBarHeight}}px;"
  >
    <view class="nav-content">
      <view class="back-icon" bindtap="navigateBack">
        <van-icon
          name="arrow-left"
          size="20px"
          color="#333"
        />
      </view>
      <view class="nav-title">企业认证</view>
    </view>
  </view>
  <!-- 内容区域 -->
  <view class="main-content" style="padding-top: {{statusBarHeight + 44}}px;">
    <!-- 营业执照上传区域 -->
    <view class="upload-section">
    <view class="section-header">
      <view class="title-row">
        <text class="section-title">上传营业执照<text class="required-mark">*</text></text>
      </view>
      <text class="section-subtitle">仅支持汽车相关的企业（含个体）认证，不得盗用他人的公司营业执照</text>
    </view>

    <view class="section-content">
      <!-- 图片上传区域 -->
      <view class="upload-area" bindtap="chooseImage" wx:if="{{!licenseImage && !isApproved}}">
        <view class="upload-icon">+</view>
        <view class="upload-text">点击上传营业执照</view>
      </view>
      <view class="image-preview" wx:if="{{licenseImage}}">
        <image src="{{licenseImage}}" mode="aspectFit"></image>
        <view class="delete-btn" catchtap="deleteLicenseImage" wx:if="{{!isApproved}}">×</view>
      </view>



      <!-- 表单区域 -->
      <view class="form-group">
        <view class="form-row">
          <view class="form-label-left">
            公司全称
            <text class="required-mark">*</text>
          </view>
          <input
            class="form-input-right"
            placeholder="请输入公司的具体名称"
            value="{{companyName}}"
            bindinput="onCompanyNameInput"
            data-field="companyName"
            maxlength="18"
            disabled="{{isApproved}}"
          />
        </view>
        <view class="form-row">
          <view class="form-label-left">
            公司地址
            <text class="required-mark">*</text>
          </view>
          <input
            class="form-input-right"
            placeholder="请输入公司的具体地址"
            value="{{address}}"
            bindinput="onAddressInput"
            data-field="address"
            maxlength="18"
            disabled="{{isApproved}}"
          />
        </view>
        <view class="form-row">
          <view class="form-label-left">
            注册资金
            <text class="required-mark">*</text>
          </view>
          <input
            class="form-input-right"
            placeholder="请输入公司的注册资金"
            value="{{registeredCapital}}"
            bindinput="onRegisteredCapitalInput"
            data-field="registeredCapital"
            maxlength="18"
            disabled="{{isApproved}}"
          />
        </view>
      </view>
    </view>
  </view>


   <view class="upload-section">
    <view class="section-header">
      <view class="title-row">
        <text class="section-title">企业手册上传</text>
      </view>
      <text class="section-subtitle">请上传企业手册，PDF格式</text>
    </view>

    <view class="section-content">
      <!-- 手册pdf上传 -->
      <view class="upload-area" bindtap="choosePdf" wx:if="{{!pdfPath && !isApproved}}">
        <view class="upload-icon">+</view>
        <view class="upload-text">点击上传PDF文件</view>
      </view>
      <view class="file-preview" wx:if="{{pdfPath}}">
        <view class="pdf-content" bindtap="previewPdf">
          <view class="pdf-icon">PDF</view>
          <view class="pdf-info">
            <view class="pdf-name">{{pdfName}}</view>
            <view class="pdf-size">{{pdfSize}}</view>
          </view>
        </view>
        <view class="delete-btn" catchtap="deletePdf" wx:if="{{!isApproved}}">×</view>
      </view>

      <!-- 企业手册图片上传区域 -->
      <view class="upload-area" bindtap="chooseHandbookImage" wx:if="{{!handbookImage && !isApproved}}">
        <view class="upload-icon">+</view>
        <view class="upload-text">点击上传图片</view>
      </view>
      <view class="image-preview" wx:if="{{handbookImage}}">
        <image src="{{handbookImage}}" mode="aspectFit"></image>
        <view class="delete-btn" catchtap="deleteHandbookImage" wx:if="{{!isApproved}}">×</view>
      </view>
 
    </view>
    </view>



      <view class="upload-section">
    <view class="section-header">
      <view class="title-row">
        <text class="section-title">企业详细介绍</text>
      </view>
      <text class="section-subtitle">请详细介绍您的企业</text>
    </view>

    <view class="section-content">
       <textarea class="textarea" placeholder="请输入企业详细介绍" value="{{companyIntro}}" bindinput="onCompanyIntroInput" maxlength="1000" disabled="{{isApproved}}"/>
       <view class="word-count">{{companyIntro.length}}/1000</view>
    </view>
  </view>


   <view class="upload-section">
    <view class="section-header">
      <view class="title-row">
        <text class="section-title">企业主营品牌和车型</text>
      </view>
      <text class="section-subtitle">请输入主营品牌和车型</text>
    </view>

    <view class="section-content">
       <textarea class="textarea" placeholder="请输入主营品牌和车型" value="{{brandInfo}}" bindinput="onBrandInfoInput" maxlength="500" disabled="{{isApproved}}"/>
       <view class="word-count">{{brandInfo.length}}/500</view>
    </view>
  </view>



  <!-- 协议同意区域 - 审核通过时隐藏 -->
  <view class="agreement-section" wx:if="{{status !== 2 && !isApproved}}">
    <checkbox class="checkbox" checked="{{isAgree}}" bindtap="toggleAgree" color="#1296db" disabled="{{isApproved}}" />
    <view class="agreement-text">
      已阅读并同意<text class="link" bindtap="viewAgreement" data-type="service">《用户服务协议》</text>、<text class="link" bindtap="viewAgreement" data-type="privacy">《隐私政策》</text>
    </view>
  </view>

  <!-- 提交按钮 - 审核通过时隐藏 -->
  <view class="submit-container" wx:if="{{status !== 2 && !isApproved}}">
    <button
      class="submit-btn"
      bindtap="submitInfo"
      disabled="{{status === 1 || status === 2}}">
      {{status === 1 ? '审核中' : (status === 2 ? '已认证' : (status === 3 ? '重新提交' : '认证'))}}
    </button>
  </view>

  <!-- 认证成功模态卡片 -->
  <view class="success-modal" wx:if="{{showSuccessModal}}">
    <view class="modal-overlay" bindtap="hideSuccessModal"></view>
    <view class="modal-content">
      <view class="success-icon">
        <view class="check-circle">
          <text class="check-mark">✓</text>
        </view>
      </view>
      <view class="modal-title">恭喜您，认证成功</view>
      <view class="modal-subtitle">您的企业认证审核通过，请登录平台网，如遇问题请联系客服专线！</view>
    </view>
  </view>

  </view>
</view>


