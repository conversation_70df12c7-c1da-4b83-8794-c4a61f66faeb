<!-- pages/brand/brand.wxml -->
<view class="container_brand">
  <view class="recommend-header">
    <text>今日推荐</text>
  </view>

  <view class="hot-brands-section">
    <view class="hot-brands-list">
      <view
        class="hot-brand-item"
        wx:for="{{hotBrands}}"
        wx:key="id"
        bindtap="onBrandTap"
        data-id="{{item.id}}"
        data-name="{{item.name}}"
      >
        <image
          class="hot-brand-icon"
          src="{{item.icon}}"
          mode="aspectFit"
        ></image>
        <text class="hot-brand-name">{{item.name}}</text>
      </view>
    </view>
  </view>


  <van-index-bar>
  <block wx:for="{{brandList}}" wx:key="index">
    <view>
      <van-index-anchor index="{{item.letter}}" />
      <van-cell wx:for="{{item.list}}" wx:key="id" data-id="{{item.id}}" data-name="{{item.name}}" bind:click="onBrandSelect">
        <!-- 自定义内容 -->
        <view class="custom-cell-content">
          <image src="{{item.logo}}" class="logo" />
          <text class="name">{{item.name}}</text>
        </view>
      </van-cell>
    </view>
  </block>
</van-index-bar>
</view>