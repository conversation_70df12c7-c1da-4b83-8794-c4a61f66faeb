<!-- 引入登录弹窗模板 -->
<import src="../../templates/loginPopup/loginPopup.wxml" />

<view
  class="container"
  style="{{ pageStyle }}"
>
  <!-- 头部区域占位符 - 防止内容被固定顶部遮挡 -->
  <view class="header-placeholder"></view>

  <!-- 固定头部区域 -->
  <view class="fixed-header">
    <!-- 顶部状态栏 -->
    <view
      class="status-bar"
      style="height: {{statusBarHeight}}px;"
    ></view>

    <!-- 导航栏 -->
    <view class="nav-bar">
      <view
        class="nav-back"
        bindtap="goBack"
      >
        <van-icon
          name="arrow-left"
          size="20px"
          color="#333"
        />
      </view>
      <view class="nav-title">{{vehicleType == 'new' ? '新车' : vehicleType == 'batch' ? '批量车' : vehicleType == 'used' ?
        '二手车列表' : vehicleType == 'right' ? '右舵车列表' : '车辆列表'}}</view>
      <view class="nav-placeholder"></view>
    </view>

    <!-- 搜索区域 -->
    <view class="search-bar">
      <view class="search-input-wrapper">
        <icon
          type="search"
          size="14"
          class="search-icon"
        ></icon>
        <input
          class="search-input"
          placeholder="请输入品牌或车型"
          placeholder-style="color: #aaa; font-size: 26rpx;"
          value="{{ searchValue }}"
          bindinput="onSearchInput"
          bindconfirm="onSearch"
        />
        <view
          class="search-btn"
          bindtap="onSearch"
        >搜索</view>
      </view>
    </view>

    <!-- 筛选区域容器 -->
    <view
      class="filter-container"
      wx:if="{{ !hideFilterBar }}"
    >
      <!-- 自定义筛选条件栏 -->
      <view class="filter-menu">
        <view
          class="filter-item {{ currentFilter === 'brand' ? 'active' : '' }}"
          bindtap="toggleFilter"
          data-type="brand"
        >
          <text>品牌</text>
          <van-icon
            name="arrow-down"
            size="12px"
            class="filter-arrow"
          />
        </view>
        <!--  <view class="filter-item {{ currentFilter === 'mileage' ? 'active' : '' }}" bindtap="toggleFilter" data-type="mileage">
          <text>里程</text>
          <van-icon name="arrow-down" size="12px" class="filter-arrow" />
        </view>
        -->
        <view
          class="filter-item {{ currentFilter === 'price' ? 'active' : '' }}"
          bindtap="toggleFilter"
          data-type="price"
        >
          <text>价格</text>
          <van-icon
            name="arrow-down"
            size="12px"
            class="filter-arrow"
          />
        </view>
        <view
          class="filter-item {{ currentFilter === 'filter' ? 'active' : '' }}"
          bindtap="onFilterTap"
        >
          <text>筛选</text>
          <van-icon
            name="arrow-down"
            size="12px"
            class="filter-arrow"
          />
        </view>

        <!-- 筛选内容 - 移动到筛选菜单下方 -->
        <view class="filter-content {{ showFilterContent ? 'show' : '' }}">
          <!-- 品牌筛选选项 -->
          <scroll-view
            scroll-y
            class="filter-options"
            wx:if="{{ currentFilter === 'brand' }}"
          >
            <view
              wx:for="{{ brandOptions }}"
              wx:key="value"
              class="filter-option {{ brandValue === item.value ? 'active' : '' }}"
              bindtap="onFilterOptionSelect"
              data-type="brand"
              data-value="{{ item.value }}"
            >
              <text>{{ item.text }}</text>
            </view>
          </scroll-view>

          <!-- 里程筛选选项 -->
          <view
            class="filter-options"
            wx:if="{{ currentFilter === 'mileage' }}"
          >
            <view
              class="filter-option {{ mileageValue === item.value ? 'active' : '' }}"
              wx:for="{{ mileageOptions }}"
              wx:key="value"
              bindtap="onFilterOptionSelect"
              data-type="mileage"
              data-value="{{ item.value }}"
            >
              {{ item.text }}
            </view>
          </view>

          <!-- 价格筛选选项 -->
          <view
            class="filter-options"
            wx:if="{{ currentFilter === 'price' }}"
          >
            <view
              class="filter-option {{ priceValue === item.value ? 'active' : '' }}"
              wx:for="{{ priceOptions }}"
              wx:key="value"
              bindtap="onFilterOptionSelect"
              data-type="price"
              data-value="{{ item.value }}"
            >
              {{ item.text }}
            </view>
          </view>

          <!-- 车龄筛选选项 -->
          <view
            class="filter-options"
            wx:if="{{ currentFilter === 'age' }}"
          >
            <view
              class="filter-option {{ ageValue === item.value ? 'active' : '' }}"
              wx:for="{{ ageOptions }}"
              wx:key="value"
              bindtap="onFilterOptionSelect"
              data-type="age"
              data-value="{{ item.value }}"
            >
              {{ item.text }}
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 遮罩层 -->
  <view
    class="filter-mask {{ showFilterContent ? 'show' : '' }}"
    bindtap="closeFilter"
    catchtouchmove="preventScroll"
  ></view>

  <!-- 车辆列表 -->
  <view class="car-list">
    <block
      wx:for="{{carList}}"
      wx:key="id"
    >
      <!-- 批量车卡片 - 仅在vehicleType为batch时显示 -->
      <view
        wx:if="{{vehicleType == 'batch'}}"
        class="batch-car-item"
        data-id="{{item.id}}"
        bindtap="toDetail"
      >
        <!-- 批量车标题 -->
        <view class="batch-car-title">{{item.title}}</view>

        <!-- 价格区域 -->
        <view class="batch-price-section">
          <text class="batch-price-location">{{item.location || '上海'}}</text>
          <text
            class="batch-price-sub"
            style="text-decoration: line-through;"
          >{{item.newPrice ? '新车指导价' + item.newPrice + '万元' : '新车指导价：13.5万元'}}</text>
          <text class="batch-price-main">{{item.salePrice ? item.salePrice + '万元' : '13万元'}}</text>
        </view>

        <!-- 属性区域 -->
        <view class="batch-attrs-section">
          <view class="batch-attr-item">
            <text class="batch-attr-label">出售数量</text>
            <text class="batch-attr-value">{{item.sell_number || 15}}台</text>
          </view>
          <view class="batch-attr-item">
            <text class="batch-attr-label">车辆属性</text>
            <text class="batch-attr-value">{{item.nature || '展示车'}}</text>
          </view>
          <view class="batch-attr-item">
            <text class="batch-attr-label">发布时间</text>
            <text class="batch-attr-value">{{item.create_time || '2025-04-01'}}</text>
          </view>
        </view>

        <!-- 标签区域 
        <view class="batch-tags">
          <text class="batch-tag">现车</text>
          <text class="batch-tag">口岸</text>
          <text class="batch-tag">特价</text>
          <text class="batch-tag">增票</text>
          <text class="batch-tag">认证商家</text>
        </view>
        -->
      </view>

      <!-- 普通车辆卡片 - 非批量车显示 -->
      <view
        wx:else
        class="car-item"
        data-id="{{item.id}}"
        bindtap="toDetail"
      >
        <view class="car-main-content">
          <!-- 车辆图片 -->
          <view class="car-image-container">
            <image
              class="car-image"
              src="{{item.imageUrl}}"
              mode="aspectFill"
            ></image>
          </view>
          <view class="car-info">
            <view class="car-title">{{item.title}}</view>

            <!-- 车辆参数信息行 - 紧凑排列 -->
            <view class="car-params">
              <text class="param-item">{{item.year || '2023年'}}</text>
              <text class="separator">|</text>
              <text class="param-item">国六</text>
            </view>

            <!-- 价格区域 - 更紧凑的布局 -->
            <view class="price-info">
              <text class="new-price">新车指导价 {{item.newPrice}}万元</text>
              <text class="sale-price">{{item.salePrice}}万元</text>
            </view>

            <!-- 标签区域 
            <view class="car-tags">
              <text class="tag tag-blue">现车</text>
              <text class="tag tag-blue">口岸</text>
              <text class="tag tag-blue">特价</text>
              <text class="tag tag-blue">增票</text>
              <text class="tag tag-blue">认证商家</text>
            </view>
            -->
          </view>
        </view>
      </view>
    </block>

    <view
      class="loading"
      wx:if="{{isLoading}}"
    >
      <text>加载中...</text>
    </view>
    <view
      class="no-more"
      wx:if="{{!hasMoreData && carList.length > 0}}"
    >
      <text>没有更多数据了</text>
    </view>
    <view
      class="empty"
      wx:if="{{!isLoading && carList.length === 0}}"
    >
      <text>暂无车辆数据</text>
    </view>
  </view>

  <!-- 使用 Vant 的 Overlay 组件 -->
  <van-overlay
    show="{{ showAreaPopup }}"
    z-index="1001"
    bind:click="onCloseAreaPopup"
  />

  <van-popup
    show="{{ showAreaPopup }}"
    position="top"
    bind:close="onCloseAreaPopup"
    z-index="1002"
    overlay="false"
    custom-class="city-popup"
  >
    <!-- 添加全国选项和省市选择器 -->
    <view class="area-container">
      <!-- 全国选项 -->
      <view
        class="all-city"
        bindtap="onSelectAllCity"
      >
        <text>全国</text>
      </view>
      <!-- 省市选择器 -->
      <van-area
        area-list="{{ areaList }}"
        value="{{ selectedAreaCode }}"
        bind:confirm="onConfirmArea"
        bind:cancel="onCloseAreaPopup"
        custom-class="custom-area"
        columns-num="{{ 2 }}"
      />
    </view>
  </van-popup>

  <!-- 筛选弹出层 -->
  <van-popup
    show="{{ filterPopupVisible }}"
    position="right"
    custom-style="width: 100%; height: 100%;"
    bind:close="onCloseFilterPopup"
    overlay="true"
    z-index="1100"
  >
    <view class="filter-popup">
      <!-- 顶部安全区域 -->
      <view class="filter-popup-safe-area"></view>

      <view class="filter-popup-header">
        <view class="filter-popup-title">筛选</view>
        <view
          class="filter-popup-close"
          bindtap="onCloseFilterPopup"
        >✕</view>
      </view>

      <view class="filter-popup-content">
        <!-- 地区选择 -->
        <view class="filter-section">
          <view class="section-title">所在地</view>
          <van-area
            area-list="{{ areaList }}"
            columns-num="2"
            bind:change="onAreaChange"
            value="{{ selectedAreaCode }}"
            show-toolbar="{{ false }}"
          />
        </view>

        <!-- 车龄范围 -->
        <view class="filter-section">
          <view class="section-title">车龄</view>
          <van-slider
            value="{{ ageRange }}"
            min="0"
            max="8"
            step="2"
            range
            bind:change="onAgeChange"
          />
          <view class="slider-labels">
            <text>0年</text>
            <text>2年</text>
            <text>4年</text>
            <text>6年</text>
            <text>8年</text>
            <text>不限</text>
          </view>
        </view>

        <!-- 里程范围 -->
        <view class="filter-section">
          <view class="section-title">里程（万公里）</view>
          <van-slider
            value="{{ mileageRange }}"
            min="0"
            max="8"
            step="2"
            range
            bind:change="onMileageChange"
          />
          <view class="slider-labels">
            <text>0</text>
            <text>2</text>
            <text>4</text>
            <text>6</text>
            <text>8</text>
            <text>不限</text>
          </view>
        </view>

        <!-- 价格范围 -->
        <view class="filter-section">
          <view class="section-title">价格范围（万元）</view>
          <van-slider
            value="{{ priceRange }}"
            min="0"
            max="40"
            step="10"
            range
            bind:change="onPriceChange"
          />
          <view class="slider-labels">
            <text>0</text>
            <text>10</text>
            <text>20</text>
            <text>30</text>
            <text>40</text>
            <text>不限</text>
          </view>
        </view>
      </view>

      <!-- 底部按钮 -->
      <view class="filter-popup-footer">
        <view
          class="reset-btn"
          bindtap="onResetFilter"
        >重置</view>
        <view
          class="confirm-btn"
          bindtap="onConfirmFilter"
        >确定</view>
      </view>
    </view>
  </van-popup>

  <!-- 引用登录弹窗模板 -->
  <template
    is="loginPopup"
    data="{{ showLoginPopup, loginPopupOptions }}"
  />
</view>