<!--pages/train_operation/index.wxml-->
<view
  class="container"
  style="--status-bar-height: {{statusBarHeight}}px;"
>
  <!-- 自定义导航栏 -->
  <view
    class="custom-nav"
    style="padding-top: {{statusBarHeight}}px;"
  >
    <view class="nav-content">
      <view
        class="back-icon"
        bindtap="navigateBack"
      >
        <image
          src="/icons/moments/back.svg"
          mode="aspectFit"
        ></image>
      </view>
      <view class="nav-title">车务列表</view>
    </view>
  </view>

  <!-- 主内容区域 -->
  <view
    class="main-content"
    style="padding-top: {{statusBarHeight + 50}}px;"
  >
    <!-- 筛选栏 -->
    <view class="filter-bar">
      <view
        wx:for="{{filterTypes}}"
        wx:key="index"
        class="filter-item {{currentFilter === index ? 'active' : ''}}"
        data-index="{{index}}"
        bindtap="tapFilter"
      >
        <text>{{item}}</text>
        <!-- 根据不同的筛选项和状态使用不同的图标 -->
        <block wx:if="{{index === 2}}">
          <!-- 发布时间图标 -->
          <image
            wx:if="{{sortOrder === 'asc'}}"
            src="/icons/train_operation/asc.png"
            mode="aspectFit"
            class="filter-icon {{currentFilter === index ? 'active' : ''}}"
          ></image>
          <image
            wx:elif="{{sortOrder === 'desc'}}"
            src="/icons/train_operation/desc.png"
            mode="aspectFit"
            class="filter-icon {{currentFilter === index ? 'active' : ''}}"
          ></image>
          <image
            wx:else
            src="/icons/train_operation/sort.png"
            mode="aspectFit"
            class="filter-icon {{currentFilter === index ? 'active' : ''}}"
          ></image>
        </block>
        <block wx:else>
          <!-- 全部品牌和订单类型图标 -->
          <image
            src="/icons/common/down.svg"
            mode="aspectFit"
            class="filter-icon {{currentFilter === index ? 'active' : ''}}"
          ></image>
        </block>
      </view>
    </view>

    <!-- 筛选面板 -->
    <view
      class="filter-panel"
      wx:if="{{showFilterPanel}}"
    >
      <!-- 品牌筛选面板 -->
      <scroll-view
        class="filter-panel-content"
        scroll-y
        wx:if="{{currentFilter === 0}}"
      >
        <view class="filter-title">选择品牌</view>
        <view class="brand-list">
          <view
            wx:for="{{brandList}}"
            wx:key="id"
            class="brand-item {{selectedBrand === item.id ? 'active' : ''}}"
            data-id="{{item.id}}"
            data-name="{{item.brand_name}}"
            bindtap="selectBrand"
          >
            <text>{{item.brand_name}}</text>
          </view>
        </view>
      </scroll-view>

      <!-- 订单类型筛选面板 -->
      <view
        class="filter-panel-content"
        wx:if="{{currentFilter === 1}}"
      >
        <view class="filter-title">选择订单类型</view>
        <view class="order-type-list">
          <view
            wx:for="{{orderTypes}}"
            wx:key="id"
            class="order-type-item {{selectedOrderType === item.id ? 'active' : ''}}"
            data-id="{{item.id}}"
            bindtap="selectOrderType"
          >
            <text>{{item.name}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 车辆列表(使用scroll-view替换普通view) -->
    <scroll-view
      class="vehicle-list"
      scroll-y="true"
      bindscrolltolower="onScrollToLower"
      lower-threshold="100"
      enable-back-to-top="true"
      refresher-enabled="{{false}}"
      style="height: calc(100vh - {{statusBarHeight + 44}}px - 90rpx - 60rpx);"
    >
      <view
        wx:for="{{vehicleList}}"
        wx:key="id"
        class="vehicle-item"
        data-id="{{item.id || item.chewu_orderid}}"
        bindtap="tapItem"
      >
        <!-- 车型信息和右箭头 -->
        <view class="vehicle-name-row">
          <view class="vehicle-name">{{item.name || item.title}}</view>
          <!-- 右箭头 -->
          <view class="arrow">
            <image
              src="https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/wechat/assets/icons/arrow-right.png"
              mode="aspectFit"
            ></image>
          </view>
        </view>

        <!-- 详细信息区块 -->
        <view class="vehicle-info">
          <!-- 三列标题 -->
          <view class="info-header">
            <text>订单类型</text>
            <text>车务公司所在地</text>
            <text>交付地点</text>
          </view>

          <!-- 三列内容 -->
          <view class="info-content">
            <text>{{item.orderType || item.order_type || '-'}}</text>
            <text>{{item.company || item.address || '-'}}</text>
            <text>{{item.deliveryLocation || item.duijie_address || '-'}}</text>
          </view>
        </view>

        <!-- 发布时间 -->
        <view class="publish-time">发布时间: {{item.publishTime || item.create_time}}</view>
      </view>

      <!-- 没有数据时显示的提示 -->
      <view
        class="empty-tip"
        wx:if="{{vehicleList.length === 0}}"
      >
        暂无数据
      </view>

      <!-- 加载更多提示 -->
      <view
        class="loading-more"
        wx:if="{{vehicleList.length > 0}}"
      >
        <view
          wx:if="{{isLoading}}"
          class="loading"
        >
          <view class="loading-icon"></view>
          <text>加载中...</text>
        </view>
        <view
          wx:elif="{{!hasMore}}"
          class="no-more"
        >
          <text>—— 已经到底了 ——</text>
        </view>
        <view
          wx:else
          class="pull-tip"
        >
          <text>上拉加载更多</text>
        </view>
      </view>

      <!-- 添加手动加载更多按钮 -->
      <view
        wx:if="{{hasMore && !isLoading && vehicleList.length > 0}}"
        class="load-more-btn"
        bindtap="loadMore"
      >
        点击加载更多
      </view>
    </scroll-view>
  </view>
</view>