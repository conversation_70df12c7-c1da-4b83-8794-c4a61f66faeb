// pages/information/index.js
import api from '../../utils/api';  // 导入API
import util from '../../utils/util';
import config from '../../config';  // 导入配置文件

Page({

  /**
   * 页面的初始数据
   */
  data: {
    currentTab: 'used',
    carList: [],
    merchantInfo: {},
    COS_CONFIG: config.COS_CONFIG, // 添加腾讯云配置
    isLoading: false,
    pageLoading: true,
    statusBarHeight: 20, // 默认状态栏高度
    navHeight: 0, // 新增导航栏高度
    app_id: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取系统状态栏高度
    this.getStatusBarHeight();

    // 可以从options获取商家ID，然后请求商家信息
    const merchantId = options.id;
    this.fetchMerchantInfo(merchantId);

    // 设置加载状态
    this.setData({ pageLoading: true, app_id: merchantId });

    // 加载车辆数据
    this.fetchCarList('used', merchantId);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 获取系统状态栏高度
  getStatusBarHeight() {
    wx.getSystemInfo({
      success: (res) => {
        this.setData({
          statusBarHeight: res.statusBarHeight,
          navHeight: res.statusBarHeight + 44
        });
      }
    });
  },

  // 切换标签页
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      currentTab: tab
    });
    // 根据选中的标签页加载不同类型的车辆
    this.fetchCarList(tab, this.data.app_id);
  },

  // 拨打电话联系商家
  callMerchant() {
    wx.makePhoneCall({
      phoneNumber: this.data.merchantInfo.mobile,
      fail(err) {
        console.error('拨打电话失败', err);
      }
    });
  },

  // 在线联系商家（可以跳转到聊天页面）
  navigateToMerchant() {
    wx.navigateTo({
      url: '/pages/chat/index?merchantId=' + this.data.merchantInfo.app_id
    });
  },

  // 查看车辆详情
  goToCarDetail(e) {
    const carId = e.currentTarget.dataset.id;
    let url = '/pages/buy/detail';
    if (this.data.currentTab == 'batch') {
      url = '/pages/vehicle/bdetail'
    } else if (this.data.currentTab == 'new') {
      url = '/pages/vehicle/ndetail'
    } else if (this.data.currentTab == 'commercial') {
      url = '/pages/commercial/detail'
    }

    wx.navigateTo({
      url: url + '?id=' + carId
    });
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  },

  // 获取商家信息（实际应用中需要从服务器获取）
  async fetchMerchantInfo(merchantId) {
    const res = await api.merchant.information({
      app_id: merchantId
    })
    res.time = '周一-周五 09:00-20:30',
      console.log(res)
    this.setData({
      merchantInfo: res
    })
  },

  // 获取车辆列表（使用首页的推荐车辆接口）
  async fetchCarList(type = 'used', app_id) {
    this.setData({ isLoading: true });

    try {
      // 根据选项卡类型设置不同的请求参数
      const params = {
        page: 1,
        list_rows: 10,
        app_id: app_id
      };

      // 根据标签类型添加筛选条件
      if (type === 'used') {
        params.type = 'used';
      } else if (type === 'batch') {
        params.type = 'batch';
      } else if (type === 'new') {
        params.type = 'new';
      } else if (type === 'commercial') {
        params.type = 'commercial';
        params.type_id = 1;
      }
      let result; // 提前声明
      if (type == 'used') {
        result = await api.car.getCarList(params);
      } else {
        result = await api.car.getList(params);
      }

      if (result && result.data) {
        const carListData = Array.isArray(result.data) ? result.data : [];

        // 使用格式化函数处理车辆数据
        const formattedCars = this.formatCarData(carListData);

        this.setData({
          carList: formattedCars,
          isLoading: false,
          pageLoading: false
        });
      } else {
        this.setData({
          isLoading: false,
          pageLoading: false
        });
      }
    } catch (error) {
      console.error('获取车辆列表失败:', error);
      this.setData({
        isLoading: false,
        pageLoading: false
      });
    }
  },

  // 将车辆数据转换为展示格式的函数
  formatCarData(carListData) {
    return carListData.map(car => ({
      id: car.id,
      image: car.main_url || (car.image_urls ? car.image_urls.split(',')[0] : '') || 'https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/uploads/HD069231685/650685767643205.jpg',
      title: car.ui_vehicle_name || car.car_title || '',
      year: car.first_registration_time ? car.first_registration_time.substring(0, 4) : car.create_time.substring(0, 4),
      mileage: (car.mileage || 0).toString(),
      type: car.source_type === 1 ? '进口' : '国产',
      publishTime: car.create_time ? car.create_time.substring(0, 10) : '',
      guidePrice: car.guide_price || (parseFloat(car.sell_price || 0) + 3.4).toFixed(2), // 新车指导价，如果没有则使用售价加3.4万的默认值
      price: car.sell_price || '0',
      address: util.splitValue(car.vehicle_source_location, " ", 0) || '全国',
      nature_name: car.nature_name || '',
      sell_number: car.sell_number || 0
    }));
  }
})