<!--pages/article/index.wxml-->
<view class="container">
  <!-- 顶部区域背景容器 -->
  <view
    class="top-container"
    style="height: {{navBarHeight + 44}}px;"
  >
    <!-- 渐变背景层 -->
    <view class="gradient-bg"></view>

    <!-- 自定义导航栏 -->
    <view
      class="custom-navbar"
      style="height: {{navBarHeight}}px; padding-top: {{statusBarHeight}}px;"
    >
      <view
        class="nav-back"
        bindtap="navigateBack"
        style="top: {{statusBarHeight}}px; height: {{menuButtonHeight}}px; line-height: {{menuButtonHeight}}px;"
      >
        <van-icon
          name="arrow-left"
          size="20px"
          color="#333"
        />
      </view>
      <view
        class="nav-title"
        style="height: {{menuButtonHeight}}px; line-height: {{menuButtonHeight}}px;"
      >我的帖子</view>
    </view>

    <!-- 选项卡导航 -->
    <view
      class="tab-nav"
      style="top: {{navBarHeight}}px;"
    >
      <view
        class="tab-item {{activeTab === 0 ? 'active' : ''}}"
        bindtap="switchTab"
        data-index="0"
      >
        <text>我的帖子</text>
        <view
          class="tab-line"
          wx:if="{{activeTab === 0}}"
        ></view>
      </view>
      <view
        class="tab-item {{activeTab === 1 ? 'active' : ''}}"
        bindtap="switchTab"
        data-index="1"
      >
        <text>收到的点赞</text>
        <view
          class="tab-line"
          wx:if="{{activeTab === 1}}"
        ></view>
      </view>
      <view
        class="tab-item {{activeTab === 2 ? 'active' : ''}}"
        bindtap="switchTab"
        data-index="2"
      >
        <text>收到的评论</text>
        <view
          class="tab-line"
          wx:if="{{activeTab === 2}}"
        ></view>
      </view>
      <view
        class="tab-edit"
        bindtap="onEditTap"
      >
        <image
          class="edit-icon"
          src="/icons/favorite/edit.png"
          mode="aspectFit"
        ></image>
        <text>{{isEditMode ? '取消' : '编辑'}}</text>
      </view>
    </view>
  </view>

  <!-- 添加遮罩层，用于点击关闭菜单 -->
  <view
    class="mask"
    wx:if="{{hasOpenMenu}}"
    bindtap="closeAllMenus"
  ></view>

  <!-- 内容区域 - 使用条件渲染展示不同标签页的内容 -->
  <block wx:if="{{activeTab === 0}}">
    <view
      class="article-list"
      style="padding-top: {{navBarHeight + 44}}px;"
    >
      <block
        wx:for="{{articleList}}"
        wx:key="id"
      >
        <!-- 日期标记 - 仅在需要显示的帖子上显示 -->
        <view
          class="date-tag"
          wx:if="{{item.showDateLabel}}"
        >{{item.dateLabel}}</view>
        <view
          class="article-item"
          bindtap="{{isEditMode ? 'toggleSelectItem' : 'goToMomentDetail'}}"
          data-id="{{item.id}}"
          data-index="{{index}}"
          data-tab="0"
          data-status="{{item.is_review}}"
        >
          <!-- 选择框 - 仅在编辑模式显示 -->
          <view
            class="select-checkbox"
            wx:if="{{isEditMode}}"
            catchtap="toggleSelectItem"
            data-id="{{item.id}}"
            data-index="{{index}}"
            data-tab="0"
          >
            <view class="checkbox-inner {{item.selected ? 'selected' : ''}}">
              <van-icon
                wx:if="{{item.selected}}"
                name="success"
                size="12px"
                color="#fff"
              />
            </view>
          </view>

          <!-- 文章内容区域 -->
          <view class="article-content {{isEditMode ? 'edit-mode' : ''}}">
            <view class="title">{{item.ui_vehicle_name}}</view>

            <view class="info-row">
              <view class="info-left">
                <view class="info-item">类型：{{item.used_type_name || '二手车'}}</view>
              </view>
              <view class="info-right">
                <view class="info-item">价格：{{item.price}}万</view>
              </view>
            </view>

            <view class="info-row">
              <view class="info-left">
                <view
                  class="info-item"
                  wx:if="{{item.quantity}}"
                >数量：{{item.quantity}}辆</view>
              </view>
              <view class="info-right">
                <view
                  class="info-item"
                  wx:if="{{item.delivery_location}}"
                >交付地：{{item.delivery_location}}</view>
              </view>
            </view>

            <!-- 底部区域 -->
            <view class="bottom-row">
              <!-- 阅读量 -->
              <view
                class="view-count"
                wx:if="{{item.is_review != 1 && item.is_review != 3}}"
              >
                <image
                  src="/icons/article/view.png"
                  mode="aspectFit"
                ></image>
                <text>浏览{{item.views || 0}}次</text>
              </view>

              <!-- 状态标签 -->
              <view class="publish-tag {{ item.tag.class }}">
                <text>{{ item.tag.name }}</text>
              </view>
            </view>

            <!-- 底部日期 -->
            <!-- <view class="bottom-date">{{item.fullDate}}</view> -->
          </view>

          <!-- 操作按钮 - 仅在编辑模式显示 -->
          <view
            class="operations"
            wx:if="{{isEditMode && !showSelectButtons}}"
          >
            <view
              class="delete-btn"
              data-id="{{item.id}}"
              bindtap="onDeleteTap"
            >删除</view>
            <view
              class="edit-btn"
              data-id="{{item.id}}"
              bindtap="onEditArticleTap"
            >编辑</view>
          </view>
        </view>
      </block>

      <!-- 加载更多 -->
      <view
        class="no-more"
        wx:if="{{noMore}}"
      >没有更多了</view>
    </view>
  </block>

  <block wx:elif="{{activeTab === 1}}">
    <view
      class="likes-list {{isEditMode ? 'isEditMode' : ''}}"
      style="padding-top: {{navBarHeight + 44}}px;"
    >
      <!-- 调试信息 -->
      <view
        wx:if="{{!likesList || likesList.length === 0}}"
        class="debug-info"
      >
        <view style="padding: 20px; color: #999; text-align: center;">当前没有点赞数据显示</view>
      </view>

      <block
        wx:for="{{likesList}}"
        wx:key="id"
      >
        <!-- 日期标记 -->
        <view
          class="date-tag"
          wx:if="{{item.showDateLabel}}"
        >{{item.dateStr}}</view>

        <!-- 点赞卡片 -->
        <view
          class="like-item"
          bindtap="{{isEditMode ? 'toggleSelectItem' : ''}}"
          data-id="{{item.id}}"
          data-index="{{index}}"
          data-tab="1"
        >
          <!-- 选择框 - 仅在编辑模式显示 -->
          <view
            class="select-checkbox"
            wx:if="{{isEditMode}}"
            catchtap="toggleSelectItem"
            data-id="{{item.id}}"
            data-index="{{index}}"
            data-tab="1"
          >
            <view class="checkbox-inner {{item.selected ? 'selected' : ''}}">
              <van-icon
                wx:if="{{item.selected}}"
                name="success"
                size="12px"
                color="#fff"
              />
            </view>
          </view>

          <!-- 点赞用户信息 -->
          <view class="like-header">
            <!-- 左侧用户信息 -->
            <view class="like-user-info">
              <image
                class="like-avatar"
                src="{{item.user.avatar || 'https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/uploads/proof/1751272635627_786.png'}}"
                mode="aspectFill"
              ></image>
              <view class="like-user-details">
                <view class="like-user-name">{{item.user.name}}</view>
                <!-- 点赞信息放在用户名下方 -->
                <view class="like-info">
                  <view class="like-action">赞了你的帖子</view>
                  <view class="like-time">{{item.dateTimeStr || '昨天 12:55'}}</view>
                </view>
              </view>
            </view>
          </view>

          <!-- 帖子内容预览 -->
          <view
            class="liked-article"
            bindtap="{{!isEditMode ? 'goToMomentDetail' : ''}}"
            data-id="{{item.article.id || item.post_id}}"
          >
            <view class="liked-title-card">
              <view class="liked-title">{{item.article.title || '比亚迪 秦PLUS 2023 冠军版 DM-i 120KM领先型'}}</view>
              <text class="liked-dots">•••••</text>
            </view>
          </view>
        </view>
      </block>

      <!-- 加载更多 -->
      <view
        class="no-more"
        wx:if="{{noMore}}"
      >没有更多了</view>
    </view>
  </block>

  <block wx:elif="{{activeTab === 2}}">
    <!-- 收到的评论列表 -->
    <view
      class="comments-list {{isEditMode ? 'isEditMode' : ''}}"
      style="padding-top: {{navBarHeight + 44}}px;"
    >
      <block
        wx:for="{{commentsList}}"
        wx:key="id"
      >
        <!-- 评论卡片 -->
        <view
          class="comment-item"
          bindtap="{{isEditMode ? 'toggleSelectItem' : ''}}"
          data-id="{{item.id}}"
          data-index="{{index}}"
          data-tab="2"
        >
          <!-- 选择框 - 仅在编辑模式显示 -->
          <view
            class="select-checkbox"
            wx:if="{{isEditMode}}"
            catchtap="toggleSelectItem"
            data-id="{{item.id}}"
            data-index="{{index}}"
            data-tab="2"
          >
            <view class="checkbox-inner {{item.selected ? 'selected' : ''}}">
              <van-icon
                wx:if="{{item.selected}}"
                name="success"
                size="12px"
                color="#fff"
              />
            </view>
          </view>

          <!-- 评论用户信息 -->
          <view class="comment-user">
            <image
              class="comment-avatar {{item.user.isOfficial ? 'official-avatar' : ''}}"
              src="{{item.user.avatar}}"
              mode="aspectFill"
            ></image>
            <view class="comment-user-info">
              <view class="comment-user-name">{{item.user.name}}</view>
              <view class="comment-action-row">
                <text class="comment-action">{{item.actionText}}</text>
                <text class="comment-time">{{item.dateDisplay}}</text>
              </view>
            </view>
          </view>

          <!-- 评论内容 -->
          <view class="comment-content">
            <view
              class="comment-target"
              wx:if="{{item.targetUser}}"
            >@{{item.targetUser}}</view>
            <view class="comment-text">{{item.content}}</view>
          </view>

          <!-- 回复列表 -->
          <view
            class="replies-container"
            wx:if="{{item.replies && item.replies.length > 0}}"
          >
            <view
              class="replies-header"
              bindtap="{{!isEditMode ? 'toggleReplies' : ''}}"
              data-id="{{item.id}}"
            >
              <text>{{item.showReplies ? '收起' : '查看'}}{{item.replies.length}}条回复</text>
              <van-icon
                name="{{item.showReplies ? 'arrow-up' : 'arrow-down'}}"
                size="12px"
                color="#999"
              />
            </view>
            <view
              class="replies-list"
              wx:if="{{item.showReplies}}"
            >
              <view
                class="reply-item"
                wx:for="{{item.replies}}"
                wx:for-item="reply"
                wx:key="id"
              >
                <view class="reply-user">{{reply.short_name || '用户'}}</view>
                <view class="reply-content">{{reply.content}}</view>
                <view class="reply-time">{{reply.create_time}}</view>
              </view>
            </view>
          </view>

          <!-- 评论所属的帖子 -->
          <view
            class="comment-article"
            bindtap="{{!isEditMode ? 'goToArticleDetail' : ''}}"
            data-id="{{item.article.id}}"
          >
            <text class="article-title">{{item.article.title}}</text>
            <text class="article-more">•••••</text>
          </view>

          <!-- 回复按钮 -->
          <view
            class="reply-btn"
            bindtap="{{!isEditMode ? 'onReplyComment' : ''}}"
            data-id="{{item.id}}"
          >
            <image
              src="/icons/article/reply.png"
              mode="aspectFit"
            ></image>
            <text>回复</text>
          </view>
        </view>
      </block>

      <!-- 加载更多 -->
      <view
        class="no-more"
        wx:if="{{noMore}}"
      >没有更多了</view>

      <!-- 无评论提示 -->
      <view
        class="empty-notice"
        wx:if="{{!commentsList || commentsList.length === 0}}"
      >
        暂无收到的评论
      </view>

      <!-- 底部留白，防止被评论框遮挡 -->
      <view
        class="bottom-space"
        wx:if="{{showCommentInput}}"
      ></view>
    </view>
  </block>

  <!-- 固定在底部的草稿箱按钮 - 仅在"我的帖子"标签页显示 -->
  <view
    class="draft-box-btn"
    bindtap="goDraftBox"
    wx:if="{{activeTab === 0 && !isEditMode}}"
  >
    <image src="/icons/article/draft.png"></image>
    <text>草稿箱</text>
  </view>

  <!-- 评论输入框 - 仅在需要回复评论时显示 -->
  <view
    class="comment-bar"
    wx:if="{{showCommentInput}}"
  >
    <view class="comment-input-container">
      <image
        class="comment-icon"
        src="/icons/moments/enter_comment.png"
      />
      <input
        class="comment-input"
        placeholder="{{commentPlaceholder}}"
        value="{{commentValue}}"
        bindinput="onCommentInput"
        focus="{{commentFocus}}"
        confirm-type="send"
        bindconfirm="submitComment"
      />
      <view
        class="cancel-reply"
        bindtap="cancelReply"
      >取消</view>
    </view>
  </view>

  <!-- 底部操作栏 - 仅在编辑模式显示 -->
  <view
    class="bottom-action-bar"
    wx:if="{{isEditMode}}"
  >
    <view
      class="select-all-btn"
      bindtap="toggleSelectAll"
    >
      <view class="select-all-checkbox">
        <view class="checkbox-inner {{allSelected ? 'selected' : ''}}">
          <van-icon
            wx:if="{{allSelected}}"
            name="success"
            size="12px"
            color="#fff"
          />
        </view>
      </view>
      <text>全选</text>
    </view>
    <view
      class="delete-selected-btn"
      bindtap="deleteSelectedItems"
    >删除({{selectedCount || 0}})</view>
  </view>
</view>