// pages/set/phoneget.js
import util from '../../utils/util';
import api from '../../utils/api';
import config from '../../config';

Page({

  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: 20, // 默认状态栏高度，会在onLoad中动态获取
    phone: '', // 手机号
    phoneFormatted: '', // 格式化后的手机号显示
    verifyCode: '', // 验证码
    countDown: 0, // 验证码倒计时
    timer: null // 定时器
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });

    // 隐藏原生导航栏
    wx.hideNavigationBarLoading();
    wx.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: 'transparent',
      animation: {
        duration: 0,
        timingFunc: 'easeIn'
      }
    });

    // 从路由参数中获取手机号
    if (options.phone) {
      const phone = options.phone;
      // 格式化手机号显示 例如：152 6659 8845
      const phoneFormatted = this.formatPhoneNumber(phone);

      this.setData({
        phone,
        phoneFormatted
      });
    }
  },

  /**
   * 格式化手机号
   * 将11位手机号格式化为 xxx xxxx xxxx 的形式
   */
  formatPhoneNumber(phone) {
    if (!phone || phone.length !== 11) return phone;
    return phone.substring(0, 3) + ' ' + phone.substring(3, 7) + ' ' + phone.substring(7);
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 输入验证码
   */
  inputVerifyCode(e) {
    this.setData({
      verifyCode: e.detail.value
    });
  },

  /**
   * 获取验证码
   */
  getVerifyCode() {
    const { phone, countDown } = this.data;

    // 如果正在倒计时，不允许重新获取
    if (countDown > 0) return;

    // 确保手机号存在
    if (!phone) {
      wx.showToast({
        title: '手机号不能为空',
        icon: 'none'
      });
      return;
    }

    // 校验手机号格式
    if (!/^1[3-9]\d{9}$/.test(phone)) {
      wx.showToast({
        title: '手机号格式不正确',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '发送中',
    });

    // 打印请求参数便于调试
    const params = {
      mobile: phone,
      event: 'register',
      type: 'admin'
    };
    console.log('发送验证码请求参数:', params);

    // 调用获取验证码API - 直接使用wx.request以避开可能的http封装问题
    wx.request({
      url: config.BASE_URL + 'addons/shopro/index/send',
      method: 'POST',
      data: params,
      success: (res) => {
        wx.hideLoading();

        // 打印原始响应，查看完整结构
        console.log('验证码接口原始响应:', res);

        // 获取实际的业务数据
        const responseData = res.data;
        console.log('验证码业务数据:', responseData);

        // 响应处理
        if (responseData.code === 1) {
          // 发送成功
          // 开始60秒倒计时
          this.setData({
            countDown: 60
          });

          this.startCountDown();

          wx.showToast({
            title: '验证码已发送',
            icon: 'success'
          });
        } else if (responseData.code === 0 && responseData.msg && responseData.msg.includes('已经被注册')) {
          // 手机号已被注册的特殊处理
          wx.showToast({
            title: responseData.msg || '手机号已注册',
            icon: 'none'
          });
        } else {
          // 其他错误情况
          wx.showToast({
            title: responseData.msg || '验证码发送失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('验证码请求失败:', err);
        wx.showToast({
          title: '验证码发送失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 开始倒计时
   */
  startCountDown() {
    // 清除之前的定时器
    if (this.data.timer) {
      clearInterval(this.data.timer);
    }

    // 创建新的定时器
    const timer = setInterval(() => {
      if (this.data.countDown <= 1) {
        clearInterval(timer);
        this.setData({
          countDown: 0,
          timer: null
        });
        return;
      }

      this.setData({
        countDown: this.data.countDown - 1
      });
    }, 1000);

    this.setData({
      timer: timer
    });
  },

  /**
   * 提交验证码
   */
  submitVerify() {
    const { phone, verifyCode } = this.data;

    // 验证码不能为空
    if (!verifyCode) {
      wx.showToast({
        title: '请输入验证码',
        icon: 'none'
      });
      return;
    }

    // 调用验证接口
    wx.showLoading({
      title: '验证中',
    });

    // 获取本地存储的旧手机号
    // 获取用户信息
    const userInfo = util.getCacheWithExpiry('userInfo');
    const old_mobile = userInfo?.mobile || '';

    // 获取缓存中的app_id
    const app_id = userInfo.app_id || '';

    // 调用更新手机号API
    api.user.updatePhone({
      mobile: phone,
      code: verifyCode,
      old_mobile: old_mobile,
      app_id: app_id
    }).then(res => {
      wx.hideLoading();
      console.log('更新手机号响应:', res);
      console.log('响应类型:', typeof res, '响应code类型:', typeof res.code);

      // 确保正确处理后端返回的成功状态，使用==而不是===，兼容数字和字符串类型的code
      if (res.code == 0 || res.code == '0' || res.msg && res.msg.includes('成功')) {
        // 验证成功
        console.log('手机号更新成功，开始清除缓存');

        // 清除用户缓存 - util中没有removeCacheWithExpiry函数
        // 使用wx.removeStorageSync直接清除userInfo缓存
        wx.removeStorageSync('userInfo');
        wx.removeStorageSync('token');
        console.log('缓存已清除');

        wx.showToast({
          title: '手机号已更新',
          icon: 'success',
          duration: 2000,
          success: () => {
            console.log('Toast显示成功，准备跳转');

            // 直接跳转到我的页面并提示重新登录
            wx.showModal({
              title: '提示',
              content: '手机号已更新，需要重新登录',
              showCancel: false,
              success: (res) => {
                console.log('Modal确认，跳转到我的页面');
                if (res.confirm) {
                  wx.switchTab({
                    url: '/pages/my/index',
                    success: () => {
                      console.log('成功跳转到我的页面');
                    },
                    fail: (err) => {
                      console.error('跳转失败:', err);
                    }
                  });
                }
              }
            });
          },
          fail: (err) => {
            console.error('Toast显示失败:', err);
          }
        });
      } else {
        // 验证失败
        wx.showToast({
          title: res.msg || '手机号更新失败',
          icon: 'none',
          duration: 2000
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('更新手机号失败:', err);
      wx.showToast({
        title: '验证码错误，更新手机号失败，请重试',
        icon: 'none',
        duration: 2000
      });
    });
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 清除定时器
    if (this.data.timer) {
      clearInterval(this.data.timer);
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})