// 导入 API 模块
import api from '../../utils/api';

import util from '../../utils/util';

import share from '../../utils/share';  // 确保导入 分享

Page({
  behaviors: [share], //分享设置
  data: {
    vehicleInfo: {},
    shareData: {},
    current: 0,
    isFavorite: false,
    merchantInfo: {} // 添加商家信息对象
  },

  onLoad: function (options) {
    // 显示右上角的转发按钮
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });

    // 获取路由参数中的车辆ID
    const { id } = options;
    this.setData({ id: id })
    if (id) {
      this.getVehicleDetail(id);
    }
  },

  // 预览图片
  previewImage: function (e) {
    const current = e.currentTarget.dataset.url;
    wx.previewImage({
      current: current,
      urls: this.data.vehicleInfo.images
    });
  },

  // 获取车辆详情数据
  async getVehicleDetail(id) {
    wx.showLoading({
      title: '加载中...',
    });

    // 构建查询参数
    const queryParams = {
      id,
      type: 'new'
    };

    const result = await api.car.getDetail(queryParams);
    // console.log(result)
    if (result) {
      // 尝试获取商家信息
      this.getMerchantInfo(result.app_id);

      // 处理基本信息
      const vehicleInfo = {
        title: result.ui_vehicle_name || '--',
        publishTime: util.formatDate(result.create_time) || '--',
        location: result.vehicle_location || '--',
        guidePrice: result.official_price || '--',
        salePrice: result.msrp || '--',
        images: result.image_urls || [
          'https://p9-dcd.byteimg.com/motor-mis-img/9bf27d1eccd74d35d7efc4917dbaf13e~tplv-f042mdwyw7-original:960:0.image',
          "https://p9-dcd.byteimg.com/motor-mis-img/6eedac9a14d3a84c7a5654075fa38ba2~tplv-f042mdwyw7-original:960:0.image",
        ],
        brand: result.brand_name || '--',
        series: result.series_name || '--',
        manufacturer: result.make || '--',
        bodyType: result.body_type || '--',
        level: result.level_type || '--',
        stock: result.inventory_qty || '--'
      };

      // 处理规格数据
      if (result.specs && result.specs.length > 0) {
        const spec = result.specs[0];

        // 解析车身尺寸
        if (spec.body_cs) {
          try {
            const csData = JSON.parse(spec.body_cs.replace(/'/g, '"'));
            vehicleInfo.length = csData.length || '--';
            vehicleInfo.width = csData.width || '--';
            vehicleInfo.height = csData.height || '--';
            vehicleInfo.wheelbase = csData.wheelbase || '--';
            vehicleInfo.bodyStruct = csData.body_struct || '--';
            vehicleInfo.seatCount = csData.seat_count || '--';
            vehicleInfo.curbWeight = csData.curb_weight || '--';
            vehicleInfo.fullLoadWeight = csData.full_load_weight || '--';
            vehicleInfo.oilTankVolume = csData.oil_tank_volume || '--';
            vehicleInfo.baggageVolume = csData.baggage_volume || '--';
            vehicleInfo.frontTrack = csData.front_track || '--';
            vehicleInfo.rearTrack = csData.rear_track || '--';
            vehicleInfo.doorNums = csData.door_nums || '--';
            vehicleInfo.doorOpenWay = csData.door_open_way || '--';
            vehicleInfo.minTurningRadius = csData.min_turning_radius || '--';
          } catch (e) {
            console.error('解析车身尺寸数据失败', e);
          }
        }

        // 解析变速箱
        if (spec.body_bsx) {
          try {
            const bsxData = JSON.parse(spec.body_bsx.replace(/'/g, '"'));
            vehicleInfo.gearboxType = bsxData.gearbox_type || '--';
            vehicleInfo.gearboxDesc = bsxData.gearbox_description || '--';
            vehicleInfo.gearboxStalls = bsxData.stalls || '--';
          } catch (e) {
            console.error('解析变速箱数据失败', e);
          }
        }

        // 解析电动机
        if (spec.body_ddj) {
          try {
            const ddjData = JSON.parse(spec.body_ddj.replace(/'/g, '"'));
            vehicleInfo.electricDescription = ddjData.electric_description || '--';
            vehicleInfo.electricType = ddjData.electric_type || '--';
            vehicleInfo.totalElectricPower = ddjData.total_electric_power || '--';
            vehicleInfo.frontElectricMaxHorsepower = ddjData.front_electric_max_horsepower || '--';
            vehicleInfo.totalElectricTorque = ddjData.total_electric_torque || '--';
            vehicleInfo.frontElectricMaxPower = ddjData.front_electric_max_power || '--';
            vehicleInfo.frontElectricMaxTorque = ddjData.front_electric_max_torque || '--';
            vehicleInfo.rearElectricMaxPower = ddjData.rear_electric_max_power || '--';
            vehicleInfo.rearElectricMaxTorque = ddjData.rear_electric_max_torque || '--';
            vehicleInfo.electricSystemPower = ddjData.electric_system_power || '--';
            vehicleInfo.electricSystemTorque = ddjData.electric_system_torque || '--';
            vehicleInfo.electricDriveNumber = ddjData.electric_drive_number || '--';
            vehicleInfo.electricLayout = ddjData.electric_layout || '--';
          } catch (e) {
            console.error('解析电动机数据失败', e);
          }
        }

        // 解析电池/充电
        if (spec.body_dccd) {
          try {
            const dccdData = JSON.parse(spec.body_dccd.replace(/'/g, '"'));
            vehicleInfo.batteryType = dccdData.battery_type || '--';
            vehicleInfo.batterySpecialTechnology = dccdData.battery_special_technology || '--';
            vehicleInfo.batteryBrand = dccdData.battery_brand || '--';
            vehicleInfo.batteryWarranty = dccdData.battery_warranty || '--';
            vehicleInfo.batteryCapacity = dccdData.battery_capacity || '--';
            vehicleInfo.batteryEnergyDensity = dccdData.battery_energy_density || '--';
            vehicleInfo.batteryChargingRate = dccdData.battery_charging_rate || '--';
            vehicleInfo.batteryCharge = dccdData.battery_charge || '--';
            vehicleInfo.maxFastChargePower = dccdData.max_fast_charge_power || '--';
            vehicleInfo.quickChargePosition = dccdData.quick_charge_position_v5 || '--';
            vehicleInfo.slowChargePosition = dccdData.slow_charge_position_v5 || '--';
            vehicleInfo.batteryTemperatureSystem = dccdData.battery_temperature_management_system || '--';
            vehicleInfo.singleFootboardMode = dccdData.single_footboard_mode || '--';
            vehicleInfo.vtolPowerStation = dccdData.vtol_power_station || '--';
            vehicleInfo.maxExternalDischargePower = dccdData.max_external_discharge_power || '--';
            vehicleInfo.vtovMaxExternalDischargePower = dccdData.vtov_max_external_discharge_power || '--';
            vehicleInfo.minBatteryValueDischarge = dccdData.min_battery_value_discharge || '--';
          } catch (e) {
            console.error('解析电池/充电数据失败', e);
          }
        }

        // 解析底盘转向
        if (spec.body_dpzx) {
          try {
            const dpzxData = JSON.parse(spec.body_dpzx.replace(/'/g, '"'));
            vehicleInfo.driveForm = dpzxData.driver_form || '--';
            vehicleInfo.fourwheelDriveType = dpzxData.fourwheel_drive_type || '--';
            vehicleInfo.frontSuspension = dpzxData.front_suspension_form || '--';
            vehicleInfo.rearSuspension = dpzxData.rear_suspension_form || '--';
            vehicleInfo.powerSteeringType = dpzxData.power_steering_type || '--';
            vehicleInfo.carBodyStructure = dpzxData.car_body_structure || '--';
          } catch (e) {
            console.error('解析底盘转向数据失败', e);
          }
        }

        // 解析车轮制动
        if (spec.body_clzd) {
          try {
            const clzdData = JSON.parse(spec.body_clzd.replace(/'/g, '"'));
            vehicleInfo.frontBrakeType = clzdData.front_brake_type || '--';
            vehicleInfo.rearBrakeType = clzdData.rear_brake_type || '--';
            vehicleInfo.parkBrakeType = clzdData.park_brake_type || '--';
            vehicleInfo.frontTireSize = clzdData.front_tire_size || '--';
            vehicleInfo.rearTireSize = clzdData.rear_tire_size || '--';
            vehicleInfo.spareTireSpec = clzdData.spare_tire_specification || '--';
            vehicleInfo.spareTirePlacement = clzdData.spare_tire_placement || '--';
          } catch (e) {
            console.error('解析车轮制动数据失败', e);
          }
        }

        // 解析发动机参数
        if (spec.body_fdj) {
          try {
            const fdjData = JSON.parse(spec.body_fdj.replace(/'/g, '"'));
            vehicleInfo.engineModel = fdjData.engine_model || '--';
            vehicleInfo.cylinderVolume = fdjData.cylinder_volume_ml || '--';
            vehicleInfo.capacityL = fdjData.capacity_l || '--';
            vehicleInfo.gasForm = fdjData.gas_form || '--';
            vehicleInfo.engineLayoutForm = fdjData.engine_layout_form || '--';
            vehicleInfo.cylinderArrangement = fdjData.cylinder_arrangement || '--';
            vehicleInfo.cylinderNums = fdjData.cylinder_nums || '--';
            vehicleInfo.valvesPerCylinder = fdjData.valves_per_cylinder_nums || '--';
            vehicleInfo.airSupply = fdjData.air_supply || '--';
            vehicleInfo.engineMaxHorsepower = fdjData.engine_max_horsepower || '--';
            vehicleInfo.engineMaxPower = fdjData.engine_max_power || '--';
            vehicleInfo.maxPowerRevolution = fdjData.max_power_revolution || '--';
            vehicleInfo.engineMaxTorque = fdjData.engine_max_torque || '--';
            vehicleInfo.engineMaxTorqueRevolution = fdjData.engine_max_torque_revolution || '--';
            vehicleInfo.fuelForm = fdjData.fuel_form || '--';
            vehicleInfo.fuelLabel = fdjData.fuel_label || '--';
            vehicleInfo.oilSupply = fdjData.oil_supply || '--';
            vehicleInfo.cylinderHeadMaterial = fdjData.cylinder_head_material || '--';
            vehicleInfo.cylinderMaterial = fdjData.cylinder_material || '--';
            vehicleInfo.environmentalStandards = fdjData.environmental_standards || '--';
          } catch (e) {
            console.error('解析发动机参数数据失败', e);
          }
        }

        // 解析主动安全
        if (spec.body_zdaq) {
          try {
            const zdaqData = JSON.parse(spec.body_zdaq.replace(/'/g, '"'));
            vehicleInfo.absAntiLock = zdaqData.abs_anti_lock || '--';
            vehicleInfo.brakeForce = zdaqData.brake_force || '--';
            vehicleInfo.brakeAssist = zdaqData.brake_assist || '--';
            vehicleInfo.tractionControl = zdaqData.traction_control || '--';
            vehicleInfo.bodyStabilitySystem = zdaqData.body_stability_system || '--';
            vehicleInfo.carWarningSystem = zdaqData.car_warning_system || '--';
            vehicleInfo.activeBrake = zdaqData.active_brake || '--';
            vehicleInfo.lineSupport = zdaqData.line_support || '--';
            vehicleInfo.laneKeepingAssist = zdaqData.lane_keeping_assist || '--';
            vehicleInfo.laneCenter = zdaqData.lane_center || '--';
            vehicleInfo.fatigueDrivingWarning = zdaqData.fatigue_driving_warning || '--';
            vehicleInfo.activeDmsFatigueDetection = zdaqData.active_dms_fatigue_detection || '--';
            vehicleInfo.vitalSignsDetection = zdaqData.vital_signs_detection || '--';
            vehicleInfo.roadTrafficSignRecognition = zdaqData.road_traffic_sign_recognition || '--';
            vehicleInfo.signalRecognition = zdaqData.signal_recognition || '--';
            vehicleInfo.nightVisionSystem = zdaqData.night_vision_system || '--';
          } catch (e) {
            console.error('解析主动安全数据失败', e);
          }
        }

        // 解析被动安全
        if (spec.body_bdaq) {
          try {
            const bdaqData = JSON.parse(spec.body_bdaq.replace(/'/g, '"'));
            vehicleInfo.mainViceAirbag = bdaqData.main_vice_airbag || '--';
            vehicleInfo.frontRearAirbag = bdaqData.front_rear_airbag || '--';
            vehicleInfo.sideAirCurtain = bdaqData.side_air_curtain || '--';
            vehicleInfo.mainViceKneeAirbag = bdaqData.main_vice_knee_airbag || '--';
            vehicleInfo.frontNearCenterAirbag = bdaqData.front_near_center_airbag || '--';
            vehicleInfo.seatBeltPrompted = bdaqData.seat_belt_prompted || '--';
            vehicleInfo.tirePressureSystem = bdaqData.tire_pressure_system || '--';
            vehicleInfo.childSeatInterface = bdaqData.child_seat_interface || '--';
            vehicleInfo.passivePedestrianProtection = bdaqData.passive_pedestrian_protection || '--';
            vehicleInfo.explosionTire = bdaqData.explosion_tire || '--';
          } catch (e) {
            console.error('解析被动安全数据失败', e);
          }
        }

        // 解析辅助操控
        if (spec.body_fzck) {
          try {
            const fzckData = JSON.parse(spec.body_fzck.replace(/'/g, '"'));
            vehicleInfo.parkingRadar = fzckData.parking_radar || '--';
            vehicleInfo.forwardCarDepartureReminder = fzckData.forward_car_departure_reminder || '--';
            vehicleInfo.narrowRoadAssistance = fzckData.narrow_road_assistance || '--';
            vehicleInfo.drivingAssistantCamera = fzckData.driving_assistant_camera || '--';
            vehicleInfo.cruiseSystem = fzckData.cruise_system || '--';
            vehicleInfo.autoRoadChange = fzckData.auto_road_change || '--';
            vehicleInfo.autoRoadOutIn = fzckData.auto_road_out_in || '--';
            vehicleInfo.navigationAssistedDriving = fzckData.navigation_assisted_driving || '--';
            vehicleInfo.automaticDriveLevel = fzckData.automatic_drive_level || '--';
            vehicleInfo.autoParkEntry = fzckData.auto_park_entry || '--';
            vehicleInfo.trackReverse = fzckData.track_reverse || '--';
            vehicleInfo.memoryParking = fzckData.memory_parking || '--';
            vehicleInfo.automatedValetParking = fzckData.automated_valet_parking || '--';
            vehicleInfo.autoPark = fzckData.auto_park || '--';
            vehicleInfo.uphillSupport = fzckData.uphill_support || '--';
            vehicleInfo.steepSlope = fzckData.steep_slope || '--';
            vehicleInfo.engineSasTech = fzckData.engine_sas_tech || '--';
            vehicleInfo.variableSuspension = fzckData.variable_suspension || '--';
            vehicleInfo.airSuspension = fzckData.air_suspension || '--';
            vehicleInfo.electromagneticInductSuspension = fzckData.electromagnetic_induct_suspension || '--';
            vehicleInfo.magicBodyControl = fzckData.magic_body_control || '--';
            vehicleInfo.variableSteerSystem = fzckData.variable_steer_system || '--';
            vehicleInfo.frontSlipMethod = fzckData.front_slip_method || '--';
            vehicleInfo.rearSlipMethod = fzckData.rear_slip_method || '--';
            vehicleInfo.centralDifferentialLock = fzckData.central_differential_lock || '--';
            vehicleInfo.fourWdLow = fzckData.four_wd_low || '--';
            vehicleInfo.overallTurn = fzckData.overall_turn || '--';
            vehicleInfo.driveMode = fzckData.drive_mode || '--';
            vehicleInfo.brakeEnergyRegeneration = fzckData.brake_energy_regeneration || '--';
            vehicleInfo.lowSpeedDrivingWarning = fzckData.low_speed_driving_warning || '--';
          } catch (e) {
            console.error('解析辅助操控数据失败', e);
          }
        }

        // 解析外部配置
        if (spec.body_wbpz) {
          try {
            const wbpzData = JSON.parse(spec.body_wbpz.replace(/'/g, '"'));
            vehicleInfo.skylightType = wbpzData.skylight_type || '--';
            vehicleInfo.lightSensingCanopy = wbpzData.light_sensing_canopy || '--';
            vehicleInfo.roofRacks = wbpzData.roof_racks || '--';
            vehicleInfo.sportsAppearanceKit = wbpzData.sports_appearance_kit || '--';
            vehicleInfo.electricSpoiler = wbpzData.electric_spoiler || '--';
            vehicleInfo.activeClosedInletGrid = wbpzData.active_closed_inlet_grid || '--';
            vehicleInfo.alloyWheel = wbpzData.alloy_wheel || '--';
            vehicleInfo.sideFootrest = wbpzData.side_footrest || '--';
            vehicleInfo.framelessDesignDoor = wbpzData.frameless_design_door || '--';
            vehicleInfo.hiddenDoorHandle = wbpzData.hidden_door_handle || '--';
            vehicleInfo.welcomeFunction = wbpzData.welcome_function || '--';
            vehicleInfo.dragHook = wbpzData.drag_hook || '--';
          } catch (e) {
            console.error('解析外部配置数据失败', e);
          }
        }

        // 解析内部配置
        if (spec.body_nbpz) {
          try {
            const nbpzData = JSON.parse(spec.body_nbpz.replace(/'/g, '"'));
            vehicleInfo.steerWheelMaterial = nbpzData.steer_wheel_material || '--';
            vehicleInfo.steerWheelAdjustment = nbpzData.steer_wheel_adjustment || '--';
            vehicleInfo.elecSteerWheelAdjustment = nbpzData.elec_steer_wheel_adjustment || '--';
            vehicleInfo.steerWheelFunctional = nbpzData.steer_wheel_functional || '--';
            vehicleInfo.gearShiftMode = nbpzData.gear_shift_mode || '--';
            vehicleInfo.drivingComputerDisplay = nbpzData.driving_computer_display_screen || '--';
            vehicleInfo.lcdDashboardType = nbpzData.lcd_dashboard_type || '--';
            vehicleInfo.lcdDashboardSize = nbpzData.lcd_dashboard_size || '--';
          } catch (e) {
            console.error('解析内部配置数据失败', e);
          }
        }

        // 解析舒适/防盗配置
        if (spec.body_ssfd) {
          try {
            const ssfdData = JSON.parse(spec.body_ssfd.replace(/'/g, '"'));
            vehicleInfo.electricDoor = ssfdData.electric_door || '--';
            vehicleInfo.electricBackDoor = ssfdData.electric_back_door || '--';
            vehicleInfo.inductiveBackDoor = ssfdData.inductive_back_door || '--';
            vehicleInfo.electricBackDoorMemory = ssfdData.electric_back_door_memory || '--';
            vehicleInfo.engineAntiTheft = ssfdData.engine_anti_theft || '--';
            vehicleInfo.centralLockingCar = ssfdData.central_locking_car || '--';
            vehicleInfo.remoteKey = ssfdData.remote_key || '--';
            vehicleInfo.keylessEntry = ssfdData.keyless_entry || '--';
            vehicleInfo.keylessStart = ssfdData.keyless_start || '--';
            vehicleInfo.engineRemoteStart = ssfdData.engine_remote_start || '--';
            vehicleInfo.carCall = ssfdData.car_call || '--';
            vehicleInfo.headerDisplaySystem = ssfdData.header_display_system || '--';
            vehicleInfo.hudSize = ssfdData.hud_size || '--';
            vehicleInfo.builtInTachograph = ssfdData.built_in_tachograph || '--';
            vehicleInfo.activeNoiseReduction = ssfdData.active_noise_reduction || '--';
            vehicleInfo.mobileWirelessCharging = ssfdData.mobile_wireless_charging || '--';
            vehicleInfo.wirelessChargingMaxPower = ssfdData.wireless_charging_max_power || '--';
            vehicleInfo.powerOutlet = ssfdData.power_outlet || '--';
            vehicleInfo.baggage12vPowerOutlet = ssfdData.baggage_12v_power_outlet || '--';
          } catch (e) {
            console.error('解析舒适/防盗配置数据失败', e);
          }
        }

        // 解析座椅配置
        if (spec.body_zypz) {
          try {
            const zypzData = JSON.parse(spec.body_zypz.replace(/'/g, '"'));
            vehicleInfo.seatMaterial = zypzData.seat_material || '--';
            vehicleInfo.seatCorkStyle = zypzData.seat_cork_style || '--';
            vehicleInfo.sportStyleSeat = zypzData.sport_style_seat || '--';
            vehicleInfo.layoutSeat = zypzData.layout_seat || '--';
            vehicleInfo.secondIndependentSeat = zypzData.second_independent_seat || '--';
            vehicleInfo.thirdRowSeatCount = zypzData.third_row_seat_count || '--';
            vehicleInfo.queenSeat = zypzData.queen_seat || '--';
            vehicleInfo.seatElectricalAdjustment = zypzData.seat_electrical_adjustment || '--';
            vehicleInfo.mainDriveWholeAdjustment = zypzData.main_drive_whole_adjustment || '--';
            vehicleInfo.mainDrivePartAdjustment = zypzData.main_drive_part_adjustment || '--';
            vehicleInfo.viceDriveWholeAdjustment = zypzData.vice_drive_whole_adjustment || '--';
            vehicleInfo.viceDrivePartAdjustment = zypzData.vice_drive_part_adjustment || '--';
            vehicleInfo.secondSeatControlFunctional = zypzData.second_seat_control_functional || '--';
            vehicleInfo.secondLocalSeat = zypzData.second_local_seat || '--';
            vehicleInfo.frontSeatFunctional = zypzData.front_seat_functional || '--';
            vehicleInfo.rearSeatFunctional = zypzData.rear_seat_functional || '--';
            vehicleInfo.thirdSeatFunctional = zypzData.third_seat_functional || '--';
            vehicleInfo.coPilotRearAdjustableButton = zypzData.co_pilot_rear_adjustable_button || '--';
            vehicleInfo.centreArmrest = zypzData.centre_armrest || '--';
            vehicleInfo.rearCupHolder = zypzData.rear_cup_holder || '--';
            vehicleInfo.hotColdCupHolder = zypzData.hot_cold_cup_holder || '--';
            vehicleInfo.frontSeatsFlattened = zypzData.front_seats_flattened || '--';
            vehicleInfo.secondRowSeatDownRatio = zypzData.second_row_seat_down_ratio || '--';
            vehicleInfo.rearSeatElectricDown = zypzData.rear_seat_electric_down || '--';
            vehicleInfo.secondRowSmallDesktop = zypzData.second_row_small_desktop || '--';
          } catch (e) {
            console.error('解析座椅配置数据失败', e);
          }
        }

        // 解析智能互联
        if (spec.body_znhl) {
          try {
            const znhlData = JSON.parse(spec.body_znhl.replace(/'/g, '"'));
            vehicleInfo.centerScreenSize = znhlData.center_screen_size || '--';
            vehicleInfo.centerConsoleScreenMaterial = znhlData.center_console_screen_material || '--';
            vehicleInfo.centerScreenResolution = znhlData.center_screen_resolution || '--';
            vehicleInfo.centerScreenPpi = znhlData.center_screen_ppi || '--';
            vehicleInfo.viceScreenSize = znhlData.vice_screen_size || '--';
            vehicleInfo.viceScreenResolution = znhlData.vice_screen_resolution || '--';
            vehicleInfo.viceScreenPpi = znhlData.vice_screen_ppi || '--';
            vehicleInfo.vibrateFeedback = znhlData.vibrate_feedback || '--';
            vehicleInfo.gps = znhlData.gps || '--';
            vehicleInfo.arRealityNavigation = znhlData.ar_reality_navigation || '--';
            vehicleInfo.navigationSystem = znhlData.navigation_system || '--';
            vehicleInfo.positionService = znhlData.position_service || '--';
            vehicleInfo.bluetoothAndCarPhone = znhlData.bluetooth_and_car_phone || '--';
            vehicleInfo.mobileSystem = znhlData.mobile_system || '--';
            vehicleInfo.carNetworking = znhlData.car_networking || '--';
            vehicleInfo.dataNetwork = znhlData.data_network || '--';
            vehicleInfo.otaUpgrade = znhlData.ota_upgrade || '--';
            vehicleInfo.facialRecognition = znhlData.facial_recognition || '--';
            vehicleInfo.fingerprintRecognition = znhlData.fingerprint_recognition || '--';
            vehicleInfo.voiceprintRecognition = znhlData.voiceprint_recognition || '--';
            vehicleInfo.emotionRecognition = znhlData.emotion_recognition || '--';
            vehicleInfo.speechRecognitionSystem = znhlData.speech_recognition_system || '--';
            vehicleInfo.voiceWakeUpFree = znhlData.voice_wake_up_free || '--';
            vehicleInfo.voiceWakeUpRecognition = znhlData.voice_wake_up_recognition || '--';
            vehicleInfo.voiceRecognition = znhlData.voice_recognition || '--';
            vehicleInfo.visibleToSay = znhlData.visible_to_say || '--';
            vehicleInfo.voiceWakeUpWord = znhlData.voice_wake_up_word || '--';
            vehicleInfo.gestureControlSystem = znhlData.gesture_control_system || '--';
            vehicleInfo.wifi = znhlData.wifi || '--';
          } catch (e) {
            console.error('解析智能互联数据失败', e);
          }
        }

        // 解析影音娱乐
        if (spec.body_yyyl) {
          try {
            const yyylData = JSON.parse(spec.body_yyyl.replace(/'/g, '"'));
            vehicleInfo.multiFingerScreenControl = yyylData.multi_finger_screen_control || '--';
            vehicleInfo.appStore = yyylData.app_store || '--';
            vehicleInfo.multimediaInterface = yyylData.multimedia_interface || '--';
            vehicleInfo.usbTypecInterfaceCount = yyylData.usb_typec_interface_count || '--';
            vehicleInfo.usbTypecInterfaceMaxChargingPower = yyylData.usb_typec_interface_max_charging_power || '--';
            vehicleInfo.carTv = yyylData.car_tv || '--';
            vehicleInfo.rearLcdScreen = yyylData.rear_lcd_screen || '--';
            vehicleInfo.voiceSimulate = yyylData.voice_simulate || '--';
            vehicleInfo.relaxMode = yyylData.relax_mode || '--';
            vehicleInfo.karaoke = yyylData.karaoke || '--';
            vehicleInfo.soundBrand = yyylData.sound_brand || '--';
            vehicleInfo.speaker = yyylData.speaker || '--';
            vehicleInfo.dolbyPanoramicSound = yyylData.dolby_panoramic_sound || '--';
            vehicleInfo.soundSystemLayout = yyylData.sound_system_layout || '--';
            vehicleInfo.amplifierMaxOutputPowerWatt = yyylData.amplifier_max_output_power_watt || '--';
            vehicleInfo.skySoundChannel = yyylData.sky_sound_channel || '--';
            vehicleInfo.rearTouchControlSystem = yyylData.rear_touch_control_system || '--';
          } catch (e) {
            console.error('解析影音娱乐数据失败', e);
          }
        }

        // 解析灯光配置
        if (spec.body_dgpz) {
          try {
            const dgpzData = JSON.parse(spec.body_dgpz.replace(/'/g, '"'));
            vehicleInfo.lowHeadlampType = dgpzData.low_headlamp_type || '--';
            vehicleInfo.highHeadlampType = dgpzData.high_headlamp_type || '--';
            vehicleInfo.daytimeLight = dgpzData.daytime_light || '--';
            vehicleInfo.adaptiveLight = dgpzData.adaptive_light || '--';
            vehicleInfo.autoHeadlamp = dgpzData.auto_headlamp || '--';
            vehicleInfo.steerAssistLight = dgpzData.steer_assist_light || '--';
            vehicleInfo.frontFogLight = dgpzData.front_fog_light || '--';
            vehicleInfo.headlampFollowUp = dgpzData.headlamp_follow_up || '--';
            vehicleInfo.headlightHeightAdjustment = dgpzData.headlight_height_adjustment || '--';
            vehicleInfo.headlightCleanFunction = dgpzData.headlight_clean_function || '--';
            vehicleInfo.interiorLight = dgpzData.interior_light || '--';
            vehicleInfo.activeAmbientLight = dgpzData.active_ambient_light || '--';
            vehicleInfo.lightSpecialFunction = dgpzData.light_special_function || '--';
            vehicleInfo.lightProjectionTechnology = dgpzData.light_projection_technology || '--';
            vehicleInfo.headlampDelayOff = dgpzData.headlamp_delay_off || '--';
            vehicleInfo.headlampRainFogMode = dgpzData.headlamp_rain_fog_mode || '--';
          } catch (e) {
            console.error('解析灯光配置数据失败', e);
          }
        }

        // 解析玻璃/后视镜配置
        if (spec.body_blhsj) {
          try {
            const blhsjData = JSON.parse(spec.body_blhsj.replace(/'/g, '"'));
            vehicleInfo.electricWindow = blhsjData.electric_window || '--';
            vehicleInfo.windowOneKeyLift = blhsjData.window_one_key_lift || '--';
            vehicleInfo.windowAntiClipFunction = blhsjData.window_anti_clip_function || '--';
            vehicleInfo.exterMirrorFunctional = blhsjData.exter_mirror_functional || '--';
            vehicleInfo.insideMirrorFunctional = blhsjData.inside_mirror_functional || '--';
            vehicleInfo.carWindowSunshadeMirror = blhsjData.car_window_sunshade_mirror || '--';
            vehicleInfo.backsidePrivacyGlass = blhsjData.backside_privacy_glass || '--';
            vehicleInfo.windowSunshade = blhsjData.window_sunshade || '--';
            vehicleInfo.rainInductionWiper = blhsjData.rain_induction_wiper || '--';
            vehicleInfo.rearWiper = blhsjData.rear_wiper || '--';
            vehicleInfo.rearWindowOpenMethod = blhsjData.rear_window_open_method || '--';
            vehicleInfo.multilayerSoundproofGlass = blhsjData.multilayer_soundproof_glass || '--';
            vehicleInfo.frontWindshieldElectricHeating = blhsjData.front_windshield_electric_heating || '--';
            vehicleInfo.heatedNozzle = blhsjData.heated_nozzle || '--';
          } catch (e) {
            console.error('解析玻璃/后视镜配置数据失败', e);
          }
        }

        // 解析空调/冰箱配置
        if (spec.body_ktbx) {
          try {
            const ktbxData = JSON.parse(spec.body_ktbx.replace(/'/g, '"'));
            vehicleInfo.airControlModel = ktbxData.air_control_model || '--';
            vehicleInfo.rearIndependentAirConditioning = ktbxData.rear_independent_air_conditioning || '--';
            vehicleInfo.rearExhaust = ktbxData.rear_exhaust || '--';
            vehicleInfo.hiddenAirVent = ktbxData.hidden_air_vent || '--';
            vehicleInfo.temperaturePartitionControl = ktbxData.temperature_partition_control || '--';
            vehicleInfo.carPurifier = ktbxData.car_purifier || '--';
            vehicleInfo.pm25FiltratingEquipment = ktbxData.pm25_filtrating_equipment || '--';
            vehicleInfo.hepaFilterDevice = ktbxData.hepa_filter_device || '--';
            vehicleInfo.negativeIonGenerator = ktbxData.negative_ion_generator || '--';
            vehicleInfo.carFragranceDevice = ktbxData.car_fragrance_device || '--';
            vehicleInfo.aqsAirQualityManagementSystem = ktbxData.aqs_air_quality_management_system || '--';
            vehicleInfo.carRefrigerator = ktbxData.car_refrigerator || '--';
            vehicleInfo.carFridgeFeature = ktbxData.car_fridge_feature || '--';
          } catch (e) {
            console.error('解析空调/冰箱配置数据失败', e);
          }
        }

        // 解析智能化配置
        if (spec.body_znhpz) {
          try {
            const znhpzData = JSON.parse(spec.body_znhpz.replace(/'/g, '"'));
            vehicleInfo.drivingAssistOpSystem = znhpzData.driving_assist_op_system || '--';
            vehicleInfo.drivingAssistChip = znhpzData.driving_assist_chip || '--';
            vehicleInfo.drivingAssistChipComputing = znhpzData.driving_assist_chip_computing || '--';
            vehicleInfo.carIntelligentChip = znhpzData.car_intelligent_chip || '--';
            vehicleInfo.carSystemMemory = znhpzData.car_system_memory || '--';
            vehicleInfo.carSystemStorage = znhpzData.car_system_storage || '--';
            vehicleInfo.mobileRemoteControl = znhpzData.mobile_remote_control || '--';
            vehicleInfo.heatPumpManagementSystem = znhpzData.heat_pump_management_system || '--';
            vehicleInfo.cameraCount = znhpzData.camera_count || '--';
            vehicleInfo.outerCameraPixel = znhpzData.outer_camera_pixel || '--';
            vehicleInfo.incarCameraCount = znhpzData.incar_camera_count || '--';
            vehicleInfo.innerCameraPixel = znhpzData.inner_camera_pixel || '--';
            vehicleInfo.ultrasonicRadar = znhpzData.ultrasonic_radar || '--';
            vehicleInfo.millimeterWaveRadar = znhpzData.millimeter_wave_radar || '--';
            vehicleInfo.laserRadar = znhpzData.laser_radar || '--';
            vehicleInfo.laserRadarBrand = znhpzData.laser_radar_brand || '--';
            vehicleInfo.laserRadarRayNums = znhpzData.laser_radar_ray_nums || '--';
            vehicleInfo.laserRadarPointCloudNums = znhpzData.laser_radar_point_cloud_nums || '--';
            vehicleInfo.highPrecisionPositionSystem = znhpzData.high_precision_position_system || '--';
            vehicleInfo.highPrecisionMap = znhpzData.high_precision_map || '--';
            vehicleInfo.sentinelMode = znhpzData.sentinel_mode || '--';
            vehicleInfo.v2xCommunication = znhpzData.v2x_communication || '--';
          } catch (e) {
            console.error('解析智能化配置数据失败', e);
          }
        }

        // 解析选装包
        if (spec.body_xzb) {
          try {
            const xzbData = JSON.parse(spec.body_xzb.replace(/'/g, '"'));
            vehicleInfo.userCustomPkg = xzbData.user_custom_pkg || '--';
          } catch (e) {
            console.error('解析选装包数据失败', e);
          }
        }
      }

      const shareData = {
        title: result.ui_vehicle_name || '暂无标题',
        path: '/pages/vehicle/ndetail?id=' + id,
        imageUrl: vehicleInfo.images[0] || "",
        isDetailPage: true,
      }
      this.setData({ shareData })

      // 更新页面数据
      this.setData({
        vehicleInfo
      });
    }
    wx.hideLoading();
  },

  // 添加轮播图变化事件处理函数
  swiperChange: function (e) {
    this.setData({
      current: e.detail.current
    });
  },

  // 返回按钮点击事件
  goBack: function () {
    // 检查页面栈，如果只有一个页面就跳转到首页，否则返回上一页
    const pages = getCurrentPages();
    if (pages.length === 1) {
      wx.reLaunch({
        url: '/pages/index/index'
      });
    } else {
      wx.navigateBack();
    }
  },

  // 添加按钮事件处理函数
  toggleFavorite: function () {
    this.setData({
      isFavorite: !this.data.isFavorite
    });

    // 收藏/取消收藏逻辑
    if (this.data.isFavorite) {
      wx.showToast({
        title: '已收藏',
        icon: 'success'
      });
    } else {
      wx.showToast({
        title: '已取消收藏',
        icon: 'none'
      });
    }
  },

  shareVehicle: function () {
    // 只能提示用户点击右上角进行转发
    wx.showToast({
      title: '点击右上角"..."转发',
      icon: 'none',
      duration: 2000
    });
  },

  contactSeller: function () {
    // 联系商家
    wx.makePhoneCall({
      phoneNumber: this.data.vehicleInfo.contactPhone || '14779202443',
      success: function () {
        console.log('拨打电话成功');
      },
      fail: function () {
        // console.log('拨打电话失败');
        wx.showToast({
          title: '拨打电话失败',
          icon: 'none'
        });
      }
    });
  },

  downloadManual: function () {
    // 下载车辆手册
    wx.showActionSheet({
      itemList: ['下载中文版手册', '下载英语版手册', '下载阿拉伯语版手册'],
      success: (res) => {
        let language = '';
        switch (res.tapIndex) {
          case 0:
            language = 'zh';
            break;
          case 1:
            language = 'en';
            break;
          case 2:
            language = 'ar';
            break;
        }

        if (language) {
          wx.showLoading({
            title: '准备下载...',
          });

          // 这里可以根据不同语言版本设置不同的下载链接
          const downloadUrl = `https://example.com/manuals/${this.data.vehicleInfo.carId || 'default'}_${language}.pdf`;

          wx.downloadFile({
            url: downloadUrl,
            success: (res) => {
              wx.hideLoading();
              if (res.statusCode === 200) {
                wx.openDocument({
                  filePath: res.tempFilePath,
                  showMenu: true,
                  success: () => {
                    console.log('打开文档成功');
                  },
                  fail: (error) => {
                    console.error('打开文档失败', error);
                    wx.showToast({
                      title: '打开文档失败',
                      icon: 'none'
                    });
                  }
                });
              } else {
                wx.showToast({
                  title: '下载失败，请稍后重试',
                  icon: 'none'
                });
              }
            },
            fail: (error) => {
              wx.hideLoading();
              console.error('下载失败', error);
              wx.showToast({
                title: '下载失败，请检查网络',
                icon: 'none'
              });
            }
          });
        }
      },
      fail: (res) => {
        console.log(res.errMsg);
      }
    });
  },

  // 显示详细参数配置
  showDetailedParams: function () {
    // 导航到参数配置页面，并传递车辆id和type值
    wx.navigateTo({
      url: `/pages/vehicle/params?id=${this.data.id}&type=${this.data.type || 'new'}`
    });
  },

  // 在 Page 对象中添加 onShareAppMessage 方法
  onShareAppMessage: function () {
    // 使用之前准备好的 shareData
    return this.data.shareData || {
      title: this.data.vehicleInfo.title || '车辆详情',
      path: '/pages/vehicle/ndetail?id=' + this.data.id,
      imageUrl: this.data.vehicleInfo.images && this.data.vehicleInfo.images.length > 0 ?
        this.data.vehicleInfo.images[0] : ''
    };
  },

  // 查看商家详情
  viewMerchantDetail() {
    const { merchantInfo } = this.data;
    if (!merchantInfo || !merchantInfo.app_id) {
      wx.showToast({
        title: '商家信息不完整',
        icon: 'none'
      });
      return;
    }

    // 跳转到商家详情页面，可以根据实际页面路径调整
    wx.navigateTo({
      url: `/pages/information/index?id=${merchantInfo.app_id}`
    });
  },

  // 获取商家信息方法
  async getMerchantInfo(app_id) {
    try {
      if (!app_id) {
        console.warn('未获取到app_id，无法获取商家信息');
        return;
      }

      const result = await api.user.getCompanyInfo({
        app_id: app_id
      });

      if (result) {
        // 将接口返回的数据适配到页面使用的字段
        const merchantInfo = {
          app_id: result.app_id,
          company_name: result.company_name,
          address: result.address,
          car_count: result.vehicle_count || 0,
          logo: result.logo || '',
          score: result.score || '4.5',
          is_auth: result.is_auth || false,
          phone: result.mobile || ''
        };

        this.setData({
          merchantInfo
        });
      }
    } catch (error) {
      console.error('获取商家信息失败:', error);
    }
  },
}); 