// 导入 API 模块
import api from '../../utils/api';
import util from '../../utils/util';

Page({
    data: {
        // 品牌、车系、车型数据
        brandList: [],
        seriesList: [],
        modelList: [],
        
        // 选中的数据
        selectedBrand: '',
        selectedSeries: '',
        selectedModel: '',
        selectedLocation: '',
        selectedRegisterTime: '',
        selectedMileage: '',
        
        // 表单显示数据
        brandName: '请选择品牌',
        seriesName: '请选择车系',
        modelName: '请选择车型',
        locationName: '请选择车辆所在地',
        registerTimeName: '请选择上牌时间',
        mileageName: '请选择行驶里程',
        
        // 协议同意状态
        isAgreed: false,
        
        // 年月选择器
        showTimePicker: false,
        selectedYear: '2023',
        selectedMonth: '1',
        years: ['2025', '2024', '2023', '2022', '2021', '2020', '2019', '2018', '2017', '2016', '2015', '2014', '2013', '2012', '2011', '2010'],
        months: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']
    },
    onLoad() {
        // 页面加载时获取品牌列表
        this.loadBrandList();
    },
    // 获取品牌列表
    async loadBrandList() {
        try {
            // 这里应该调用实际的API
            // const result = await api.vehicle.getBrandList();
            // if (result && result.data) {
            //   this.setData({ brandList: result.data });
            // }
            
            // 模拟数据
            this.setData({
                brandList: [
                    { id: 1, name: '奔驰' },
                    { id: 2, name: '宝马' },
                    { id: 3, name: '奥迪' },
                    { id: 4, name: '特斯拉' },
                    { id: 5, name: '丰田' }
                ]
            });
        } catch (error) {
            console.error('获取品牌列表失败:', error);
            wx.showToast({
                title: '获取品牌列表失败',
                icon: 'none'
            });
        }
    },
    // 根据品牌ID获取车系列表
    async loadSeriesList(brandId) {
        try {
            // 这里应该调用实际的API
            // const result = await api.vehicle.getSeriesList({ brandId });
            // if (result && result.data) {
            //   this.setData({ seriesList: result.data });
            // }
            
            // 模拟数据
            this.setData({
                seriesList: [
                    { id: 1, name: brandId + '系列1' },
                    { id: 2, name: brandId + '系列2' },
                    { id: 3, name: brandId + '系列3' }
                ]
            });
        } catch (error) {
            console.error('获取车系列表失败:', error);
            wx.showToast({
                title: '获取车系列表失败',
                icon: 'none'
            });
        }
    },
    // 根据车系ID获取车型列表
    async loadModelList(seriesId) {
        try {
            // 这里应该调用实际的API
            // const result = await api.vehicle.getModelList({ seriesId });
            // if (result && result.data) {
            //   this.setData({ modelList: result.data });
            // }
            
            // 模拟数据
            this.setData({
                modelList: [
                    { id: 1, name: seriesId + '车型1' },
                    { id: 2, name: seriesId + '车型2' },
                    { id: 3, name: seriesId + '车型3' }
                ]
            });
        } catch (error) {
            console.error('获取车型列表失败:', error);
            wx.showToast({
                title: '获取车型列表失败',
                icon: 'none'
            });
        }
    },
    // 选择品牌
    selectBrand() {
        const that = this;
        wx.showActionSheet({
            itemList: this.data.brandList.map(item => item.name),
            success(res) {
                const index = res.tapIndex;
                const selectedBrand = that.data.brandList[index];
                that.setData({
                    selectedBrand: selectedBrand.id,
                    brandName: selectedBrand.name,
                    // 重置车系和车型
                    selectedSeries: '',
                    seriesName: '请选择车系',
                    selectedModel: '',
                    modelName: '请选择车型'
                });
                // 加载对应的车系列表
                that.loadSeriesList(selectedBrand.id);
            }
        });
    },
    // 选择车系
    selectSeries() {
        // 如果没有选择品牌，提示先选择品牌
        if (!this.data.selectedBrand) {
            wx.showToast({
                title: '请先选择品牌',
                icon: 'none'
            });
            return;
        }
        
        const that = this;
        wx.showActionSheet({
            itemList: this.data.seriesList.map(item => item.name),
            success(res) {
                const index = res.tapIndex;
                const selectedSeries = that.data.seriesList[index];
                that.setData({
                    selectedSeries: selectedSeries.id,
                    seriesName: selectedSeries.name,
                    // 重置车型
                    selectedModel: '',
                    modelName: '请选择车型'
                });
                // 加载对应的车型列表
                that.loadModelList(selectedSeries.id);
            }
        });
    },
    // 选择车型
    selectModel() {
        // 如果没有选择车系，提示先选择车系
        if (!this.data.selectedSeries) {
            wx.showToast({
                title: '请先选择车系',
                icon: 'none'
            });
            return;
        }
        
        const that = this;
        wx.showActionSheet({
            itemList: this.data.modelList.map(item => item.name),
            success(res) {
                const index = res.tapIndex;
                const selectedModel = that.data.modelList[index];
                that.setData({
                    selectedModel: selectedModel.id,
                    modelName: selectedModel.name
                });
            }
        });
    },
    // 选择车辆所在地
    selectLocation() {
        // 这里应该调用地图选择组件
        const that = this;
        const locations = ['北京', '上海', '广州', '深圳', '杭州', '成都'];
        
        wx.showActionSheet({
            itemList: locations,
            success(res) {
                const locationName = locations[res.tapIndex];
                that.setData({
                    selectedLocation: locationName,
                    locationName: locationName
                });
            }
        });
    },
    // 选择上牌时间
    selectRegisterTime() {
        this.setData({
            showTimePicker: true
        });
    },
    // 关闭时间选择器
    closeTimePicker() {
        this.setData({
            showTimePicker: false
        });
    },
    // 确认选择的年月
    confirmTimePicker() {
        const { selectedYear, selectedMonth } = this.data;
        const registerTime = `${selectedYear}年${selectedMonth}月`;
        
        this.setData({
            selectedRegisterTime: registerTime,
            registerTimeName: registerTime,
            showTimePicker: false
        });
    },
    // 选择年份
    onYearChange(e) {
        this.setData({
            selectedYear: this.data.years[e.detail.value]
        });
    },
    // 选择月份
    onMonthChange(e) {
        this.setData({
            selectedMonth: this.data.months[e.detail.value]
        });
    },
    // 选择行驶里程
    selectMileage() {
        const that = this;
        const mileages = ['0-1万', '1-3万', '3-5万', '5-8万', '8-10万', '10万以上'];
        
        wx.showActionSheet({
            itemList: mileages,
            success(res) {
                const mileage = mileages[res.tapIndex];
                that.setData({
                    selectedMileage: mileage,
                    mileageName: mileage
                });
            }
        });
    },
    // 切换协议同意状态
    toggleAgreement() {
        this.setData({
            isAgreed: !this.data.isAgreed
        });
    },
    // 提交表单
    submitForm() {
        // 验证表单数据
        if (!this.data.selectedBrand) {
            wx.showToast({
                title: '请选择品牌',
                icon: 'none'
            });
            return;
        }
        
        if (!this.data.selectedSeries) {
            wx.showToast({
                title: '请选择车系',
                icon: 'none'
            });
            return;
        }
        
        if (!this.data.selectedModel) {
            wx.showToast({
                title: '请选择车型',
                icon: 'none'
            });
            return;
        }
        
        if (!this.data.selectedLocation) {
            wx.showToast({
                title: '请选择车辆所在地',
                icon: 'none'
            });
            return;
        }
        
        if (!this.data.selectedRegisterTime) {
            wx.showToast({
                title: '请选择上牌时间',
                icon: 'none'
            });
            return;
        }
        
        if (!this.data.selectedMileage) {
            wx.showToast({
                title: '请选择行驶里程',
                icon: 'none'
            });
            return;
        }
        
        if (!this.data.isAgreed) {
            wx.showToast({
                title: '请同意隐私政策和二手车买卖咨询',
                icon: 'none'
            });
            return;
        }
        
        // 提交数据
        const formData = {
            brand_id: this.data.selectedBrand,
            brand_name: this.data.brandName,
            series_id: this.data.selectedSeries,
            series_name: this.data.seriesName,
            model_id: this.data.selectedModel,
            model_name: this.data.modelName,
            location: this.data.selectedLocation,
            register_time: this.data.selectedRegisterTime,
            mileage: this.data.selectedMileage
        };
        
        // 模拟API提交
        wx.showLoading({
            title: '正在评估',
        });
        
        setTimeout(() => {
            wx.hideLoading();
            
            // 模拟返回结果页面
            wx.navigateTo({
                url: '/pages/evaluate/result'
            });
        }, 1500);
    },
    // 跳转到隐私政策页面
    goToPrivacyPolicy() {
        wx.navigateTo({
            url: '/pages/agreement/privacy'
        });
    },
    // 跳转到二手车买卖咨询页面
    goToConsultation() {
        wx.navigateTo({
            url: '/pages/agreement/service'
        });
    },
    // 返回上一页
    navigateBack() {
        wx.navigateBack();
    },
    onShow() {
        if (typeof this.getTabBar === 'function' && this.getTabBar()) {
            this.getTabBar().setData({
                selected: 2  // 评估是第三个 tab
            })
        }
    }
}) 