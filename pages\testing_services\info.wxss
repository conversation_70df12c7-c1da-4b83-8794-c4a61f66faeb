/* 引入登录弹窗样式 */
@import "../../templates/loginPopup/loginPopup.wxss";

/* 全局溢出控制 - 防止横向滚动 */
page {
  overflow-x: hidden;
  width: 100%;
  box-sizing: border-box;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
}

/* 骨架屏样式 */
.skeleton-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: #fff;
  z-index: 100;
  padding: 0;
  overflow: auto;
}

/* 骨架自定义导航栏 */
.skeleton-custom-nav {
  width: 100%;
  background: #f7f7f7;
  position: relative;
}

.skeleton-status-bar {
  width: 100%;
  background-color: #f7f7f7;
}

.skeleton-nav-title {
  height: 44px;
  width: 5rpx;
  background-color: #eeeeee;
  margin: 0 auto;
  border-radius: 8rpx;
}

/* 骨架顶部容器 */
.skeleton-top-container {
  width: 100%;
  background-color: #f7f7f7;
}

.top-container{
  height: 0rpx !important;
}

/* 骨架搜索栏 */
.skeleton-search-bar {
  padding: 20rpx 30rpx;
  width: 100%;
  box-sizing: border-box;
}

.skeleton-search-input {
  height: 70rpx;
  width: 100%;
  background-color: #eeeeee;
  border-radius: 35rpx;
}

/* 骨架筛选菜单 */
.skeleton-filter-menu {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #f7f7f7;
  border-top: 1rpx solid #eeeeee;
  border-bottom: 1rpx solid #eeeeee;
}

.skeleton-filter-item {
  width: 120rpx;
  height: 50rpx;
  background-color: #eeeeee;
  border-radius: 8rpx;
}

.skeleton-car-list {
  padding: 20rpx 30rpx;
  margin-top: 20rpx;
}

.skeleton-car-item {
  display: flex;
  margin-bottom: 30rpx;
  height: 200rpx;
  background-color: #f7f7f7;
  border-radius: 8rpx;
  padding: 20rpx;
}

.skeleton-car-image {
  width: 260rpx;
  height: 160rpx;
  background-color: #eeeeee;
  border-radius: 8rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.skeleton-car-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.skeleton-car-title {
  height: 60rpx;
  background-color: #eeeeee;
  border-radius: 6rpx;
  margin-bottom: 15rpx;
}

.skeleton-car-params {
  height: 30rpx;
  background-color: #eeeeee;
  border-radius: 6rpx;
  margin-bottom: 15rpx;
  width: 80%;
}

.skeleton-car-price {
  height: 40rpx;
  background-color: #eeeeee;
  border-radius: 6rpx;
  width: 60%;
}

/* 闪光动画 */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

.skeleton-nav-title,
.skeleton-search-input,
.skeleton-filter-item,
.skeleton-car-image,
.skeleton-car-title,
.skeleton-car-params,
.skeleton-car-price {
  background-image: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.container {
  padding: 0;
  background-color: transparent;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
  /* 防止横向滚动 */
  position: relative;
  /* 添加相对定位 */
}

/* 新的搜索栏样式 */
.search-bar {
  padding: 20rpx 30rpx;
  background-color: #fff;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.search-input-wrapper {
  flex: 1;
  display: flex;
  height: 70rpx;
  box-sizing: border-box;
  border-radius: 35rpx;
  overflow: hidden;
  background-color: #f7f7f7;
  align-items: center;
  position: relative;
  margin-right: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid #eeeeee;
  transition: all 0.2s ease-in-out;
}

.search-icon {
  margin-left: 20rpx;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-input {
  flex: 1;
  height: 100%;
  padding: 0 110rpx 0 15rpx;
  font-size: 28rpx;
  background-color: transparent;
  border: none;
  color: #333;
}

.search-btn {
  height: 56rpx;
  line-height: 56rpx;
  background: linear-gradient(to right, #1C82F5, #27AFF5);
  color: #fff;
  font-size: 28rpx;
  text-align: center;
  padding: 0 30rpx;
  border-radius: 16rpx;
  position: absolute;
  right: 7rpx;
  top: 50%;
  transform: translateY(-50%);
  box-shadow: 0 2rpx 8rpx rgba(56, 136, 255, 0.3);
  transition: all 0.2s ease-in-out;
}

.search-btn:active {
  background: linear-gradient(to right, #1973dd, #24a0e5);
  box-shadow: 0 1rpx 3rpx rgba(56, 136, 255, 0.2);
  transform: translateY(-50%) scale(0.98);
}

.filter-tabs {
  width: 100%;
}

/* 自定义 van-tabs 样式 */
.filter-tabs .van-tabs__nav {
  display: flex !important;
}

.filter-tabs .van-tab {
  -webkit-box-flex: 1 !important;
  flex: 1 !important;
  min-width: 0 !important;
}

.filter-tabs .van-tabs__line {
  width: 40rpx !important;
  background-color: #ff0000 !important;
}

.filter-nav {
  width: 100%;
}

.filter-tab {
  flex: 1 !important;
  min-width: 100% !important;
  font-size: 28rpx;
}

.filter-tab-active {
  font-weight: bold;
  color: #ff0000 !important;
}

/* 车辆列表 */
.car-list {
  width: 100%;
  padding: 0;
  background-color: transparent;
  padding-bottom: 0rpx;
  /* 保留原有的底部内边距，考虑 TabBar 的高度 */
  margin-top: 180rpx;
}

.info-out{
  padding-left: 12rpx;
  padding-right: 11rpx;
}

.info-inside{
  background-color: white;
  border-radius: 5px 5px 5px 5px;
}

.info-inside-row{
  display: flex;
  flex-direction: row;
  align-items: center;
  font-size: 12px;
  line-height: 30px;
  border-bottom: 1px solid #eee;
  padding-top: 10rpx;
  padding-bottom: 10rpx;
}

.info-inside-row-title{
  width: 30%;
  margin-left: 20rpx;
  color: #808384;
}

.custom-nav {
  width: 100%;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  position: relative;
}

.status-bar {
  width: 100%;
}

.nav-title {
  height: 44px;
  line-height: 44px;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  position: relative;
}

.car-item {
  display: flex;
  flex-direction: column;
  padding: 20rpx 30rpx;
  border-bottom: 2rpx solid #f5f5f5;
  background-color: #fff;
  margin: 0 0 20rpx 0;
  align-items: flex-start;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
}


.company-card{
  width: 100%;
}

.company-title{
  width: 100%;
  padding-top: 20rpx;
  padding-bottom: 20rpx;
  color: #3D3D3D;
}

.company-title {
  display: flex;
  align-items: center;
  width: 100%;
  padding-top: 20rpx;
  padding-bottom: 20rpx;
  color: #3D3D3D;
}
.company-title-name {
  width: 70%;
  flex: 1;
  min-width: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.arrow {
  margin-left: 20%;
}

/* .company-info .info-row {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 10rpx;
} */
.company-info .info-row {
  display:flex;
  justify-self:flex-start;
  margin-bottom: 10rpx;
  text-align: center;
}

.info-lable{
  width: 33.33%;
  font-size: 26rpx;
  color: #6B7280;
}

.company-info .info-row-content{
  display: flex;
  justify-self:flex-start;
  margin-bottom: 10rpx;
  margin-top: 30rpx;
}

.info-row-area{
  margin-top: 20rpx;
}

.info-row-area-title{
  font-size: 26rpx;
  color: #6B7280;
}

.info-row-area-conent{
  font-size: 26rpx;
  color: #3D3D3D;
  margin-left: 20rpx;
}

.info-lable-content{
  width: 33.33%;
  font-size: 26rpx;
  color: #3D3D3D;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.car-image-container {
  width: 260rpx;
  /* 再增加宽度，从240rpx增加到260rpx，使其更加长方形 */
  height: 200rpx;
  /* 略微减少高度，从180rpx减少到165rpx，进一步强调长方形效果 */
  margin-right: 20rpx;
  /* 减少右侧边距 */
  border-radius: 8rpx;
  overflow: hidden;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  align-self: flex-start;
  /* 确保图片在顶部对齐 */
  margin-top: 10rpx;
  /* 增加顶部外边距 */
  position: relative;
  /* 添加相对定位 */
}

.car-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6rpx;
  /* 添加圆角 */
}

.car-content-wrapper {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 2rpx;
  margin-top: 1rpx;
  /* 与标题的间距 */
}

.car-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  height: 210rpx;
  /* 调整高度，与car-main-content保持一致 */
  box-sizing: border-box;
  position: relative;
  padding: 0;
  margin-left: 10rpx;
  /* 增加左侧边距 */
}

.car-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
  max-height: 84rpx;
  width: 100%;
  margin: 0;
  margin-bottom: 1rpx;
  /* 添加底部外边距 */
  padding-top: 4rpx;
  flex-shrink: 0;
}

/* 车辆参数信息样式 */
.car-params {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
  margin-bottom: 35rpx;
  /* 增加下边距，与下面内容保持距离 */
  flex-wrap: nowrap;
  width: 100%;
  position: relative;
  /* 添加相对定位 */
  z-index: 1;
  /* 确保层级较高 */
}

.param-item {
  white-space: nowrap;
}

.separator {
  margin: 0 8rpx;
  color: #ddd;
}

.car-attrs {
  font-size: 24rpx;
  color: #666;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  gap: 12rpx;
  justify-content: space-between;
  width: 100%;
  margin: 0;
  padding-top: 6rpx;
  /* 减少顶部内边距，从36rpx调整为6rpx */
}

/* 调整价格区域样式 */
.price-section {
  width: 100%;
  position: static;
  padding: 0 4rpx 4rpx 0;
  margin: 4rpx 0 0 0;
  /* 增加顶部外边距 */
}

.price-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

/* 创建左侧价格容器 */
.price-info {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: space-between;
}

.sale-price {
  font-size: 32rpx;
  color: #2B68F6;
  font-weight: 500;
  white-space: nowrap;
  text-align: right;
  padding-left: 10rpx;
}

.new-price {
  font-size: 20rpx;
  color: #999;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 60%;
  text-decoration: line-through;
  /* 添加横线效果 */
}

.car-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  width: 100%;
  margin-top: 8rpx;
  /* 增加与上方内容的间距 */
  padding-top: 4rpx;
}

.tag {
  font-size: 20rpx;
  padding: 2rpx 10rpx;
  border-radius: 4rpx;
  line-height: 1.4;
  display: inline-block;
}

.tag-blue {
  color: #3B82F6;
  background-color: #EEF4FF;
}

.tag-red {
  color: #FF4800;
  background-color: #F9E5DB;
}


/* 加载状态样式 */
.loading,
.no-more,
.empty {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 60vh; /* 或根据实际页面高度调整 */
  width: 100%;
  text-align: center;
  font-size: 24rpx;
  color: #999;
}

/* 底部区域样式 */
.car-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 22rpx;
  color: #999;
  margin-top: auto;
  /* 将底部信息推到底部 */
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  /* 防止内容溢出 */
}

.car-location {
  max-width: 50%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.car-time {
  font-size: 22rpx;
  color: #999;
  padding-left: 10rpx;
  text-align: right;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 40%;
  /* 限制最大宽度 */
}

.car-actions {
  padding: 20rpx;
  border-top: 1rpx solid #eee;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

.action-buttons {
  display: flex;
  margin-left: auto;
}

.action-button {
  margin-left: 10rpx !important;
  border: none !important;
}

.custom-filter-bar {
  width: 100%;
  background-color: #fff;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
  box-sizing: border-box;
}

.custom-filter-item {
  width: 100%;
  text-align: center;
  font-size: 28rpx;
  color: #333;
  position: relative;
  padding-bottom: 10rpx;
}

.custom-filter-item.active {
  color: #ff0000;
  font-weight: bold;
}

.custom-filter-line {
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #ff0000;
}

.custom-tabs {
  width: 100%;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

.custom-tab {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #333;
  padding: 20rpx 0;
  position: relative;
}

.custom-tab.active {
  color: #ff0000;
  font-weight: bold;
}

.custom-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #ff0000;
}

.tab-content {
  width: 100%;
  background-color: #fff;
}

.van-search__content {
  padding-left: 10rpx !important;
  padding-right: 10rpx !important;
}

.van-field__input {
  min-height: 28px !important;
}

/* 确保下拉菜单横向铺满 */
.custom-tabs {
  width: 100%;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

.filter-dropdown-menu {
  width: 100% !important;
}

/* 强制覆盖 Vant 组件样式 */
.van-dropdown-menu {
  width: 100% !important;
  display: flex !important;
  height: 48px !important;
  line-height: 48px !important;
  transition: all 0.3s ease-in-out !important;
}

.van-dropdown-menu__item {
  flex: 1 !important;
  width: 25% !important;
  /* 强制宽度为 25%，确保四个菜单项平均分配 */
  text-align: center !important;
}

/* 调整菜单标题样式 */
.van-dropdown-menu__title {
  padding: 0 !important;
  max-width: none !important;
  font-size: 28rpx !important;
  display: inline-block !important;
  position: relative !important;
}

/* 修复下拉箭头位置 */
.van-dropdown-menu__title::after {
  position: relative !important;
  display: inline-block !important;
  margin-left: 4px !important;
  border-color: #999 transparent transparent !important;
  border-style: solid !important;
  border-width: 4px 4px 0 !important;
  content: "" !important;
  transition: transform 0.3s !important;
  transform: translateY(-50%) !important;
  transform-origin: center !important;
}

/* 修复激活状态下的箭头 */
.van-dropdown-menu__item--active .van-dropdown-menu__title::after {
  transform: rotate(180deg) !important;
  margin-top: -3px !important;
}

/* 自定义下拉菜单标题样式 */
.custom-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 10px;
}

/* 下拉箭头样式 */
.dropdown-arrow {
  flex-shrink: 0;
  transition: transform 0.3s ease-in-out !important;
}

/* 隐藏原有的下拉箭头 */
.van-dropdown-menu__title::after {
  display: none !important;
}

/* 激活状态下箭头旋转 */
.van-dropdown-item--active .dropdown-arrow {
  transform: rotate(180deg);
  transition: transform 0.3s ease-in-out !important;
}

/* 使用文本作为下拉箭头 */
.dropdown-arrow-text {
  margin-left: 8px;
  font-size: 12px;
  color: #333;
}

/* 激活状态下箭头旋转 */
.van-dropdown-item--active .dropdown-arrow-text {
  transform: rotate(180deg);
  display: inline-block;
  transition: transform 0.3s ease-in-out !important;
}

/* 使用 CSS 绘制三角形 */
.css-arrow {
  width: 0;
  height: 0;
  margin-left: 8px;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #333;
  transition: transform 0.3s ease-in-out !important;
}

/* 激活状态下箭头旋转 */
.van-dropdown-item--active .css-arrow {
  transform: rotate(180deg);
}

/* 页面遮罩层 */
.page-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  z-index: 900;
  opacity: 0;
  transition: opacity 0.3s ease-in-out !important;
  pointer-events: none;
}

.page-mask.show {
  opacity: 1;
  pointer-events: auto;
}

/* 优化下拉菜单过渡动画 */
.van-dropdown-item {
  transition: all 0.3s ease-in-out !important;
}

.van-dropdown-item__content {
  transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out !important;
  transform-origin: top center !important;
}

/* 遮罩层动画 */
.van-overlay {
  transition: opacity 0.3s ease-in-out !important;
}

/* 下拉选项动画 */
.van-cell {
  transition: background-color 0.2s ease-in-out !important;
}

/* 新的筛选菜单样式 */
.filter-menu {
  display: flex;
  width: 100%;
  height: 80rpx;
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
  z-index: 101;
}

.filter-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 26rpx;
  color: #333;
}

.filter-item text {
  margin-right: 4rpx;
}

.filter-arrow {
  color: #999;
  transition: transform 0.3s ease;
}

.filter-item.active .filter-arrow {
  transform: rotate(180deg);
  color: #ff0000;
}

.filter-container {
  position: relative;
  z-index: 100;
  width: 100%;
  background-color: #fff;
}

.filter-content {
  position: absolute;
  top: 80rpx;
  left: 0;
  width: 100%;
  background: #fff;
  z-index: 99;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease-out;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  opacity: 0;
  transform: translateY(-10rpx);
}

.filter-content.show {
  max-height: 600rpx;
  opacity: 1;
  transform: translateY(0);
}

.filter-options {
  padding: 10rpx 20rpx;
  max-height: 600rpx;
  overflow-y: auto;
  overflow-x: hidden;
  /* 防止水平滚动 */
  box-sizing: border-box;
  border-top: none;
  /* 移除上边框，消除间隙 */
}

.filter-options::before,
.filter-options::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  height: 20rpx;
  pointer-events: none;
  z-index: 1;
}

.filter-options::before {
  top: 0;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
}

.filter-options::after {
  bottom: 0;
  background: linear-gradient(to top, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
}

/* 筛选选项 */
.filter-option {
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1rpx solid #eee;
}

.filter-option.active {
  color: #ff0000;
  background-color: #f7f7f7;
}

/* 遮罩层 */
.filter-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 99;
  display: none;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

.filter-mask.show {
  display: block;
}

/* 车辆列表中的最后一个卡片 */
.car-list .car-item:last-child {
  margin-bottom: 30rpx;
  /* 为最后一张卡片添加底部外边距 */
}

/* 确保地区选择器和筛选栏的样式互不影响 */
.global-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1001;
  /* 提高 z-index，确保在筛选栏之上 */
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
}

.global-mask.show {
  opacity: 1;
  visibility: visible;
}

/* 确保地区选择弹出层在全局遮罩层之上 */
.van-popup {
  z-index: 1002 !important;
  /* 提高 z-index，确保在全局遮罩层之上 */
}

/* 顶部弹出的城市选择器样式 */
.city-popup {
  width: 100%;
  max-height: 70vh;
  /* 限制最大高度为视口高度的70% */
  overflow-y: auto;
  border-bottom-left-radius: 16rpx;
  border-bottom-right-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 自定义 van-area 组件样式 */
.custom-area .van-picker__toolbar {
  height: 88rpx;
}

.custom-area .van-picker__title {
  font-size: 32rpx;
  font-weight: bold;
}

.custom-area .van-picker__cancel,
.custom-area .van-picker__confirm {
  font-size: 28rpx;
  padding: 0 30rpx;
}

/* 使用 !important 提高优先级 */
.filter-content,
.filter-options,
.filter-option {
  transform: translateX(0) !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
}

.area-container {
  background: #fff;
}

.all-city {
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1px solid #eee;
}

.all-city:active {
  background-color: #f7f7f7;
}

/* 筛选标题样式 */
.filter-title {
  opacity: 0;
  transform: translateY(-100%);
  pointer-events: none;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background: #fff;
  z-index: 1002;
  /* 确保筛选标题在最上层 */
  visibility: hidden;
  /* 添加隐藏属性 */
  transition: opacity 0.3s ease, transform 0.3s ease, visibility 0.3s ease;
}

.filter-title.visible {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
  visibility: visible;
  /* 显示时恢复可见性 */
}

.filter-close {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24rpx;
  color: #999;
  padding: 10rpx;
  z-index: 1003;
}

.top-container {
  position: relative;
  width: 100%;
  box-sizing: border-box;
  z-index: 1001;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  overflow: hidden;
  padding: 0;
  /* 移除可能的内边距 */
}

/* 搜索区域 */
.search-bar,
.filter-title {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  padding: 0;
  /* 移除可能的内边距 */
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.search-bar {
  padding: 20rpx 0;
  /* 只保留上下内边距，移除左右内边距 */
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  width: 100%;
  box-sizing: border-box;
}

.search-bar.hidden {
  opacity: 0;
  transform: translateY(-100%);
  pointer-events: none;
  z-index: -1;
  /* 添加负z-index确保它完全隐藏 */
}

.search-input-wrapper {
  display: flex;
  height: 70rpx;
  width: calc(100% - 40rpx);
  /* 减去左右边距的总和 */
  box-sizing: border-box;
  border-radius: 16rpx;
  overflow: hidden;
  margin: 0 20rpx;
  /* 如果需要与边缘保持一定距离，可以使用外边距代替内边距 */
  border-color: #1C82F5;
}

/* 筛选弹出层样式 */
.filter-popup {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
  margin-top: 155rpx;
}

.filter-popup-header {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 88rpx;
  position: relative;
  border-bottom: 1px solid #eee;
}

.filter-popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.filter-popup-close {
  position: absolute;
  right: 30rpx;
  font-size: 28rpx;
  color: #999;
  padding: 20rpx;
}

.filter-popup-content {
  flex: 1;
  overflow-y: auto;
}

.filter-section {
  padding: 30rpx;
  border-bottom: 1px solid #eee;
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
  padding: 0 15rpx;
}

.slider-labels text {
  font-size: 24rpx;
  color: #666;
  transform: translateX(-50%);
}

/* 自定义滑块样式 */
.van-slider {
  margin: 30rpx 0;
}

.van-slider__button {
  width: 5rpx !important;
  height: 5rpx !important;
  background-color: #fff !important;
  border: 2rpx solid #ffc62c !important;
}

.van-slider__bar {
  background-color: #ffc62c !important;
}

.filter-popup-footer {
  display: flex;
  padding: 20rpx 30rpx;
  border-top: 1px solid #eee;
}

.reset-btn,
.confirm-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 10rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.reset-btn {
  background: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background: #ffc62c;
  color: #fff;
}

/* van-area 组件的自定义样式 */
.van-area {
  height: 400rpx;
}

.van-area__title {
  font-size: 28rpx;
  color: #333;
  padding: 20rpx;
}

.van-picker-column__item {
  font-size: 28rpx;
}

.van-picker-column__item--selected {
  color: #ffc62c;
  font-weight: bold;
}

/* 地区选择器样式优化 */
.filter-section .van-area {
  height: 400rpx;
}

.filter-section .van-picker-column {
  height: 400rpx !important;
}

.filter-section .van-picker-column__item {
  font-size: 28rpx;
  line-height: 80rpx;
}

.filter-section .van-picker-column__item--selected {
  color: #ffc62c;
  font-weight: bold;
}

/* 确保选择器内容居中显示 */
.filter-section .van-picker-column__wrapper {
  height: 400rpx !important;
}

/* 固定头部样式 */
.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  background-color: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 头部占位符，防止内容被固定头部遮挡 */
.header-placeholder {
  width: 100%;
}

/* 确保筛选内容在固定头部下方显示 */
.filter-content.show {
  position: absolute;
  top: 80rpx;
  /* 与筛选菜单高度一致，确保无间隙 */
  left: 0;
  width: 100%;
  z-index: 99;
}

/* 标签容器 - 新增 */
.car-tags-container {
  padding: 0 20rpx 15rpx 50rpx;
  /* 内边距为左侧30rpx(car-item-content) + 20rpx(car-image-container) = 50rpx */
  /* 移除顶部边框 */
  margin-top: -20rpx;
  /* 增加负边距，让标签向上移动更多，使其与图片更接近 */
  width: 100%;
  box-sizing: border-box;
}

/* 背景广告样式 */
.bg-ad {
  width: 100%;
  height: 240rpx;
  position: relative;
  margin: 20rpx 0;
  padding: 0 24rpx;
  box-sizing: border-box;
}

.bg-ad-image {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.bg-ad-content {
  position: absolute;
  left: 48rpx;
  top: 50%;
  transform: translateY(-50%);
  color: #fff;
  z-index: 1;
}

.bg-ad-title {
  font-size: 40rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.bg-ad-desc {
  font-size: 28rpx;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.bg-ad-btn {
  display: inline-block;
  padding: 12rpx 32rpx;
  background: #fff;
  color: #333;
  font-size: 28rpx;
  font-weight: 500;
  border-radius: 32rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 添加渐变遮罩 */
.bg-ad::after {
  content: '';
  position: absolute;
  left: 24rpx;
  top: 0;
  width: calc(100% - 48rpx);
  height: 100%;
  background: linear-gradient(to right, rgba(0,0,0,0.4), rgba(0,0,0,0.1));
  border-radius: 16rpx;
}

.section-title {
  width: 98%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* font-size: 32rpx;
  line-height: 32rpx; */
  height: 80rpx;
  color: #3D3D3D;
  margin-bottom: 5rpx;
}

.section-title-title{
  color: #3D3D3D;
  width: auto;
  text-align: center;
  margin-left: 10rpx;
}

.section-divider {
  width: 60%;
  height: 2rpx;
  background: #e0e0e0;  /* 只用一个颜色 */
  border: none;
}

.star-group {
  display: flex;
  align-items: center;   /* 垂直居中 */
}

.star {
  color: #D8D8D8;
  /* font-size: 40rpx; */
  margin-left: 32rpx;
}

.star-group .star:nth-child(1){
  font-size: 20rpx;
}

.star-group .star:nth-child(2){
  font-size: 30rpx;
  padding-bottom:1.5rpx;
}

.star-group .star:nth-child(3){
  font-size: 40rpx;
  padding-bottom:3.5rpx;
}

.car-list-2{
  margin-top: 0rpx;
}