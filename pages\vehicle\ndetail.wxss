/* 页面容器 */
.container {
  padding: 0;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 顶部导航栏 */
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 88rpx;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24rpx;
  z-index: 100;
  border-bottom: 1rpx solid #f5f5f5;
}

.nav-back {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 40rpx;
  color: #333;
}

.nav-title {
  font-size: 34rpx;
  font-weight: 500;
  color: #333;
  flex: 1;
  text-align: center;
}

.nav-right {
  display: flex;
  align-items: center;
}

.more-icon,
.share-icon {
  font-size: 40rpx;
  color: #333;
  margin-left: 30rpx;
}

/* 车辆标题样式 */
.vehicle-header {
  background: #fff;
  padding: 24rpx;
  width: 100%;
  box-sizing: border-box;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  margin-bottom: 16rpx;
}

.info-row {
  margin-bottom: 16rpx;
}

.sub-info {
  display: flex;
  flex-wrap: wrap;
  font-size: 26rpx;
  color: #999;
}

.sub-info text {
  margin-right: 30rpx;
}

/* 价格区域样式 */
.price-section {
  display: flex;
  flex-direction: column;
  padding: 10rpx 0;
}

.guide-price,
.sale-price {
  display: flex;
  align-items: baseline;
  margin-right: 40rpx;
}

.guide-price text:first-child,
.sale-price text:first-child {
  font-size: 28rpx;
  color: #666;
}

.price {
  font-size: 40rpx;
  font-weight: 500;
  margin-left: 8rpx;
}

.guide-price .price {
  color: #3B7FFF;
}

.sale-price .price {
  color: #FF4D4F;
}

.sale-price {
  font-size: 48rpx;
  font-weight: bold;
  color: #FF4D4F;
  margin-bottom: 10rpx;
}

.original-price {
  font-size: 26rpx;
  color: #999;
}

/* 图片轮播样式 */
.image-swiper {
  width: 100%;
  height: 480rpx;
  background: #000;
  position: relative;
}

.slide-image {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: cover;
}

.swiper-counter {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  z-index: 10;
}

/* 信息表格样式 */
.info-table {
  width: 100%;
  background: #fff;
  margin-top: 20rpx;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05), 0 1rpx 3rpx rgba(0, 0, 0, 0.03);
  margin-bottom: 50rpx;
}

.table-title {
  font-size: 32rpx;
  color: #333;
  padding: 24rpx 30rpx;
  font-weight: 500;
}

.table-content {
  background: #fff;
}

.table-row {
  display: flex;
  padding: 28rpx 30rpx;
  border-top: 1rpx solid #f5f5f5;
  align-items: center;
  background: #fff;
}

.table-cell {
  font-size: 28rpx;
  line-height: 1.5;
}

.table-cell:first-child {
  color: #666;
  flex: 0 0 200rpx;
  font-weight: 400;
}

.table-cell:last-child {
  flex: 1;
  color: #333;
  padding-left: 20rpx;
}

/* 轮播指示点样式 */
.wx-swiper-dots {
  position: relative;
  left: unset !important;
  right: 0;
}

.wx-swiper-dots.wx-swiper-dots-horizontal {
  margin-bottom: 16rpx;
}

.wx-swiper-dot {
  width: 16rpx !important;
  height: 16rpx !important;
  background: rgba(255, 255, 255, 0.5) !important;
}

.wx-swiper-dot.wx-swiper-dot-active {
  background: #fff !important;
}

/* 信息卡片样式 */
.info-card {
  margin-top: 20rpx;
  background: #fff;
  padding: 20rpx;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.info-item {
  width: 33.33%;
  padding: 15rpx 0;
  box-sizing: border-box;
}

.info-label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 交易信息样式 */
.transaction-info {
  margin-top: 20rpx;
  background: #fff;
  padding: 30rpx 20rpx;
  display: flex;
  justify-content: space-around;
}

.transaction-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.transaction-time {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.green-text {
  color: #4CAF50;
}

.transaction-line {
  width: 2rpx;
  height: 40rpx;
  background: #ddd;
}

.transaction-desc {
  font-size: 26rpx;
  color: #333;
  margin-top: 15rpx;
}

/* 底部按钮区域 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: #fff;
  display: flex;
  align-items: center;
  border-top: 1rpx solid #eee;
  padding: 0 10rpx;
}

.action-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.action-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 6rpx;
}

.phone-icon {
  background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTYuNjIgMTAuNzljLjM1IDMuMDQgMS45NSA1LjY1IDQuMiA3LjA0bDEuNDItMS40MmMuMTgtLjE4LjQ1LS4yNC42OS0uMTUgMS4yMS4zOSAyLjUyLjYgMy44Ny42LjQxIDAgLjc1LjM0Ljc1Ljc1djIuMjVjMCAuNDEtLjM0Ljc1LS43NS43NS0xMC4wNSAwLTE4LjI1LTguMi0xOC4yNS0xOC4yNSAwLS40MS4zNC0uNzUuNzUtLjc1aDIuMjVjLjQxIDAgLjc1LjM0Ljc1Ljc1IDAgMS4zNS4yMSAyLjY2LjYgMy44Ny4wOS4yNC4wMy41MS0uMTUuNjlsLTEuNDIgMS40MnoiIGZpbGw9IiM2NjY2NjYiLz48L3N2Zz4=') no-repeat center;
  background-size: contain;
}

.message-icon {
  background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTIwIDJoLTE2Yy0xLjEgMC0xLjk5LjktMS45OSAybC0uMDEgMThjMCAxLjEuOSAyIDIgMmgxNmMxLjEgMCAyLS45IDItMnYtMThjMC0xLjEtLjktMi0yLTJ6bTAgMTRoLTE2di0xMGgxNnYxMHptLTMtN2gtMTB2MmgxMHYtMnptMCAzaC0xMHYyaDEwdi0yeiIgZmlsbD0iIzY2NjY2NiIvPjwvc3ZnPg==') no-repeat center;
  background-size: contain;
}

.favorite-icon {
  background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTEyIDIxLjM1bC0xLjQ1LTEuMzJDNS40IDE1LjM2IDIgMTIuMjggMiA4LjUgMiA1LjQyIDQuNDIgMyA3LjUgM2MxLjc4IDAgMy40MS44MSA0LjUgMi4wOUMxMy4wOSAzLjgxIDE0Ljc2IDMgMTYuNSAzIDE5LjU4IDMgMjIgNS40MiAyMiA4LjVjMCAzLjc4LTMuNCA2Ljg2LTguNTUgMTEuNTRMMTIgMjEuMzV6IiBmaWxsPSIjNjY2NjY2Ii8+PC9zdmc+') no-repeat center;
  background-size: contain;
}

.action-text {
  font-size: 22rpx;
  color: #666;
}

.reserve-btn {
  flex: 2;
  height: 70rpx;
  line-height: 70rpx;
  background: #FF4D4F;
  color: #fff;
  font-size: 28rpx;
  text-align: center;
  border-radius: 35rpx;
  margin: 0 15rpx;
}

/* 渐变背景类 */
.gradient-bg {
  background: linear-gradient(135deg, #ff6b6b 0%, #ff4757 100%);
}

/* 车辆详情页样式 */
page {
  background-color: #f5f7fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: #333;
  font-size: 28rpx;
  line-height: 1.5;
  height: 100%;
}

.detail-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  position: relative;
  overflow: hidden;
}

/* 固定头部区域 */
.fixed-header {
  position: relative;
  z-index: 10;
  background-color: #fff;
}

/* 轮播图区域 */
.swiper-container {
  width: 100%;
  height: 600rpx;
  position: relative;
}

/* 返回按钮样式 */
.back-button {
  position: absolute;
  top: 90rpx;
  left: 30rpx;
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10rpx);
}

.back-icon {
  width: 32rpx;
  height: 32rpx;
  position: relative;
}

.back-icon::before {
  content: '';
  position: absolute;
  left: 8rpx;
  top: 50%;
  width: 16rpx;
  height: 16rpx;
  border-left: 3rpx solid #333;
  border-bottom: 3rpx solid #333;
  transform: translateY(-50%) rotate(45deg);
}

.image-swiper {
  width: 100%;
  height: 100%;
}

.swiper-image {
  width: 100%;
  height: 100%;
}

.image-counter {
  position: absolute;
  left: 50%;
  bottom: 30rpx;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: #ffffff;
  font-size: 28rpx;
  padding: 12rpx 28rpx;
  border-radius: 32rpx;
  z-index: 1000;
  font-weight: 500;
  backdrop-filter: blur(4rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  min-width: 80rpx;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
}

/* 可滚动内容区域 */
.scrollable-content {
  flex: 1;
  height: 0;
  padding-bottom: 120rpx;
}

/* 底部占位区域 */
.bottom-placeholder {
  height: 170rpx;
  width: 100%;
}

/* 车辆标题与价格 */
.vehicle-title-section {
  padding: 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.vehicle-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #222;
  line-height: 1.3;
  margin-bottom: 16rpx;
}

.vehicle-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.vehicle-location {
  font-size: 26rpx;
  color: #999;
}

.vehicle-date {
  font-size: 26rpx;
  color: #999;
  text-align: right;
}

.price-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.price-left {
  display: flex;
  align-items: baseline;
  font-size: 26rpx;
  color: #666;
  margin-right: 8rpx;
}

.price-right {
  display: flex;
  align-items: baseline;
}

.strikethrough {
  text-decoration: line-through;
  margin: 0 4rpx;
  color: #999;
}

.price-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #3b82f6;
  line-height: 1;
}

.price-unit {
  font-size: 28rpx;
  color: #3b82f6;
  margin-left: 4rpx;
}

/* 价格比较样式 */
.price-comparison {
  font-size: 24rpx;
  color: #999;
  line-height: 1.2;
  display: flex;
  align-items: baseline;
  margin-left: 30rpx;
}

.discount-tag {
  background-color: #fff0f0;
  color: #f44336;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  margin-top: 8rpx;
  font-size: 22rpx;
}

/* 核心参数 */
.core-params {
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.param-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.param-value {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.param-label {
  font-size: 24rpx;
  color: #999;
}

/* 详细参数 */
.detail-section {
  background-color: #fff;
  border-radius: 0;
  padding: 30rpx;
  margin-bottom: 0;
  box-shadow: none;
  min-height: 100%;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #222;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 6rpx;
  height: 32rpx;
  width: 8rpx;
  background-color: #3a86ff;
  border-radius: 4rpx;
}

.params-grid {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}

.param-grid-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 50%;
  padding: 10rpx 20rpx;
  box-sizing: border-box;
  border-bottom: 1rpx solid #f0f0f0;
}

.grid-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 5rpx;
}

.grid-value {
  font-size: 30rpx;
  color: #333;
}

/* 描述部分 */
.description-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.description-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 联系咨询 */
.contact-section {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;
}

.contact-btn {
  width: 48%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: 500;
}

.primary {
  background-color: #3a86ff;
  color: #fff;
}

.secondary {
  background-color: #fff;
  color: #3a86ff;
  border: 2rpx solid #3a86ff;
}

/* 底部固定操作栏 */
.bottom-action-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 110rpx;
  display: flex;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.action-buttons-container {
  display: flex;
  flex: 1;
  padding: 10rpx 20rpx 10rpx;
  gap: 20rpx;
  margin-top: 6rpx;
}

.action-btn-collect {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 100%;
  font-size: 24rpx;
  color: #666;
  background-color: #fff;
  margin-top: 2rpx;
}

.collect-icon {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 6rpx;
}

.action-btn-manual {
  flex: 1;
  background-color: #4080FF;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
}

.action-btn-contact {
  flex: 1;
  background-color: #FF5A00;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
}

/* 图标样式 */
.icon-home,
.icon-favorite,
.icon-share {
  width: 40rpx;
  height: 40rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.icon-home {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB0PSIxNjgzMjA1NjI4NTczIiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjM4NzMiIHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIj48cGF0aCBkPSJNNTEyIDEyOEwyMTMuMzMzIDM4NGwwIDQ2OS4zMzMgMTcwLjY2NyAwIDAgLTE3MC42NjcgMjU2IDAgMCAxNzAuNjY3IDE3MC42NjcgMEw4MTAuNjY3IDM4NCAiIGZpbGw9IiM2NjY2NjYiIHAtaWQ9IjM4NzQiPjwvcGF0aD48L3N2Zz4=');
}

.icon-favorite {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB0PSIxNjgzMjA1NjU3NTk0IiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjUxNzMiIHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIj48cGF0aCBkPSJNNzA3LjU4NCAyMTMuMzMzYzU1LjQ2NyAwIDEwMC4wNTMgMTkuNjI3IDEzNS4wODMgNTkuNzMzIDM0LjEzMyAzOS4yNTMgNTEuMiA4OC43NDcgNTEuMiAxNDguNDggMCAxMDEuMTItNjguMjY3IDIwMy41Mi0yMDQuOCA0MDYuNjEzbC0xNzcuMDY3IDE5Ni4yNjdMMzM0LjkzMyA4MjguMTZjLTEzNi41MzMtMjAzLjA5My0yMDQuOC0zMDUuNDkzLTIwNC44LTQwNi42MTMgMC01OS43MzMgMTcuMDY3LTEwOS4yMjcgNTEuMi0xNDguNDggMzUuMDI5LTQwLjEwNyA3OS42MTYtNTkuNzMzIDEzNS4wODMtNTkuNzMzIDg4LjMyIDAgMTQ5LjMzMyA1Ny4xNzMgMTk1LjU4NCAxNzEuNTIgNDYuMjUxLTExNC4zNDcgMTA3LjI2NC0xNzEuNTIgMTk1LjU4NC0xNzEuNTJ6IiBmaWxsPSIjNjY2NjY2IiBwLWlkPSI1MTc0Ij48L3BhdGg+PC9zdmc+');
}

.icon-favorite.active {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB0PSIxNjgzMjA1NjU3NTk0IiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjUxNzMiIHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIj48cGF0aCBkPSJNNzA3LjU4NCAyMTMuMzMzYzU1LjQ2NyAwIDEwMC4wNTMgMTkuNjI3IDEzNS4wODMgNTkuNzMzIDM0LjEzMyAzOS4yNTMgNTEuMiA4OC43NDcgNTEuMiAxNDguNDggMCAxMDEuMTItNjguMjY3IDIwMy41Mi0yMDQuOCA0MDYuNjEzbC0xNzcuMDY3IDE5Ni4yNjdMMzM0LjkzMyA4MjguMTZjLTEzNi41MzMtMjAzLjA5My0yMDQuOC0zMDUuNDkzLTIwNC44LTQwNi42MTMgMC01OS43MzMgMTcuMDY3LTEwOS4yMjcgNTEuMi0xNDguNDggMzUuMDI5LTQwLjEwNyA3OS42MTYtNTkuNzMzIDEzNS4wODMtNTkuNzMzIDg4LjMyIDAgMTQ5LjMzMyA1Ny4xNzMgMTk1LjU4NCAxNzEuNTIgNDYuMjUxLTExNC4zNDcgMTA3LjI2NC0xNzEuNTIgMTk1LjU4NC0xNzEuNTJ6IiBmaWxsPSIjRjQ0MzM2IiBwLWlkPSI1MTc0Ij48L3BhdGg+PC9zdmc+');
}

.icon-share {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB0PSIxNjgzMjA1Njg1NTM0IiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjY0NzMiIHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIj48cGF0aCBkPSJNNzY4IDY4Mi42NjY2NjdjLTMyLjQyNjY2NyAwLTYxLjg2NjY2NyAxMi44LTgzLjYyNjY2NyAzMy4yOGwtMjY2LjI0LTE1NS4zMDY2NjdjMi45ODY2NjctMTEuOTQ2NjY3IDQuOTkyLTI0LjMyIDQuOTkyLTM3LjEyIDAtMTIuOC0yLjAwNTMzMy0yNS4xNzMzMzMtNC45OTItMzcuMTJsMjY2LjI0LTE1NS4zMDY2NjdjMjEuNzYgMjAuNDggNTEuMiAzMy4yOCA4My42MjY2NjcgMzMuMjggNjcuNjI2NjY3IDAgMTIyLjY2NjY2Ny01NS4wNDAgMTIyLjY2NjY2Ny0xMjIuNjY2NjY3cy01NS4wNC0xMjIuNjY2NjY3LTEyMi42NjY2NjctMTIyLjY2NjY2Ny0xMjIuNjY2NjY3IDU1LjA0LTEyMi42NjY2NjcgMTIyLjY2NjY2N2MwIDEyLjggMi4wMDUzMzMgMjUuMTczMzMzIDQuOTkyIDM3LjEybC0yNjYuMjQgMTU1LjMwNjY2N2MtMjEuNzYtMjAuNDgtNTEuMi0zMy4yOC04My42MjY2NjctMzMuMjgtNjcuNjI2NjY3IDAtMTIyLjY2NjY2NyA1NS4wNC0xMjIuNjY2NjY2IDEyMi42NjY2NjdzNTUuMDQgMTIyLjY2NjY2NyAxMjIuNjY2NjY2IDEyMi42NjY2NjdjMzIuNDI2NjY3IDAgNjEuODY2NjY3LTEyLjggODMuNjI2NjY3LTMzLjI4bDI2Ni4yNCAxNTUuMzA2NjY3Yy0yLjk4NjY2NyAxMS45NDY2NjctNC45OTIgMjQuMzItNC45OTIgMzcuMTIgMCA2Ny42MjY2NjcgNTUuMDQgMTIyLjY2NjY2NyAxMjIuNjY2NjY3IDEyMi42NjY2NjdzMTIyLjY2NjY2Ny01NS4wNCAxMjIuNjY2NjY3LTEyMi42NjY2NjctNTUuMDQtMTIyLjY2NjY2Ny0xMjIuNjY2NjY3LTEyMi42NjY2Njd6IiBmaWxsPSIjNjY2NjY2IiBwLWlkPSI2NDc0Ij48L3BhdGg+PC9zdmc+');
}

/* 按钮样式 */
.contact-btn,
.download-btn {
  flex: 1;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 28rpx;
  font-weight: 500;
  border-radius: 35rpx;
  margin: 0 10rpx;
  padding: 0 15rpx;
}

.contact-btn {
  background-color: #f44336;
  color: #fff;
}

.download-btn {
  flex: 1.2;
  background-color: #3a86ff;
  color: #fff;
  padding: 0 20rpx;
}

.param-group-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  padding: 20rpx 20rpx 10rpx;
  background-color: #f8f8f8;
  width: 100%;
  box-sizing: border-box;
}

/* 车辆参数卡片样式 */
.vehicle-params-card {
  background-color: #fff;
  border-radius: 20rpx;
  margin: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.params-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 30rpx;
}

.params-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.detailed-config-btn {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #999;
  padding: 10rpx 0;
}

.detailed-config-btn text {
  margin-right: 8rpx;
}

.arrow-right {
  width: 0;
  height: 0;
  border-left: 8rpx solid #999;
  border-top: 6rpx solid transparent;
  border-bottom: 6rpx solid transparent;
}

.params-content {
  padding: 20rpx 30rpx 30rpx;
}

.param-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.param-row:last-child {
  margin-bottom: 0;
}

.param-item {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-right: 40rpx;
}

.param-item:last-child {
  margin-right: 0;
}

.param-label {
  font-size: 24rpx;
  color: #999;
  line-height: 1.2;
}

.param-value {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.3;
}

/* 商家信息卡片样式 */
.merchant-card {
  background-color: #fff;
  border-radius: 20rpx;
  margin: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.merchant-info {
  display: flex;
  align-items: flex-start;
  padding: 15rpx 30rpx 30rpx;
}

.merchant-logo {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.merchant-logo image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: #f5f5f5;
}

.merchant-details {
  flex: 1;
}

.merchant-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 5rpx;
  line-height: 1.4;
}

.merchant-address {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 5rpx;
  line-height: 1.4;
}

.merchant-stats {
  display: flex;
  align-items: center;
  margin-top: 5rpx;
}

.merchant-rating {
  font-size: 28rpx;
  color: #FF6634;
  margin-right: 20rpx;
}

.merchant-stock {
  font-size: 28rpx;
  color: #666;
}

.merchant-contact {
  margin-left: auto;
  display: flex;
  align-items: center;
}

.verify-btn {
  font-size: 24rpx;
  background-color: #E5F1FE;
  color: #3A84FF;
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  line-height: 1.5;
  font-weight: normal;
  min-height: unset;
  margin: 0;
}

.verify-btn::after {
  border: none;
}

/* 车辆实拍区域样式 */
.vehicle-photos-card {
  margin: 20rpx;
  padding: 30rpx 0 20rpx;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.vehicle-photos-container {
  padding: 20rpx 0;
  width: 100%;
}

.photos-column {
  display: flex;
  flex-direction: column;
  padding: 0 20rpx;
}

.photo-item {
  width: 100%;
  height: 420rpx;
  margin-bottom: 20rpx;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.real-photo {
  width: 100%;
  height: 100%;
  object-fit: cover;
}