// pages/info/certification_review.js
const config = require('../../config.js');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: 0,
    COS_CONFIG: config.COS_CONFIG, // 添加腾讯云配置
    showOfficialAccount: true, // 控制是否显示官方关注公众号组件
    hasSubscribed: false, // 是否已订阅消息通知
    hasRejectedSubscribe: false, // 是否拒绝过订阅
    templateIds: [
      'Y2YoweIQCGK7-X4yWP2tl62ZgmznpGmx4fTYw1kqQlY', // 认证审核结果模板ID - 需要替换为真实模板ID // 认证进度更新模板ID - 可选
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });

    // 检查订阅状态
    this.checkSubscriptionStatus();
  },

  /**
   * 返回上一页
   */
  navigateBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 关注微信公众号
   */
  followWechat() {
    wx.showModal({
      title: '关注公众号',
      content: '请在微信中搜索"找车侠"公众号并关注，输入"审核"获取最新认证动态',
      showCancel: true,
      cancelText: '取消',
      confirmText: '知道了',
      success: (res) => {
        if (res.confirm) {
          // 可以在这里添加复制公众号名称到剪贴板的功能
          wx.setClipboardData({
            data: '找车侠',
            success: () => {
              wx.showToast({
                title: '公众号名称已复制',
                icon: 'success'
              });
            }
          });
        }
      }
    });
  },

  /**
   * 官方关注公众号组件加载成功
   */
  onOfficialAccountLoad(e) {
    console.log('官方关注公众号组件加载成功:', e.detail);
    // 组件加载成功，继续显示官方组件
    this.setData({
      showOfficialAccount: true
    });
  },

  /**
   * 官方关注公众号组件加载失败
   */
  onOfficialAccountError(e) {
    console.log('官方关注公众号组件加载失败:', e.detail);
    
    const { status, errMsg } = e.detail;
    let errorMessage = '无法加载关注组件';
    
    // 根据错误状态码提供具体信息
    switch (status) {
      case -2:
        errorMessage = '网络错误，请检查网络连接';
        break;
      case -1:
        errorMessage = '数据解析错误';
        break;
      case 1:
        errorMessage = '小程序关注公众号功能被封禁';
        break;
      case 2:
        errorMessage = '关联公众号被封禁';
        break;
      case 3:
        errorMessage = '关联关系解除或未选中关联公众号';
        break;
      case 4:
        errorMessage = '未开启关注公众号功能';
        break;
      case 5:
        errorMessage = '当前场景不支持关注公众号';
        break;
      case 6:
        errorMessage = '重复创建组件';
        break;
      default:
        errorMessage = errMsg || '未知错误';
    }
    
    console.warn('官方关注组件错误:', errorMessage);
    
    // 降级到自定义关注按钮
    this.setData({
      showOfficialAccount: false
    });
  },

  /**
   * 撤回审核
   */
  withdrawReview() {
    wx.showModal({
      title: '撤回审核',
      content: '确定要撤回当前的企业认证审核吗？撤回后需要重新提交认证资料。',
      showCancel: true,
      cancelText: '取消',
      confirmText: '确定撤回',
      confirmColor: '#FF6B35',
      success: (res) => {
        if (res.confirm) {
          // 显示加载提示
          wx.showLoading({
            title: '撤回中...',
            mask: true
          });

          // 获取用户信息
          const util = require('../../utils/util.js');
          const api = require('../../utils/api.js').default;
          const userInfo = util.getUserInfo();

          if (!userInfo || !userInfo.app_id) {
            wx.hideLoading();
            wx.showToast({
              title: '用户信息获取失败',
              icon: 'none'
            });
            return;
          }

          // 调用撤回审核接口
          api.user.withdrawCertification({
            app_id: userInfo.app_id,
            status: 4 // 撤回审核状态
          })
          .then(result => {
            wx.hideLoading();
            
            // 更新本地用户信息状态
            if (userInfo) {
              userInfo.is_auth = 4; // 设置为撤回审核状态
              util.setCacheWithExpiry('userInfo', userInfo);
            }

            wx.showToast({
              title: '撤回成功',
              icon: 'success',
              duration: 2000
            });

            // 延迟返回到企业认证页面
            setTimeout(() => {
              wx.redirectTo({
                url: '/pages/info/certification'
              });
            }, 2000);
          })
          .catch(error => {
            wx.hideLoading();
            console.error('撤回审核失败:', error);
            wx.showToast({
              title: error.message || '撤回失败，请重试',
              icon: 'none',
              duration: 2000
            });
          });
        }
      }
    });
  },

  /**
   * 检查订阅状态
   */
  checkSubscriptionStatus() {
    try {
      // 每次进入页面都重置订阅状态，允许用户重新订阅
      this.setData({
        hasSubscribed: false,
        hasRejectedSubscribe: false
      });

      console.log('订阅状态已重置，用户可以重新进行订阅操作');
    } catch (error) {
      console.log('检查订阅状态失败:', error);
    }
  },

  /**
   * 申请订阅消息权限
   */
  requestSubscribeMessage() {
    wx.requestSubscribeMessage({
      tmplIds: this.data.templateIds,
      success: (res) => {
        console.log('订阅结果:', res);
        
        // 检查主要模板的订阅结果
        const mainTemplateId = this.data.templateIds[0];
        const subscribeResult = res[mainTemplateId];
        
        if (subscribeResult === 'accept') {
          // 用户同意订阅
          this.handleSubscribeSuccess();
        } else if (subscribeResult === 'reject') {
          // 用户拒绝订阅
          this.handleSubscribeReject();
        } else if (subscribeResult === 'ban') {
          // 用户被封禁
          wx.showToast({
            title: '订阅功能暂时不可用',
            icon: 'none',
            duration: 2000
          });
        }
      },
      fail: (err) => {
        console.error('订阅申请失败:', err);
        wx.showToast({
          title: '订阅申请失败，请稍后重试',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  /**
   * 处理订阅成功
   */
  handleSubscribeSuccess() {
    try {
      // 仅在当前页面会话中隐藏订阅卡片，不永久保存状态
      // 这样下次进入页面时仍可以重新订阅
      this.setData({
        hasSubscribed: true,
        hasRejectedSubscribe: false
      });

      wx.showToast({
        title: '订阅成功！审核完成后将通知您',
        icon: 'success',
        duration: 2500
      });
    } catch (error) {
      console.error('处理订阅成功状态失败:', error);
    }
  },

  /**
   * 处理订阅拒绝
   */
  handleSubscribeReject() {
    try {
      // 仅在当前页面会话中隐藏订阅卡片，不永久保存拒绝状态
      // 这样下次进入页面时仍可以重新尝试订阅
      this.setData({
        hasRejectedSubscribe: true
      });

      wx.showToast({
        title: '您拒绝了消息订阅，下次进入页面时可重新选择',
        icon: 'none',
        duration: 2500
      });
    } catch (error) {
      console.error('处理订阅拒绝状态失败:', error);
    }
  },

  /**
   * 稍后提醒
   */
  remindLater() {
    try {
      // 仅在当前页面会话中隐藏订阅卡片
      // 下次进入页面时会重新显示，实现"稍后提醒"的效果
      this.setData({
        hasRejectedSubscribe: true
      });

      wx.showToast({
        title: '下次进入页面时将再次提醒您',
        icon: 'none',
        duration: 1500
      });
    } catch (error) {
      console.error('设置稍后提醒失败:', error);
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次页面显示时都检查订阅状态，确保状态同步
    this.checkSubscriptionStatus();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 模拟刷新
    setTimeout(() => {
      wx.stopPullDownRefresh();
      wx.showToast({
        title: '已是最新状态',
        icon: 'none'
      });
    }, 1000);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '企业认证中',
      path: '/pages/info/certification_review'
    };
  }
})