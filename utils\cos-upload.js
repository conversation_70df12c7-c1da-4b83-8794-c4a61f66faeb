import COS from 'cos-wx-sdk-v5';

// 腾讯云COS配置
const config = {
    SecretId: 'AKIDKl02rSaEoEVdljYEj5HIAtO2UjnmbEeq',      // 替换为你的SecretId
    SecretKey: 'A8btypSs166yhHmBWhiXAaopdFsSpeHh',    // 替换为你的SecretKey
    Bucket: 'zhaochexia-1331160188',      // 存储桶名称
    Region: 'ap-guangzhou',    // 存储桶所在地域，例如ap-guangzhou
    Directory: 'uploads/'      // 存储目录
};

// 初始化COS实例
const cos = new COS({
    SecretId: config.SecretId,
    SecretKey: config.SecretKey
});

/**
 * 上传文件到腾讯云COS
 * @param {string} filePath - 本地文件路径
 * @param {string} fileType - 文件类型，例如'image'或'pdf'
 * @returns {Promise} - 返回上传结果Promise
 */
const uploadFile = (filePath, fileType = 'image') => {
    return new Promise((resolve, reject) => {
        // 生成文件名
        const fileName = `${Date.now()}_${Math.floor(Math.random() * 1000)}`;
        const fileExtension = filePath.substring(filePath.lastIndexOf('.'));
        const key = `${config.Directory}${fileType}/${fileName}${fileExtension}`;

        cos.postObject({
            Bucket: config.Bucket,
            Region: config.Region,
            Key: key,
            FilePath: filePath,
            onProgress: (info) => {
                console.log('上传进度:', info.percent * 100 + '%');
            }
        }, (err, data) => {
            if (err) {
                console.error('上传失败:', err);
                reject(err);
                return;
            }

            // 上传成功，返回文件访问URL
            const fileUrl = `https://${config.Bucket}.cos.${config.Region}.myqcloud.com/${key}`;
            resolve({
                url: fileUrl,
                key: key
            });
        });
    });
};

export default {
    uploadFile,
    config
}; 