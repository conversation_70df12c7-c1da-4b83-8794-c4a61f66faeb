<!-- 引入登录弹窗模板 -->
<import src="../../templates/loginPopup/loginPopup.wxml" />

<view
  class="container"
  style="{{ pageStyle }}"
>
  <!-- 顶部区域：固定高度容器，用于容纳搜索框或筛选标题 -->
  <view class="fixed-header">
    <!-- 自定义导航栏 -->
    <view class="custom-nav">
      <view
        class="status-bar"
        style="height: {{statusBarHeight}}px;"
      ></view>
      <view class="nav-content">
        <view
          class="nav-back"
          bindtap="onBackTap"
        >
          <van-icon
            name="arrow-left"
            size="20px"
            color="#333"
          />
        </view>
        <view class="nav-title">{{typeName || '商用车'}}</view>
        <view class="nav-placeholder"></view>
      </view>
    </view>
    <view
      class="top-container"
      style="height: {{ topContainerHeight }}px;"
    >
      <!-- 搜索区域 -->
      <view class="search-bar">
        <view class="search-input-wrapper">
          <icon
            type="search"
            size="14"
            class="search-icon"
          ></icon>
          <input
            class="search-input"
            placeholder="请输入品牌或车型"
            placeholder-style="color: #aaa; font-size: 26rpx;"
            value="{{ searchValue }}"
            bindinput="onSearchInput"
            bindconfirm="onSearch"
          />
          <view
            class="search-btn"
            bindtap="onSearch"
          >搜索</view>
        </view>
      </view>
    </view>

    <!-- 筛选区域容器 -->
    <view
      class="filter-container"
      wx:if="{{ !hideFilterBar }}"
    >
      <!-- 自定义筛选条件栏 -->
      <view class="filter-menu">
        <view
          class="filter-item {{ currentFilter === 'brand' ? 'active' : '' }}"
          bindtap="toggleFilter"
          data-type="brand"
        >
          <text>品牌</text>
          <van-icon
            name="arrow-down"
            size="12px"
            class="filter-arrow"
          />
        </view>
        <view
          class="filter-item {{ currentFilter === 'price' ? 'active' : '' }}"
          bindtap="toggleFilter"
          data-type="price"
        >
          <text>价格</text>
          <van-icon
            name="arrow-down"
            size="12px"
            class="filter-arrow"
          />
        </view>
        <view
          class="filter-item {{ currentFilter === 'filter' ? 'active' : '' }}"
          bindtap="onFilterTap"
        >
          <text>全国</text>
          <van-icon
            name="arrow-down"
            size="12px"
            class="filter-arrow"
          />
        </view>

        <!-- 筛选内容 - 移动到筛选菜单内部 -->
        <view class="filter-content {{ showFilterContent ? 'show' : '' }}">
          <!-- 品牌筛选选项 -->
          <scroll-view
            scroll-y
            class="filter-options"
            wx:if="{{ currentFilter === 'brand' }}"
          >
            <view
              wx:for="{{ brandOptions }}"
              wx:key="value"
              class="filter-option {{ brandValue === item.value ? 'active' : '' }}"
              bindtap="onFilterOptionSelect"
              data-type="brand"
              data-value="{{ item.value }}"
            >
              <text>{{ item.text }}</text>
            </view>
          </scroll-view>

          <!-- 里程筛选选项 -->
          <view
            class="filter-options"
            wx:if="{{ currentFilter === 'mileage' }}"
          >
            <view
              class="filter-option {{ mileageValue === item.value ? 'active' : '' }}"
              wx:for="{{ mileageOptions }}"
              wx:key="value"
              bindtap="onFilterOptionSelect"
              data-type="mileage"
              data-value="{{ item.value }}"
            >
              {{ item.text }}
            </view>
          </view>

          <!-- 价格筛选选项 -->
          <view
            class="filter-options"
            wx:if="{{ currentFilter === 'price' }}"
          >
            <view
              class="filter-option {{ priceValue === item.value ? 'active' : '' }}"
              wx:for="{{ priceOptions }}"
              wx:key="value"
              bindtap="onFilterOptionSelect"
              data-type="price"
              data-value="{{ item.value }}"
            >
              {{ item.text }}
            </view>
          </view>

          <!-- 车龄筛选选项 -->
          <view
            class="filter-options"
            wx:if="{{ currentFilter === 'age' }}"
          >
            <view
              class="filter-option {{ ageValue === item.value ? 'active' : '' }}"
              wx:for="{{ ageOptions }}"
              wx:key="value"
              bindtap="onFilterOptionSelect"
              data-type="age"
              data-value="{{ item.value }}"
            >
              {{ item.text }}
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 添加遮罩层 -->
  <view
    class="page-mask {{ isDropdownOpen ? 'show' : '' }}"
    catchtouchmove="preventScroll"
  ></view>

  <!-- 遮罩层 -->
  <view
    class="filter-mask {{ showFilterContent ? 'show' : '' }}"
    bindtap="closeFilter"
    catchtouchmove="preventScroll"
  ></view>

  <!-- 内容区域 - 添加一个占位符，高度等于固定头部的高度 -->
  <view class="header-placeholder"></view>

  <!-- 车辆列表 -->
  <view class="car-list">
    <block
      wx:for="{{carList}}"
      wx:key="id"
    >
      <view
        class="car-item"
        data-id="{{item.id}}"
        bindtap="toDetail"
      >
        <view class="car-main-content">
          <!-- 只在新车类型时显示图片 -->
          <view class="car-image-container">
            <image
              class="car-image"
              src="{{item.imageUrl}}"
              mode="aspectFill"
            ></image>
          </view>
          <view class="car-info">
            <view class="car-title">{{item.title}}</view>
            <view class="car-content-wrapper">
              <view class="car-attrs">
                <text>{{item.location}}</text>
                <text>{{item.date}}</text>
              </view>
              <view class="price-section">
                <view class="price-row">
                  <view class="price-info">
                    <text class="new-price">新车指导价{{item.newPrice}}万元</text>
                    <text class="sale-price">{{item.salePrice}}万元</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- 标签放在下方 
        <view class="car-tags">
          <text class="tag">现车</text>
          <text class="tag">口岸</text>
          <text class="tag">特价</text>
          <text class="tag">增票</text>
          <text class="tag">认证商家</text>
        </view>
        -->
      </view>
    </block>
    <view
      class="loading"
      wx:if="{{isLoading}}"
    >
      <text>加载中...</text>
    </view>
    <view
      class="no-more"
      wx:if="{{!hasMoreData && carList.length > 0}}"
    >
      <text>没有更多数据了</text>
    </view>
    <view
      class="empty"
      wx:if="{{!isLoading && carList.length === 0}}"
    >
      <text>暂无车辆数据</text>
    </view>
  </view>

  <!-- 使用 Vant 的 Overlay 组件 -->
  <van-overlay
    show="{{ showAreaPopup }}"
    z-index="1001"
    bind:click="onCloseAreaPopup"
  />

  <van-popup
    show="{{ showAreaPopup }}"
    position="top"
    bind:close="onCloseAreaPopup"
    z-index="1002"
    overlay="false"
    custom-class="city-popup"
  >
    <!-- 添加全国选项和省市选择器 -->
    <view class="area-container">
      <!-- 添加关闭按钮 -->
      <view
        class="area-close-btn"
        bindtap="onCloseAreaPopup"
      >✕</view>
      <!-- 全国选项 -->
      <view
        class="all-city"
        bindtap="onSelectAllCity"
      >
        <text>全国</text>
      </view>
      <!-- 省市选择器 -->
      <van-area
        area-list="{{ areaList }}"
        value="{{ selectedAreaCode }}"
        bind:confirm="onConfirmArea"
        bind:cancel="onCloseAreaPopup"
        custom-class="custom-area"
        columns-num="{{ 2 }}"
      />
    </view>
  </van-popup>

  <!-- 筛选弹出层 -->
  <van-popup
    show="{{ filterPopupVisible }}"
    position="right"
    custom-style="width: 100%; height: 100%;"
    bind:close="onCloseFilterPopup"
  >
    <view class="filter-popup">
      <view class="filter-popup-header">
        <view class="filter-popup-title">全国</view>
        <view
          class="filter-popup-close"
          bindtap="onCloseFilterPopup"
        >✕</view>
      </view>

      <view class="filter-popup-content">
        <!-- 地区选择 -->
        <view class="filter-section">
          <view class="section-title">所在地</view>
          <van-area
            area-list="{{ areaList }}"
            columns-num="2"
            bind:change="onAreaChange"
            value="{{ selectedAreaCode }}"
            show-toolbar="{{ false }}"
          />
        </view>
      </view>

      <!-- 底部按钮 -->
      <view class="filter-popup-footer">
        <view
          class="reset-btn"
          bindtap="onResetFilter"
        >重置</view>
        <view
          class="confirm-btn"
          bindtap="onConfirmFilter"
        >确定</view>
      </view>
    </view>
  </van-popup>

  <!-- 引用登录弹窗模板 -->
  <template
    is="loginPopup"
    data="{{ showLoginPopup, loginPopupOptions }}"
  />
</view>