// pages/train_operation/detais.js
import api from '../../utils/api';

Page({

  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: wx.getSystemInfoSync().statusBarHeight || 20,
    currentTab: 0, // 当前选中的标签，默认为信息详情
    chewu_orderid: '', // 存储车务订单ID
    orderDetail: null, // 存储车务详情数据
    attachmentStatus: {}, // 存储附件上传状态
    // 添加流程状态数据
    processStatus: {
      '101': 'in-progress', // 合同立项
      '201': 'pending', // 收付款
      '301': 'pending', // 许可证
      '401': 'pending', // 物流
      '501': 'pending', // 报关
      '601': 'pending', // 退税
      '701': 'pending'  // 完结
    },
    // 添加流程执行人数据
    processExecutors: {
      '101': '', // 合同立项执行人
      '201': '', // 收付款执行人
      '301': '', // 许可证执行人
      '401': '', // 物流执行人
      '501': '', // 报关执行人
      '601': '', // 退税执行人
      '701': ''  // 完结执行人
    },
    // 添加流程最新时间戳数据
    processTimestamps: {
      '101': '', // 合同立项最新时间
      '201': '', // 收付款最新时间
      '301': '', // 许可证最新时间
      '401': '', // 物流最新时间
      '501': '', // 报关最新时间
      '601': '', // 退税最新时间
      '701': ''  // 完结最新时间
    },
    // 添加流程分组数据
    processGroups: {
      '101': [], // 合同立项
      '201': [], // 收付款
      '301': [], // 许可证
      '401': [], // 物流
      '501': [], // 报关
      '601': [], // 退税
      '701': []  // 完结
    },
    // 流程ID与流程类型的映射
    processTypeMap: {
      '101': '合同立项',
      '102': '合同立项',
      '103': '合同立项',
      '201': '收付款',
      '202': '收付款',
      '203': '收付款',
      '204': '收付款',
      '205': '收付款',
      '206': '收付款',
      '301': '许可证',
      '302': '许可证',
      '303': '许可证',
      '401': '物流',
      '402': '物流',
      '501': '报关',
      '502': '报关',
      '503': '报关',
      '504': '报关',
      '601': '退税',
      '602': '退税',
      '603': '退税',
      '701': '完结',
      '702': '完结'
    },
    // 默认流程按钮配置
    defaultProcessButtons: {
      '101': [
        {
          id: 'default-101-1',
          process_id: '101',
          process_txt: '上传合同附件',
          type: 'contract',
          special_action: 'contract' // 特殊操作，跳转到合同页面
        },
        {
          id: 'default-101-2',
          process_id: '101',
          process_txt: '更改合同附件',
          type: 'contract',
          special_action: 'contract' // 特殊操作，跳转到合同页面
        },
        {
          id: 'default-101-3',
          process_id: '101',
          process_txt: '变更记录',
          type: 'contract',
          special_action: 'record' // 特殊操作，跳转到记录页面
        }
      ],
      '201': [
        {
          id: 'default-201-1',
          process_id: '201',
          process_txt: '采购附件',
          type: 'payment'
        },
        {
          id: 'default-201-2',
          process_id: '201',
          process_txt: '履约保证金收款',
          type: 'payment'
        },
        {
          id: 'default-201-3',
          process_id: '201',
          process_txt: '销售回款',
          type: 'payment'
        },
        {
          id: 'default-201-4',
          process_id: '201',
          process_txt: '查看回款记录',
          type: 'payment'
        },
        {
          id: 'default-201-5',
          process_id: '201',
          process_txt: '采购付款',
          type: 'payment'
        },
        {
          id: 'default-201-6',
          process_id: '201',
          process_txt: '其它付款',
          type: 'payment'
        }
      ],
      '301': [
        {
          id: 'default-301-1',
          process_id: '301',
          process_txt: '许可证申领',
          type: 'license'
        },
        {
          id: 'default-301-2',
          process_id: '301',
          process_txt: '许可证转放',
          type: 'license'
        },
        {
          id: 'default-301-3',
          process_id: '301',
          process_txt: '二手车上户',
          type: 'license'
        }
      ],
      '401': [
        {
          id: 'default-401-1',
          process_id: '401',
          process_txt: '采购附件',
          type: 'logistics'
        },
        {
          id: 'default-401-2',
          process_id: '401',
          process_txt: '物流付款',
          type: 'logistics'
        }
      ],
      '501': [
        {
          id: 'default-501-1',
          process_id: '501',
          process_txt: '报关手续',
          type: 'customs'
        },
        {
          id: 'default-501-2',
          process_id: '501',
          process_txt: '报关费用',
          type: 'customs'
        }
      ],
      '601': [
        {
          id: 'default-601-1',
          process_id: '601',
          process_txt: '退税申请',
          type: 'taxRefund'
        },
        {
          id: 'default-601-2',
          process_id: '601',
          process_txt: '退税记录',
          type: 'taxRefund'
        }
      ],
      '701': [
        {
          id: 'default-701-1',
          process_id: '701',
          process_txt: '订单完结',
          type: 'completion'
        }
      ]
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取状态栏高度并设置
    this.setData({
      statusBarHeight: wx.getSystemInfoSync().statusBarHeight || 20,
      chewu_orderid: options.chewu_orderid || ''
    });

    // 如果有车务订单ID，则获取详情
    if (this.data.chewu_orderid) {
      this.getOrderDetail();
    } else {
      wx.showToast({
        title: '缺少订单ID',
        icon: 'none'
      });
    }
  },

  /**
   * 获取车务订单详情
   */
  getOrderDetail() {
    // 请求前先确保没有正在显示的加载提示
    try {
      wx.hideLoading();
    } catch (e) {
      // 忽略可能的错误
    }

    // 显示加载提示
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    // 使用变量跟踪请求状态
    let requestComplete = false;

    // 创建一个定时器确保最终一定会隐藏加载提示
    const loadingTimer = setTimeout(() => {
      if (!requestComplete) {
        try {
          wx.hideLoading();
        } catch (e) {
          // 忽略可能的错误
        }
      }
    }, 10000); // 10秒后强制隐藏

    // 调用API获取车务详情
    api.car.trainOperationDetail({
      chewu_orderid: this.data.chewu_orderid
    })
      .then(res => {
        console.log('车务详情数据:', res);

        let detailData = null;

        // 处理不同的数据格式
        if (res && res.data) {
          detailData = res.data;
        } else if (res && typeof res === 'object' && !res.code) {
          detailData = res;
        }

        if (detailData) {
          // 计算立项天数
          if (detailData.setup_time && detailData.close_time) {
            detailData.days = this.calculateDays(detailData.setup_time, detailData.close_time);
          }

          this.setData({
            orderDetail: detailData
          });

          // 处理流程数据
          if (detailData.process && detailData.process.length > 0) {
            this.processProcessData(detailData.process);
            this.checkAttachmentStatus(detailData.process);
          } else {
            // 即使没有流程数据，也初始化流程状态
            this.initializeProcessStatus();
          }
        } else {
          wx.showToast({
            title: '数据格式异常',
            icon: 'none'
          });

          // 初始化流程状态
          this.initializeProcessStatus();
        }
      })
      .catch(err => {
        console.error('获取车务详情失败:', err);

        // 尝试从错误中恢复
        if (typeof err === 'object' && err.data) {
          // 如果错误对象中包含数据，尝试使用它
          // 计算立项天数
          if (err.data.setup_time && err.data.close_time) {
            err.data.days = this.calculateDays(err.data.setup_time, err.data.close_time);
          }

          this.setData({
            orderDetail: err.data
          });

          // 处理流程数据
          if (err.data.process && err.data.process.length > 0) {
            this.processProcessData(err.data.process);
            this.checkAttachmentStatus(err.data.process);
          } else {
            // 初始化流程状态
            this.initializeProcessStatus();
          }

          console.log('从错误中恢复数据');
        } else if (typeof err === 'string' && err.includes('获取成功')) {
          // 从请求库错误中恢复，接口返回了"获取成功"但被当作错误
          console.log('从错误消息中检测到成功响应');

          // 这里不重新获取，避免无限循环
          wx.showToast({
            title: '数据已获取',
            icon: 'success'
          });

          // 初始化流程状态
          this.initializeProcessStatus();
        } else {
          wx.showToast({
            title: '获取详情失败',
            icon: 'none'
          });

          // 初始化流程状态
          this.initializeProcessStatus();
        }
      })
      .finally(() => {
        // 标记请求完成
        requestComplete = true;

        // 清除定时器
        clearTimeout(loadingTimer);

        // 确保隐藏加载提示
        try {
          wx.hideLoading();
        } catch (e) {
          console.error('隐藏加载提示失败:', e);
        }
      });
  },

  /**
   * 初始化流程状态
   */
  initializeProcessStatus() {
    const processStatus = {
      '101': 'in-progress', // 合同立项
      '201': 'pending', // 收付款
      '301': 'pending', // 许可证
      '401': 'pending', // 物流
      '501': 'pending', // 报关
      '601': 'pending', // 退税
      '701': 'pending'  // 完结
    };

    // 检查附件状态，如果有数据，则根据附件状态设置流程状态
    if (this.data.attachmentStatus && Object.keys(this.data.attachmentStatus).length > 0) {
      console.log('初始化流程状态时发现附件状态数据，将根据附件状态设置流程状态');

      // 调用calculateProcessStatus来计算状态
      this.calculateProcessStatus();
      return;
    }

    this.setData({ processStatus });
    console.log('已初始化流程状态:', processStatus);
  },

  /**
   * 计算从立项时间到结项时间的天数差
   * @param {string} setupTimeStr - 立项时间字符串，格式为"YYYY-MM-DD HH:mm:ss"
   * @param {string} closeTimeStr - 结项时间字符串，格式为"YYYY-MM-DD HH:mm:ss"
   * @returns {string} - 天数差，例如"31天"
   */
  calculateDays(setupTimeStr, closeTimeStr) {
    if (!setupTimeStr || !closeTimeStr) return '--';

    try {
      // 将日期字符串转换为日期对象
      // 注意处理iOS兼容性问题，替换"-"为"/"
      const setupTime = new Date(setupTimeStr.replace(/-/g, '/'));
      const closeTime = new Date(closeTimeStr.replace(/-/g, '/'));

      // 计算时间差（毫秒）
      const timeDiff = Math.abs(closeTime.getTime() - setupTime.getTime());

      // 转换为天数（向下取整）
      const daysDiff = Math.floor(timeDiff / (1000 * 3600 * 24));

      return daysDiff + '天';
    } catch (err) {
      console.error('计算天数差出错:', err, '立项时间:', setupTimeStr, '结项时间:', closeTimeStr);
      return '--';
    }
  },

  /**
   * 返回上一页
   */
  navigateBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 跳转到上传合同页面
   */
  navigateToContract() {
    wx.navigateTo({
      url: `/pages/train_operation/contract?chewu_orderid=${this.data.chewu_orderid || ''}`
    });
  },

  /**
   * 跳转到变更记录页面
   */
  navigateToRecord() {
    wx.navigateTo({
      url: `/pages/train_operation/record?chewu_orderid=${this.data.chewu_orderid || ''}`
    });
  },

  /**
   * 跳转到附录页面
   * @param {Object} e 事件对象
   */
  navigateToAppendix(e) {
    const type = e.currentTarget.dataset.type; // 获取按钮类型
    const name = e.currentTarget.dataset.name; // 获取按钮名称
    const processId = e.currentTarget.dataset.processId || ''; // 获取流程ID
    const id = e.currentTarget.dataset.id || ''; // 获取记录ID

    let url = `/pages/train_operation/appendix?type=${type}&name=${name}`;

    // 添加额外参数
    if (processId) {
      url += `&process_id=${processId}`;
    }

    if (id) {
      url += `&id=${id}`;
    }

    // 添加订单ID
    if (this.data.chewu_orderid) {
      url += `&chewu_orderid=${this.data.chewu_orderid}`;
    }

    wx.navigateTo({
      url: url
    });
  },

  /**
   * 切换标签
   */
  switchTab(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      currentTab: parseInt(index)
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    // 打印当前页面状态
    console.log('==================== 页面初次渲染完成 ====================');
    console.log('当前processStatus:', JSON.stringify(this.data.processStatus));
    console.log('当前processGroups:', JSON.stringify(this.data.processGroups));
    console.log('当前attachmentStatus:', JSON.stringify(this.data.attachmentStatus));
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('==================== 页面显示 ====================');
    console.log('当前processStatus:', JSON.stringify(this.data.processStatus));

    // 如果已经有附件状态数据，则重新计算流程状态
    if (this.data.attachmentStatus && Object.keys(this.data.attachmentStatus).length > 0) {
      console.log('页面显示时重新计算流程状态');

      // 强制设置收付款状态为进行中（如果合同立项已完成）
      if (this.data.processStatus && this.data.processStatus['101'] === 'completed') {
        const newProcessStatus = { ...this.data.processStatus };

        // 如果收付款是待处理状态，则改为进行中
        if (newProcessStatus['201'] === 'pending') {
          newProcessStatus['201'] = 'in-progress';
          console.log('强制设置收付款状态为进行中（因为合同立项已完成）');

          this.setData({ processStatus: newProcessStatus }, () => {
            console.log('更新后的processStatus:', JSON.stringify(this.data.processStatus));
          });
        }
      } else {
        // 重新计算所有流程状态
        this.calculateProcessStatus();
      }
    } else {
      // 即使没有附件状态数据，也确保流程状态正确
      this.fixProcessStatus();
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 检查附件上传状态 - 彻底简化版
   * @param {Array} processArray - 流程数组
   */
  checkAttachmentStatus(processArray) {
    // 提取流程ID
    const processIds = processArray.map(item => item.id);

    if (processIds.length === 0) {
      console.log('没有流程ID，跳过附件状态检查');
      this.initializeProcessStatus();
      return;
    }

    console.log('==================== 开始检查附件状态 ====================');
    console.log('流程IDs:', JSON.stringify(processIds));
    console.log('流程ID类型示例:', typeof processIds[0]);

    // 调用API检查附件状态
    api.car.trainOperationAttachment({
      ids: processIds
    })
      .then(res => {
        console.log('附件状态检查结果:', JSON.stringify(res));

        // 不管API返回什么格式，只要能用就行
        let attachmentData = null;

        if (res && typeof res === 'object') {
          // 如果直接返回附件状态对象（如{305: {status: 1}, 306: {status: 0}}）
          if (Object.values(res).some(item => item && typeof item === 'object' && 'status' in item)) {
            attachmentData = res;
            console.log('检测到直接返回的附件状态对象');
          }
          // 如果返回标准格式（如{code: 1, data: {305: {status: 1}}}）
          else if (res.code === 1 && res.data) {
            attachmentData = res.data;
            console.log('检测到标准格式的附件状态对象');
          }
        }

        if (attachmentData) {
          // 打印附件状态详情
          console.log('有效的附件状态数据:', JSON.stringify(attachmentData));
          console.log('附件状态数据类型:', typeof attachmentData);
          console.log('附件状态数据键列表:', Object.keys(attachmentData));

          // 检查键的类型
          const firstKey = Object.keys(attachmentData)[0];
          console.log('第一个键:', firstKey, '类型:', typeof firstKey);

          // 打印每个附件状态
          Object.entries(attachmentData).forEach(([id, status]) => {
            console.log(`附件[${id}](${typeof id})状态:`, JSON.stringify(status));
          });

          // 更新状态
          this.setData({
            attachmentStatus: attachmentData
          }, () => {
            console.log('附件状态已更新到this.data.attachmentStatus');

            // 打印更新后的attachmentStatus
            console.log('更新后的attachmentStatus:', JSON.stringify(this.data.attachmentStatus));

            // 计算流程状态
            this.calculateProcessStatus();
          });
        } else {
          console.warn('无法解析附件状态数据:', JSON.stringify(res));
          this.initializeProcessStatus();
        }
      })
      .catch(err => {
        console.error('附件状态检查失败:', err);
        this.initializeProcessStatus();
      });
  },

  /**
   * 计算每个流程组的状态 - 增强版
   */
  calculateProcessStatus() {
    const { processGroups, attachmentStatus } = this.data;
    const processStatus = {};
    // 初始化时间戳对象
    const processTimestamps = {
      '101': '', // 合同立项最新时间
      '201': '', // 收付款最新时间
      '301': '', // 许可证最新时间
      '401': '', // 物流最新时间
      '501': '', // 报关最新时间
      '601': '', // 退税最新时间
      '701': ''  // 完结最新时间
    };

    console.log('==================== 开始计算流程状态 ====================');
    console.log('附件状态数据完整对象:', JSON.stringify(attachmentStatus));
    console.log('附件状态数据类型:', typeof attachmentStatus);
    console.log('附件状态数据键列表:', Object.keys(attachmentStatus));
    console.log('流程组数据完整对象:', JSON.stringify(processGroups));

    // 检查attachmentStatus是否有数据
    if (!attachmentStatus || Object.keys(attachmentStatus).length === 0) {
      console.warn('没有附件状态数据，无法计算流程状态，使用默认状态');
      this.initializeProcessStatus();
      return;
    }

    // 创建附件状态的字符串键映射，确保所有键都是字符串
    const stringKeyAttachmentStatus = {};
    Object.keys(attachmentStatus).forEach(key => {
      stringKeyAttachmentStatus[String(key)] = attachmentStatus[key];
      console.log(`附件状态映射: 键[${key}](${typeof key}) -> 字符串键[${String(key)}], 状态:${attachmentStatus[key].status}`);
    });

    console.log('创建了字符串键映射:', Object.keys(stringKeyAttachmentStatus));

    // 遍历所有流程组
    Object.keys(processGroups).forEach(groupKey => {
      const buttons = processGroups[groupKey];
      console.log(`\n处理流程组[${groupKey}], 有${buttons ? buttons.length : 0}个按钮`);

      if (!buttons || buttons.length === 0) {
        processStatus[groupKey] = 'pending';
        console.log(`流程组[${groupKey}]没有按钮，设置为待处理状态`);
        return;
      }

      // 检查每个按钮的附件状态
      let completedCount = 0;
      let totalCount = buttons.length;
      let hasAnyAttachment = false;
      let buttonDetails = [];
      let latestTimestamp = null; // 用于跟踪最新时间戳

      buttons.forEach(button => {
        // 获取按钮ID并转换为字符串
        const buttonIdStr = String(button.id);

        // 直接在原始attachmentStatus中查找，不使用映射
        let status = null;
        let found = false;

        // 尝试多种方式查找附件状态
        if (buttonIdStr in attachmentStatus) {
          status = attachmentStatus[buttonIdStr];
          found = true;
          console.log(`  找到附件状态(字符串键直接匹配): ${buttonIdStr} -> ${JSON.stringify(status)}`);
        }
        else if (button.id in attachmentStatus) {
          status = attachmentStatus[button.id];
          found = true;
          console.log(`  找到附件状态(数字键直接匹配): ${button.id} -> ${JSON.stringify(status)}`);
        }
        else {
          // 遍历所有键，尝试找到匹配的
          Object.keys(attachmentStatus).forEach(key => {
            if (String(key) === buttonIdStr) {
              status = attachmentStatus[key];
              found = true;
              console.log(`  找到附件状态(遍历匹配): ${key} -> ${buttonIdStr} -> ${JSON.stringify(status)}`);
            }
          });
        }

        if (!found) {
          console.log(`  未找到按钮ID=${button.id}的附件状态`);
        }

        // 判断是否完成
        const isCompleted = status && status.status === 1;

        // 更新最新时间戳
        if (isCompleted && status.update_time) {
          if (!latestTimestamp || new Date(status.update_time) > new Date(latestTimestamp)) {
            latestTimestamp = status.update_time;
            console.log(`  更新流程组[${groupKey}]最新时间戳为: ${latestTimestamp}`);
          }
        }

        console.log(`检查按钮: ID=${button.id}, 字符串ID=${buttonIdStr}, 文本=${button.process_txt}`);
        console.log(`  对应附件状态: ${status ? JSON.stringify(status) : '无数据'}`);
        console.log(`  是否已完成: ${isCompleted}`);

        // 收集按钮状态详情，用于打印日志
        buttonDetails.push({
          id: button.id,
          idStr: buttonIdStr,
          text: button.process_txt,
          found: found,
          status: isCompleted ? '已完成' : '未完成',
          attachmentData: status || '无数据'
        });

        if (isCompleted) {
          completedCount++;
          hasAnyAttachment = true;
          console.log(`  按钮[${button.process_txt}]已完成，completedCount=${completedCount}`);
        } else {
          console.log(`  按钮[${button.process_txt}]未完成，completedCount=${completedCount}`);
        }
      });

      // 保存最新时间戳
      if (latestTimestamp) {
        processTimestamps[groupKey] = latestTimestamp;
        console.log(`设置流程组[${groupKey}]的最终时间戳为: ${latestTimestamp}`);
      }

      // 打印详细的按钮状态日志
      console.log(`流程组[${groupKey}]按钮状态详情:`, JSON.stringify(buttonDetails));
      console.log(`流程组[${groupKey}]总计${totalCount}个按钮，已完成${completedCount}个`);

      // 合同立项特殊处理：只要有一个按钮对应的附件状态为1，就是完成状态
      if (groupKey === '101') {
        processStatus[groupKey] = hasAnyAttachment ? 'completed' : 'in-progress';
        console.log(`流程组[${groupKey}]是合同立项，${hasAnyAttachment ? '有' : '没有'}上传附件，状态为${processStatus[groupKey]}`);
      } else {
        // 其他流程组：所有按钮都已上传附件才是完成状态，部分上传是进行中，全部未上传是待处理
        if (completedCount === totalCount && totalCount > 0) {
          processStatus[groupKey] = 'completed';
        } else if (completedCount > 0) {
          processStatus[groupKey] = 'in-progress';
        } else {
          processStatus[groupKey] = 'pending';
        }
        console.log(`流程组[${groupKey}]状态计算结果:${processStatus[groupKey]} (completedCount=${completedCount}, totalCount=${totalCount})`);
      }

      // 添加流程组之间的依赖关系
      // 如果前一个流程组已完成，但当前流程组是待处理，则将当前流程组设为进行中
      if (groupKey === '201' && processStatus['101'] === 'completed' && processStatus['201'] === 'pending') {
        processStatus['201'] = 'in-progress';
        console.log(`由于合同立项已完成，将收付款状态从待处理改为进行中`);
      }
      else if (groupKey === '301' && processStatus['201'] === 'completed' && processStatus['301'] === 'pending') {
        processStatus['301'] = 'in-progress';
        console.log(`由于收付款已完成，将许可证状态从待处理改为进行中`);
      }
      else if (groupKey === '401' && processStatus['301'] === 'completed' && processStatus['401'] === 'pending') {
        processStatus['401'] = 'in-progress';
        console.log(`由于许可证已完成，将物流状态从待处理改为进行中`);
      }
      else if (groupKey === '501' && processStatus['401'] === 'completed' && processStatus['501'] === 'pending') {
        processStatus['501'] = 'in-progress';
        console.log(`由于物流已完成，将报关状态从待处理改为进行中`);
      }
      else if (groupKey === '601' && processStatus['501'] === 'completed' && processStatus['601'] === 'pending') {
        processStatus['601'] = 'in-progress';
        console.log(`由于报关已完成，将退税状态从待处理改为进行中`);
      }
      else if (groupKey === '701' && processStatus['601'] === 'completed' && processStatus['701'] === 'pending') {
        processStatus['701'] = 'in-progress';
        console.log(`由于退税已完成，将完结状态从待处理改为进行中`);
      }
    });

    // 再次检查流程组之间的依赖关系（确保不受遍历顺序影响）
    if (processStatus['101'] === 'completed' && processStatus['201'] === 'pending') {
      processStatus['201'] = 'in-progress';
      console.log(`最终检查：由于合同立项已完成，将收付款状态从待处理改为进行中`);
    }

    console.log('流程状态计算最终结果:', JSON.stringify(processStatus));
    console.log('流程时间戳最终结果:', JSON.stringify(processTimestamps));
    console.log('==================== 流程状态计算结束 ====================');

    // 更新状态
    this.setData({
      processStatus,
      processTimestamps
    }, () => {
      // 在状态更新后，打印最终的processStatus
      console.log('实际设置到页面的processStatus:', JSON.stringify(this.data.processStatus));
      console.log('实际设置到页面的processTimestamps:', JSON.stringify(this.data.processTimestamps));

      // 最后一道保险：调用fixProcessStatus方法确保流程状态正确
      setTimeout(() => {
        this.fixProcessStatus();
      }, 100);
    });
  },

  /**
   * 处理流程数据，按流程类型分组
   * @param {Array} processArray - 流程数组
   */
  processProcessData(processArray) {
    console.log('==================== 开始处理流程数据 ====================');

    // 创建临时分组对象，使用默认流程按钮作为初始值
    const groups = {
      '101': [...this.data.defaultProcessButtons['101']], // 合同立项
      '201': [...this.data.defaultProcessButtons['201']], // 收付款
      '301': [...this.data.defaultProcessButtons['301']], // 许可证
      '401': [...this.data.defaultProcessButtons['401']], // 物流
      '501': [...this.data.defaultProcessButtons['501']], // 报关
      '601': [...this.data.defaultProcessButtons['601']], // 退税
      '701': [...this.data.defaultProcessButtons['701']]  // 完结
    };

    // 创建执行人数据对象
    const executors = {
      '101': '', // 合同立项执行人
      '201': '', // 收付款执行人
      '301': '', // 许可证执行人
      '401': '', // 物流执行人
      '501': '', // 报关执行人
      '601': '', // 退税执行人
      '701': ''  // 完结执行人
    };

    if (!processArray || !Array.isArray(processArray) || processArray.length === 0) {
      console.log('没有流程数据，使用默认配置');

      // 打印默认按钮配置的ID格式
      Object.keys(groups).forEach(key => {
        groups[key].forEach(button => {
          console.log(`默认配置按钮[${key}][${button.process_txt}]的ID:${button.id}, 类型:${typeof button.id}`);
        });
      });

      // 更新状态使用默认配置
      this.setData({
        processGroups: groups,
        processExecutors: executors
      }, () => {
        console.log('已使用默认配置更新processGroups和processExecutors');
      });
      return;
    }

    console.log('API返回的流程数据:', JSON.stringify(processArray));

    // 打印API返回的流程数据中的ID格式
    processArray.forEach(item => {
      console.log(`API返回流程项[${item.process_txt}]的ID:${item.id}, 类型:${typeof item.id}, process_id:${item.process_id}`);
    });

    // 清空临时分组，我们将使用API返回的数据
    Object.keys(groups).forEach(key => {
      groups[key] = [];
    });

    // 根据process_id的前三位数字进行分组，同时提取执行人信息
    processArray.forEach(item => {
      const processType = item.process_id.toString().substring(0, 3);
      const groupKey = processType.substring(0, 1) + '01'; // 取第一位数字加'01'作为组KEY

      console.log(`处理流程项: ID=${item.id}, process_id=${item.process_id}, 文本=${item.process_txt}`);
      console.log(`  解析得到: processType=${processType}, groupKey=${groupKey}`);

      // 添加special_action字段，根据按钮文本判断
      // 使用更宽泛的匹配条件：只要文本中包含"合同附件"关键词就认为是合同相关操作
      if (item.process_txt && item.process_txt.includes('合同附件')) {
        item.special_action = 'contract';
        console.log(`  添加special_action=contract (包含"合同附件"关键词)`);
      } else if (item.process_txt === '变更记录') {
        item.special_action = 'record';
        console.log(`  添加special_action=record (文本为"变更记录")`);
      }

      // 确保item.id字段存在，这对应着附件状态中的key
      if (!item.id && item.id !== 0) {
        console.warn(`流程项目[${item.process_txt}]没有id字段，这可能导致附件状态检查失败`);
      }

      // 提取执行人名称
      if (item.zhixing_by_name && groupKey in executors && !executors[groupKey]) {
        executors[groupKey] = item.zhixing_by_name;
        console.log(`  提取执行人名称: ${item.zhixing_by_name} 到分组[${groupKey}]`);
      }

      if (groups[groupKey]) {
        groups[groupKey].push(item);
        console.log(`  添加到分组[${groupKey}]`);
      } else {
        console.warn(`未知的流程类型: ${processType}, 无法添加到分组`);
      }
    });

    // 如果API返回的某个分组为空，使用默认按钮配置
    Object.keys(groups).forEach(key => {
      if (groups[key].length === 0) {
        console.log(`分组 ${key} 没有数据，使用默认配置`);
        groups[key] = [...this.data.defaultProcessButtons[key]];
      }
    });

    // 打印每个分组中按钮的ID，用于调试
    Object.keys(groups).forEach(key => {
      console.log(`\n分组[${key}]包含以下按钮:`);
      groups[key].forEach(button => {
        console.log(`  按钮[${button.process_txt}]的ID:${button.id}, 类型:${typeof button.id}`);
      });
    });

    console.log('流程数据分组最终结果:', JSON.stringify(groups));
    console.log('流程执行人最终结果:', JSON.stringify(executors));
    console.log('==================== 流程数据处理结束 ====================');

    // 更新状态
    this.setData({
      processGroups: groups,
      processExecutors: executors
    }, () => {
      console.log('已更新processGroups和processExecutors到页面');
    });
  },

  /**
   * 处理按钮点击事件，根据按钮的special_action属性或按钮文本决定跳转到哪个页面
   * @param {Object} e 事件对象
   */
  handleButtonClick(e) {
    const specialAction = e.currentTarget.dataset.specialAction;
    const buttonText = e.currentTarget.dataset.name; // 获取按钮文本
    const processId = e.currentTarget.dataset.processId || ''; // 获取流程ID
    const id = e.currentTarget.dataset.id || ''; // 获取记录ID

    // 检查如果是"上传合同附件"按钮，且合同立项已完成，则不执行任何操作
    if (buttonText === '上传合同附件' && this.data.processStatus['101'] === 'completed') {
      console.log('合同附件已上传，按钮已禁用');
      return;
    }

    // 特殊处理"变更合同附件"按钮：使用"上传合同附件"按钮的ID
    if (buttonText === '更改合同附件' || buttonText === '变更合同附件') {
      console.log('点击了变更合同附件按钮，查找上传合同附件按钮的ID');
      // 查找流程组101中的"上传合同附件"按钮
      const uploadContractButtons = this.data.processGroups['101'].filter(btn =>
        btn.process_txt === '上传合同附件'
      );

      if (uploadContractButtons && uploadContractButtons.length > 0) {
        const uploadContractButton = uploadContractButtons[0];
        console.log(`找到上传合同附件按钮，ID: ${uploadContractButton.id}`);
        // 使用上传合同附件按钮的ID
        this.navigateToContractWithId(uploadContractButton.id);
        return;
      } else {
        console.log('未找到上传合同附件按钮，使用当前按钮的ID');
      }
    }

    // 根据special_action或按钮文本判断跳转目标
    // 使用更宽泛的匹配条件：只要文本中包含"合同附件"关键词就跳转到合同页面
    if (specialAction === 'contract' || (buttonText && buttonText.includes('合同附件'))) {
      // 跳转到合同页面，添加process_id参数
      this.navigateToContractWithId(id || processId);
    } else if (specialAction === 'record' || buttonText === '变更记录') {
      // 跳转到变更记录页面
      this.navigateToRecord();
    } else {
      // 默认跳转到附件页面
      this.navigateToAppendix(e);
    }
  },

  /**
   * 跳转到上传合同页面（带ID参数）
   * @param {string} processId 流程ID
   */
  navigateToContractWithId(processId) {
    wx.navigateTo({
      url: `/pages/train_operation/contract?chewu_orderid=${this.data.chewu_orderid || ''}&process_id=${processId || ''}`
    });
  },

  /**
   * 修复流程状态，确保流程组之间的依赖关系正确
   */
  fixProcessStatus() {
    console.log('==================== 修复流程状态 ====================');

    // 获取当前流程状态
    const processStatus = { ...this.data.processStatus };
    let hasChanges = false;

    // 确保合同立项状态不会是'pending'
    if (processStatus['101'] === 'pending') {
      processStatus['101'] = 'in-progress';
      console.log('修复：合同立项不应为待处理状态，已改为进行中');
      hasChanges = true;
    }

    // 检查并修复流程组之间的依赖关系
    if (processStatus['101'] === 'completed' && processStatus['201'] === 'pending') {
      processStatus['201'] = 'in-progress';
      console.log('修复：由于合同立项已完成，将收付款状态从待处理改为进行中');
      hasChanges = true;
    }

    if (processStatus['201'] === 'completed' && processStatus['301'] === 'pending') {
      processStatus['301'] = 'in-progress';
      console.log('修复：由于收付款已完成，将许可证状态从待处理改为进行中');
      hasChanges = true;
    }

    if (processStatus['301'] === 'completed' && processStatus['401'] === 'pending') {
      processStatus['401'] = 'in-progress';
      console.log('修复：由于许可证已完成，将物流状态从待处理改为进行中');
      hasChanges = true;
    }

    if (processStatus['401'] === 'completed' && processStatus['501'] === 'pending') {
      processStatus['501'] = 'in-progress';
      console.log('修复：由于物流已完成，将报关状态从待处理改为进行中');
      hasChanges = true;
    }

    if (processStatus['501'] === 'completed' && processStatus['601'] === 'pending') {
      processStatus['601'] = 'in-progress';
      console.log('修复：由于报关已完成，将退税状态从待处理改为进行中');
      hasChanges = true;
    }

    if (processStatus['601'] === 'completed' && processStatus['701'] === 'pending') {
      processStatus['701'] = 'in-progress';
      console.log('修复：由于退税已完成，将完结状态从待处理改为进行中');
      hasChanges = true;
    }

    // 如果有修改，则更新状态
    if (hasChanges) {
      this.setData({ processStatus }, () => {
        console.log('修复后的processStatus:', JSON.stringify(this.data.processStatus));
      });
      return true;
    }

    console.log('流程状态无需修复');
    return false;
  },
})