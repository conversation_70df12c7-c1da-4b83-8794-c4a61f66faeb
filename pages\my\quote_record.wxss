.container {
  min-height: 100vh;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  padding: 0 0 120rpx 0;
  margin: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.page-header {
  background-color: #fff;
  padding: 0 30rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  position: relative;
  border-bottom: 1rpx solid #eee;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
}

/* 自定义导航栏样式 */
.custom-nav {
  width: 100%;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}

.status-bar {
  width: 100%;
}

.nav-content {
  height: 44px;
  display: flex;
  align-items: center;
  position: relative;
}

.back-icon {
  position: absolute;
  left: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx;
}

.back-icon image {
  width: 40rpx;
  height: 40rpx;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.arrow-left {
  width: 20rpx;
  height: 20rpx;
  border-left: 3rpx solid #333;
  border-bottom: 3rpx solid #333;
  transform: rotate(45deg);
}

.page-title {
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
  pointer-events: none;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 0;
  height: calc(100vh - 300rpx);
  width: 100%;
}

.loading-spinner {
  width: 70rpx;
  height: 70rpx;
  border: 5rpx solid rgba(64, 128, 255, 0.1);
  border-top: 5rpx solid #4080ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 内容区域 */
.content-container {
  animation: fadeIn 0.3s ease-in-out;
  padding-top: 120rpx;
  width: 100%;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.section-title {
  padding: 30rpx 30rpx 20rpx;
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
  position: relative;
}

/* 第一个标题特殊处理 */
.first-section {
  margin-top: 50rpx;
}

.quote-card {
  background-color: #fff;
  border-radius: 0;
  margin: 0 15rpx 20rpx;
  width: 88%;
  overflow: hidden;
  padding: 0 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border-radius: 16rpx;
}

.car-title {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
  padding: 30rpx 0 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.quote-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 26rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  margin: 0;
}

.quote-item:last-child {
  border-bottom: none;
}

.item-label {
  font-size: 28rpx;
  color: #333;
  flex: 2;
  padding-right: 20rpx;
}

.item-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  text-align: right;
  flex: 1;
}

.share-button {
  position: fixed;
  bottom: 30rpx;
  left: 5%;
  width: 90%;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  background: linear-gradient(135deg, #4080ff, #5091ff);
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(64, 128, 255, 0.3);
  transition: all 0.2s ease;
}

.share-button:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(64, 128, 255, 0.2);
}

/* 分享Canvas样式 */
.share-canvas {
  position: fixed;
  left: -9999px;
  width: 100%;
  height: 100%;
  opacity: 0;
  z-index: -1;
}
