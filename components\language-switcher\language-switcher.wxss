/* components/language-switcher/language-switcher.wxss */
.lang-switcher{
  position: relative;
  display: inline-block;
  z-index: 1000;
  min-width: 100rpx;
}

.lang-switcher.top-left{
  position: fixed;
  top: 110rpx;
  left: 100rpx;
}

.lang-switcher.normal .lang-trigger{
  padding: 10rpx 12rpx;
  font-size: 22rpx;
  background-color: white;
  border-radius: 24rpx;
  display: flex;
}

.lang-trigger .lang-flag{
  font-size: 15rpx;
  align-items: center;
  line-height: 32rpx;
  height: 32rpx;
  padding-right: 15rpx;
}

.dropdown-arrow{
  margin-left: 8rpx;
  font-size: 20rpx;
  color: #666;
  transition: transform 0.3s ease;
}

.dropdown-arrow.down{
  transform: rotate(180deg);
}

.dropdown-menu{
  /* position: absolute; */
  top: 100%;
  right: 0;
  margin-top: 8rpx;
  background:white;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  min-width: 120rpx;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10rpx);
  transition: all 0.3s ease;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-item.active{
  color: #3B82F6;
}

.dropdown-item .lang-flag{
  font-size: 15rpx;
  line-height: 30rpx;
  height: 30rpx;
  padding-right: 15rpx;
  padding-left: 8rpx;
}

.dropdown-item .lang-name{
  font-size: 22rpx;
  line-height: 32rpx;
  height: 32rpx;
  padding-right: 10rpx;
}

.lang-check{
  font-size: 26rpx;
}