<view class="result-container">
  <!-- 头部导航栏 -->
  <view class="nav-bar">
    <view class="nav-back" bindtap="navigateBack">
      <text class="back-icon">←</text>
    </view>
    <view class="nav-title">评估结果</view>
  </view>

  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <van-loading size="24px" color="#ffc62c">加载中...</van-loading>
  </view>

  <!-- 出错提示 -->
  <view class="error-container" wx:elif="{{error}}">
    <view class="error-text">获取评估结果失败</view>
    <view class="error-btn" bindtap="loadEvaluateResult">重新加载</view>
  </view>

  <!-- 评估结果展示 -->
  <block wx:elif="{{result}}">
    <!-- 结果卡片 -->
    <view class="result-card">
      <view class="card-title">车辆估价</view>
      
      <view class="price-area">
        <view class="price-range">
          <text class="price-value">{{result.minPrice}}-{{result.maxPrice}}</text>
          <text class="price-unit">万元</text>
        </view>
        <view class="price-desc">评估价格区间（仅供参考）</view>
      </view>
      
      <view class="car-info">
        <view class="info-item">
          <text class="item-label">品牌车型</text>
          <text class="item-value">{{carInfo.brand}} {{carInfo.series}}</text>
        </view>
        <view class="info-item">
          <text class="item-label">具体车型</text>
          <text class="item-value">{{carInfo.year}} {{carInfo.model}}</text>
        </view>
        <view class="info-item">
          <text class="item-label">行驶里程</text>
          <text class="item-value">{{carInfo.mileage}}</text>
        </view>
        <view class="info-item">
          <text class="item-label">上牌时间</text>
          <text class="item-value">{{carInfo.licensedDate}}</text>
        </view>
      </view>

      <view class="action-btns">
        <button class="sell-btn" bindtap="sellCar">立即卖车</button>
        <button class="share-btn" bindtap="shareResult">分享结果</button>
      </view>
    </view>

    <!-- 经销商报价 -->
    <view class="dealer-section">
      <view class="section-title">推荐商家 ({{dealerList.length}})</view>
      
      <view class="dealer-list">
        <view class="dealer-item" wx:for="{{dealerList}}" wx:key="id">
          <view class="dealer-info">
            <image class="dealer-logo" src="{{item.logo}}" mode="aspectFit"></image>
            <view class="dealer-detail">
              <view class="dealer-name">{{item.name}}</view>
              <view class="dealer-address">{{item.address}}</view>
            </view>
          </view>
          <view class="contact-btn" bindtap="contactDealer" data-id="{{item.id}}">联系商家</view>
        </view>
      </view>
    </view>
  </block>

  <!-- 无数据展示 -->
  <view class="no-data" wx:else>
    <image src="/static/images/no-data.png" mode="aspectFit"></image>
    <text>暂无评估数据</text>
  </view>
</view> 