<!-- 意见反馈页面 -->
<view class="feedback-container">
  <!-- 自定义导航栏 -->
  <view class="custom-nav" style="padding-top: {{statusBarHeight}}px;">
    <view class="nav-content">
      <view class="back-icon" bindtap="navigateBack">
        <image src="/icons/moments/back.svg" mode="aspectFit"></image>
      </view>
      <view class="nav-title">意见反馈</view>
    </view>
  </view>

  <!-- 表单内容 -->
  <view class="feedback-form">
    <!-- 问题类型 -->
    <view class="form-item">
      <view class="form-label required">反馈问题类型</view>
      <view class="type-options">
        <view class="type-option {{selectedType === '1' ? 'selected' : ''}}" bindtap="selectType" data-type="1">
          卡顿/闪退/加载慢
        </view>
        <view class="type-option {{selectedType === '2' ? 'selected' : ''}}" bindtap="selectType" data-type="2">
          操作体验
        </view>
        <view class="type-option {{selectedType === '3' ? 'selected' : ''}}" bindtap="selectType" data-type="3">
          功能异常
        </view>
        <view class="type-option {{selectedType === '4' ? 'selected' : ''}}" bindtap="selectType" data-type="4">
          新功能建议
        </view>
        <view class="type-option {{selectedType === '5' ? 'selected' : ''}}" bindtap="selectType" data-type="5">
          功能BUG
        </view>
        <view class="type-option {{selectedType === '6' ? 'selected' : ''}}" bindtap="selectType" data-type="6">
          其他
        </view>
      </view>
    </view>

    <!-- 问题描述 -->
    <view class="form-item">
      <view class="form-label required">问题描述</view>
      <view class="textarea-container">
        <textarea 
          class="feedback-textarea"
          placeholder-class="feedback-placeholder"
          placeholder="请您尽可能的详细描述所遇到的问题，我们将记录并安排改进选代。非常感谢您的反馈！" 
          value="{{description}}" 
          bindinput="inputDescription"
          maxlength="500"
        ></textarea>
        <view class="word-count">{{descriptionLength || 0}}/500</view>
      </view>
    </view>

    <!-- 图片上传 -->
    <view class="form-item">
      <view class="form-label">图片说明 <text class="upload-hint">(最多上传3张)</text></view>
      <view class="upload-container">
        <view class="upload-item" wx:for="{{uploadImages}}" wx:key="index">
          <image class="upload-image" src="{{item}}" mode="aspectFill"></image>
          <view class="delete-icon" catchtap="deleteImage" data-index="{{index}}">×</view>
        </view>
        <view class="upload-btn" bindtap="chooseImage" wx:if="{{uploadImages.length < 3}}">
          <image class="upload-icon" src="/icons/moments/poto.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <!-- 联系方式 -->
    <view class="form-item">
      <view class="form-label">联系方式</view>
      <input 
        class="contact-input" 
        placeholder="手机号/微信/邮箱/QQ" 
        value="{{contact}}" 
        bindinput="inputContact"
      />
      <view class="contact-hint">请填写您的联系方式，以便我们给您回复，谢谢！！</view>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-btn-container">
    <button class="submit-btn" bindtap="submitFeedback">提交反馈</button>
  </view>
</view>