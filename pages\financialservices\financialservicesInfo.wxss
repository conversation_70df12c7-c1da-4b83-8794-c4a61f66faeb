/* 引入登录弹窗样式 */
@import "../../templates/loginPopup/loginPopup.wxss";

/* 全局溢出控制 - 防止横向滚动 */
page {
  overflow-x: hidden;
  width: 100%;
  box-sizing: border-box;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
}

/* 容器样式 */
.container {
  padding: 0;
  background-color: transparent;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
  position: relative;
  padding-bottom: calc(120rpx + constant(safe-area-inset-bottom));
  /* iOS 11.0 */
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
  /* iOS 11.2+ */
  /* 为底部按钮留出空间 */
}

/* 自定义导航栏 */
.custom-nav {
  width: 100%;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  /* 移除可能存在的阴影和边框 */
  box-shadow: none;
  border-bottom: none;
}

.status-bar {
  width: 100%;
}

.nav-title {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15rpx;
  position: relative;
}

/* 返回按钮 */
.nav-back {
  width: 60rpx;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

/* 标题文本 */
.title-text {
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 34rpx;
  font-weight: 500;
  color: #333;
}

/* 占位元素 - 保持布局平衡 */
.nav-placeholder {
  width: 60rpx;
  height: 44px;
}

/* 内容容器 */
.content-container {
  padding-top: calc(44px + var(--status-bar-height, 20px) - 1px);
  /* 减少1px避免可能的间隙 */
  /* 动态计算导航栏高度以精确对接 */
  width: 100%;
  background: transparent;
  /* 确保内容容器背景透明 */
  position: relative;
  z-index: 1;
}

/* 添加导航栏和内容之间的连接区域 */
.content-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 10px;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
  z-index: 0;
}

/* 服务内容区域 */
.service-content {
  padding: 10rpx;
  padding-top: 15rpx;
  /* 增加顶部内边距 */
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  position: relative;
  z-index: 1;
}

/* 金融服务卡片 */
.service-card {
  width: 100%;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  background-color: #fff;
  position: relative;
  z-index: 2;
}

/* 金融服务卡片头部 */
.financial-card .card-image-container {
  background-color: rgba(255, 237, 209, 0.8);
  height: 240rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

/* 卡片图片 */
.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 16rpx;
}

/* 信息卡片 */
.info-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx 0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 信息内容 */
.info-inside {
  padding: 0 20rpx;
}

/* 信息行 */
.info-inside-row {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 28rpx;
}

/* 信息行标题 */
.info-inside-row-title {
  width: 180rpx;
  color: #666;
  flex-shrink: 0;
}

/* 信息行内容 */
.info-inside-row-content {
  flex: 1;
  color: #333;
}

/* 联系信息卡片 */
.contact-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 联系信息外部标题样式 */
.section-header {
  display: flex;
  align-items: center;
  margin: 20rpx 0;
  padding: 0 10rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  margin-right: 20rpx;
}

.section-line {
  flex: 1;
  height: 1px;
  background-color: #ddd;
}

.section-stars {
  display: flex;
  margin-left: 20rpx;
  align-items: center;
}

.star {
  color: #D8D8D8;
  margin-left: 5rpx;
  line-height: 1;
  vertical-align: middle;
}

.star:nth-child(1) {
  font-size: 22rpx;
}

.star:nth-child(2) {
  font-size: 26rpx;
}

.star:nth-child(3) {
  font-size: 30rpx;
}

/* 联系信息标题 - 保留但不使用 */
.contact-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 15rpx;
}

/* 联系信息行 */
.contact-row {
  display: flex;
  padding: 15rpx 0;
  font-size: 28rpx;
}

/* 联系信息标签 */
.contact-label {
  width: 180rpx;
  color: #666;
  flex-shrink: 0;
}

/* 联系信息值 */
.contact-value {
  flex: 1;
  color: #333;
}

/* 底部按钮 */
.bottom-button {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 20rpx;
  padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
  /* iOS 11.0 */
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  /* iOS 11.2+ */
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 分享按钮 */
.share-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 80rpx;
  background-color: transparent;
  padding: 0;
  margin: 0;
  line-height: normal;
  border: none;
}

.share-button::after {
  border: none;
}

.share-button text {
  font-size: 24rpx;
  color: #666;
  margin-top: 4rpx;
}

/* 咨询按钮 */
.consult-button {
  width: 560rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #1989fa;
  color: #fff;
  font-size: 30rpx;
  font-weight: 500;
  border-radius: 8rpx;
  border: none;
  margin: 0;
  padding: 0;
}

.consult-button::after {
  border: none;
}

.consult-button:active {
  opacity: 0.8;
}

/* 骨架屏样式 */
.skeleton-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
  z-index: 1000;
  padding: 0;
  overflow: auto;
}

/* 骨架自定义导航栏 */
.skeleton-custom-nav {
  width: 100%;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
  position: relative;
}

.skeleton-status-bar {
  width: 100%;
  background-color: transparent;
}

.skeleton-nav-title {
  height: 44px;
  width: 120rpx;
  background-color: #eeeeee;
  margin: 0 auto;
  border-radius: 8rpx;
}

.skeleton-car-list {
  padding: 20rpx 30rpx;
  margin-top: 20rpx;
}

.skeleton-car-item {
  display: flex;
  margin-bottom: 30rpx;
  height: 200rpx;
  background-color: #f7f7f7;
  border-radius: 8rpx;
  padding: 20rpx;
}