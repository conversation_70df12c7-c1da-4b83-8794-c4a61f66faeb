<!--pages/my/business_card.wxml-->
<view class="business_card">
  <view class="card-content">
    <text class="company-name">{{formData.company_name}}</text>
    <view class="row">
      <text class="person-name">{{formData.person_name}}</text>
      <text class="position">{{formData.position}}</text>
    </view>
    <text class="phone">{{formData.phone}}</text>
    <text class="email">{{formData.email}}</text>
    <text class="address">{{formData.address}}</text>
  </view>
</view>
<view class="info">
  <view class="info-item">
    <view class="title">公司：</view>
    <view class="value">
      <input 
        model:value="{{formData.company_name}}" 
        placeholder="请输入公司名称"
        bindinput="onInputChange"
      />
    </view>
  </view>
  <view class="info-item">
    <text class="title">姓名：</text>
    <view class="value">
      <input 
        model:value="{{formData.person_name}}" 
        placeholder="请输入姓名"
        bindinput="onPersonNameChange"
      />
    </view>
  </view>
  <view class="info-item">
    <text class="title">职位：</text>
    <view class="value">
      <input 
        model:value="{{formData.position}}" 
        placeholder="请输入职位"
        bindinput="onPositionChange"
      />
    </view>
  </view>
  <view class="info-item">
    <text class="title">电话：</text>
    <view class="value">
      <input 
        model:value="{{formData.phone}}" 
        placeholder="请输入电话"
        bindinput="onPhoneChange"
      />
    </view>
  </view>
  <view class="info-item">
    <text class="title">微信：</text>
    <view class="value">
      <input 
        model:value="{{formData.wechat}}" 
        placeholder="请输入微信"
        bindinput="onWecharChange"
      />
    </view>
  </view>
  <view class="info-item">
    <text class="title">邮箱：</text>
    <view class="value">
      <input 
        model:value="{{formData.email}}" 
        placeholder="请输入邮箱"
        bindinput="onEmailChange"
      />
    </view>
  </view>
  <view class="info-item">
    <text class="title">地址：</text>
    <view class="value">
      <input 
        model:value="{{formData.address}}" 
        placeholder="请输入地址"
        bindinput="onAddressChange"
      />
    </view>
  </view>
</view>