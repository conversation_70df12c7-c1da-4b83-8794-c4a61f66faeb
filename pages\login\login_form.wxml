<!--pages/login/login_form.wxml-->
<language-switcher position="top-left" showLangList="{{true}}" size="normal" />
<view
    class="container"
    style="--status-bar-height: {{statusBarHeight}}px;"
>
    <!-- 自定义导航栏 -->
    <view
        class="custom-nav"
        style="padding-top: {{statusBarHeight}}px;"
    >
        <view class="nav-content">
            <view
                class="back-icon"
                bindtap="navigateBack"
            >
                <van-icon
                    name="arrow-left"
                    size="20px"
                    color="#333"
                />
            </view>
            <view class="nav-title">{{text.zhaochexia}}</view>
        </view>
    </view>

    <!-- 登录内容区域 -->
    <view
        class="main-content"
        style="padding-top: {{statusBarHeight + 44}}px;"
    >
        <!-- 用户头像区域 -->
        <view class="avatar-container">
            <image
                class="avatar"
                src="https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/uploads/proof/1751274246972_981.png"
                mode="aspectFill"
            ></image>
        </view>

        <!-- 手机登入按钮区域 -->
        <view class="login-type-container">
            <view
                class="phone-login-btn"
                bindtap="navigateToPhoneLogin"
            >
                <van-icon
                    name="exchange"
                    size="16px"
                    color="#999"
                    custom-class="phone-login-icon"
                />
                <text class="phone-login-text">{{text.phone_login}}</text>
            </view>
        </view>

        <!-- 登录表单 -->
        <view class="login-form">
            <!-- 用户名输入框 -->
            <view class="input-item">
                <view class="input-icon">
                    <van-icon
                        name="manager-o"
                        size="20px"
                        color="#999"
                    />
                </view>
                <input
                    class="input-field"
                    type="text"
                    placeholder="{{text.enter_phone_or_username}}"
                    placeholder-class="placeholder"
                    model:value="{{username}}"
                />
            </view>

            <!-- 密码输入框 -->
            <view class="input-item">
                <view class="input-icon">
                    <van-icon
                        name="lock"
                        size="20px"
                        color="#999"
                    />
                </view>
                <input
                    class="input-field"
                    password="{{!showPassword}}"
                    placeholder="{{text.enter_password}}"
                    placeholder-class="placeholder"
                    model:value="{{password}}"
                />
            </view>
        </view>

        <!-- 忘记密码按钮区域 -->
        <view class="forget-password-container">
            <view
                class="forget-password-btn"
                bindtap="togglePasswordVisibility"
                hover-class="forget-password-btn-hover"
            >
                <van-icon
                    name="question-o"
                    size="16px"
                    color="#3B82F6"
                    custom-class="forget-password-icon"
                />
                <text class="forget-password-text">{{text.forgot_password}}</text>
            </view>
        </view>

        <!-- 协议区域 -->
        <view class="agreement-container">
            <checkbox
                class="checkbox"
                checked="{{isAgree}}"
                bindtap="toggleAgree"
            ></checkbox>
            <view class="agreement-text">
                {{text.i_agree_to_abide_by}}<text
                    class="link"
                    bindtap="viewAgreement"
                    data-type="service"
                >《{{text.user_service_agreement}}》</text>、<text
                    class="link"
                    bindtap="viewAgreement"
                    data-type="privacy"
                >《{{text.privacy_policy}}》</text>
            </view>
        </view>

        <!-- 登录按钮 -->
        <button
            class="login-btn {{isAgree ? '' : 'login-btn-disabled'}}"
            bindtap="handleLogin"
            disabled="{{!isAgree}}"
            loading="{{loading}}"
        >
            {{text.login}}
        </button>

        <!-- 注册按钮 -->
        <button
            class="account-login-btn"
            bindtap="navigateToRegister"
        >
            {{text.sign_up}}
        </button>
    </view>
</view>