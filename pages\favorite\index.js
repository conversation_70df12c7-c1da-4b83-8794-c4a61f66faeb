// 导入API模块
import api from '../../utils/api';
import util from '../../utils/util';

Page({
  data: {
    statusBarHeight: 20, // 默认状态栏高度
    favoriteList: [], // 收藏列表
    isEditMode: false, // 是否处于编辑模式
    isAllSelected: false, // 是否全选
    isLoading: true,
    getSelectedCount: 0,
    page: 1, // 当前页码
    pageSize: 10, // 每页数量
    hasMoreData: true, // 是否有更多数据
    isLoadingMore: false // 是否正在加载更多
  },

  onLoad() {
    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });

    // 加载收藏数据
    // this.loadFavoriteData();
  },

  onShow() {
    // 每次页面显示时刷新收藏数据
    this.loadFavoriteData();
  },

  // 加载收藏数据
  async loadFavoriteData(isLoadMore = false) {
    if (!isLoadMore) {
    wx.showLoading({
      title: '加载中...',
    });
      this.setData({ isLoading: true, page: 1 });
    } else {
      if (!this.data.hasMoreData || this.data.isLoadingMore) {
        return;
      }
      this.setData({ isLoadingMore: true });
    }

    try {
      // 调用收藏列表API
      const result = await api.car.favoriteList({
        app_id: util.getAppId(),
        page: this.data.page,
        page_size: this.data.pageSize
      });

      let favoriteList = [];
      if (result && result.data) {
        // 处理数据，添加selected属性用于编辑模式
        const newList = result.data.map(item => {
          return {
            ...item,
            selected: false
          };
        });

        if (isLoadMore) {
          favoriteList = [...this.data.favoriteList, ...newList];
        } else {
          favoriteList = newList;
        }

        // 判断是否还有更多数据
        const hasMoreData = newList.length >= this.data.pageSize;

        this.setData({
          favoriteList,
          isLoading: false,
          isLoadingMore: false,
          hasMoreData,
          page: this.data.page + 1
        });
      } else {
        this.setData({
          favoriteList: isLoadMore ? this.data.favoriteList : [],
          isLoading: false,
          isLoadingMore: false,
          hasMoreData: false
        });
      }
    } catch (error) {
      console.error('获取收藏列表失败:', error);
      this.setData({
        favoriteList: isLoadMore ? this.data.favoriteList : [],
        isLoading: false,
        isLoadingMore: false,
        hasMoreData: false
      });

      wx.showToast({
        title: '获取收藏失败',
        icon: 'none'
      });
    } finally {
      if (!isLoadMore) {
      wx.hideLoading();
      }
    }
  },

  // 切换编辑模式
  toggleEditMode() {
    const isEditMode = !this.data.isEditMode;

    // 退出编辑模式时，取消所有选择
    if (!isEditMode) {
      const favoriteList = this.data.favoriteList.map(item => {
        return {
          ...item,
          selected: false
        };
      });

      this.setData({
        favoriteList,
        isAllSelected: false
      });
    }

    this.setData({
      isEditMode
    });
  },

  // 车辆项点击事件
  onCarItemTap(e) {
    // 编辑模式下不跳转
    if (this.data.isEditMode) {
      return;
    }

    const carId = e.currentTarget.dataset.id;

    // 跳转到车辆详情页
    wx.navigateTo({
      url: `/pages/buy/detail?id=${carId}`
    });
  },

  // 选择/取消选择单个项目
  selectItem(e) {
    const id = e.currentTarget.dataset.id;
    const { favoriteList } = this.data;

    // 更新选中状态
    const updatedList = favoriteList.map(item => {
      if (item.id === id) {
        return {
          ...item,
          selected: !item.selected
        };
      }
      return item;
    });

    // 检查是否全部选中
    const isAllSelected = updatedList.every(item => item.selected);

    this.setData({
      favoriteList: updatedList,
      isAllSelected,
      getSelectedCount: this.getSelectedCount(updatedList)
    });
  },

  // 全选/取消全选
  selectAllItems() {
    const { isAllSelected, favoriteList } = this.data;
    const newSelectState = !isAllSelected;

    // 更新所有项的选中状态
    const updatedList = favoriteList.map(item => {
      return {
        ...item,
        selected: newSelectState
      };
    });

    this.setData({
      favoriteList: updatedList,
      isAllSelected: newSelectState,
      getSelectedCount: this.getSelectedCount(updatedList)
    });
  },

  // 删除选中的项目
  async deleteSelected() {
    const { favoriteList } = this.data;
    const selectedItems = favoriteList.filter(item => item.selected);

    if (selectedItems.length === 0) {
      wx.showToast({
        title: '请先选择要删除的车辆',
        icon: 'none'
      });
      return;
    }

    // 确认删除
    wx.showModal({
      title: '确认删除',
      content: `确定要删除选中的${selectedItems.length}个收藏吗？`,
      success: async (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '删除中...',
          });

          try {
            // 获取所有选中项的ID
            const selectedIds = selectedItems.map(item => item.id);

            // 删除收藏API
            await api.car.favorite({
              app_id: util.getAppId(),
              vehicle_id: selectedIds.join(),
              is_favorite: 0
            });

            // 更新列表，移除已删除的项
            const updatedList = favoriteList.filter(item => !item.selected);

            this.setData({
              favoriteList: updatedList,
              isAllSelected: false
            });

            wx.showToast({
              title: '删除成功',
              icon: 'success'
            });

            // 如果列表为空，退出编辑模式
            if (updatedList.length === 0) {
              this.setData({
                isEditMode: false
              });
            }
          } catch (error) {
            console.error('删除收藏失败:', error);
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          } finally {
            wx.hideLoading();
          }
        }
      }
    });
  },

  // 导航到首页
  navigateToHome() {
    wx.switchTab({
      url: '/pages/buy/index'
    });
  },

  // 导航到车辆详情
  navigateToDetail() {
    wx.switchTab({
      url: '/pages/buy/detail'
    });
  },

  // 计算已选中的项目数量
  getSelectedCount(favoriteList) {
    return favoriteList.filter(item => item.selected).length;
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadFavoriteData().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 上拉加载更多
  onReachBottom() {
    if (this.data.hasMoreData && !this.data.isEditMode) {
      this.loadFavoriteData(true);
    }
  },

  navigateBack: function () {
    wx.navigateBack();
  }
});
