/* 引入登录弹窗样式 */
@import "../../templates/loginPopup/loginPopup.wxss";

.container {
  min-height: 100vh;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.content {
  width: 100%;
  padding: 0;
  margin: 0;
  background-color: transparent;
  position: relative;
}

.user-info-container,
.not-login-container {
  width: 100%;
  padding: 0;
  margin-bottom: -20rpx;
  background-color: transparent;
}

/* 固定导航栏样式 */
.fixed-nav {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 101;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #<PERSON><PERSON>DFDF, #F1F4F9);
}

.status-bar {
  width: 100%;
}

.nav-title {
  height: 44px;
  line-height: 44px;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  position: relative;
}

/* 导航栏占位符 */
.nav-placeholder {
  width: 100%;
  box-sizing: border-box;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
}

/* 顶部导航栏 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  position: relative;
  z-index: 10;
}

.back-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-back:after {
  content: "←";
  font-size: 40rpx;
  color: #333;
}

.header-right {
  display: flex;
  align-items: center;
}

.menu-dots {
  font-size: 32rpx;
  margin-right: 20rpx;
  color: #333;
}

.setting-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}

.icon-setting:after {
  content: "⚙";
  font-size: 34rpx;
}

/* 用户信息区域 */
.user-info-section {
  width: 100%;
  padding: 20rpx 20rpx 10rpx;
  box-sizing: border-box;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  position: relative;
  /* 移除导航栏高度的margin-top，由占位符控制 */
}

.user-info-card {
  display: flex;
  align-items: center;
  position: relative;
  padding: 15rpx;
  background-color: transparent;
  border-radius: 10rpx;
}

.avatar-container {
  margin-right: 20rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
}

.upload-avatar-btn {
  position: absolute;
  bottom: -30rpx;
  left: 50%;
  transform: translateX(-50%);
  background: transparent;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  z-index: 10;
  min-width: 120rpx;
  text-align: center;
}

.upload-avatar-btn:active {
  opacity: 0.7;
}

.upload-icon {
  font-size: 20rpx;
  color: #666;
  text-align: center;
  white-space: nowrap;
}

.user-detail {
  flex: 1;
}

.username {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.user-meta {
  display: flex;
  flex-direction: column;
}

.meta-item {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 6rpx;
}

.auth-tag {
  display: inline-block;
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 功能菜单区 */
.function-menu {
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  margin: 0;
  padding: 0 20rpx;
  /* 修改水平内边距与其他盒子保持一致 */
  padding-top: 0;
  /* 移除顶部内边距 */
  padding-bottom: 0;
  /* 移除底部内边距 */
}

.menu-box {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 20rpx 0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.menu-row {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
}

.menu-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}

.menu-icon {
  width: 50rpx;
  height: 50rpx;
  margin-bottom: 10rpx;
  background-color: #f2f6ff;
  border-radius: 50%;
  padding: 15rpx;
}

.menu-text {
  font-size: 24rpx;
  color: #333;
  text-align: center;
}

/* 快捷工具区 */
.quick-tools {
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  margin: 0;
  padding: 15rpx 20rpx;
  /* 修改水平内边距与上下盒子保持一致 */
}

.quick-tools-row {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 0;
  gap: 20rpx;
  /* 添加间距 */
}

.quick-tool-item {
  flex: 1;
  /* 使两个工具项等宽 */
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 20rpx;
  background-color: #fff;
  border-radius: 6rpx;
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.05);
  height: 54rpx;
}

.quick-tool-icon {
  width: 30rpx;
  height: 30rpx;
  margin-right: 8rpx;
}

.quick-tool-text {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

.quick-tool-item .arrow-icon {
  width: 14rpx;
  height: 14rpx;
  border-top: 2rpx solid #999;
  border-right: 2rpx solid #999;
  transform: rotate(45deg);
  margin-left: 10rpx;
}

/* 工具菜单区 */
.tool-menu {
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  margin-top: -18rpx;
  padding: 20rpx 20rpx;
  /* 修改水平内边距与其他盒子保持一致 */
}

/* 工具菜单卡片 */
.tool-menu-card {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 15rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

/* 账号操作区 */
.account-actions {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.action-button {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  font-size: 28rpx;
  border-radius: 8rpx;
  text-align: center;
}

.action-button::after {
  border: none;
}

.logout-button {
  background-color: #fff;
  color: #ff4d4f;
  border: 1rpx solid #ff4d4f;
}

.delete-button {
  background-color: #fff;
  color: #999;
  border: 1rpx solid #ddd;
}

.logout-button:active,
.delete-button:active {
  opacity: 0.8;
}

/* 加载状态样式 */
.loading-container {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
}

.loading {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #3498db;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 推荐车辆区域样式 */
.recommend-section {
  width: 100%;
  padding: 20rpx 20rpx;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  padding-bottom: 120rpx;
  box-sizing: border-box;
}

.recommend-header {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-left: 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.recommend-header .more-cars {
  font-size: 24rpx;
  color: #999999;
  font-weight: normal;
  background-color: transparent;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
}

/* 使用网格布局确保两列显示 */
.recommend-grid {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(2, calc(50% - 10rpx));
  /* 每列宽度减去间距的一半 */
  gap: 20rpx;
  /* 行和列之间的间距 */
  justify-content: space-between;
  /* 确保两列之间有足够空间 */
  box-sizing: border-box;
}

.recommend-card {
  background-color: #fff;
  border-radius: 10rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  width: 100%;
  box-sizing: border-box;
  height: auto;
  display: flex;
  flex-direction: column;
}

.recommend-card-body {
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.recommend-car-image {
  width: 100%;
  height: 200rpx;
  object-fit: cover;
}

.recommend-tag-label {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  font-size: 22rpx;
  color: #fff;
}

.recommend-tag-label.hot {
  background-color: #ff6f00;
}

.recommend-tag-label.new {
  background-color: #1296db;
}

.recommend-car-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  padding: 10rpx 12rpx;
  word-wrap: break-word;
  word-break: break-all;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: visible;
  min-height: 76rpx;
}

.recommend-car-params {
  font-size: 22rpx;
  color: #666;
  padding: 0 12rpx;
}

.recommend-price-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 12rpx 15rpx;
}

.recommend-new-price {
  font-size: 20rpx;
  color: #999;
  text-decoration: line-through;
}

.recommend-sale-price {
  font-size: 28rpx;
  color: #2B68F6;
  font-weight: 500;
}

/* 认证状态样式 */
.auth-status {
  display: inline-block;
  /* padding: 4rpx 12rpx; */
  border-radius: 4rpx;
  font-size: 22rpx;
}

.auth-success {
  color: #52c41a;
}

.auth-warning {
  color: #fa8c16;
}

.auth-pending {
  color: #1890ff;
}

.arrow-icon {
  width: 12rpx;
  height: 12rpx;
  border-top: 2rpx solid #ccc;
  border-right: 2rpx solid #ccc;
  transform: rotate(45deg);
  margin-right: 5rpx;
}

/* 工具菜单项 */
.tool-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
  height: auto;
  min-height: 40rpx;
}

.tool-item:last-child {
  border-bottom: none;
}

.tool-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 20rpx;
}

.tool-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.not-login-container {
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 20rpx;
}

/* 未登录状态的用户信息卡片 */
.not-login-card {
  /* 修改未登录卡片的样式 */
  display: flex;
  justify-content: flex-start;
  padding: 20rpx;
  background-color: transparent;
}

.not-login-user-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  padding: 10rpx 0;
}

.avatar-placeholder {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 30rpx;
}

/* 登录相关样式 */
.login-tip {
  flex: 1;
  font-size: 32rpx;
  color: #333333;
  margin-left: 25rpx;
}

.login-btn {
  width: 160rpx;
  height: 72rpx;
  line-height: 72rpx;
  text-align: center;
  background: #4a8afa;
  color: #ffffff;
  border-radius: 16rpx;
  font-size: 28rpx;
  padding: 0;
  margin-left: 0;
}

.login-btn::after {
  border: none;
}

/* 骨架屏样式 */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

.skeleton-screen {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 999;
  background-color: #fff;
  padding: 0;
  box-sizing: border-box;
  overflow-y: auto;
}

/* 骨架导航栏样式 */
.skeleton-nav {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  background: #f8f8f8;
}

.skeleton-status-bar {
  width: 100%;
  background-color: #f8f8f8;
}

.skeleton-nav-title {
  height: 44px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.skeleton-nav-title::after {
  content: "";
  width: 80rpx;
  height: 40rpx;
  border-radius: 4rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.skeleton-user-section {
  padding: 20rpx;
  margin-top: calc(var(--status-bar-height, 20px) + 44px);
  /* 为导航栏预留空间 */
}

.skeleton-user-card {
  display: flex;
  align-items: center;
  background: #f7f7f7;
  padding: 20rpx;
  border-radius: 10rpx;
}

.skeleton-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  margin-right: 20rpx;
}

.skeleton-user-info {
  flex: 1;
}

.skeleton-username {
  width: 150rpx;
  height: 36rpx;
  border-radius: 6rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  margin-bottom: 15rpx;
}

.skeleton-meta {
  width: 200rpx;
  height: 26rpx;
  border-radius: 6rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  margin-bottom: 10rpx;
}

.skeleton-function-menu {
  margin: 20rpx;
  background: #f7f7f7;
  border-radius: 10rpx;
  padding: 20rpx;
}

.skeleton-menu-row {
  display: flex;
  justify-content: space-between;
}

.skeleton-menu-item {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.skeleton-quick-tools {
  margin: 20rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.skeleton-tool-item {
  height: 90rpx;
  border-radius: 10rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.skeleton-tool-menu {
  margin: 20rpx;
  background: #f7f7f7;
  border-radius: 10rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.skeleton-recommend {
  margin: 20rpx;
}

.skeleton-recommend-title {
  width: 150rpx;
  height: 40rpx;
  border-radius: 6rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  margin-bottom: 20rpx;
}

.skeleton-recommend-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.skeleton-recommend-card {
  height: 220rpx;
  border-radius: 10rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* 信息修改药丸按钮 */
.edit-profile-pill {
  padding: 8rpx 18rpx;
  background-color: #eff4ff;
  color: #4080ff;
  font-size: 24rpx;
  border-radius: 30rpx;
  border: 1rpx solid #c8d9ff;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.edit-profile-pill:active {
  opacity: 0.8;
}

/* 客服弹窗样式 */
.customer-service-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  animation: fadeIn 0.3s ease-in-out;
}

.cs-popup-container {
  padding: 16rpx 30rpx calc(20rpx + env(safe-area-inset-bottom));
  display: flex;
  flex-direction: column;
  gap: 16rpx; /* Slightly reduced gap between cards as well */
}

.cs-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.cs-info-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
  position: relative;
}

.cs-info-item:not(:last-child)::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1rpx;
  background-color: #f5f5f5;
}

.cs-icon, .cs-phone-icon {
  /* width: 40rpx;
  height: 40rpx; */
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cs-text {
  font-size: 32rpx;
  color: #333;
  line-height: 1.5;
}

.cs-cancel-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  width: 100%;
  padding: 30rpx 0;
  text-align: center;
}

.cs-cancel-text {
  font-size: 32rpx;
  color: #333;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 官方客服QR码弹窗样式 */
.official-service-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-in-out;
}

.qr-card {
  width: 65%;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 40rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qr-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #000;
  margin-bottom: 20rpx;
  text-align: center;
}

.qr-subtitle {
  font-size: 26rpx;
  color: #666;
  text-align: center;
  margin-bottom: 40rpx;
  padding: 0 20rpx;
  line-height: 1.5;
}

.qr-code-container {
  width: 100%;
  display: flex;
  justify-content: center;
}

.qr-code-image {
  width: 400rpx;
  height: 400rpx;
}

.qr-tip {
  font-size: 24rpx;
  color: #888;
  margin-top: 20rpx;
  text-align: center;
}

.round-close-btn {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background-color: transparent;
  border: 3rpx solid #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 40rpx;
  box-sizing: border-box;
  padding: 0; /* Remove any padding */
}

.close-icon {
  font-size: 40rpx;
  color: #fff;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  transform: translateY(-2rpx); /* Fine-tune vertical position */
}

/* 浮动客服按钮样式 */
.floating-service-btn {
  position: fixed;
  bottom: 120rpx;
  right: 30rpx;
  background-color: #4A8AFA;
  color: #fff;
  border-radius: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  padding: 16rpx 30rpx;
  z-index: 99;
  animation: float 2s ease-in-out infinite;
}

.floating-service-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 12rpx;
}

.floating-service-text {
  font-size: 28rpx;
  font-weight: 500;
}

@keyframes float {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-6rpx);
  }
  100% {
    transform: translateY(0);
  }
}