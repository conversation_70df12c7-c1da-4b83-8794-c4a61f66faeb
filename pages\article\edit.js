import { areaList } from '@vant/area-data';
import api from '../../utils/api';
import cosUpload from '../../utils/cos-upload';
import config from '../../config';

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 页面模式：create(创建) 或 edit(编辑)
    mode: 'create',
    // 文章ID，编辑模式下使用
    articleId: '',
    // 品牌选项
    brands: [],
    brandSelected: '',
    brandId: '', // 存储选中品牌ID
    showBrandPicker: false,

    // 车系选项
    series: [],
    seriesSelected: '',
    seriesId: '', // 存储选中车系ID
    showSeriesPicker: false,

    // 车型选项
    models: [],
    modelSelected: '',
    vehicle_id: '', // 存储选中车型ID
    showModelPicker: false,

    // 截止日期
    deadline: '',

    // 新旧类型
    isNew: true,

    // 过户次数
    transferCount: '',

    // 目标价格
    targetPrice: '',

    // 采购数量
    purchaseCount: '',

    // 是否带保险
    hasInsurance: true,

    // 是否带发票
    hasInvoice: true,

    // 交付地点
    areaList: areaList,
    provinces: [], // 省份列表
    cities: {}, // 城市列表，按省份ID分组
    provinceSelected: '', // 选中的省份
    provinceCode: '', // 选中的省份代码
    citySelected: '', // 选中的城市
    cityCode: '', // 选中的城市代码
    locationSelected: '', // 完整的地址（省市组合）
    showProvincePicker: false,
    showCityPicker: false,

    // 配置说明
    description: '',
    descriptionLength: 0,

    // 采购证明图片
    proofImages: [],

    // 记录是否来自草稿箱
    fromDraft: false
  },

  // 品牌选择器相关方法
  showBrandPicker() {
    // console.log('显示品牌选择器');
    this.hideAllPickers();
    this.setData({
      showBrandPicker: true
    });
    // console.log('showBrandPicker状态:', this.data.showBrandPicker);
    // console.log('品牌数据数量:', this.data.brands.length);
  },

  selectBrand(e) {
    // console.log('选择品牌事件:', e);
    const value = e.currentTarget.dataset.value;
    const brandId = e.currentTarget.dataset.id;
    // console.log('选择的品牌:', value, brandId);

    // 立即更新状态
    this.setData({
      brandSelected: value,
      brandId: brandId,
      showBrandPicker: false,
      // 清空车系和车型选择
      seriesSelected: '',
      seriesId: '',
      modelSelected: '',
      vehicle_id: '',
      series: [],
      models: []
    });

    // 获取对应的车系列表
    this.getSeriesList(brandId);
  },

  // 车系选择器相关方法
  showSeriesPicker() {
    if (!this.data.brandSelected) {
      wx.showToast({
        title: '请先选择品牌',
        icon: 'none'
      });
      return;
    }
    this.hideAllPickers();
    this.setData({
      showSeriesPicker: true
    });
  },

  selectSeries(e) {
    // console.log('选择车系事件:', e);
    const value = e.currentTarget.dataset.value;
    const seriesId = e.currentTarget.dataset.id;
    // console.log('选择的车系:', value, seriesId);

    // 确保seriesId不为undefined
    if (!seriesId) {
      wx.showToast({
        title: '车系数据异常',
        icon: 'none'
      });
      return;
    }

    this.setData({
      seriesSelected: value,
      seriesId: seriesId,
      showSeriesPicker: false,
      // 清空车型选择
      modelSelected: '',
      vehicle_id: '',
      models: []
    });
    // 获取对应的车型列表
    this.getVehicleList(seriesId);
  },

  // 车型选择器相关方法
  showModelPicker() {
    if (!this.data.seriesSelected) {
      wx.showToast({
        title: '请先选择车系',
        icon: 'none'
      });
      return;
    }
    this.hideAllPickers();
    this.setData({
      showModelPicker: true
    });
  },

  selectModel(e) {
    // console.log('选择车型事件:', e);
    const value = e.currentTarget.dataset.value;
    const vehicle_id = e.currentTarget.dataset.id;
    // console.log('选择的车型:', value, vehicle_id);

    // 确保vehicle_id不为undefined
    if (!vehicle_id) {
      wx.showToast({
        title: '车型数据异常',
        icon: 'none'
      });
      return;
    }

    this.setData({
      modelSelected: value,
      vehicle_id: vehicle_id,
      showModelPicker: false
    });
  },

  // 解析地区数据
  parseAreaData() {
    const provinces = [];
    const cities = {};

    // 提取省份数据
    Object.keys(this.data.areaList.province_list).forEach(provinceCode => {
      provinces.push({
        code: provinceCode,
        name: this.data.areaList.province_list[provinceCode]
      });
    });

    // 提取城市数据
    Object.keys(this.data.areaList.city_list).forEach(cityCode => {
      const provinceCode = cityCode.substring(0, 2) + '0000';
      if (!cities[provinceCode]) {
        cities[provinceCode] = [];
      }
      cities[provinceCode].push({
        code: cityCode,
        name: this.data.areaList.city_list[cityCode]
      });
    });

    this.setData({
      provinces,
      cities
    });

    // 设置默认选中的地点（广东 广州）
    const defaultProvince = '广东';
    const defaultCity = '广州';
    const defaultProvinceCode = '44000000'; // 广东省代码

    this.setData({
      provinceSelected: defaultProvince,
      provinceCode: defaultProvinceCode,
      citySelected: defaultCity,
      locationSelected: defaultProvince + ' ' + defaultCity
    });
  },

  // 省份选择器相关方法
  showProvincePicker() {
    this.hideAllPickers();
    this.setData({
      showProvincePicker: true
    });
  },

  selectProvince(e) {
    const province = e.currentTarget.dataset.value;
    const provinceCode = e.currentTarget.dataset.code;
    this.setData({
      provinceSelected: province,
      provinceCode: provinceCode,
      showProvincePicker: false,
      showCityPicker: true
    });
  },

  // 城市选择器相关方法
  selectCity(e) {
    const city = e.currentTarget.dataset.value;
    const cityCode = e.currentTarget.dataset.code;
    this.setData({
      citySelected: city,
      cityCode: cityCode,
      locationSelected: this.data.provinceSelected + ' ' + city,
      showCityPicker: false
    });
  },

  // 隐藏所有选择器
  hideAllPickers() {
    this.setData({
      showBrandPicker: false,
      showSeriesPicker: false,
      showModelPicker: false,
      showProvincePicker: false,
      showCityPicker: false
    });
  },

  // 页面点击事件，隐藏所有选择器
  onPageClick() {
    // console.log('页面点击，隐藏选择器');
    this.hideAllPickers();
  },

  // 阻止事件冒泡，防止点击选择器内部时触发页面点击事件
  stopPropagation(e) {
    console.log('阻止事件冒泡');
    // 阻止事件冒泡，在微信小程序中不需要return false，通过在wxml中使用catchtap来处理
  },

  // 取消选择
  cancelSelection() {
    // console.log('取消选择');
    this.hideAllPickers();
  },

  // 日期选择器变化事件
  bindDateChange(e) {
    // console.log('日期选择器变化, 原值:', this.data.deadline);
    // console.log('日期选择器变化, 新值:', e.detail.value);

    this.setData({
      deadline: e.detail.value
    }, () => {
      console.log('日期已设置为:', this.data.deadline);
    });
  },

  // 切换新旧类型
  toggleIsNew() {
    this.setData({
      isNew: !this.data.isNew
    });
  },

  // 输入过户次数
  inputTransferCount(e) {
    // console.log('过户次数输入, 原值:', this.data.transferCount);
    // console.log('过户次数输入, 新值:', e.detail.value);

    this.setData({
      transferCount: e.detail.value
    }, () => {
      console.log('过户次数已设置为:', this.data.transferCount);
    });
  },

  // 输入目标价格
  inputTargetPrice(e) {
    this.setData({
      targetPrice: e.detail.value
    });
  },

  // 输入采购数量
  inputPurchaseCount(e) {
    this.setData({
      purchaseCount: e.detail.value
    });
  },

  // 切换是否带保险
  toggleHasInsurance() {
    this.setData({
      hasInsurance: !this.data.hasInsurance
    });
  },

  // 切换是否带发票
  toggleHasInvoice() {
    this.setData({
      hasInvoice: !this.data.hasInvoice
    });
  },

  // 输入配置说明
  inputDescription(e) {
    const value = e.detail.value;
    this.setData({
      description: value,
      descriptionLength: value.length
    });
  },

  // 选择图片
  chooseImage() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        if (!res.tempFilePaths || !res.tempFilePaths[0]) {
          wx.showToast({
            title: '未选择图片',
            icon: 'none'
          });
          return;
        }

        const tempFilePath = res.tempFilePaths[0];

        // 清除可能存在的loading提示
        try {
          wx.hideLoading();
        } catch (e) { }

        // 显示新的加载提示
        wx.showLoading({
          title: '上传中...',
          mask: true
        });

        // 使用计时器确保loading能显示至少1秒
        const loadingTimer = setTimeout(() => { }, 1000);

        // 上传图片到腾讯云COS
        cosUpload.uploadFile(tempFilePath, 'proof')
          .then(result => {
            // 确保loading至少显示1秒
            clearTimeout(loadingTimer);

            // 设置数据并隐藏loading
            this.setData({
              proofImages: [this.getFullFileUrl(this.getFilePathSuffix(result.url))]
            }, () => {
              // 在回调中关闭loading，确保数据设置完成
              wx.hideLoading();
              wx.showToast({
                title: '上传成功',
                icon: 'success'
              });
            });
          })
          .catch(error => {
            // 处理错误
            console.error('上传失败:', error);
            clearTimeout(loadingTimer);

            wx.hideLoading();
            wx.showToast({
              title: '上传失败',
              icon: 'none'
            });

            // 清空图片
            this.setData({
              proofImages: []
            });
          });
      },
      fail: (error) => {
        console.error('选择图片失败:', error);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 保存草稿
  saveDraft() {
    // 如果是编辑模式，提示用户
    if (this.data.mode === 'edit') {
      wx.showModal({
        title: '提示',
        content: '您正在编辑已有文章，确定要将当前内容保存为草稿吗？',
        success: (res) => {
          if (res.confirm) {
            this.publishAsDraft();
          }
        }
      });
    } else {
      // 创建模式直接保存
      this.publishAsDraft();
    }
  },

  // 将文章以草稿形式发布
  publishAsDraft() {
    // console.log('提交草稿前数据检查:');
    // console.log('截止日期:', this.data.deadline);
    // console.log('过户次数:', this.data.transferCount);

    // 检查必填项
    if (!this.data.brandSelected || !this.data.seriesSelected || !this.data.modelSelected ||
      !this.data.deadline || !this.data.targetPrice || !this.data.purchaseCount ||
      !this.data.locationSelected || this.data.proofImages.length === 0) {
      wx.showToast({
        title: '请填写必填项',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '保存草稿中...',
    });

    // 从缓存中获取用户信息
    const userInfoStorage = wx.getStorageSync('userInfo');
    let app_id = 1; // 默认值

    if (userInfoStorage && userInfoStorage.value && userInfoStorage.value.app_id) {
      app_id = userInfoStorage.value.app_id;
    }

    // 准备发布数据
    const publishData = {
      brand_id: this.data.brandId,
      series_id: this.data.seriesId,
      used_type: this.data.isNew ? 'new' : 'used',
      vehicle_id: this.data.vehicle_id,
      due_date: this.data.deadline,
      price: this.data.targetPrice,
      quantity: this.data.purchaseCount,
      delivery_location: this.data.locationSelected,
      purchase_ref_files: this.data.proofImages.map(img => this.getFilePathSuffix(img)).join(','),
      vehicle_condition: this.data.description || '',
      app_id: app_id,
      is_draft: 1 // 标记为草稿
    };

    // 添加过户次数（如果有）
    if (this.data.transferCount !== undefined && this.data.transferCount !== null && this.data.transferCount !== '') {
      publishData.ownership_transfers = this.data.transferCount;
    }

    // 编辑模式需要添加文章ID
    if (this.data.mode === 'edit') {
      publishData.id = this.data.articleId;
    }

    // console.log('准备提交的草稿数据:', publishData);

    // 调用API发布或更新
    const apiMethod = this.data.mode === 'edit' ? api.moments.edit : api.moments.publish;

    apiMethod(publishData).then(res => {
      wx.hideLoading();
      wx.showToast({
        title: '草稿保存成功',
        icon: 'success',
        duration: 2000,
        success: () => {
          // 设置一个全局变量标记，表示需要刷新侠友圈列表数据
          getApp().globalData = getApp().globalData || {};
          getApp().globalData.needRefreshMomentsList = true;

          // 设置一个全局变量标记，表示需要刷新我的帖子列表数据
          getApp().globalData.needRefreshArticleList = true;

          // 延迟返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 2000);
        }
      });
      // console.log("草稿保存成功，返回数据:", res);
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '草稿保存失败，请重试',
        icon: 'none'
      });
      console.error("草稿保存失败:", err);
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 解析地区数据
    this.parseAreaData();

    // 获取品牌列表
    this.getBrandList();

    // console.log('页面加载参数:', options);

    // 检查是否有ID参数，确定是创建还是编辑模式
    if (options.id) {
      this.setData({
        mode: 'edit',
        articleId: options.id,
        // 记录是否来自草稿箱
        fromDraft: options.fromDraft === 'true'
      });

      // 如果是草稿，调用加载草稿的方法
      if (options.isDraft === 'true') {
        this.loadDraft(options.id);
      } else {
        // 否则加载正式文章数据
        this.getArticleDetail(options.id);
      }
    } else {
      this.setData({
        mode: 'create',
        fromDraft: false
      });
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 添加页面点击事件监听
    this.pageListener = () => {
      this.hideAllPickers();
    };

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: this.data.mode === 'edit' ? '编辑信息' : '发布信息'
    });

    // 打印当前值
    // console.log('=====页面显示时的值=====');
    // console.log('当前日期值:', this.data.deadline);
    // console.log('当前过户次数:', this.data.transferCount);
    // console.log('======================');
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  // 处理图片加载错误
  imageError(e) {
    console.error('图片加载失败:', e);
    wx.showToast({
      title: '图片加载失败，请重新上传',
      icon: 'none'
    });
    // 清空已上传的图片路径
    this.setData({
      proofImages: []
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 获取文件路径后缀
  getFilePathSuffix(path) {
    if (!path) return '';
    // 检查是否已经包含完整路径
    const baseUrl = config.COS_CONFIG.url + config.COS_CONFIG.path;
    if (path.startsWith('http')) {
      // 从URL中提取路径
      const parts = path.split(baseUrl);
      if (parts.length > 1) {
        // 确保路径以 /uploads 开头
        return '/uploads/' + parts[1];
      }
      return path;
    } else if (path.includes('uploads/')) {
      // 如果已经包含 uploads/ 但不是完整URL
      const parts = path.split('uploads/');
      return '/uploads/' + parts[1];
    } else {
      // 如果是相对路径，直接添加前缀
      return '/uploads/' + path;
    }
  },

  // 获取完整的文件URL
  getFullFileUrl(pathSuffix) {
    if (!pathSuffix) return '';
    if (pathSuffix.indexOf('https://') === 0 || pathSuffix.indexOf('http://') === 0) {
      return pathSuffix;
    }
    // 移除开头的 /uploads 如果存在
    const cleanPath = pathSuffix.startsWith('/uploads/')
      ? pathSuffix.substring(9)
      : (pathSuffix.startsWith('uploads/') ? pathSuffix.substring(8) : pathSuffix);
    return config.COS_CONFIG.url + config.COS_CONFIG.path + cleanPath;
  },

  // 获取品牌列表
  getBrandList() {
    wx.showLoading({
      title: '加载中',
    });
    api.car.getBrandList().then(res => {
      wx.hideLoading();
      // console.log('品牌数据:', res);

      // 直接处理返回的数据，无需判断code
      if (res && Array.isArray(res)) {
        // console.log('品牌数据是一个数组，长度:', res.length);
        if (res.length > 0) {
          console.log('品牌数据第一项:', res[0]);
        }

        // 设置数据到状态
        this.setData({
          brands: res
        }, () => {
          // console.log('设置后的品牌数据长度:', this.data.brands.length);

          // 如果有品牌ID但没有品牌名称，尝试从列表中找到对应的品牌名称
          if (this.data.brandId && !this.data.brandSelected) {
            for (let i = 0; i < res.length; i++) {
              if (res[i].id == this.data.brandId) {
                this.setData({
                  brandSelected: res[i].brand_name
                });
                // console.log('从列表中找到的品牌名称:', res[i].brand_name);
                break;
              }
            }
          }
        });
      } else {
        console.error('品牌数据不是数组格式:', res);
        wx.showToast({
          title: '获取品牌列表失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '获取品牌列表失败',
        icon: 'none'
      });
      console.error('获取品牌列表失败', err);
    });
  },

  // 获取车系列表
  getSeriesList(brandId) {
    if (!brandId) {
      console.error('未提供品牌ID');
      return;
    }

    // console.log('传递的品牌ID:', brandId);

    wx.showLoading({
      title: '加载中',
    });
    api.car.getSeriesList({
      brand_id: brandId  // 确保参数名称与后端API要求一致
    }).then(res => {
      wx.hideLoading();
      // console.log('车系数据:', res);

      // 直接处理返回的数据，无需判断code
      if (res && Array.isArray(res)) {
        if (res.length > 0) {
          console.log('车系数据第一项:', res[0]);
        }
        this.setData({
          series: res
        }, () => {
          // 如果有车系ID但没有车系名称，尝试从列表中找到对应的车系名称
          if (this.data.seriesId && !this.data.seriesSelected) {
            for (let i = 0; i < res.length; i++) {
              if (res[i].series_id == this.data.seriesId) {
                this.setData({
                  seriesSelected: res[i].series_name
                });
                // console.log('从列表中找到的车系名称:', res[i].series_name);
                break;
              }
            }
          }
        });
      } else {
        wx.showToast({
          title: '获取车系列表失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '获取车系列表失败',
        icon: 'none'
      });
      console.error('获取车系列表失败', err);
    });
  },

  // 获取车型列表
  getVehicleList(seriesId) {
    if (!seriesId) {
      console.error('未提供车系ID');
      return;
    }

    // console.log('传递的车系ID:', seriesId);

    wx.showLoading({
      title: '加载中',
    });
    api.car.getVehicleList({
      series_id: seriesId  // 确保参数名称与后端API要求一致
    }).then(res => {
      wx.hideLoading();
      // console.log('车型数据:', res);

      // 直接处理返回的数据，无需判断code
      if (res && Array.isArray(res)) {
        if (res.length > 0) {
          console.log('车型数据第一项:', res[0]);
        }
        this.setData({
          models: res
        }, () => {
          // 如果有车型ID但没有车型名称，尝试从列表中找到对应的车型名称
          if (this.data.vehicle_id && !this.data.modelSelected) {
            for (let i = 0; i < res.length; i++) {
              if (res[i].id == this.data.vehicle_id) {
                const modelName = res[i].ui_vehicle_name + (res[i].official_price ? ' ¥' + res[i].official_price + '万元' : '');
                this.setData({
                  modelSelected: modelName
                });
                // console.log('从列表中找到的车型名称:', modelName);
                break;
              }
            }
          }
        });
      } else {
        wx.showToast({
          title: '获取车型列表失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '获取车型列表失败',
        icon: 'none'
      });
      console.error('获取车型列表失败', err);
    });
  },

  // 加载草稿数据
  loadDraft(id) {
    const draftData = wx.getStorageSync('momentDraft');
    if (!draftData) return;

    // 提示用户是否要恢复草稿
    wx.showModal({
      title: '提示',
      content: '检测到有未完成的草稿，是否恢复？',
      success: (res) => {
        if (res.confirm) {
          // 恢复草稿数据
          this.setData({
            brandSelected: draftData.brandSelected || '',
            brandId: draftData.brandId || '',
            seriesSelected: draftData.seriesSelected || '',
            seriesId: draftData.seriesId || '',
            modelSelected: draftData.modelSelected || '',
            vehicle_id: draftData.vehicle_id || '',
            deadline: draftData.deadline || '',
            isNew: draftData.isNew !== undefined ? draftData.isNew : true,
            transferCount: draftData.transferCount || '',
            targetPrice: draftData.targetPrice || '',
            purchaseCount: draftData.purchaseCount || '',
            hasInsurance: draftData.hasInsurance !== undefined ? draftData.hasInsurance : true,
            hasInvoice: draftData.hasInvoice !== undefined ? draftData.hasInvoice : true,
            provinceSelected: draftData.provinceSelected || this.data.provinceSelected,
            provinceCode: draftData.provinceCode || this.data.provinceCode,
            citySelected: draftData.citySelected || this.data.citySelected,
            cityCode: draftData.cityCode || this.data.cityCode,
            locationSelected: draftData.locationSelected || this.data.locationSelected,
            description: draftData.description || '',
            descriptionLength: draftData.description ? draftData.description.length : 0,
            proofImages: draftData.proofImages || []
          });

          // 如果有车系和车型信息，需要加载相应的数据列表
          if (draftData.brandId) {
            this.getSeriesList(draftData.brandId);
          }

          if (draftData.seriesId) {
            this.getVehicleList(draftData.seriesId);
          }
        } else {
          // 用户不想恢复，清除草稿
          wx.removeStorageSync('momentDraft');
        }
      }
    });
  },

  // 获取文章详情
  getArticleDetail(id) {
    if (!id) return;

    wx.showLoading({
      title: '加载中...',
    });

    api.moments.getDetail({ id: id }).then(res => {
      wx.hideLoading();
      // console.log('文章详情数据:', res);
      // console.log('品牌ID:', res.brand_id);
      // console.log('车型名称:', res.ui_vehicle_name);
      // console.log('车系ID:', res.series_id);
      // console.log('截止日期原始值:', res.due_date);
      // console.log('过户次数原始值:', res.ownership_transfers);
      // console.log('过户次数类型:', typeof res.ownership_transfers);
      // console.log('图片数据:', res.purchase_ref_files);

      // 转换截止日期格式（假设服务器返回的是YYYY-MM-DD HH:mm:ss格式）
      let formattedDate = '';
      if (res.due_date) {
        // 尝试提取YYYY-MM-DD部分
        const dateMatch = res.due_date.match(/^(\d{4}-\d{2}-\d{2})/);
        if (dateMatch && dateMatch[1]) {
          formattedDate = dateMatch[1];
        } else {
          formattedDate = res.due_date;
        }
      }
      // console.log('格式化后的日期:', formattedDate);

      // 确保过户次数是字符串
      let transferCount = '';
      if (res.ownership_transfers !== undefined && res.ownership_transfers !== null) {
        transferCount = String(res.ownership_transfers);
      }
      // console.log('格式化后的过户次数:', transferCount);

      if (res) {
        // 设置表单数据 - 注意：品牌、车系、车型名称在获取列表后会被设置
        this.setData({
          brandId: res.brand_id || '',
          seriesId: res.series_id || '',
          vehicle_id: res.vehicle_id || '',
          deadline: formattedDate,
          isNew: res.used_type === 'new',
          transferCount: transferCount,
          targetPrice: res.price || '',
          purchaseCount: res.quantity || '',


          locationSelected: res.delivery_location || '',
          description: res.vehicle_condition || '',
          descriptionLength: res.vehicle_condition ? res.vehicle_condition.length : 0
        }, () => {
          // 设置完成后打印
          // console.log('设置后的品牌ID:', this.data.brandId);
          // console.log('设置后的车系ID:', this.data.seriesId);
          // console.log('设置后的车型ID:', this.data.vehicle_id);
          // console.log('设置后的日期:', this.data.deadline);
          // console.log('设置后的过户次数:', this.data.transferCount);
          // console.log('过户次数类型:', typeof this.data.transferCount);
        });

        // 延迟再次确认过户次数设置情况
        setTimeout(() => {
          console.log('延迟检查过户次数:', this.data.transferCount);
        }, 1000);

        // 如果响应中包含了名称字段，直接设置它们
        if (res.brand_name) {
          this.setData({ brandSelected: res.brand_name });
          // console.log('直接设置品牌名称:', res.brand_name);
        }

        if (res.series_name) {
          this.setData({ seriesSelected: res.series_name });
          // console.log('直接设置车系名称:', res.series_name);
        }

        if (res.ui_vehicle_name) {
          this.setData({ modelSelected: res.ui_vehicle_name });
          // console.log('直接设置车型名称:', res.ui_vehicle_name);
        }

        // 处理图片数据
        if (res.purchase_ref_files) {
          // console.log('原始图片数据:', res.purchase_ref_files);
          let imageUrls = [];

          // 判断是否为数组
          if (Array.isArray(res.purchase_ref_files)) {
            // console.log('图片数据为数组');
            // 直接使用URL数组
            imageUrls = res.purchase_ref_files;
          } else if (typeof res.purchase_ref_files === 'string') {
            // 如果是字符串，则尝试分割
            // console.log('图片数据为字符串');
            imageUrls = res.purchase_ref_files.split(',').map(path => {
              return this.getFullFileUrl(path);
            });
          }

          // console.log('处理后的图片URLs:', imageUrls);
          this.setData({
            proofImages: imageUrls
          }, () => {
            console.log('设置后的proofImages:', this.data.proofImages);
          });
        }

        // 加载关联数据
        this.getBrandList(); // 获取所有品牌，在回调中会自动设置选中的品牌

        if (res.brand_id) {
          // console.log('开始加载车系列表, brand_id:', res.brand_id);
          this.getSeriesList(res.brand_id);
        }

        if (res.series_id) {
          // console.log('开始加载车型列表, series_id:', res.series_id);
          this.getVehicleList(res.series_id);
        }

        // 处理省市选择
        if (res.delivery_location) {
          const parts = res.delivery_location.split(' ');
          if (parts.length >= 2) {
            const province = parts[0];
            const city = parts[1];
            // 查找省份代码
            const provinces = this.data.provinces;
            for (let i = 0; i < provinces.length; i++) {
              if (provinces[i].name === province) {
                this.setData({
                  provinceSelected: province,
                  provinceCode: provinces[i].code,
                  citySelected: city
                });
                break;
              }
            }
          }
        }
      } else {
        wx.showToast({
          title: '获取文章详情失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '获取文章详情失败',
        icon: 'none'
      });
      console.error('获取文章详情失败', err);
    });
  },

  /**
   * 构建请求参数
   */
  buildRequestParams() {
    // 从缓存中获取用户信息
    const userInfoStorage = wx.getStorageSync('userInfo');
    let app_id = 1; // 默认值

    if (userInfoStorage && userInfoStorage.value && userInfoStorage.value.app_id) {
      app_id = userInfoStorage.value.app_id;
    }

    // 准备发布数据
    const params = {
      brand_id: this.data.brandId,
      series_id: this.data.seriesId,
      used_type: this.data.isNew ? 'new' : 'used',
      vehicle_id: this.data.vehicle_id,
      due_date: this.data.deadline,
      price: this.data.targetPrice,
      quantity: this.data.purchaseCount,
      delivery_location: this.data.locationSelected,
      purchase_ref_files: this.data.proofImages.map(img => this.getFilePathSuffix(img)).join(','),
      vehicle_condition: this.data.description || '',
      app_id: app_id,

    };

    // 添加过户次数（如果有）
    if (this.data.transferCount !== undefined && this.data.transferCount !== null && this.data.transferCount !== '') {
      params.ownership_transfers = this.data.transferCount;
    }

    // console.log('准备提交的数据:', params);
    return params;
  },

  /**
   * 发布或更新帖子
   */
  publish() {
    // 检查必填字段
    const {
      brandSelected, seriesSelected, modelSelected, deadline, targetPrice,
      purchaseCount, locationSelected, vehicle_id
    } = this.data;

    if (!brandSelected || !seriesSelected || !modelSelected || !deadline || !targetPrice ||
      !purchaseCount || !locationSelected || !vehicle_id) {
      wx.showToast({
        title: '请填写所有必填项',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: this.data.mode === 'edit' && !this.data.fromDraft ? '更新中...' : '发布中...'
    });

    // 构建提交数据
    const params = this.buildRequestParams();

    // 区分编辑和创建模式
    let apiMethod = api.moments.publish;
    if (this.data.mode === 'edit') {
      apiMethod = api.moments.edit;
      params.id = this.data.articleId;
    }

    // 提交到服务器
    apiMethod(params).then(res => {
      wx.hideLoading();
      console.log('服务器响应:', res);

      // 检查res是否有code字段，如果有并且等于0，则处理成功逻辑
      if (res && (res.code === 0 || res.code === '0')) {
        // console.log('发布/更新成功!');
        wx.showToast({
          title: this.data.mode === 'edit' && !this.data.fromDraft ? '更新成功' : '发布成功',
          icon: 'success'
        });

        // 设置全局变量标记，表示需要刷新侠友圈列表和文章列表数据
        getApp().globalData = getApp().globalData || {};
        getApp().globalData.needRefreshMomentsList = true;
        getApp().globalData.needRefreshArticleList = true;

        // 发布成功后返回上一页，并刷新上一页数据
        setTimeout(() => {
          // 返回上一页
          const pages = getCurrentPages();
          const prevPage = pages[pages.length - 2];

          // 如果上一页是草稿箱或首页，刷新其数据
          if (prevPage &&
            (prevPage.route === 'pages/article/draft' ||
              prevPage.route === 'pages/index/index')) {
            // 通知上一页刷新数据
            prevPage.setData({
              needRefresh: true
            });
          }

          wx.navigateBack();
        }, 1500);
      } else {
        // 服务器返回错误码
        console.error('服务器返回错误:', res);
        wx.showToast({
          title: res && res.msg ? res.msg : '操作失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      // 网络错误或其他异常
      wx.hideLoading();
      console.error('提交失败, 详细错误:', err);
      wx.showToast({
        title: '网络异常，请稍后重试',
        icon: 'none'
      });
    });
  },
})