/* 引入登录弹窗样式 */
@import "../../templates/loginPopup/loginPopup.wxss";

/* 全局溢出控制 - 防止横向滚动 */
page {
  overflow-x: hidden;
  width: 100%;
  box-sizing: border-box;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
}

.container {
  padding: 0;
  background-color: transparent;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
  /* 防止横向滚动 */
  position: relative;
  /* 添加相对定位 */
}

/* 新的搜索栏样式 */
.search-bar {
  padding: 20rpx 0;
  /* 只保留上下内边距，移除左右内边距 */
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  width: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-input-wrapper {
  flex: 1;
  display: flex;
  height: 70rpx;
  box-sizing: border-box;
  border-radius: 16rpx;
  overflow: hidden;
  background-color: #f7f7f7;
  align-items: center;
  position: relative;
  margin-right: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  border: 2rpx solid #1C82F5;
  transition: all 0.2s ease-in-out;
}

.search-icon {
  margin-left: 20rpx;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-input {
  flex: 1;
  height: 100%;
  padding: 0 110rpx 0 15rpx;
  font-size: 28rpx;
  background-color: transparent;
  border: none;
  color: #333;
}

.search-btn {
  height: 56rpx;
  line-height: 56rpx;
  background: linear-gradient(to right, #1C82F5, #27AFF5);
  color: #fff;
  font-size: 28rpx;
  text-align: center;
  padding: 0 30rpx;
  border-radius: 12rpx;
  position: absolute;
  right: 7rpx;
  top: 50%;
  transform: translateY(-50%);
  box-shadow: 0 2rpx 8rpx rgba(56, 136, 255, 0.3);
  transition: all 0.2s ease-in-out;
}

.search-btn:active {
  background: linear-gradient(to right, #1973dd, #24a0e5);
  box-shadow: 0 1rpx 3rpx rgba(56, 136, 255, 0.2);
  transform: translateY(-50%) scale(0.98);
}

/* 筛选条件栏 */
.filter-tabs {
  width: 100%;
}

/* 自定义 van-tabs 样式 */
.filter-tabs .van-tabs__nav {
  display: flex !important;
}

.filter-tabs .van-tab {
  -webkit-box-flex: 1 !important;
  flex: 1 !important;
  min-width: 0 !important;
}

.filter-tabs .van-tabs__line {
  width: 40rpx !important;
  background-color: #ff0000 !important;
}

.filter-nav {
  width: 100%;
}

.filter-tab {
  flex: 1 !important;
  min-width: 100% !important;
  font-size: 28rpx;
}

.filter-tab-active {
  font-weight: bold;
  color: #ff0000 !important;
}

/* 车辆列表 */
.car-list {
  width: 100%;
  padding: 0;
  background-color: transparent;
  padding-bottom: 120rpx;
  /* 保留原有的底部内边距，考虑 TabBar 的高度 */
  margin-top: 20rpx;
  /* 从180rpx减小到20rpx，减小列表与筛选栏之间的距离 */
}

.car-item {
  display: flex;
  flex-direction: column;
  padding: 20rpx 30rpx;
  border-bottom: 2rpx solid #f5f5f5;
  background-color: #fff;
  margin: 0 0 20rpx 0;
  align-items: flex-start;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
}

.car-main-content {
  display: flex;
  flex-direction: row;
  width: 100%;
  margin-bottom: 12rpx;
  align-items: flex-start;
  height: 190rpx;
  /* 将高度从170rpx增加到190rpx */
  position: relative;
  overflow: visible;
}

.car-image-container {
  width: 220rpx;
  height: 170rpx;
  /* 从160rpx增加到170rpx */
  margin-right: 24rpx;
  border-radius: 8rpx;
  overflow: hidden;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  align-self: flex-start;
  /* 确保图片在顶部对齐 */
  margin-top: 5rpx;
  /* 添加顶部外边距，与标题对齐 */
}

.car-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.car-content-wrapper {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

/* 增加一个辅助函数，确保标题与内容间距合理 */
.car-title-spacer {
  display: none;
}

.car-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  height: 190rpx;
  /* 将高度从170rpx增加到190rpx，与car-main-content保持一致 */
  box-sizing: border-box;
  position: relative;
  padding: 0;
}

.car-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
  max-height: 84rpx;
  width: 100%;
  margin: 0;
  padding-top: 4rpx;
  flex-shrink: 0;
}

.car-attrs {
  font-size: 24rpx;
  color: #666;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  gap: 12rpx;
  justify-content: space-between;
  width: 100%;
  margin: 0;
  padding-top: 36rpx;
  /* 将顶部内边距从24rpx增加到36rpx，增加与标题的距离 */
}

.separator {
  color: #ddd;
}

/* 调整价格区域样式 */
.price-section {
  width: 100%;
  position: static;
  padding: 0 4rpx 4rpx 0;
  margin: 4rpx 0 0 0;
  /* 增加顶部外边距 */
}

.price-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

/* 创建左侧价格容器 */
.price-info {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: space-between;
}

.sale-price {
  font-size: 32rpx;
  color: #2563eb;
  font-weight: 500;
  white-space: nowrap;
  text-align: right;
  padding-left: 10rpx;
}

.new-price {
  font-size: 22rpx;
  color: #999;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 60%;
  text-decoration: line-through;
  /* 添加横线效果 */
}

.car-date {
  font-size: 22rpx;
  color: #999;
  white-space: nowrap;
  /* 防止换行 */
  flex-shrink: 0;
  /* 防止收缩 */
}

.divider {
  height: 2rpx;
  background-color: #f5f5f5;
  margin: 20rpx 0;
}

.car-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  width: 100%;
  margin-top: 8rpx;
  /* 从0增加到8rpx，增加与上方内容的间距 */
  padding-top: 4rpx;
}

.tag {
  font-size: 20rpx;
  color: #07c160;
  background-color: rgba(7, 193, 96, 0.1);
  padding: 2rpx 10rpx;
  border-radius: 4rpx;
  line-height: 1.4;
  display: inline-block;
}

.car-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12rpx;
}

.action-tag {
  margin-right: 10rpx;
  margin-bottom: 6rpx;
  font-size: 20rpx !important;
  padding: 2rpx 8rpx !important;
}

/* 加载状态样式 */
.loading,
.no-more,
.empty {
  text-align: center;
  padding: 30rpx;
  color: #999;
  font-size: 26rpx;
}

.empty {
  padding: 100rpx 30rpx;
  color: #666;
}

.custom-filter-bar {
  width: 100%;
  background-color: #fff;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
  box-sizing: border-box;
}

.custom-filter-item {
  width: 100%;
  text-align: center;
  font-size: 28rpx;
  color: #333;
  position: relative;
  padding-bottom: 10rpx;
}

.custom-filter-item.active {
  color: #ff0000;
  font-weight: bold;
}

.custom-filter-line {
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #ff0000;
}

.custom-tabs {
  width: 100%;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

.custom-tab {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #333;
  padding: 20rpx 0;
  position: relative;
}

.custom-tab.active {
  color: #ff0000;
  font-weight: bold;
}

.custom-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #ff0000;
}

.tab-content {
  width: 100%;
  background-color: #fff;
}

.van-search__content {
  padding-left: 10rpx !important;
  padding-right: 10rpx !important;
}

.van-field__input {
  min-height: 28px !important;
}

/* 确保下拉菜单横向铺满 */
.custom-tabs {
  width: 100%;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

.filter-dropdown-menu {
  width: 100% !important;
}

/* 强制覆盖 Vant 组件样式 */
.van-dropdown-menu {
  width: 100% !important;
  display: flex !important;
  height: 48px !important;
  line-height: 48px !important;
  transition: all 0.3s ease-in-out !important;
}

.van-dropdown-menu__item {
  flex: 1 !important;
  width: 25% !important;
  /* 强制宽度为 25%，确保四个菜单项平均分配 */
  text-align: center !important;
}

/* 调整菜单标题样式 */
.van-dropdown-menu__title {
  padding: 0 !important;
  max-width: none !important;
  font-size: 28rpx !important;
  display: inline-block !important;
  position: relative !important;
}

/* 修复下拉箭头位置 */
.van-dropdown-menu__title::after {
  position: relative !important;
  display: inline-block !important;
  margin-left: 4px !important;
  border-color: #999 transparent transparent !important;
  border-style: solid !important;
  border-width: 4px 4px 0 !important;
  content: "" !important;
  transition: transform 0.3s !important;
  transform: translateY(-50%) !important;
  transform-origin: center !important;
}

/* 修复激活状态下的箭头 */
.van-dropdown-menu__item--active .van-dropdown-menu__title::after {
  transform: rotate(180deg) !important;
  margin-top: -3px !important;
}

/* 自定义下拉菜单标题样式 */
.custom-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 10px;
}

/* 下拉箭头样式 */
.dropdown-arrow {
  flex-shrink: 0;
  transition: transform 0.3s ease-in-out !important;
}

/* 隐藏原有的下拉箭头 */
.van-dropdown-menu__title::after {
  display: none !important;
}

/* 激活状态下箭头旋转 */
.van-dropdown-item--active .dropdown-arrow {
  transform: rotate(180deg);
  transition: transform 0.3s ease-in-out !important;
}

/* 使用文本作为下拉箭头 */
.dropdown-arrow-text {
  margin-left: 8px;
  font-size: 12px;
  color: #333;
}

/* 激活状态下箭头旋转 */
.van-dropdown-item--active .dropdown-arrow-text {
  transform: rotate(180deg);
  display: inline-block;
  transition: transform 0.3s ease-in-out !important;
}

/* 使用 CSS 绘制三角形 */
.css-arrow {
  width: 0;
  height: 0;
  margin-left: 8px;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #333;
  transition: transform 0.3s ease-in-out !important;
}

/* 激活状态下箭头旋转 */
.van-dropdown-item--active .css-arrow {
  transform: rotate(180deg);
}

/* 页面遮罩层 */
.page-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  z-index: 900;
  opacity: 0;
  transition: opacity 0.3s ease-in-out !important;
  pointer-events: none;
}

.page-mask.show {
  opacity: 1;
  pointer-events: auto;
}

/* 优化下拉菜单过渡动画 */
.van-dropdown-item {
  transition: all 0.3s ease-in-out !important;
}

.van-dropdown-item__content {
  transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out !important;
  transform-origin: top center !important;
}

/* 遮罩层动画 */
.van-overlay {
  transition: opacity 0.3s ease-in-out !important;
}

/* 下拉选项动画 */
.van-cell {
  transition: background-color 0.2s ease-in-out !important;
}

/* 新的筛选菜单样式 */
.filter-menu {
  display: flex;
  width: 100%;
  height: 80rpx;
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
  z-index: 101;
  flex-wrap: wrap;
}

.filter-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 26rpx;
  color: #333;
}

.filter-item.active {
  color: #1C82F5;
}

.filter-item text {
  margin-right: 4rpx;
}

.filter-arrow {
  color: #999;
  transition: transform 0.3s ease;
}

.filter-item.active .filter-arrow {
  color: #1C82F5;
  transform: rotate(180deg);
}

/* 筛选区域容器 */
.filter-container {
  position: relative;
  z-index: 999;
  width: 100%;
  background-color: #fff;
  margin-left: -20rpx;
  margin-right: -20rpx;
  width: calc(100% + 40rpx);
}

/* 筛选内容 */
.filter-content {
  position: absolute;
  top: 79rpx;
  left: 0;
  width: 100%;
  background: #fff;
  z-index: 99;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease-out;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  opacity: 0;
  transform: translateY(-10rpx);
  margin-top: 5rpx;
  /* 添加适当的间隙 */
  border-top: none;
}

.filter-content.show {
  max-height: 600rpx;
  opacity: 1;
  transform: translateY(0);
}

/* 为了更好的滚动体验，添加滚动条样式 */
.filter-content::-webkit-scrollbar {
  width: 4px;
}

.filter-content::-webkit-scrollbar-thumb {
  background-color: #ddd;
  border-radius: 2px;
}

/* 筛选选项容器 */
.filter-options {
  padding: 10rpx 20rpx;
  max-height: 600rpx;
  overflow-y: auto;
  overflow-x: hidden;
  /* 防止水平滚动 */
  box-sizing: border-box;
}

.filter-options::before,
.filter-options::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  height: 20rpx;
  pointer-events: none;
  z-index: 1;
}

.filter-options::before {
  top: 0;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
}

.filter-options::after {
  bottom: 0;
  background: linear-gradient(to top, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
}

/* 筛选选项 */
.filter-option {
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1rpx solid #eee;
}

.filter-option.active {
  color: #1C82F5;
  background-color: #f7f7f7;
}

/* 遮罩层 */
.filter-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 99;
  display: none;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

.filter-mask.show {
  display: block;
}

/* 车辆列表中的最后一个卡片 */
.car-list .car-item:last-child {
  margin-bottom: 30rpx;
  /* 为最后一张卡片添加底部外边距 */
}

/* 确保地区选择器和筛选栏的样式互不影响 */
/* 全局遮罩层 - 提高 z-index */
.global-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1001;
  /* 提高 z-index，确保在筛选栏之上 */
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
}

.global-mask.show {
  opacity: 1;
  visibility: visible;
}

/* 确保地区选择弹出层在全局遮罩层之上 */
.van-popup {
  z-index: 1002 !important;
  /* 提高 z-index，确保在全局遮罩层之上 */
}

/* 顶部弹出的城市选择器样式 */
.city-popup {
  width: 100%;
  max-height: 70vh;
  /* 限制最大高度为视口高度的70% */
  overflow-y: auto;
  border-bottom-left-radius: 16rpx;
  border-bottom-right-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  padding-top: 44px;
  /* 添加顶部内边距，避免内容被状态栏遮挡 */
}

/* 自定义 van-area 组件样式 */
.custom-area .van-picker__toolbar {
  height: 88rpx;
}

.custom-area .van-picker__title {
  font-size: 32rpx;
  font-weight: bold;
}

.custom-area .van-picker__cancel,
.custom-area .van-picker__confirm {
  font-size: 28rpx;
  padding: 0 30rpx;
}

/* 使用 !important 提高优先级 */
.filter-content,
.filter-options,
.filter-option {
  transform: translateX(0) !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
}

.area-container {
  background: #fff;
  position: relative;
  /* 添加定位上下文 */
}

/* 添加关闭按钮样式 */
.area-close-btn {
  position: absolute;
  top: 20rpx;
  right: 30rpx;
  font-size: 28rpx;
  color: #999;
  padding: 20rpx;
  z-index: 1003;
}

.all-city {
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1px solid #eee;
}

.all-city:active {
  background-color: #f7f7f7;
}

/* 筛选标题样式 */
.filter-title {
  opacity: 0;
  transform: translateY(-100%);
  pointer-events: none;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background: #fff;
  z-index: 1002;
  /* 确保筛选标题在最上层 */
  visibility: hidden;
  /* 添加隐藏属性 */
  transition: opacity 0.3s ease, transform 0.3s ease, visibility 0.3s ease;
}

.filter-title.visible {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
  visibility: visible;
  /* 显示时恢复可见性 */
}

.filter-close {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24rpx;
  color: #999;
  padding: 10rpx;
  z-index: 1003;
}

.top-container {
  position: relative;
  width: 100%;
  box-sizing: border-box;
  z-index: 1001;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  overflow: hidden;
  padding: 0;
  /* 移除可能的内边距 */
}

/* 搜索区域 */
.search-bar,
.filter-title {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  padding: 0;
  /* 移除可能的内边距 */
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.search-bar {
  padding: 20rpx 0;
  /* 只保留上下内边距，移除左右内边距 */
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  width: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-bar.hidden {
  opacity: 0;
  transform: translateY(-100%);
  pointer-events: none;
  z-index: -1;
  /* 添加负z-index确保它完全隐藏 */
}

.search-input-wrapper {
  display: flex;
  height: 70rpx;
  width: calc(100% - 40rpx);
  /* 减去左右边距的总和 */
  box-sizing: border-box;
  border-radius: 16rpx;
  overflow: hidden;
  margin: 0 20rpx;
  /* 如果需要与边缘保持一定距离，可以使用外边距代替内边距 */
  border: 2rpx solid #1C82F5;
  background-color: #f7f7f7;
  align-items: center;
}

/* 筛选弹出层样式 */
.filter-popup {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
  padding-top: 88px;
  /* 添加顶部内边距，避免关闭按钮被状态栏遮挡 */
}

.filter-popup-header {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 88rpx;
  position: relative;
  border-bottom: 1px solid #eee;
}

.filter-popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.filter-popup-close {
  position: absolute;
  right: 30rpx;
  font-size: 28rpx;
  color: #999;
  padding: 20rpx;
}

.filter-popup-content {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 120rpx;
  /* 添加底部内边距，为底部固定按钮预留空间 */
}

.filter-section {
  padding: 30rpx;
  border-bottom: 1px solid #eee;
}

.section-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: bold;
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
  padding: 0 15rpx;
}

.slider-labels text {
  font-size: 24rpx;
  color: #666;
  transform: translateX(-50%);
}

/* 自定义滑块样式 */
.van-slider {
  margin: 30rpx 0;
}

.van-slider__button {
  width: 40rpx !important;
  height: 40rpx !important;
  background-color: #fff !important;
  border: 2rpx solid #ffc62c !important;
}

.van-slider__bar {
  background-color: #ffc62c !important;
}

.filter-popup-footer {
  display: flex;
  padding: 20rpx 30rpx;
  border-top: 1px solid #eee;
  position: fixed;
  /* 固定定位 */
  bottom: 40rpx;
  /* 距离底部40rpx，上移 */
  left: 0;
  right: 0;
  background: #fff;
  z-index: 1003;
}

.reset-btn,
.confirm-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 10rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.reset-btn {
  background: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background: #ffc62c;
  color: #fff;
}

/* van-area 组件的自定义样式 */
.van-area {
  height: 400rpx;
}

.van-area__title {
  font-size: 28rpx;
  color: #333;
  padding: 20rpx;
}

.van-picker-column__item {
  font-size: 28rpx;
}

.van-picker-column__item--selected {
  color: #ffc62c;
  font-weight: bold;
}

/* 地区选择器样式优化 */
.filter-section .van-area {
  height: 400rpx;
}

.filter-section .van-picker-column {
  height: 400rpx !important;
}

.filter-section .van-picker-column__item {
  font-size: 28rpx;
  line-height: 80rpx;
}

.filter-section .van-picker-column__item--selected {
  color: #ffc62c;
  font-weight: bold;
}

/* 确保选择器内容居中显示 */
.filter-section .van-picker-column__wrapper {
  height: 400rpx !important;
}

/* 固定头部样式 */
.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  background-color: transparent;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 头部占位符，防止内容被固定头部遮挡 */
.header-placeholder {
  width: 100%;
  height: var(--header-placeholder-height, 180px);
  /* 使用动态计算的高度 */
  transition: height 0.3s ease;
  /* 添加平滑过渡效果 */
}

/* 自定义导航栏 */
.custom-nav {
  width: 100%;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  position: relative;
}

.status-bar {
  width: 100%;
}

.nav-content {
  display: flex;
  height: 44px;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
}

.nav-back {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-top: 2px solid #333;
  border-left: 2px solid #333;
  transform: rotate(-45deg);
}

.back-image {
  width: 20px;
  height: 20px;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
}

.nav-placeholder {
  width: 44px;
}

.back-text {
  font-size: 32rpx;
  color: #333;
  font-weight: normal;
  line-height: 32px;
}