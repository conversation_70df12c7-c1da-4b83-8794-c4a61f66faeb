// pages/train_operation/contract.js
import cosUpload from '../../utils/cos-upload';
import config from '../../config';
import api from '../../utils/api';  // 导入api模块
import util from '../../utils/util';  // 导入util模块

Page({

  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: wx.getSystemInfoSync().statusBarHeight || 20, // 默认值20
    foreignFiles: [], // 外贸公司与海外客户签订的合同附件
    domesticFiles: [], // 外贸公司跟国内出口企业签订的合同附件
    exportFiles: [], // 国内出口企业跟背户公司签订的合同附件
    dealerFiles: [], // 背户公司跟4S店签订的合同附件
    remark: '', // 备注说明
    remarkModified: false, // 备注是否已修改过
    chewu_process_id: '', // 流程ID
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getStatusBarHeight();

    // 保存流程ID
    if (options && options.process_id) {
      this.setData({
        chewu_process_id: options.process_id
      });
      console.log('接收到流程ID:', options.process_id);

      // 当有流程ID时，获取现有合同附件数据
      this.loadExistingFiles(options.process_id);
    }
  },

  /**
   * 获取状态栏高度
   */
  getStatusBarHeight() {
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });
  },

  /**
   * 导航返回
   */
  navigateBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 上传文件
   */
  uploadFile(e) {
    const type = e.currentTarget.dataset.type;
    const fileTypeMap = {
      'foreign': 'foreignFiles',
      'domestic': 'domesticFiles',
      'export': 'exportFiles',
      'dealer': 'dealerFiles'
    };

    const currentFiles = this.data[fileTypeMap[type]];
    if (currentFiles.length >= 3) {
      wx.showToast({
        title: '最多上传3个附件',
        icon: 'none'
      });
      return;
    }

    // 显示操作菜单
    wx.showActionSheet({
      itemList: ['从聊天记录选择', '从相册选择', '拍照'],
      success: (res) => {
        const tapIndex = res.tapIndex;

        if (tapIndex === 0) {
          // 从聊天记录选择
          this.chooseMessageFile(type);
        } else if (tapIndex === 1) {
          // 从相册选择
          this.chooseImage(type, 'album');
        } else if (tapIndex === 2) {
          // 拍照
          this.chooseImage(type, 'camera');
        }
      }
    });
  },

  /**
   * 从聊天记录选择文件
   */
  chooseMessageFile(type) {
    const fileTypeMap = {
      'foreign': 'foreignFiles',
      'domestic': 'domesticFiles',
      'export': 'exportFiles',
      'dealer': 'dealerFiles'
    };

    const currentFiles = this.data[fileTypeMap[type]];

    wx.chooseMessageFile({
      count: 1,
      type: 'file',
      success: (res) => {
        if (!res.tempFiles || !res.tempFiles[0]) {
          wx.showToast({
            title: '未选择文件',
            icon: 'none'
          });
          return;
        }

        const tempFilePath = res.tempFiles[0].path;
        const fileName = res.tempFiles[0].name;

        // 检查文件类型是否为图片
        const isImage = this.checkIsImageFile(fileName);

        this.uploadToCloud(tempFilePath, fileName, type, currentFiles, isImage);
      }
    });
  },

  /**
   * 从相册选择或拍照
   */
  chooseImage(type, sourceType) {
    const fileTypeMap = {
      'foreign': 'foreignFiles',
      'domestic': 'domesticFiles',
      'export': 'exportFiles',
      'dealer': 'dealerFiles'
    };

    const currentFiles = this.data[fileTypeMap[type]];

    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: [sourceType], // 'album' 或 'camera'
      success: (res) => {
        if (!res.tempFilePaths || !res.tempFilePaths[0]) {
          wx.showToast({
            title: '未选择图片',
            icon: 'none'
          });
          return;
        }

        const tempFilePath = res.tempFilePaths[0];
        // 为图片生成一个文件名
        const fileName = `image_${new Date().getTime()}.jpg`;

        // 从相册或相机选择的一定是图片
        this.uploadToCloud(tempFilePath, fileName, type, currentFiles, true);
      }
    });
  },

  /**
   * 检查是否为图片文件
   */
  checkIsImageFile(fileName) {
    if (!fileName) return false;
    const lowerCaseFileName = fileName.toLowerCase();
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    return imageExtensions.some(ext => lowerCaseFileName.endsWith(ext));
  },

  /**
   * 上传文件到云存储
   */
  uploadToCloud(tempFilePath, fileName, type, currentFiles, isImage) {
    const fileTypeMap = {
      'foreign': 'foreignFiles',
      'domestic': 'domesticFiles',
      'export': 'exportFiles',
      'dealer': 'dealerFiles'
    };

    // 清除可能存在的loading提示
    try {
      wx.hideLoading();
    } catch (e) { }

    // 显示新的加载提示
    wx.showLoading({
      title: '上传中...',
      mask: true
    });

    // 使用计时器确保loading能显示至少1秒
    const loadingTimer = setTimeout(() => { }, 1000);

    // 上传文件到腾讯云COS
    cosUpload.uploadFile(tempFilePath, 'contract')
      .then(result => {
        // 确保loading至少显示1秒
        clearTimeout(loadingTimer);

        const files = [...currentFiles, {
          url: this.getFullFileUrl(this.getFilePathSuffix(result.url)),
          name: fileName,
          fileUrl: this.getFilePathSuffix(result.url), // 保存相对路径，用于提交
          isImage: isImage // 添加标记，标识是否为图片
        }];

        const updateData = {};
        updateData[fileTypeMap[type]] = files;

        // 设置数据并隐藏loading
        this.setData(updateData, () => {
          // 在回调中关闭loading，确保数据设置完成
          wx.hideLoading();
          wx.showToast({
            title: '上传成功',
            icon: 'success'
          });
        });
      })
      .catch(error => {
        // 处理错误
        console.error('上传失败:', error);
        clearTimeout(loadingTimer);

        wx.hideLoading();
        wx.showToast({
          title: '上传失败',
          icon: 'none'
        });
      });
  },

  /**
   * 删除文件
   */
  deleteFile(e) {
    const { type, index } = e.currentTarget.dataset;
    const fileTypeMap = {
      'foreign': 'foreignFiles',
      'domestic': 'domesticFiles',
      'export': 'exportFiles',
      'dealer': 'dealerFiles'
    };

    wx.showModal({
      title: '提示',
      content: '确定要删除此文件吗？',
      success: (res) => {
        if (res.confirm) {
          const files = [...this.data[fileTypeMap[type]]];
          files.splice(index, 1);

          const updateData = {};
          updateData[fileTypeMap[type]] = files;

          this.setData(updateData, () => {
            wx.showToast({
              title: '已删除',
              icon: 'success'
            });
          });
        }
      }
    });
  },

  /**
   * 获取文件路径后缀
   */
  getFilePathSuffix(path) {
    if (!path) return '';
    // 检查是否已经包含完整路径
    const baseUrl = config.COS_CONFIG.url + config.COS_CONFIG.path;
    if (path.startsWith('http')) {
      // 从URL中提取路径
      const parts = path.split(baseUrl);
      if (parts.length > 1) {
        // 确保路径以 /uploads 开头
        return '/uploads/' + parts[1];
      }
      return path;
    } else if (path.includes('uploads/')) {
      // 如果已经包含 uploads/ 但不是完整URL
      const parts = path.split('uploads/');
      return '/uploads/' + parts[1];
    } else {
      // 如果是相对路径，直接添加前缀
      return '/uploads/' + path;
    }
  },

  /**
   * 获取完整的文件URL
   */
  getFullFileUrl(pathSuffix) {
    if (!pathSuffix) return '';
    if (pathSuffix.indexOf('https://') === 0 || pathSuffix.indexOf('http://') === 0) {
      return pathSuffix;
    }
    // 移除开头的 /uploads 如果存在
    const cleanPath = pathSuffix.startsWith('/uploads/')
      ? pathSuffix.substring(9)
      : (pathSuffix.startsWith('uploads/') ? pathSuffix.substring(8) : pathSuffix);
    return config.COS_CONFIG.url + config.COS_CONFIG.path + cleanPath;
  },

  /**
   * 处理备注输入
   */
  onRemarkInput(e) {
    this.setData({
      remark: e.detail.value,
      remarkModified: true // 标记备注已被修改过
    });
  },

  /**
   * 暂存
   */
  onSave() {
    // 暂存逻辑，可以保存到本地或服务器
    wx.showToast({
      title: '暂存成功',
      icon: 'success'
    });
  },

  /**
   * 提交
   */
  onSubmit() {
    // 提交前验证
    const { foreignFiles, domesticFiles, exportFiles, dealerFiles, remark, remarkModified, chewu_process_id } = this.data;

    // 检查是否有流程ID
    if (!chewu_process_id) {
      wx.showToast({
        title: '缺少流程ID',
        icon: 'none'
      });
      return;
    }

    // 检查备注说明是否已修改过
    if (!remarkModified) {
      wx.showToast({
        title: '请修改备注说明',
        icon: 'none'
      });
      return;
    }

    if (foreignFiles.length === 0 && domesticFiles.length === 0 &&
      exportFiles.length === 0 && dealerFiles.length === 0) {
      wx.showToast({
        title: '请至少上传一个附件',
        icon: 'none'
      });
      return;
    }

    // 显示Loading
    wx.showLoading({
      title: '提交中...',
      mask: true
    });

    // 从缓存获取app_id和short_name
    const app_id = util.getAppId() || '';
    const userInfo = util.getUserInfo() || {};
    const short_name = userInfo.short_name || '';

    // 准备接口所需参数
    const params = {
      chewu_process_id: chewu_process_id,
      remark: remark || '',
      operator_id: app_id, // 添加操作者ID
      operator_name: short_name // 添加操作者姓名
    };

    // 构造files参数
    const files = {};

    // foreign: 外贸公司与海外客户签订的合同附件
    if (foreignFiles.length > 0) {
      // 修改：将foreign转为数组格式
      files.foreign = foreignFiles.map(file => ({
        name: file.name,
        url: file.fileUrl
      }));
    }

    // domestic: 外贸公司跟国内出口企业签订的合同附件
    if (domesticFiles.length > 0) {
      // 修改：将domestic转为数组格式
      files.domestic = domesticFiles.map(file => ({
        name: file.name,
        url: file.fileUrl
      }));
    }

    // export: 国内出口企业跟背户公司签订的合同附件 
    if (exportFiles.length > 0) {
      // 修改：将export转为数组格式
      files.export = exportFiles.map(file => ({
        name: file.name,
        url: file.fileUrl
      }));
    }

    // dealer: 背户公司跟4S店签订的合同附件
    if (dealerFiles.length > 0) {
      // 修改：将dealer转为数组格式
      files.dealer = dealerFiles.map(file => ({
        name: file.name,
        url: file.fileUrl
      }));
    }

    params.files = JSON.stringify(files);

    // 调用接口
    api.car.setProcessSchedulePost(params)
      .then(res => {
        wx.hideLoading();
        if (res.code === 1 || res.code === 200) {
          wx.showToast({
            title: '提交成功',
            icon: 'success',
            success: () => {
              // 提交成功后，获取页面栈中的上一页实例，调用其刷新方法
              try {
                const pages = getCurrentPages();
                if (pages.length > 1) {
                  const prevPage = pages[pages.length - 2]; // 上一个页面
                  if (prevPage && typeof prevPage.getOrderDetail === 'function') {
                    console.log('调用上一页的刷新方法');
                    prevPage.getOrderDetail(); // 刷新上一页的数据
                  }
                }
              } catch (e) {
                console.error('刷新上一页失败:', e);
              }

              // 然后返回上一页
              setTimeout(() => {
                this.navigateBack();
              }, 1500);
            }
          });
        } else {
          wx.showToast({
            title: res.msg || '提交失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('提交失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '提交失败，请稍后重试',
          icon: 'none'
        });
      });
  },

  /**
   * 获取并加载现有合同附件数据
   * @param {string} processId - 流程ID
   */
  loadExistingFiles(processId) {
    wx.showLoading({
      title: '加载数据...',
      mask: true
    });

    // 调用API获取现有合同附件数据
    api.car.setProcessScheduleGet({
      chewu_process_id: processId
    })
      .then(res => {
        console.log('获取到的合同附件数据:', res);

        let filesData = null;

        // 处理直接返回数组的情况
        if (Array.isArray(res) && res.length > 0) {
          console.log('API直接返回了数组格式数据');
          const firstItem = res[0];

          // 检查是否有files_array属性
          if (firstItem.files_array && typeof firstItem.files_array === 'object') {
            console.log('使用files_array中的数据');
            filesData = firstItem.files_array;
          }
          // 检查是否有files属性，且为字符串
          else if (firstItem.files && typeof firstItem.files === 'string') {
            try {
              console.log('解析files字符串为JSON');
              filesData = JSON.parse(firstItem.files);
            } catch (e) {
              console.error('解析files字符串失败:', e);
            }
          }

          // 如果有remark，也保存下来
          if (firstItem.remark) {
            this.setData({
              remark: firstItem.remark
            });
            console.log('设置备注:', firstItem.remark);
          }
        }
        // 处理不同的数据格式
        else if (res && res.code === 1 && res.data) {
          // 如果data是数组，取第一个元素
          if (Array.isArray(res.data) && res.data.length > 0) {
            const firstItem = res.data[0];
            console.log('处理数组格式的返回数据，提取第一项:', firstItem);

            // 检查是否有files_array属性
            if (firstItem.files_array && typeof firstItem.files_array === 'object') {
              console.log('使用files_array中的数据');
              filesData = firstItem.files_array;
            }
            // 检查是否有files属性，且为字符串
            else if (firstItem.files && typeof firstItem.files === 'string') {
              try {
                console.log('解析files字符串为JSON');
                filesData = JSON.parse(firstItem.files);
              } catch (e) {
                console.error('解析files字符串失败:', e);
              }
            }
            // 如果有remark，也保存下来
            if (firstItem.remark) {
              this.setData({
                remark: firstItem.remark
              });
              console.log('设置备注:', firstItem.remark);
            }
          } else {
            // 原有的处理逻辑：如果返回标准格式（如{code: 1, data: {files: {...}}}）
            if (res.data.files) {
              filesData = res.data.files;
            }
          }
        } else if (res && res.files) {
          // 如果直接返回数据对象（如{files: {...}}）
          filesData = res.files;
        } else if (typeof res === 'string') {
          // 如果返回的是字符串，尝试解析为JSON
          try {
            const jsonData = JSON.parse(res);
            filesData = jsonData.files || jsonData;
          } catch (e) {
            console.error('解析合同附件数据失败:', e);
          }
        }

        if (filesData) {
          console.log('解析后的文件数据:', filesData);

          // 初始化各类型文件数组
          const foreignFiles = [];
          const domesticFiles = [];
          const exportFiles = [];
          const dealerFiles = [];

          // 处理foreign类型文件
          if (filesData.foreign) {
            // 检查foreign是否为数组
            if (Array.isArray(filesData.foreign)) {
              console.log('处理foreign数组数据，数组长度:', filesData.foreign.length);
              // 遍历数组中的每个文件
              filesData.foreign.forEach(file => {
                if (file && file.name && file.url) {
                  // 检查是否为图片文件
                  const isImage = this.checkIsImageFile(file.name);
                  // 处理文件URL，确保是完整的URL
                  const url = this.getFullFileUrl(file.url);

                  // 添加到foreign文件数组
                  foreignFiles.push({
                    name: file.name,
                    url: url,
                    fileUrl: file.url,
                    isImage: isImage
                  });
                }
              });
            } else {
              // 原有的处理逻辑：处理单个文件对象
              const file = filesData.foreign;
              if (file.name && file.url) {
                const isImage = this.checkIsImageFile(file.name);
                const url = this.getFullFileUrl(file.url);
                foreignFiles.push({
                  name: file.name,
                  url: url,
                  fileUrl: file.url,
                  isImage: isImage
                });
              }
            }
          }

          // 处理domestic类型文件
          if (filesData.domestic) {
            // 检查domestic是否为数组
            if (Array.isArray(filesData.domestic)) {
              console.log('处理domestic数组数据，数组长度:', filesData.domestic.length);
              // 遍历数组中的每个文件
              filesData.domestic.forEach(file => {
                if (file && file.name && file.url) {
                  // 检查是否为图片文件
                  const isImage = this.checkIsImageFile(file.name);
                  // 处理文件URL，确保是完整的URL
                  const url = this.getFullFileUrl(file.url);

                  // 添加到domestic文件数组
                  domesticFiles.push({
                    name: file.name,
                    url: url,
                    fileUrl: file.url,
                    isImage: isImage
                  });
                }
              });
            } else {
              // 原有的处理逻辑：处理单个文件对象
              const file = filesData.domestic;
              if (file.name && file.url) {
                const isImage = this.checkIsImageFile(file.name);
                const url = this.getFullFileUrl(file.url);
                domesticFiles.push({
                  name: file.name,
                  url: url,
                  fileUrl: file.url,
                  isImage: isImage
                });
              }
            }
          }

          // 处理export类型文件
          if (filesData.export) {
            // 检查export是否为数组
            if (Array.isArray(filesData.export)) {
              console.log('处理export数组数据，数组长度:', filesData.export.length);
              // 遍历数组中的每个文件
              filesData.export.forEach(file => {
                if (file && file.name && file.url) {
                  // 检查是否为图片文件
                  const isImage = this.checkIsImageFile(file.name);
                  // 处理文件URL，确保是完整的URL
                  const url = this.getFullFileUrl(file.url);

                  // 添加到export文件数组
                  exportFiles.push({
                    name: file.name,
                    url: url,
                    fileUrl: file.url,
                    isImage: isImage
                  });
                }
              });
            } else {
              // 原有的处理逻辑：处理单个文件对象
              const file = filesData.export;
              if (file.name && file.url) {
                const isImage = this.checkIsImageFile(file.name);
                const url = this.getFullFileUrl(file.url);
                exportFiles.push({
                  name: file.name,
                  url: url,
                  fileUrl: file.url,
                  isImage: isImage
                });
              }
            }
          }

          // 处理dealer类型文件
          if (filesData.dealer) {
            // 检查dealer是否为数组
            if (Array.isArray(filesData.dealer)) {
              console.log('处理dealer数组数据，数组长度:', filesData.dealer.length);
              // 遍历数组中的每个文件
              filesData.dealer.forEach(file => {
                if (file && file.name && file.url) {
                  // 检查是否为图片文件
                  const isImage = this.checkIsImageFile(file.name);
                  // 处理文件URL，确保是完整的URL
                  const url = this.getFullFileUrl(file.url);

                  // 添加到dealer文件数组
                  dealerFiles.push({
                    name: file.name,
                    url: url,
                    fileUrl: file.url,
                    isImage: isImage
                  });
                }
              });
            } else {
              // 原有的处理逻辑：处理单个文件对象
              const file = filesData.dealer;
              if (file.name && file.url) {
                const isImage = this.checkIsImageFile(file.name);
                const url = this.getFullFileUrl(file.url);
                dealerFiles.push({
                  name: file.name,
                  url: url,
                  fileUrl: file.url,
                  isImage: isImage
                });
              }
            }
          }

          // 更新页面数据，显示文件
          this.setData({
            foreignFiles,
            domesticFiles,
            exportFiles,
            dealerFiles
          }, () => {
            // 回调显示文件数量
            console.log('文件回显完成，文件数量:', {
              foreignFiles: this.data.foreignFiles.length,
              domesticFiles: this.data.domesticFiles.length,
              exportFiles: this.data.exportFiles.length,
              dealerFiles: this.data.dealerFiles.length
            });
          });

          console.log('已加载现有合同附件数据:', { foreignFiles, domesticFiles, exportFiles, dealerFiles });
        } else {
          console.log('未获取到有效的合同附件数据');
        }
      })
      .catch(err => {
        console.error('获取合同附件数据失败:', err);
      })
      .finally(() => {
        wx.hideLoading();
      });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})