import util from '../../utils/util';
import api from '../../utils/api';
import cosUpload from '../../utils/cos-upload';
const langData = require('../../utils/lang-function.js');
Page({
    data: {
        avatarUrl: '', // 头像URL
        avatar: '', // 头像URL相对路径(存数据)
        companyShortName: '', // 昵称
        mainBrand: '', // 主营品牌文本显示
        selectedBrands: [], // 选中的品牌值数组
        userType: 1, // 1: 车源商, 2: 服务商, 3: 汽配商, 4: 采购商
        vehicleTypes: [], // 车辆类型
        isAgree: false, // 是否同意协议
        isFormValid: false, // 表单是否有效
        vehicleTypeOptions:[],
        isSubmitted: false,  // 添加提交状态标记
        isUploading: false,  // 添加上传状态标记
        statusBarHeight: 20, // 默认状态栏高度
        vehicleTypeContainerStyle: '', // 车辆类型容器样式

        // 汽配商主营类型
        businessTypeOptions:[], // 汽配商主营类型选项
        businessTypes: [], // 选中的主营类型值数组

        //品牌
        brandOptions: [], // 从API获取，不再使用硬编码
        isBrandLoading: false, // 品牌加载状态
        showBrandPicker: false, // 是否显示品牌选择器
        showBrandDropdown: false, // 是否显示品牌下拉框

        //业务
        businessOptions:[],
        showBusinessPicker: false, // 是否显示业务选择器
        showBusinessDropdown: false, // 是否显示业务下拉框

        // 配件类目（汽配商专用）
        partsCategories: [], // 从API获取，不再使用硬编码
        isPartsLoading: false, // 配件类目加载状态
        showPartsDropdown: false, // 是否显示配件类目下拉框
        currentLevel: 1, // 当前选择级别
        selectedLevel1: null, // 选中的一级分类
        selectedLevel2: null, // 选中的二级分类
        selectedLevel3: null, // 选中的三级分类
        level2Options: [], // 二级分类选项
        level3Options: [], // 三级分类选项
        mainPartsCategory: '', // 主营配件类目显示文本
        selectedPartsPath: [], // 选中的配件类目路径

        // 新增多选相关
        selectedPartCategories: [], // 选中的配件类目数组

        // 国家和地区（采购商专用）
        countryOptions: [], // 从API获取，不再使用硬编码
        isAreaLoading: false, // 地区加载状态
        showCountryRegionDropdown: false, // 是否显示国家地区选择器
        selectedCountry: null, // 选中的国家
        selectedRegion: null, // 选中的地区
        currentRegionOptions: [], // 当前可选地区
        selectionStep: 'country', // 当前选择步骤：country 或 region
        mainLocation: '', // 主要地点显示文本
        selectedCountries: [], // 多选国家数组

        //手机号码和密码
        phone: '',
        password: '', // 添加密码字段
        showVehicleTypeOptionsDropdown:false,
        mainVehicleType:'',
        selectedVehicleType:[],
        text: {}
    },

  //语言刷新
  refreshLanguage(newLang){
    console.log('页面语言已切换到:', newLang);
  },

  // 更新页面文本
  updateText() {
    const businessTypeOptions = [
        { name: langData.t('complete_vehicles'), value: '1', isActive: false },
        { name: langData.t('auto_parts'), value: '2', isActive: false }
    ]
    const businessOptions = [
        { name: langData.t('vehicle_services'), value: '1', isActive: false },
        { name: langData.t('inspection'), value: '2', isActive: false },
        { name: langData.t('flashing_reflashing'), value: '3', isActive: false },
        { name: langData.t('export_channel'), value: '4', isActive: false },
        { name: langData.t('domestic_logistics'), value: '5', isActive: false },
        { name: langData.t('international_logistics'), value: '6', isActive: false },
        { name: langData.t('finance'), value: '7', isActive: false },
        { name: langData.t('modification_refurbishment'), value: '8', isActive: false },
        { name: langData.t('insurance'), value: '9', isActive: false },
        { name: langData.t('extended_after_sales_service'), value: '10', isActive: false },
        { name: langData.t('others'), value: '11', isActive: false }
    ]

    const vehicleTypeOptions = [
        { name: langData.t('used_cars'), value: '1', isActive: false },
        { name: langData.t('new_cars'), value: '2', isActive: false },
        { name: langData.t('fleet_vehicles'), value: '3', isActive: false },
        // { name: langData.t('construction'), value: '4', isActive: false }
        { name: langData.t('right_hand_drive_cars'), value: '5', isActive: false },
        { name: langData.t('commercial_vehicles'), value: '6', isActive: false },
        { name: langData.t('low_speed_vehicles'), value: '7', isActive: false },
        { name: langData.t('trucks'), value: '8', isActive: false },
        { name: langData.t('buses'), value: '9', isActive: false },
        { name: langData.t('special_vehicle'), value: '10', isActive: false },
        { name: langData.t('trailers'), value: '11', isActive: false },
    ];
    this.setData({
      vehicleTypeOptions,
      businessOptions,
      businessTypeOptions
    })
    this.setData({
      text:{
        name:langData.t('name'),
        register_personal_information:langData.t('register_personal_information'),
        click_to_upload_avatar:langData.t('click_to_upload_avatar'),
        please_enter_the_company_name:langData.t('please_enter_the_company_name'),
        please_select_user_type:langData.t('please_select_user_type'),
        vehicle_supplier:langData.t('vehicle_supplier'),
        auto_parts_dealer:langData.t('auto_parts_dealer'),
        service_provider:langData.t('service_provider'),
        buyer:langData.t('buyer'),
        international_buyer:langData.t('international_buyer'),
        primary_brands_select_multiple:langData.t('primary_brands_select_multiple'),
        business_types_Multi_select:langData.t('business_types_Multi_select'),
        vehicle_types_multi_select:langData.t('vehicle_types_multi_select'),
        select_your_main_vehicle_types:langData.t('select_your_main_vehicle_types'),
        select_vehicle_your_main_brands:langData.t('select_vehicle_your_main_brands'),
        core_business_select_multiple:langData.t('core_business_select_multiple'),
        select_your_business_type:langData.t('select_your_business_type'),
        sales_markets_multi_select:langData.t('sales_markets_multi_select'),
        select_your_country:langData.t('select_your_country'),
        main_parts_category:langData.t('main_parts_category'),
        select_your_main_parts_category:langData.t('select_your_main_parts_category'),
      }
    })
  },

    onLoad(options) {
        this.updateText();
        console.log('个人信息页面接收参数:', options);

        // 初始化页面数据
        this.setData({
            vehicleTypes: []
        });

        this.checkFormValidity();

        // 获取状态栏高度
        const systemInfo = wx.getSystemInfoSync();
        this.setData({
            statusBarHeight: systemInfo.statusBarHeight,
            // 计算适当的容器样式，防止滚动
            vehicleTypeContainerStyle: 'display: flex; flex-wrap: wrap; overflow: hidden;'
        });

        // 获取品牌列表
        this.fetchBrandList();

        // 获取配件类目列表
        this.fetchPartsList();

        // 获取国家地区列表
        this.fetchAreaList();

        // 保存手机号和密码
        this.setData({
            phone: options.phone || util.generateRandomPhone(),
            password: options.password || '' // 保存密码
        });

        console.log('设置手机号:', this.data.phone);
        console.log('设置密码:', this.data.password ? '已设置(不显示明文)' : '未设置');
    },
    // 获取品牌列表
    fetchBrandList(lang = '') {
        console.log(lang)
        this.setData({
            isBrandLoading: true
        });

        api.car.getBrandList('',lang)
            .then(res => {
                if (res) {
                    // 转换API返回的品牌数据为需要的格式
                    const brandOptions = res.map(item => ({
                        name: item.brand_name || item.name,
                        value: item.id || item.brand_id,
                        isActive: false
                    }));

                    this.setData({
                        brandOptions,
                        isBrandLoading: false
                    });
                } else {
                    console.error('Brand list API returned unexpected format:', res);
                    this.setData({
                        isBrandLoading: false
                    });
                    wx.showToast({
                        title: '获取品牌列表失败',
                        icon: 'none'
                    });
                }
            })
            .catch(err => {
                console.error('Error fetching brand list:', err);
                this.setData({
                    isBrandLoading: false
                });
                wx.showToast({
                    title: '获取品牌列表失败',
                    icon: 'none'
                });
            });
    },

    // 获取配件类目列表
    fetchPartsList() {
        this.setData({
            isPartsLoading: true
        });

        api.car.autoParts()
            .then(res => {
                if (res) {
                    // 为每个类目添加 isActive 属性
                    const partsCategories = res.map(item => ({
                        ...item,
                        isActive: false
                    }));

                    this.setData({
                        partsCategories,
                        isPartsLoading: false
                    });
                } else {
                    console.error('Parts categories API returned unexpected format:', res);
                    this.setData({
                        isPartsLoading: false
                    });
                    wx.showToast({
                        title: '获取配件类目失败',
                        icon: 'none'
                    });
                }
            })
            .catch(err => {
                console.error('Error fetching parts categories:', err);
                this.setData({
                    isPartsLoading: false
                });
                wx.showToast({
                    title: '获取配件类目失败',
                    icon: 'none'
                });
            });
    },

    // 获取国家地区列表
    fetchAreaList(lang = 'zh-CN') {
        this.setData({
            isAreaLoading: true
        });
        const params = {
          lang:lang
        }
        api.common.area(params)
            .then(res => {
                if (res) {
                    // 提取国家数据（level=2）
                    const countryOptions = res
                        .filter(item => item.level == 2)
                        .map(item => ({
                            id: item.id,
                            name: item.name,
                            value: item.id.toString(),
                            code: item.code,
                            children: item.childrens || [],
                            isActive: false
                        }));
                    this.setData({
                        countryOptions,
                        isAreaLoading: false
                    });
                } else {
                    console.error('Area API returned unexpected format:', res);
                    this.setData({
                        isAreaLoading: false
                    });
                    wx.showToast({
                        title: '获取地区数据失败',
                        icon: 'none'
                    });
                }
            })
            .catch(err => {
                console.error('Error fetching area data:', err);
                this.setData({
                    isAreaLoading: false
                });
                wx.showToast({
                    title: '获取地区数据失败',
                    icon: 'none'
                });
            });
    },

    // 处理返回按钮事件
    handleBack() {
        // 直接清除登录信息
        wx.removeStorageSync('userInfo');

        // 返回到我的页面（使用 switchTab 更流畅）
        wx.switchTab({
            url: '/pages/my/index'
        });
    },

    // 自定义导航栏返回按钮事件
    onNavBack() {
        this.handleBack();
    },

    // 头像选择处理函数
    onChooseAvatar(e) {
        const { avatarUrl } = e.detail;

        // 防止重复上传
        if (this.data.isUploading) {
            return;
        }

        if (avatarUrl) {
            // 设置上传状态
            this.setData({
                isUploading: true
            });

            // 显示上传中提示
            wx.showLoading({
                title: '上传中...',
                mask: true
            });

            // 将微信临时链接转为本地临时文件
            wx.getImageInfo({
                src: avatarUrl,
                success: (res) => {
                    // 上传图片到腾讯云COS
                    cosUpload.uploadFile(res.path, 'my')
                        .then(result => {
                            console.log(result)
                            // 更新页面数据
                            this.setData({
                                avatarUrl: result.url,
                                avatar: '/' + result.key,
                                isUploading: false
                            });

                            wx.hideLoading();
                            wx.showToast({
                                title: '头像上传成功',
                                icon: 'success'
                            });
                        })
                        .catch(error => {
                            console.error('上传失败:', error);
                            this.setData({
                                isUploading: false
                            });
                            wx.hideLoading();
                            wx.showToast({
                                title: '上传失败',
                                icon: 'none'
                            });
                        });
                },
                fail: (error) => {
                    console.error('获取图片信息失败:', error);
                    this.setData({
                        isUploading: false
                    });
                    wx.hideLoading();
                    wx.showToast({
                        title: '获取图片信息失败',
                        icon: 'none'
                    });
                }
            });
        }
    },

    // 输入昵称
    onCompanyShortNameInput(e) {
        this.setData({
            companyShortName: e.detail.value
        });
        this.checkFormValidity();
    },

    // 切换品牌下拉框显示
    toggleBrandDropdown() {
        this.fetchBrandList(langData.getCurrentLang());
        this.setData({
            showBrandDropdown: !this.data.showBrandDropdown
        });
    },

     // 选择主营车类型
    selectVehicleType(e) {
      const { name, value, index } = e.currentTarget.dataset;
      const vehicleTypeOptions = [...this.data.vehicleTypeOptions];
      vehicleTypeOptions[index].isActive = !vehicleTypeOptions[index].isActive;
      let selectedVehicleTypeData = [];
      let mainVehicleTypeData = [];
      vehicleTypeOptions.forEach(element => {
        if(element.isActive){
          selectedVehicleTypeData.push(element.value)
          mainVehicleTypeData.push(element.name)
        }
      });
      
      this.setData({
          vehicleTypes:selectedVehicleTypeData,
          vehicleTypeOptions,
          selectedVehicleType:selectedVehicleTypeData,
          mainVehicleType:mainVehicleTypeData.join(','),
      });
    },
    // 选择品牌
    selectBrand(e) {
        const { name, value, index } = e.currentTarget.dataset;
        const brandOptions = [...this.data.brandOptions];

        // 切换选中状态
        brandOptions[index].isActive = !brandOptions[index].isActive;

        // 更新 selectedBrands 数组
        const selectedBrands = brandOptions
            .filter(option => option.isActive)
            .map(option => option.value);

        // 更新显示文本
        const mainBrand = brandOptions
            .filter(option => option.isActive)
            .map(option => option.name)
            .join(', ');

        this.setData({
            brandOptions,
            selectedBrands,
            mainBrand
        });

        this.checkFormValidity();
    },


    // 切换业务下拉框显示
    toggleBusinessDropdown() {
        this.setData({
            showBusinessDropdown: !this.data.showBusinessDropdown
        });
    },
    // 切换业务下拉框显示
    toggleVehicleTypeOptionsDropdown() {
      this.setData({
        showVehicleTypeOptionsDropdown: !this.data.showVehicleTypeOptionsDropdown
      });
    },
    // 选择业务
    selectBusiness(e) {
        const { name, value, index } = e.currentTarget.dataset;
        const businessOptions = [...this.data.businessOptions];

        // 切换选中状态
        businessOptions[index].isActive = !businessOptions[index].isActive;

        // 更新 selectedBusiness 数组
        const selectedBusiness = businessOptions
            .filter(option => option.isActive)
            .map(option => option.value);

        // 更新显示文本
        const mainBusiness = businessOptions
            .filter(option => option.isActive)
            .map(option => option.name)
            .join(', ');

        this.setData({
            businessOptions,
            selectedBusiness,
            mainBusiness
        });

        this.checkFormValidity();
    },

    // 切换配件类目下拉框显示
    togglePartsDropdown() {
        this.setData({
            showPartsDropdown: !this.data.showPartsDropdown,
            currentLevel: 1, // 重置到一级分类
            selectedLevel1: null,
            selectedLevel2: null,
            selectedLevel3: null,
            level2Options: [],
            level3Options: []
        });
    },

    // 选择配件类目（修改为多选）
    selectPartCategory(e) {
        const { id, name, index } = e.currentTarget.dataset;

        // 深拷贝当前分类数据
        const partsCategories = JSON.parse(JSON.stringify(this.data.partsCategories));

        // 切换选中状态
        if (!partsCategories[index].isActive) {
            partsCategories[index].isActive = true;
        } else {
            partsCategories[index].isActive = false;
        }

        // 更新选中的配件类目数组
        const selectedPartCategories = partsCategories
            .filter(item => item.isActive)
            .map(item => ({
                id: item.id,
                name: item.name
            }));

        // 更新显示文本
        const mainPartsCategory = selectedPartCategories
            .map(item => item.name)
            .join(', ');

        // 更新选中的配件类目路径数组（只包含ID）
        const selectedPartsPath = selectedPartCategories.map(item => item.id);

        this.setData({
            partsCategories,
            selectedPartCategories,
            mainPartsCategory,
            selectedPartsPath
        });

        this.checkFormValidity();
    },

    // 原来的选择一级分类函数保留但不再使用
    selectLevel1(e) {
        const { id } = e.currentTarget.dataset;
        const { partsCategories } = this.data;

        // 获取选中的一级分类
        const selectedCategory = partsCategories.find(item => item.id === id);
        this.setData({
            selectedLevel1: selectedCategory,
            selectedLevel2: null,
            selectedLevel3: null,
            level2Options: selectedCategory.children,
            level3Options: [],
            currentLevel: 2
        });
    },

    // 原来的选择二级分类函数保留但不再使用
    selectLevel2(e) {
        const { id } = e.currentTarget.dataset;
        const { level2Options } = this.data;

        // 获取选中的二级分类
        const selectedCategory = level2Options.find(item => item.id === id);

        // 如果二级分类没有子级，直接可以选择确认
        if (!selectedCategory.children || selectedCategory.children.length === 0) {
            this.setData({
                selectedLevel2: selectedCategory,
                selectedLevel3: null,
                level3Options: [],
                currentLevel: 2 // 保持在二级，因为没有三级
            });
        } else {
            this.setData({
                selectedLevel2: selectedCategory,
                selectedLevel3: null,
                level3Options: selectedCategory.children,
                currentLevel: 3
            });
        }
    },

    // 原来的选择三级分类函数保留但不再使用
    selectLevel3(e) {
        const { id } = e.currentTarget.dataset;
        const { level3Options } = this.data;

        // 获取选中的三级分类
        const selectedCategory = level3Options.find(item => item.id === id);

        this.setData({
            selectedLevel3: selectedCategory
        });
    },

    // 原来的确认配件类目选择函数保留但不再使用
    confirmPartsSelection() {
        const { selectedLevel1, selectedLevel2, selectedLevel3, level3Options } = this.data;

        // 检查是否已选择一级分类
        if (!selectedLevel1) {
            wx.showToast({
                title: '请选择一级分类',
                icon: 'none'
            });
            return;
        }

        // 检查是否已选择二级分类
        if (!selectedLevel2) {
            wx.showToast({
                title: '请选择二级分类',
                icon: 'none'
            });
            return;
        }

        // 如果有三级分类选项但未选择，则要求选择三级
        if (level3Options && level3Options.length > 0 && !selectedLevel3) {
            wx.showToast({
                title: '请选择三级分类',
                icon: 'none'
            });
            return;
        }

        // 构建显示文本和选择路径
        let mainPartsCategory, selectedPartsPath;

        if (selectedLevel3) {
            // 有三级分类的情况
            mainPartsCategory = `${selectedLevel1.name} > ${selectedLevel2.name} > ${selectedLevel3.name}`;
            selectedPartsPath = [selectedLevel1.id, selectedLevel2.id, selectedLevel3.id];
        } else {
            // 只有二级分类的情况
            mainPartsCategory = `${selectedLevel1.name} > ${selectedLevel2.name}`;
            selectedPartsPath = [selectedLevel1.id, selectedLevel2.id];
        }

        this.setData({
            mainPartsCategory,
            selectedPartsPath,
            showPartsDropdown: false
        });

        this.checkFormValidity();
    },

    // 返回上一级分类
    goBackLevel() {
        const { currentLevel } = this.data;

        if (currentLevel === 2) {
            this.setData({
                currentLevel: 1,
                selectedLevel1: null,
                selectedLevel2: null,
                selectedLevel3: null,
                level2Options: [],
                level3Options: []
            });
        } else if (currentLevel === 3) {
            this.setData({
                currentLevel: 2,
                selectedLevel2: null,
                selectedLevel3: null,
                level3Options: []
            });
        }
    },

    // 设置用户类型
    setUserType(e) {
        const type = parseInt(e.currentTarget.dataset.type);
        this.setData({
            userType: type,
            // 清空之前选择的类型
            vehicleTypes: [],
            // 重置品牌和业务选择
            showBrandDropdown: false,
            showBusinessDropdown: false,
            showCountryRegionDropdown: false, // 重置国家地区下拉框
            // 重置配件类目选择
            showPartsDropdown: false,
            selectedLevel1: null,
            selectedLevel2: null,
            selectedLevel3: null,
            level2Options: [],
            level3Options: [],
            mainPartsCategory: '',
            selectedPartsPath: [],
            currentLevel: 1,
            // 重置配件类目多选
            selectedPartCategories: [],
            partsCategories: this.data.partsCategories.map(item => ({ ...item, isActive: false })),
            // 重置品牌选择
            selectedBrands: [],
            mainBrand: '',
            // 重置品牌选项的选中状态
            brandOptions: this.data.brandOptions.map(item => ({ ...item, isActive: false })),
            // 重置业务选择
            selectedBusiness: [],
            mainBusiness: '',
            // 重置地区选择
            selectedCountry: null,
            selectedRegion: null,
            currentRegionOptions: [],
            selectionStep: 'country',
            mainLocation: '',
            // 重置车辆类型选择
            vehicleTypeOptions: this.data.vehicleTypeOptions.map(item => ({ ...item, isActive: false })),
            // 重置业务选择
            businessOptions: this.data.businessOptions.map(item => ({ ...item, isActive: false })),
            // 重置主营类型选择
            businessTypeOptions: this.data.businessTypeOptions.map(item => ({ ...item, isActive: false })),
            businessTypes: [],
            // 重置国家地区多选
            selectedCountries: [],
            countryOptions: this.data.countryOptions.map(item => ({ ...item, isActive: false })),
            // 重置国家地区下拉框
            showCountryRegionDropdown: false,
            selectionStep: 'country',
            // 重置主要地点
            mainLocation: '',
            //主营车类型显示状态
            showVehicleTypeOptionsDropdown:false,
            mainVehicleType: '',
        });
        this.checkFormValidity();
    },

    // 切换车辆类型（新实现）
    toggleVehicleTypeNew(e) {
        const index = e.currentTarget.dataset.index;

        // 创建数组副本
        const vehicleTypeOptions = [...this.data.vehicleTypeOptions];

        // 直接使用索引切换选中状态
        vehicleTypeOptions[index].isActive = !vehicleTypeOptions[index].isActive;

        // 更新车型数组
        const vehicleTypes = vehicleTypeOptions
            .filter(option => option.isActive)
            .map(option => option.value);

        // 使用setData更新状态
        this.setData({
            vehicleTypeOptions,
            vehicleTypes
        });

        this.checkFormValidity();
    },

    // 切换汽配商主营类型
    toggleBusinessType(e) {
        const index = e.currentTarget.dataset.index;

        // 创建数组副本
        const businessTypeOptions = [...this.data.businessTypeOptions];

        // 直接使用索引切换选中状态
        businessTypeOptions[index].isActive = !businessTypeOptions[index].isActive;

        // 更新主营类型数组
        const businessTypes = businessTypeOptions
            .filter(option => option.isActive)
            .map(option => option.value);

        // 使用setData更新状态
        this.setData({
            businessTypeOptions,
            businessTypes
        });

        this.checkFormValidity();
    },

    // 原有的切换车辆类型函数保留，避免影响其他功能
    toggleVehicleType(e) {
        const type = e.currentTarget.dataset.type;
        const value = e.currentTarget.dataset.value;
        const index = parseInt(e.currentTarget.dataset.index);

        // 防止错误点击，创建一个精确的匹配机制
        // 通过克隆数组来避免引用问题
        const vehicleTypeOptions = JSON.parse(JSON.stringify(this.data.vehicleTypeOptions));

        // 直接通过多重匹配确保找到正确的选项
        const targetIndex = vehicleTypeOptions.findIndex(item =>
            item.name === type && item.value === value
        );

        if (targetIndex !== -1) {
            // 切换选中状态
            vehicleTypeOptions[targetIndex].isActive = !vehicleTypeOptions[targetIndex].isActive;

            // 更新 vehicleTypes 数组
            const vehicleTypes = vehicleTypeOptions
                .filter(option => option.isActive)
                .map(option => option.value);

            this.setData({
                vehicleTypeOptions,
                vehicleTypes
            });

            this.checkFormValidity();
        } else {
            console.error('Could not find vehicle type with name:', type, 'and value:', value);
        }
    },

    // 切换国家地区下拉框
    toggleCountryRegionDropdown() {
        // 获取国家地区列表
        this.fetchAreaList(langData.getCurrentLang());
        this.setData({
            showCountryRegionDropdown: !this.data.showCountryRegionDropdown
        });
    },

    // 选择国家
    selectCountry(e) {
        const { value, name, index } = e.currentTarget.dataset;
        const countryOptions = [...this.data.countryOptions];

        // 切换选中状态
        countryOptions[index].isActive = !countryOptions[index].isActive;

        // 更新 selectedCountries 数组
        const selectedCountries = countryOptions
            .filter(option => option.isActive)
            .map(option => option.id);

        // 更新显示文本
        const mainLocation = countryOptions
            .filter(option => option.isActive)
            .map(option => option.name)
            .join(', ');

        this.setData({
            countryOptions,
            selectedCountries,
            mainLocation
        });

        this.checkFormValidity();
    },

    // 检查表单是否有效
    checkFormValidity() {
        const { companyShortName, selectedBrands, selectedBusiness, selectedPartsPath, selectedCountries, userType, vehicleTypes, businessTypes } = this.data;

        // 基本验证：公司名称必填
        let isValid = companyShortName.trim() !== '';

        // 根据用户类型验证
        if (userType === 1) { // 车源商
            // 需要选择品牌和车辆类型
            isValid = isValid && selectedBrands && selectedBrands.length > 0;
            isValid = isValid && vehicleTypes.length > 0;
        } else if (userType === 3) { // 汽配商
            // 需要选择品牌、主营类型和配件类目（现在只需要一级）
            isValid = isValid && selectedBrands && selectedBrands.length > 0;
            isValid = isValid && businessTypes && businessTypes.length > 0; // 验证主营类型
            isValid = isValid && selectedPartsPath && selectedPartsPath.length > 0; // 修改为只要选择了配件类目就可以
        } else if (userType === 2) { // 服务商
            // 需要选择业务
            isValid = isValid && selectedBusiness && selectedBusiness.length > 0;
        } else if (userType === 4) { // 采购商
            // 需要选择国家
            isValid = isValid && selectedCountries !== null && selectedCountries.length > 0;
            // 需要选择品牌
            isValid = isValid && selectedBrands && selectedBrands.length > 0;
        }

        this.setData({
            isFormValid: isValid
        });
    },

    // 提交信息
    submitInfo() {
        const {
            avatar,
            avatarUrl,
            companyShortName,
            selectedBrands,
            selectedBusiness,
            selectedPartsPath,
            selectedCountries,
            userType,
            vehicleTypes,
            businessTypes,
            password // 获取密码
        } = this.data;

        // 验证头像
        if (!avatar) {
            wx.showToast({
                title: '请上传头像',
                icon: 'none'
            });
            return;
        }

        // 验证昵称
        if (!companyShortName.trim()) {
            wx.showToast({
                title: '请输入昵称',
                icon: 'none'
            });
            return;
        }

        // 验证主营品牌（车源商、汽配商和采购商）
        if ((userType === 1 || userType === 3 || userType === 4) && (!selectedBrands || selectedBrands.length === 0)) {
            wx.showToast({
                title: '请选择主营品牌',
                icon: 'none'
            });
            return;
        }

        // 验证主营类型（仅汽配商）
        if (userType === 3 && (!businessTypes || businessTypes.length === 0)) {
            wx.showToast({
                title: '请选择主营类型',
                icon: 'none'
            });
            return;
        }

        // 验证配件类目（仅汽配商）
        if (userType === 3 && (!selectedPartsPath || selectedPartsPath.length === 0)) {
            wx.showToast({
                title: '请选择主营配件类目',
                icon: 'none'
            });
            return;
        }

        // 验证主营业务（仅服务商）
        if (userType === 2 && (!selectedBusiness || selectedBusiness.length === 0)) {
            wx.showToast({
                title: '请选择主营业务',
                icon: 'none'
            });
            return;
        }

        // 验证国家地区（仅采购商）
        if (userType === 4 && (!selectedCountries || selectedCountries.length === 0)) {
            wx.showToast({
                title: '请选择国家',
                icon: 'none'
            });
            return;
        }

        // 验证车辆类型（仅车源商）
        if (userType === 1 && vehicleTypes.length === 0) {
            wx.showToast({
                title: '请选择车辆类型',
                icon: 'none'
            });
            return;
        }

        // 显示提交中
        wx.showLoading({
            title: '提交中...',
            mask: true
        });

        // 防止重复提交
        this.setData({
            isSubmitted: true
        });

        // 构建请求参数
        const params = {
            logo: avatar,
            short_name: companyShortName,
            identity: userType,
            mobile: this.data.phone
        };

        // 如果有密码，添加到参数中
        if (password) {
            params.password = password;
            console.log('添加密码参数（不显示明文）');
        }

        // 根据用户类型添加不同的字段
        if (userType === 1) { // 车源商
            params.ids = selectedBrands.join(',');
            params.types = vehicleTypes.join(',');
        } else if (userType === 3) { // 汽配商
            params.ids = selectedBrands.join(',');
            params.auto_parts_ids = selectedPartsPath.join(','); // 使用多选的ID数组
            params.business_types = businessTypes.join(','); // 添加主营类型参数
        } else if (userType === 2) { // 服务商
            params.ids = selectedBusiness.join(',');
        } else if (userType === 4) { // 采购商
            params.ids = selectedBrands.join(',');
            params.country_ids = selectedCountries.join(',');
            params.area = this.data.mainLocation
        }

        console.log('提交个人信息，参数:', { ...params, password: password ? '***已设置***' : '未设置' });

        // 调用API提交信息
        api.user.completeInfo(params)
            .then(res => {
                wx.hideLoading();
                if (res) {
                    // 更新本地存储的用户信息
                    const userInfo = {
                        purePhoneNumber: this.data.phone,
                        info: {
                            is_info: 1,
                            short_name: companyShortName,
                            avatar: avatarUrl,
                            identity: this.getUserTypeIdentity(userType),
                            app_id: res.app_id,
                            account_type: "1"
                        }
                    };
                    util.storeLoginFlags(userInfo)

                    wx.showToast({
                        title: '信息提交成功',
                        icon: 'success'
                    });

                    // 延迟跳转
                    setTimeout(() => {
                        wx.switchTab({
                            url: '/pages/my/index'
                        });
                    }, 1500);
                } else {
                    this.setData({
                        isSubmitted: false
                    });

                    wx.showToast({
                        title: res.msg || '提交失败',
                        icon: 'none'
                    });
                }
            })
            .catch(err => {
                wx.hideLoading();
                this.setData({
                    isSubmitted: false
                });
                console.log(err, 123333)
                wx.showToast({
                    title: '网络错误，请重试',
                    icon: 'none'
                });
            });
    },

    // 根据用户类型获取身份标识
    getUserTypeIdentity(userType) {
        switch (userType) {
            case 1: return 'vehicle'; // 车源商
            case 3: return 'trade'; // 汽配商
            case 2: return 'service'; // 服务商
            case 4: return 'purchase'; // 采购商
            default: return 'vehicle';
        }
    },

    // 返回上一页
    navigateBack() {
        wx.navigateBack();
    },
}); 