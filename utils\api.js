/**
 * API接口管理
 * 集中管理所有接口，便于维护
 */
import http from './request';

// 车辆相关接口
const carApi = {
  // 获取车辆列表
  getCarList: (params) => http.get('FxcXHmLown.php/vehicle/used_car/getList', params),

  // 获取车辆详情
  getCarDetail: (id) => http.get(`FxcXHmLown.php/vehicle/used_car/getDetail?id=${id}`),

  // 获取品牌列表 - 注意：这里使用两个接口，如果一个失败会尝试另一个
  getBrandList: async (b_id = 0,lang = 'zh-CN') => {
    try {
      // 尝试第一个接口
      return await http.get('FxcXHmLown.php/vehicle/brand/getList?b_id=' + b_id+'&lang='+lang);
    } catch (error) {
      // console.log('第一个品牌接口失败，尝试第二个接口', error);
      // 第一个接口失败，尝试第二个接口
      return await http.get('wechat/car/getBrands');
    }
  },

  // 获取推荐列表
  getRecommendBrandList: async () => {
    return await http.get('FxcXHmLown.php/vehicle/brand/getRecommendBrandList');
  },

  // 获取车辆列表
  getList: (params) => http.get('wechat/vehicle/getList', params),

  getDetail: (params) => http.get('wechat/vehicle/detail', params),

  //品牌车系车型
  // getBrandList: () => http.get('wechat/car/getBrands'),

  getSeriesList: (params) => http.get('wechat/car/getSeries', params),

  getVehicleList: (params) => http.get('wechat/car/getVehicles', params),

  //轮播图
  getBannerList: (params) => http.get('wechat/car/getBanner', params),

  //收藏列表
  favoriteList: (params) => http.get('wechat/vehicle/favoriteList', params),

  //收藏车辆
  favorite: (params) => http.post('wechat/vehicle/favorite', params),

  //汽配类目
  autoParts: (params) => http.get('wechat/vehicle/auto_parts', params),

  //车务
  trainOperation: (params) => http.get('wechat/vehicle/train_operation', params),

  //车务详情
  trainOperationDetail: (params) => http.get('wechat/transportation/getOrderDetail', params),

  //车务检测附件信息是否上传
  trainOperationAttachment: (params) => http.post('wechat/transportation/checkProcessSchedule', params),

  //新增更新附件
  setProcessSchedulePost: (params) => http.post('wechat/transportation/setProcessSchedule', params),
  //获取附件列表
  setProcessScheduleGet: (params) => http.get('wechat/transportation/setProcessSchedule', params),

  //获取变更合同记录
  getContractChangeLog: (params) => http.get('wechat/transportation/getContractChangeLog', params),
};

// 用户相关接口
const userApi = {

  // 微信授权信息
  getWechatInfo: (data) => http.post('api/wechat/getSessionKey', data),

  // 微信手机号一键登录
  loginWithWechatPhone: (data) => http.post('api/wechat/decryptPhoneNumber', data),

  // 完善用户信息
  completeInfo: (data) => http.post('api/wechat/setInfo', data),

  //更新用户头像
  updateUserLogo: (data) => http.post('wechat/user/updateUserLogo', data),

  //更新用户昵称
  updateUserNickname: (data) => http.post('wechat/user/updateUserNickname', data),

  // 提交企业认证信息
  setAuthInfo: (data) => http.post('api/wechat/setAuthInfo', data),

  //企业认证提交审核
  submitCertification: (data) => http.post('api/wechat/submitCertification', data),

  //获取审核状态
  getCertificationStatus: (data) => http.get('api/wechat/getCertificationStatus', data),

  // 获取企业认证信息
  getCertificationStatus: (data) => http.get('api/wechat/getCertificationStatus', data),

  //获取认证信息和状态


  //根据app_id获取商家的公司名称地址和车辆数据
  getCompanyInfo: (params) => http.get('wechat/car/getCompanyInfo', params),



  //获取验证码
  getCode: (data) => http.post('addons/shopro/index/send', data),

  //更新手机号
  updatePhone: (data) => http.post('wechat/user/verifyCode', data),

  //获取微信授权头像和昵称相关信息
  getWechatUserInfo: (params) => http.post('api/wechat/decryptUserInfo', params),

  //意见反馈
  feedback: (params) => http.post('wechat/user/feedback', params),

  //充值记录
  rechargeRecord: (params) => http.get('wechat/user/rechargeRecord', params),

  //取消订单
  cancelOrder: (params) => http.get('wechat/user/cancel', params),

  //查询是否存在待支付订单
  checkOrder: (params) => http.get('wechat/user/checkTradeState', params),

  //传入app_id查询是否进行过黄金或者白银的首月首年充值
  checkFirstMonthFirstYear: (params) => http.get('wechat/user/checkIsRecharge', params),

  //主商户信息
  mainMerchantInfo: (params) => http.get('wechat/user/mainMerchantInfo', params),

  //视频列表
  getVideoList: (params) => http.get('wechat/video/getList', params),

  //视频详情
  getVideoDetail: (params) => http.get('wechat/video/getDetail', params),

    //视频合计详情
    getVideoCollectionsDetail: (params) => http.get('wechat/video/getVideoCollectionsDetail', params),

  //账号密码登录
  accountLogin: (params) => http.post('api/wechat/loginByPassword', params),

  //验证码获取
  codeLogin: (params) => http.post('api/wechat/sendCode', params),

  //验证码验证获取手机号和密码
  codeLoginVerify: (params) => http.post('api/wechat/registerByPassword', params),

  //修改密码
  updatePassword: (params) => http.post('api/wechat/changePassword', params),

  //撤回企业认证审核
  withdrawCertification: (data) => http.post('api/wechat/withdrawCertification', data),

  // 营业执照OCR识别
  bizLicenseOCR: (data) => http.post('api/recognition/bizLicenseOCR', data),

  // 查询应用使用次数
  getAppUsage: (data) => http.post('api/recognition/getAppUsage', data),
};

// 侠友圈相关接口
const momentsApi = {
  // 获取列表
  getList: (params) => http.get('wechat/purchase/getList', params),

  getDetail: (params) => http.get('wechat/purchase/getDetail', params),

  //删除帖子
  delete: (params) => http.post('wechat/purchase/delete', params),

  //编辑帖子
  edit: (params) => http.post('wechat/purchase/edit', params),

  // 发布信息
  publish: (data) => http.post('wechat/purchase/add', data),

  //更新帖子
  update: (data) => http.post('wechat/purchase/update', data),

  //获取详情
  getDetail: (params) => http.get('wechat/purchase/getDetail', params),

  //草稿箱列表 draftList
  draftList: (params) => http.get('wechat/purchase/draftList', params),
  //获取评论列表
  getCommentsList: (params) => http.get('wechat/purchase/commentsList', params),

  toggleLike: (params) => http.post('wechat/purchase/toggleLike', params),

  addComment: (params) => http.post('wechat/purchase/addComment', params),

  //收到的点赞
  getLikeList: (params) => http.get('wechat/purchase/receivedLike', params),

  //收到的评论
  getCommentList: (params) => http.get('wechat/purchase/receivedComment', params),

  //删除我的帖子
  deleteMyPost: (params) => http.post('wechat/purchase/deletePost', params),
  //删除点赞
  deleteLike: (params) => http.post('wechat/purchase/deleteLike', params),
  //删除评论
  deleteComment: (params) => http.post('wechat/purchase/deleteComment', params),

  //撤回审核帖子
  withdraw: (params) => http.post('wechat/purchase/withdraw', params),

}

//报价单
const quoteApi = {
  // 获取列表
  getList: (params) => http.get('wechat/quote/getList', params),

  // 获取详情
  getDetail: (params) => http.get('wechat/quote/detail', params),

  // 报价数据写入接口
  addQuote: (data) => http.post('wechat/quote/add', data),
}

//商户
const merchantApi = {
  // 商户信息
  information: (params) => http.get('wechat/merchant/information', params),
}

//公共
const commonApi = {
  // 世界地区
  area: (params) => http.get('wechat/vehicle/area', params),
}
//微信支付接口
const wxPayApi = {
  // 获取微信支付参数
  getWxPayParams: (data) => http.post('wechat/pay/unifiedOrder', data),

  //重新付款
  rePay: (data) => http.post('wechat/pay/repayAgain', data),
}

// 金融服务
const serviceFinance = {
  getInfo: (id) => http.get(`FxcXHmLown.php/management/finance/getFinanceInfo?id=${id}`),
  getList: (params) => http.get('FxcXHmLown.php/management/finance/getFinanceList', params),
}

const serviceFlash = {
  //刷机服务
  getList: (params) => http.get('FxcXHmLown.php/management/serviceflash/serviceFlashList', params),
  getInfo: (id) => http.get(`FxcXHmLown.php/management/serviceflash/serviceFlashInfo?id=${id}`),
}

const testingService = {
  //检查服务
  getList: (params) => http.get('FxcXHmLown.php/management/testingservice/TestingserviceList', params),
  getInfo: (id) => http.get(`FxcXHmLown.php/management/testingservice/testingServiceInfo?id=${id}`),
}

const domesticTrailer = {
  //国内拖车服务
  getList: (params) => http.get('FxcXHmLown.php/management/domesticTrailer/domesticTrailerList', params),
  getInfo: (id) => http.get(`FxcXHmLown.php/management/domesticTrailer/domesticTrailerInfo?id=${id}`),
}

const foreignLogistics = {
  //国际物流
  getList: (params) => http.get('FxcXHmLown.php/management/foreignlogistics/foreignLogisticsList', params),
  getInfo: (id) => http.get(`FxcXHmLown.php/management/foreignlogistics/foreignLogisticsInfo?id=${id}`),
}

//通道服务
const channelService = {
  //通道服务列表
  getList: (params) => http.get('wechat/supplyer/channellist', params),
  //通道服务详情
  getInfo: (id, params, options) => http.get(`wechat/supplyer/channelDetail?id=${id}`, params, options),

}
//车务服务
const vehicleService = {
  //车务服务列表
  getList: (params, options) => http.get('wechat/supplyer/transactionlist', params, {
    toast: false, // 禁用默认toast
    allowErrorCodes: [200, 0, 1], // 允许的业务状态码
    ...(options || {})
  }),
  //车务服务详情
  getInfo: (id, params, options) => http.get(`wechat/supplyer/transactionDetail?id=${id}`, params, {
    toast: false, // 禁用默认toast
    allowErrorCodes: [200, 0, 1], // 允许的业务状态码
    ...(options || {})
  }),
}
// 统一导出所有API
export default {
  car: carApi,
  user: userApi,
  moments: momentsApi,
  quote: quoteApi,
  merchant: merchantApi,
  common: commonApi,
  wxPay: wxPayApi,
  serviceFinance: serviceFinance,
  serviceFlash: serviceFlash,
  testingService: testingService,
  domesticTrailer: domesticTrailer,
  foreignLogistics: foreignLogistics,
  channelService: channelService,
  vehicleService: vehicleService,
}; 