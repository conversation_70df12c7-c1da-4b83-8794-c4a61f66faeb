.container {
  padding: 20rpx;
} 

/* 整体容器 */
.evaluate-container {
  width: 100%;
  height: 100vh;
  background: linear-gradient(to bottom, #e74c50 0%, #e74c50 30%, #f8f8f8 100%);
  display: flex;
  flex-direction: column;
}

/* 导航栏样式 */
.nav-bar {
  width: 100%;
  height: 88rpx;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  color: #fff;
  position: relative;
  margin-top: 100rpx;
}

.nav-back {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

.back-icon {
  font-size: 40rpx;
  font-weight: bold;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  margin-right: 90rpx;
}

.nav-right {
  display: flex;
  align-items: center;
}

.more-icon, .record-icon {
  font-size: 32rpx;
  margin-left: 20rpx;
}

/* 表单容器 */
.form-container {
  margin: 30rpx;
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08);
}

/* 表单项 */
.form-item {
  display: flex;
  height: 100rpx;
  border-bottom: 1rpx solid #eee;
  padding: 0 30rpx;
  align-items: center;
  transition: background-color 0.3s ease;
}

.form-item:active {
  background-color: #f9f9f9;
}

.form-item:last-child {
  border-bottom: none;
}

.label {
  width: 220rpx;
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.value-container {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.value {
  flex: 1;
  font-size: 30rpx;
  color: #999;
}

.arrow {
  font-size: 36rpx;
  color: #ccc;
  margin-left: 10rpx;
}

/* 提交区域 */
.submit-container {
  margin: 30rpx;
  padding-bottom: 50rpx;
}

/* 协议区域 */
.agreement-container {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30rpx;
}

.checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid #fff;
  border-radius: 50%;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 6rpx;
  background-color: rgba(255, 255, 255, 0.2);
}

.checkbox.checked {
  background: #fff;
}

.check-icon {
  color: #e74c50;
  font-size: 24rpx;
  font-weight: bold;
}

.agreement-text {
  flex: 1;
  font-size: 26rpx;
  color: #fff;
  line-height: 1.5;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.link {
  color: #fff;
  text-decoration: underline;
}

/* 提交按钮 */
.submit-button {
  width: 100%;
  height: 88rpx;
  background-color: #e30b1b;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 44rpx;
  border: none;
  box-shadow: 0 4rpx 16rpx rgba(227, 11, 27, 0.3);
  transition: all 0.3s ease;
}

.submit-button:active {
  transform: scale(0.98);
  background-color: #c90916;
  box-shadow: 0 2rpx 8rpx rgba(227, 11, 27, 0.2);
}

.submit-button::after {
  border: none;
}

/* 时间选择器弹出层 */
.time-picker-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.time-picker-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  z-index: 1001;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.time-picker-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.time-picker-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
}

.close-icon {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 48rpx;
  color: #333;
  font-weight: 300;
  padding: 10rpx;
}

.time-picker-content {
  display: flex;
  padding: 20rpx 0;
}

.time-picker-column {
  flex: 1;
  position: relative;
  text-align: center;
}

.picker-item {
  line-height: 70rpx;
  text-align: center;
  font-size: 32rpx;
  color: #333;
}

.picker-label {
  position: absolute;
  top: 50%;
  right: 20%;
  transform: translateY(-50%);
  font-size: 36rpx;
  color: #333;
  font-weight: 500;
}

.time-picker-footer {
  padding: 20rpx 30rpx 50rpx;
}

.confirm-button {
  width: 100%;
  height: 88rpx;
  background-color: #e30b1b;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 44rpx;
  border: none;
  box-shadow: 0 4rpx 16rpx rgba(227, 11, 27, 0.3);
  transition: all 0.3s ease;
}

.confirm-button:active {
  transform: scale(0.98);
  background-color: #c90916;
  box-shadow: 0 2rpx 8rpx rgba(227, 11, 27, 0.2);
}

.confirm-button::after {
  border: none;
} 