Component({
    properties: {
        current: {
            type: String,
            value: 'home'
        }
    },
    data: {
        tabs: [
            {
                icon: '/assets/icons/home.png',
                selectedIcon: '/assets/icons/home-selected.png',
                text: '首页',
                key: 'home',
                path: '/pages/index/index'
            },
            {
                icon: '/assets/icons/buy.png',
                selectedIcon: '/assets/icons/buy-selected.png',
                text: '找车',
                key: 'buy',
                path: '/pages/buy/index'
            },
            {
                icon: '',
                selectedIcon: '',
                text: '评估',
                key: 'evaluate',
                path: '/pages/evaluate/index'
            },
            {
                icon: '/assets/icons/my.png',
                selectedIcon: '/assets/icons/my-selected.png',
                text: '我的',
                key: 'my',
                path: '/pages/my/index'
            }
        ]
    },
    methods: {
        onTabTap(e) {
            const { key, path } = e.currentTarget.dataset
            if (key !== this.properties.current) {
                wx.switchTab({
                    url: path
                })
            }
        }
    }
}) 