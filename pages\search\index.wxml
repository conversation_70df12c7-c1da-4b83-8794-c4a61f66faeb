<view class="container">
  <view class="search-header">
    <view class="search-input-wrap">
      <image class="search-icon" src="/assets/images/search.png" mode="aspectFit"></image>
      <input class="search-input" placeholder="搜索车型" value="{{keyword}}" bindinput="onInput" bindconfirm="onSearch" confirm-type="search" focus="{{true}}" />
    </view>
    <view class="search-btn" bindtap="onSearch">搜索</view>
  </view>
  
  <view class="search-results">
    <view class="no-result" wx:if="{{searchResults.length === 0}}">
      <text>没有找到相关车型</text>
    </view>
    <view class="result-list" wx:else>
      <view class="result-item" wx:for="{{searchResults}}" wx:key="id">
        <image class="result-image" src="{{item.image}}" mode="aspectFill"></image>
        <view class="result-info">
          <text class="result-name">{{item.name}}</text>
          <text class="result-price">{{item.price}}万</text>
        </view>
      </view>
    </view>
  </view>
</view> 