// 修改引入方式
import { areaList } from '@vant/area-data';
// 导入 API 模块
import api from '../../utils/api';
// 导入util工具类
import util from '../../utils/util';

import share from '../../utils/share';  // 确保导入 分享

Page({
    behaviors: [share], //分享设置
    data: {
        //处理分享页面 统一写
        shareData: {},
        searchValue: '',
        active: 'brand', // 默认选中品牌标签
        carList: [], // 改为空数组，这样就不会显示预设数据
        areaList: areaList, // 直接使用导入的 areaList 数据
        showAreaPopup: false, // 控制弹出层显示
        selectedAreaCode: '', // 移除默认值
        selectedCity: '', // 移除默认值 '广州'
        // 品牌筛选选项
        brandValue: 0,
        brandOptions: [], // 改为空数组，等待接口数据填充
        brandText: '品牌',
        // 价格筛选选项
        priceValue: 0,
        priceOptions: [
            { text: '价格', value: 0 },
            { text: '价格降序', value: 'desc' },
            { text: '价格升序', value: 'asc' }
        ],
        priceText: '价格',
        // 排序筛选选项
        sortValue: 0,
        sortOptions: [
            { text: '默认排序', value: 0 },
            { text: '价格降序', value: 'price_desc' },
            { text: '价格升序', value: 'price_asc' },
            { text: '最新发布', value: 'date_desc' }
        ],
        sortText: '排序',
        // 里程筛选选项
        mileageValue: 0,
        mileageOptions: [
            { text: '里程', value: 0 },
            { text: '里程降序', value: 'desc' },
            { text: '里程升序', value: 'asc' }
        ],
        mileageText: '里程',
        isDropdownOpen: false,
        pageStyle: '',
        currentDropdownIndex: null,
        currentFilter: '', // 当前选中的筛选类型
        showFilterContent: false, // 是否显示筛选内容
        topContainerHeight: 110, // 顶部容器高度，单位px
        statusBarHeight: 0, // 状态栏高度
        // 添加分页相关数据
        currentPage: 1,
        pageSize: 10,
        hasMoreData: true,
        isLoading: false,
        hideFilterBar: false,
        // 添加热门城市数据
        hotCities: [
            { name: '北京', code: '110100', fullName: '北京市' },
            { name: '上海', code: '310100', fullName: '上海市' },
            { name: '广州', code: '440100', fullName: '广东省 广州市' },
            { name: '深圳', code: '440300', fullName: '广东省 深圳市' },
            { name: '杭州', code: '330100', fullName: '浙江省 杭州市' },
            { name: '南京', code: '320100', fullName: '江苏省 南京市' },
            { name: '武汉', code: '420100', fullName: '湖北省 武汉市' },
            { name: '成都', code: '510100', fullName: '四川省 成都市' },
            { name: '重庆', code: '500100', fullName: '重庆市' }
        ],
        currentCityFullName: '', // 添加 currentCityFullName 属性
        filterTitleVisible: false, // 初始状态下筛选标题应该隐藏
        // 添加筛选相关数据
        filterPopupVisible: false,  // 控制筛选弹出层显示
        selectedProvince: '',       // 选中的省份
        areaList: areaList, // 直接使用导入的 areaList 数据
        vehicleType: 'new'
    },
    vehicle(type) {
        const arr = {
            'used': '二手车',
            'new': '新车',
            'batch': '批量车',
            'right': '右舵车'
        };

        return arr[type] || '新车'
    },
    async onLoad(options) {
        // 获取状态栏高度
        const systemInfo = wx.getSystemInfoSync();
        this.setData({
            statusBarHeight: systemInfo.statusBarHeight
        });

        // 设置动态标题
        if (options.title) {
            wx.setNavigationBarTitle({
                title: this.vehicle(options.title)
            });
            this.setData({
                vehicleType: options.title
            });
        }

        // 隐藏原生导航栏
        wx.hideNavigationBarLoading();
        // 隐藏原生导航栏
        wx.setNavigationBarColor({
            frontColor: '#ffffff',
            backgroundColor: 'transparent',
            animation: {
                duration: 0,
                timingFunc: 'easeIn'
            }
        });

        //处理分享页面 统一写
        const shareData = {
            title: this.vehicle(options.title),
            path: '/pages/vehicle/index',
            isDetailPage: false, // 标记是否详情页
        }
        this.setData({ shareData });

        // 设置默认显示文本
        this.setData({
            selectedCity: '全国'
        });

        try {
            const brandList = await this.getBrandList();
            // 再获取车辆列表
            await this.getCarList();
        } catch (error) {
            console.error('数据加载失败:', error);
        }

        // 更新页面样式，添加渐变背景
        const bgStyle = 'background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);';

        // 设置CSS变量用于头部占位符的高度计算
        const cssVars = `--status-bar-height: ${systemInfo.statusBarHeight}px;`;

        this.setData({
            pageStyle: bgStyle + cssVars
        });
    },


    onShow() {
        if (typeof this.getTabBar === 'function' && this.getTabBar()) {
            this.getTabBar().setData({
                selected: 1  // 确保选中购车 tab
            });
        }

        // 检查是否有搜索条件
        const searchFilter = wx.getStorageSync('searchFilter');
        if (searchFilter && searchFilter.from === 'index') {
            // 清除存储的搜索条件
            wx.removeStorageSync('searchFilter');

            // 设置搜索关键词和城市
            this.setData({
                searchValue: searchFilter.keyword,
                selectedCity: searchFilter.city,
                currentCityFullName: searchFilter.fullCityName
            });

            // 构建查询参数
            const params = {};

            // 添加搜索关键词
            if (searchFilter.keyword) {
                params.title_desc = searchFilter.keyword;
            }

            // 添加城市筛选
            if (searchFilter.city !== '全国' && searchFilter.fullCityName) {
                params.car_city = searchFilter.fullCityName;
            }

            // 使用参数搜索
            this.getCarList(params);
        }
    },
    // 位置点击事件
    onLocationTap() {
        // 确保关闭任何可能打开的筛选菜单
        this.setData({
            showAreaPopup: true,
            // 重置筛选相关状态
            currentFilter: '',
            showFilterContent: false,
            isDropdownOpen: false,
            // 添加页面遮罩样式
            pageStyle: 'overflow: hidden; position: relative;',
            // 临时隐藏筛选栏
            hideFilterBar: true
        });
    },
    // 处理搜索输入
    onSearchInput(e) {
        this.setData({
            searchValue: e.detail.value
        });
    },
    // 搜索事件
    onSearch(e) {
        const keyword = typeof e.detail === 'string' ? e.detail : this.data.searchValue;
        // 更新搜索关键词到状态
        this.setData({
            searchValue: keyword || '',
            // isLoading: true
        });

        // 直接调用 getCarList，让它来处理所有参数的组合
        // 不需要在这里构建参数，让 getCarList 统一处理
        this.getCarList();
    },
    // 分享按钮点击事件
    onShareTap() {
        wx.showShareMenu({
            withShareTicket: true,
            menus: ['shareAppMessage', 'shareTimeline']
        });
    },
    // 更多按钮点击事件
    onMoreTap() {
        wx.showActionSheet({
            itemList: ['筛选', '排序', '收藏'],
            success(res) {
                console.log(res.tapIndex);
            }
        });
    },
    // 标签点击事件
    onTabClick(e) {
        const name = e.currentTarget.dataset.name;
        this.setData({
            active: name
        });

        // 根据标签名称获取对应的数据
        switch (name) {
            case 'brand':
                // 获取品牌筛选数据
                break;
            case 'price':
                // 获取价格筛选数据
                break;
            case 'age':
                // 获取车龄筛选数据
                break;
            case 'mileage':
                // 获取里程筛选数据
                break;
        }
    },
    // 车辆项点击事件
    onCarItemTap(e) {
        const id = e.currentTarget.dataset.id;
        // 使用 navigateTo 代替 redirectTo
        wx.navigateTo({
            url: `/pages/buy/detail?id=${id}`
        });
    },
    // 添加筛选按钮点击事件处理方法
    onFilterTap() {
        // 获取当前页面滚动位置
        wx.createSelectorQuery()
            .selectViewport()
            .scrollOffset(res => {
                this.scrollPosition = res.scrollTop || 0;
            })
            .exec();

        // 打开全屏筛选弹出层
        this.setData({
            filterPopupVisible: true,
            showFilterContent: false,
            currentFilter: 'filter',
            pageStyle: 'overflow: hidden;'  // 锁定页面滚动
        });
    },
    // 关闭城市选择器
    onCloseAreaPopup() {
        this.setData({
            showAreaPopup: false,
            // 移除页面遮罩样式
            pageStyle: '',
            // 恢复显示筛选栏
            hideFilterBar: false
        });
    },
    // 选择全国
    onSelectAllCity() {
        this.setData({
            selectedCity: '全国',
            selectedAreaCode: '',
            currentCityFullName: '',
            showAreaPopup: false,
            pageStyle: '',
            hideFilterBar: false
        }, () => {
            // 重新获取数据，不传城市参数
            this.getCarList();
        });
    },
    // 手动确认地区选择
    onManualConfirmArea() {
        // 获取当前选中的省市信息
        const { selectedProvince, selectedCity, selectedAreaCode } = this.data;

        // 如果没有选择任何地区，则默认为全国
        if (!selectedProvince && !selectedCity) {
            this.onSelectAllCity();
            return;
        }

        // 构建完整的地区名称（省 市）- 用于请求参数
        const fullCityName = selectedCity ? `${selectedProvince} ${selectedCity}` : selectedProvince;

        // 获取显示的城市名称（只显示市名，并去掉"市"字）
        const displayCity = (selectedCity || selectedProvince).replace(/市$/, '');

        this.setData({
            selectedCity: displayCity,
            showAreaPopup: false,
            pageStyle: '',
            hideFilterBar: false,
            currentCityFullName: fullCityName
        }, () => {
            this.getCarList({
                car_city: fullCityName
            });
        });
    },
    // 重置地区选择
    onResetAreaFilter() {
        this.setData({
            selectedProvince: '',
            selectedCity: '',
            selectedAreaCode: ''
        });
    },
    // 修改确认选择地区的方法
    onConfirmArea(e) {
        const { values } = e.detail;
        // 只取省市两级
        const province = values[0]?.name || '';
        const city = values[1]?.name || '';

        // 构建完整的地区名称（省 市）- 用于请求参数
        const fullCityName = city ? `${province} ${city}` : province;

        // 获取显示的城市名称（只显示市名，并去掉"市"字）
        const displayCity = (city || province).replace(/市$/, '');

        this.setData({
            selectedCity: displayCity,
            selectedAreaCode: values[1]?.code || values[0]?.code,
            showAreaPopup: false,
            pageStyle: '',
            hideFilterBar: false,
            currentCityFullName: fullCityName
        }, () => {
            this.getCarList({
                car_city: fullCityName
            });
        });
    },
    // 品牌筛选变化事件
    onBrandChange(e) {
        const value = e.detail;
        const text = this.getOptionText('brandOptions', value);
        this.setData({
            brandValue: value,
            brandText: text
        });
        this.filterCars();
    },
    // 价格筛选变化事件
    onPriceChange(e) {
        const value = e.detail;
        const text = this.getOptionText('priceOptions', value);
        this.setData({
            priceValue: value,
            priceText: text
        });
        this.filterCars();
    },
    // 里程筛选变化事件
    onMileageChange(e) {
        const value = e.detail;
        const text = this.getOptionText('mileageOptions', value);
        this.setData({
            mileageValue: value,
            mileageText: text
        });
        this.filterCars();
    },
    // 获取选项文本的辅助方法
    getOptionText(optionsName, value) {
        const options = this.data[optionsName];
        for (let i = 0; i < options.length; i++) {
            if (options[i].value === value) {
                return options[i].text;
            }
        }
        return '';
    },
    // 下拉菜单打开事件
    onDropdownOpen(e) {
        // 获取当前打开的下拉菜单索引
        const index = e.currentTarget.dataset.index;

        // 设置当前打开的下拉菜单索引
        this.setData({
            isDropdownOpen: true,
            pageStyle: 'overflow: hidden; height: 100vh;',
            currentDropdownIndex: index
        });

        // 关闭其他下拉菜单
        const dropdownItems = this.selectAllComponents('.van-dropdown-item');
        dropdownItems.forEach((item, i) => {
            if (i !== index && item.data.showPopup) {
                item.toggle(false);
            }
        });
    },
    // 下拉菜单关闭事件
    onDropdownClose() {
        this.setData({
            isDropdownOpen: false,
            pageStyle: ''
        });
    },
    // 锁定页面滚动
    lockPage() {
        // 获取页面根元素
        const pageContainer = document.querySelector('.container');
        if (pageContainer) {
            pageContainer.style.overflow = 'hidden';
            pageContainer.style.height = '100vh';
        }
    },
    // 解锁页面滚动
    unlockPage() {
        // 获取页面根元素
        const pageContainer = document.querySelector('.container');
        if (pageContainer) {
            pageContainer.style.overflow = '';
            pageContainer.style.height = '';
        }
    },
    // 阻止页面滚动
    preventScroll() {
        return false;
    },
    // 添加一个方法来处理下拉菜单的点击事件
    onDropdownItemClick(e) {
        const { index } = e.currentTarget.dataset;

        // 获取所有下拉菜单项
        const dropdownItems = this.selectAllComponents('.van-dropdown-item');

        // 如果当前有打开的下拉菜单，且不是点击的这个，则关闭它
        dropdownItems.forEach((item, i) => {
            if (i !== index && item.data.showPopup) {
                item.toggle(false);
            }
        });
    },
    // 切换筛选内容显示状态
    showFilterContent(type) {
        if (this.data.currentFilter === type && this.data.showFilterContent) {
            this.setData({
                showFilterContent: false,
                currentFilter: '',
                pageStyle: '',
                filterTitleVisible: false
            });
        } else {
            this.setData({
                currentFilter: type,
                showFilterContent: true,
                pageStyle: 'overflow: hidden;',
                filterTitleVisible: true
            }, () => {
                console.log('筛选内容已更新:', {
                    currentFilter: this.data.currentFilter,
                    showFilterContent: this.data.showFilterContent,
                    options: this.data[`${type}Options`]
                });
            });
        }
    },
    // 关闭筛选内容
    closeFilter() {
        this.setData({
            showFilterContent: false,
            currentFilter: '',
            pageStyle: '',
            filterTitleVisible: false
        });
    },
    // 选择筛选选项
    onFilterOptionSelect(e) {
        const { type, value } = e.currentTarget.dataset;

        // 获取选中的选项文本
        const options = this.data[`${type}Options`];
        const selectedOption = options.find(item => item.value === value);
        const text = selectedOption ? selectedOption.text : '';

        // 更新状态
        const updateData = {
            [`${type}Value`]: value,
            [`${type}Text`]: text,
            showFilterContent: false,
            currentFilter: '',
            pageStyle: '',
            filterTitleVisible: false
        };

        this.setData(updateData, () => {
            // 构建请求参数
            const params = {};

            // 如果是品牌筛选
            if (type === 'brand' && value !== 0) {
                params.brand_id = value;
            }

            // 重新获取数据
            this.getCarList(params);
        });
    },
    // 统一的获取车辆列表方法
    async getCarList(params = {}) {
        try {
            this.setData({ isLoading: true });

            // 构建查询参数
            const queryParams = {
                page: params.page || this.data.currentPage || 1,
                list_rows: this.data.pageSize,
                ...params
            };

            //
            queryParams.type = this.data.vehicleType;

            // 添加搜索关键词
            if (this.data.searchValue) {
                queryParams.title_desc = this.data.searchValue;
            }

            // 添加城市筛选
            if (this.data.selectedCity !== '全国' && this.data.currentCityFullName) {
                queryParams.car_city = this.data.currentCityFullName;
            }

            // 添加品牌筛选
            if (this.data.brandValue) {
                queryParams.brand_id = this.data.brandValue;
            }

            // 添加价格筛选
            if (this.data.priceValue) {
                queryParams.price_sort = this.data.priceValue;
            }

            const result = await api.car.getList(queryParams);

            if (result && result.data) {
                const newCarList = result.data.map(car => {
                    // 尝试解析 urls 字符串为数组
                    let imageUrl = '';
                    if (car.urls && typeof car.urls === 'string') {
                        try {
                            const urlsArray = JSON.parse(car.urls);
                            if (urlsArray && urlsArray.length > 0) {
                                imageUrl = urlsArray[0]; // 使用第一张图片
                            }
                        } catch (e) {
                            console.error('解析 URLs 失败', e);
                        }
                    }

                    // 如果解析失败或没有图片，使用 main_url 或默认图片
                    if (!imageUrl) {
                        imageUrl = car.main_url || 'https://p9-dcd.byteimg.com/motor-mis-img/9bf27d1eccd74d35d7efc4917dbaf13e~tplv-f042mdwyw7-original:960:0.image';
                    }

                    return {
                        id: car.id,
                        title: car.ui_vehicle_name || car.title_desc,
                        imageUrl: imageUrl,
                        year: car.first_registration_time ? car.first_registration_time.substring(0, 4) : '',
                        mileage: (car.mileage || 0).toString(),
                        location: car.vehicle_source_location || '',
                        date: car.create_time ? car.create_time.substring(0, 10) : '',
                        salePrice: car.sell_price || '--',
                        newPrice: car.official_price || '',
                        badge: car.badge || '新车',
                        nature: car.nature_name || '运营车',
                        deadline: car.deadline || '2017年',
                        sell_number: car.sell_number || '200',
                    };
                });

                // 更新页面数据
                this.setData({
                    carList: queryParams.page === 1 ? newCarList : [...this.data.carList, ...newCarList],
                    currentPage: queryParams.page,
                    hasMoreData: newCarList.length === this.data.pageSize
                });
            }
        } catch (error) {
            console.error('获取车辆列表失败:', error);
            wx.showToast({
                title: '获取数据失败',
                icon: 'none'
            });
        } finally {
            this.setData({ isLoading: false });
            wx.stopPullDownRefresh();
        }
    },
    // 选择城市事件
    onSelectHotCity(e) {
        const city = e.currentTarget.dataset.city;
        this.setData({
            selectedCity: city.name,
            selectedAreaCode: city.code,
            showAreaPopup: false,
            pageStyle: '',
            hideFilterBar: false
        }, () => {
            // 在状态更新后调用获取数据的方法
            // 准备请求参数
            const params = {};

            // 添加城市参数
            if (city.name !== '全国') {
                params.car_city = city.fullName;
            }

            // 如果有搜索关键词，添加搜索参数
            if (this.data.searchValue) {
                params.title_desc = this.data.searchValue;
            }

            this.getCarList(params);
        });
    },
    // 加载更多数据
    onReachBottom() {
        if (this.data.hasMoreData && !this.data.isLoading) {
            this.getCarList({ page: this.data.currentPage + 1 });
        }
    },
    // 下拉刷新
    onPullDownRefresh() {
        this.getCarList().then(() => {
            wx.stopPullDownRefresh();
        });
    },
    // 获取品牌列表
    async getBrandList() {
        try {
            const result = await api.car.getBrandList();

            // 检查 result 是否是数组
            if (Array.isArray(result)) {
                // 转换数据格式
                const brandList = result.map(brand => {
                    // 检查每个品牌对象的结构
                    return {
                        text: brand.brand_name,
                        value: brand.id
                    };
                });

                // 在列表开头添加"全部"选项
                brandList.unshift({ text: '品牌', value: 0 });

                // 使用 Promise 包装 setData
                return new Promise((resolve) => {
                    this.setData({
                        brandOptions: brandList,
                        brandText: '品牌',
                        brandValue: 0
                    }, () => {
                        // 验证数据是否成功设置
                        const currentData = this.data.brandOptions;
                        resolve(brandList);
                    });
                });
            } else {
                console.error('API 返回的数据格式不正确:', result);
                throw new Error('品牌数据格式不正确');
            }
        } catch (error) {
            console.error('获取品牌列表失败:', error);
            wx.showToast({
                title: '获取品牌列表失败',
                icon: 'none',
                duration: 2000
            });
            throw error;
        }
    },
    // 关闭筛选弹出层
    onCloseFilterPopup() {
        this.setData({
            filterPopupVisible: false,
            currentFilter: ''
        });

        // 延迟恢复滚动位置，确保DOM已更新
        setTimeout(() => {
            if (typeof this.scrollPosition === 'number') {
                wx.pageScrollTo({
                    scrollTop: this.scrollPosition,
                    duration: 0
                });
            }
        }, 50);
    },
    // 添加重置筛选方法
    onResetFilter() {
        this.setData({
            selectedProvince: '',
            selectedCity: '',
            selectedAreaCode: ''
        });
    },
    // 修改确认筛选方法
    onConfirmFilter() {
        const { selectedProvince, selectedCity } = this.data;

        // 构建地区完整名称
        const locationText = selectedCity ? `${selectedProvince} ${selectedCity}` : '';

        // 构建筛选参数
        const params = {
            // 地区
            car_city: locationText || undefined  // 如果没有选择地区则不传此参数
        };

        // 更新筛选条件
        this.setData({
            filterPopupVisible: false,
            filterTitleVisible: false,
            currentFilter: '',
            currentCityFullName: locationText
        }, () => {
            // 重新获取数据
            this.getCarList(params);
        });
    },
    // 处理地区选择变化
    onAreaChange(e) {
        const { values } = e.detail;
        // console.log('选择的地区:', values);
        this.setData({
            selectedProvince: values[0]?.name || '',
            selectedCity: values[1]?.name || '',
            selectedAreaCode: values[1]?.code || values[0]?.code || ''
        });
    },
    // 添加 toggleFilter 方法
    toggleFilter(e) {
        const type = e.currentTarget.dataset.type;

        // 如果点击的是筛选按钮
        if (type === 'filter') {
            this.onFilterTap(); // 直接调用onFilterTap方法
            return;
        }

        // 处理品牌、里程和价格的点击
        if (this.data.currentFilter === type && this.data.showFilterContent) {
            // 如果当前筛选项已经打开，则关闭它
            this.setData({
                showFilterContent: false,
                currentFilter: '',
                pageStyle: '',
                filterTitleVisible: false
            });
        } else {
            // 打开选中的筛选项
            this.setData({
                currentFilter: type,
                showFilterContent: true,
                pageStyle: 'overflow: hidden;',
                filterTitleVisible: true
            });

            // 如果是品牌筛选且没有品牌数据，则获取品牌列表
            if (type === 'brand' && (!this.data.brandOptions || this.data.brandOptions.length === 0)) {
                this.getBrandList();
            }
        }
    },

    toDetail(e) {
        let nType = this.data.vehicleType[0];
        if (nType === 'r') {
            nType = 'n'
        }
        const type = nType + 'detail';
        const id = e.currentTarget.dataset.id;

        // 获取用户信息
        const userInfo = util.getUserInfo();

        // 检查登录状态
        if (userInfo && userInfo.app_id) {
            // 用户已登录，直接跳转到详情页
            wx.navigateTo({
                url: `/pages/vehicle/${type}?id=${id}`
            });
        } else {
            // 用户未登录，直接跳转到登录页面
            wx.navigateTo({
                url: '/pages/login/index'
            });
        }
    },
    // 添加返回按钮处理方法
    goBack() {
        wx.navigateBack({
            delta: 1,
            fail: function () {
                // 如果无法返回上一页，则跳转到首页
                wx.switchTab({
                    url: '/pages/buy/index'
                })
            }
        });
    }
}) 