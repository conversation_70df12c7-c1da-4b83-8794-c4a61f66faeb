<view class="quote-container">
  <!-- 自定义导航栏 -->
  <view class="custom-nav">
    <view
      class="status-bar"
      style="height: {{statusBarHeight}}px;"
    ></view>
    <view class="nav-content">
      <view
        class="back-icon"
        bindtap="navigateBack"
      >
        <image src="/icons/moments/back.svg"></image>
      </view>
      <view class="nav-title">生成报价单</view>
    </view>
  </view>

  <!-- 导航栏占位符 -->
  <view
    class="nav-placeholder"
    style="height: {{statusBarHeight + 44}}px;"
  ></view>

  <!-- 步骤条 -->
  <view class="step-container">
    <view
      class="step-list"
      style="--current-step: {{currentStep}};"
    >
      <view class="step-item {{currentStep >= 1 ? 'active' : ''}}">
        <view class="step-circle">●</view>
        <view class="step-text">车辆采购</view>
      </view>
      <view class="step-item {{currentStep >= 2 ? 'active' : ''}}">
        <view class="step-circle">●</view>
        <view class="step-text">运费和车务</view>
      </view>
      <view class="step-item {{currentStep >= 3 ? 'active' : ''}}">
        <view class="step-circle">●</view>
        <view class="step-text">通道</view>
      </view>
      <view class="step-item {{currentStep >= 4 ? 'active' : ''}}">
        <view class="step-circle">●</view>
        <view class="step-text">出口</view>
      </view>
      <view class="step-item {{currentStep >= 5 ? 'active' : ''}}">
        <view class="step-circle">●</view>
        <view class="step-text">垫资账税</view>
      </view>
    </view>
  </view>

  <!-- 内容区域 - 仅显示当前步骤的内容 -->
  <view class="content-container">
    <!-- 步骤1：车辆采购 -->
    <block wx:if="{{currentStep === 1}}">

      <!-- 能源类型和美元汇率卡片 -->
      <view class="section-header">能源类型</view>
      <view class="vehicle-card">
        <!-- 能源类型 -->
        <view class="form-item">
          <text class="label">能源类型</text>
          <view class="select-box">
            <picker
              bindchange="energyTypeChange"
              value="{{energyTypeIndex}}"
              range="{{energyTypes}}"
            >
              <view class="picker-content">
                <text>{{energyTypes[energyTypeIndex]}}</text>
                <view class="arrow-container">
                  <view class="arrow"></view>
                </view>
              </view>
            </picker>
          </view>
        </view>

        <!-- 美元汇率 -->
        <view class="form-item">
          <text class="label">美元汇率</text>
          <input
            class="input-box"
            type="digit"
            placeholder="请输入美元汇率"
            value="{{exchangeRate}}"
            bindinput="inputChange"
            bindblur="inputBlur"
            data-field="exchangeRate"
          />
        </view>
      </view>

      <!-- 车辆采购 -->
      <view class="section-header">车辆采购</view>

      <!-- 车型和车辆信息表单在同一个卡片内 -->
      <view class="vehicle-card">
        <!-- 车型选择 -->
        <view
          class="car-select"
          bindtap="navigateTo"
          data-url="/pages/car/select"
        >
          <text>{{car.name}}</text>
          <view class="arrow"></view>
        </view>

        <!-- 分隔线 -->
        <view class="card-divider"></view>

        <!-- 外观 -->
        <view class="form-item">
          <text class="label">外观</text>
          <input
            class="input-box"
            placeholder="请输入车辆外观颜色"
            value="{{appearance}}"
            bindinput="inputChange"
            data-field="appearance"
          />
        </view>

        <!-- 内饰 -->
        <view class="form-item">
          <text class="label">内饰</text>
          <input
            class="input-box"
            placeholder="请输入车辆内饰颜色"
            value="{{interior}}"
            bindinput="inputChange"
            data-field="interior"
          />
        </view>

        <!-- 车辆指导价 -->
        <view class="form-item">
          <text class="label">车辆指导价</text>
          <input
            class="input-box"
            type="digit"
            placeholder="请输入车辆指导价格（万元）"
            value="{{vehiclePrice}}"
            bindinput="inputChange"
            bindblur="inputBlur"
            data-field="vehiclePrice"
          />
        </view>

        <!-- 车价下浮 -->
        <view class="form-item">
          <text class="label">车价下浮</text>
          <input
            class="input-box"
            type="digit"
            placeholder="请输入车辆价格的下浮（万元）"
            value="{{priceRange}}"
            bindinput="inputChange"
            bindblur="inputBlur"
            data-field="priceRange"
          />
        </view>

        <!-- 刷机费 -->
        <view class="form-item">
          <text class="label">刷机费</text>
          <input
            class="input-box"
            type="digit"
            placeholder="请输入刷机费（万元）"
            value="{{deletionFee}}"
            bindinput="inputChange"
            bindblur="inputBlur"
            data-field="deletionFee"
          />
        </view>

        <!-- 预付比例 -->
        <view class="form-item">
          <text class="label">预付比例</text>
          <input
            class="input-box"
            type="digit"
            placeholder="请输入车辆价格的预付比例"
            value="{{depositRatio}}"
            bindinput="inputChange"
            bindblur="inputBlur"
            data-field="depositRatio"
          />
        </view>
      </view>



    </block>

    <!-- 步骤2：国内运费和车务 -->
    <block wx:if="{{currentStep === 2}}">
      <view class="section-header">国内运费</view>
      <!-- 国内运费卡片 -->
      <view class="vehicle-card">
        <!-- 来院运费 -->
        <view class="form-item">
          <text class="label">采购运费</text>
          <input
            class="input-box"
            type="digit"
            value="{{pickupFee}}"
            placeholder="请输入车辆采购的运费（万元）"
            bindinput="inputChange"
            bindblur="inputBlur"
            data-field="pickupFee"
          />
        </view>

        <!-- 上牌运费 -->
        <view class="form-item">
          <text class="label">上牌运费</text>
          <input
            class="input-box"
            type="digit"
            value="{{portFee}}"
            placeholder="请输入上牌需要的运费（万元）"
            bindinput="inputChange"
            bindblur="inputBlur"
            data-field="portFee"
          />
        </view>

        <!-- 口岸运费 -->
        <view class="form-item">
          <text class="label">口岸运费</text>
          <input
            class="input-box"
            type="digit"
            value="{{customsFee}}"
            placeholder="请输入车辆至口岸的运费（万元）"
            bindinput="inputChange"
            bindblur="inputBlur"
            data-field="customsFee"
          />
        </view>
      </view>

      <view class="section-header">车务服务</view>
      <!-- 车务服务卡片 -->
      <view class="vehicle-card">
        <!-- 上牌费用 -->
        <view class="form-item">
          <text class="label">上牌费用</text>
          <input
            class="input-box"
            type="digit"
            value="{{licenseFee}}"
            placeholder="请输入上牌费用（万元）"
            bindinput="inputChange"
            bindblur="inputBlur"
            data-field="licenseFee"
          />
        </view>

        <!-- 保险费用 -->
        <view class="form-item">
          <text class="label">保险费用</text>
          <input
            class="input-box"
            type="digit"
            value="{{insurance}}"
            placeholder="请输入保险费用（万元）"
            bindinput="inputChange"
            bindblur="inputBlur"
            data-field="insurance"
          />
        </view>

        <!-- 过户费用 -->
        <view class="form-item">
          <text class="label">过户费用</text>
          <input
            class="input-box"
            type="digit"
            value="{{transfer}}"
            placeholder="请输入过户费用（万元）"
            bindinput="inputChange"
            bindblur="inputBlur"
            data-field="transfer"
          />
        </view>

        <!-- 背户 -->
        <view class="form-item">
          <text class="label">背户</text>
          <input
            class="input-box"
            value="{{mortgage}}"
            placeholder="请输入背户"
            bindinput="inputChange"
            bindblur="inputBlur"
            data-field="mortgage"
          />
        </view>
      </view>
    </block>

    <!-- 步骤3：通道 -->
    <block wx:if="{{currentStep === 3}}">
      <view class="section-header">通道</view>
      <view class="vehicle-card">
        <!-- 出口许可 -->
        <view class="form-item">
          <text class="label">出口许可</text>
          <input
            class="input-box"
            type="digit"
            value="{{exportPermit}}"
            placeholder="请输入办理出口许可费用（万元）"
            bindinput="inputChange"
            bindblur="inputBlur"
            data-field="exportPermit"
          />
        </view>

        <!-- 境产地证 -->
        <view class="form-item">
          <text class="label">原产地证</text>
          <input
            class="input-box"
            type="digit"
            value="{{originCert}}"
            placeholder="请输入办理原产地证书费用（万元）"
            bindinput="inputChange"
            bindblur="inputBlur"
            data-field="originCert"
          />
        </view>

        <!-- 退税通道 -->
        <view class="form-item">
          <text class="label">退税通道</text>
          <input
            class="input-box"
            type="digit"
            value="{{channelFee}}"
            placeholder="请输入退税通道费用（万元）"
            bindinput="inputChange"
            bindblur="inputBlur"
            data-field="channelFee"
          />
        </view>

        <!-- 注销 -->
        <view class="form-item">
          <text class="label">注销</text>
          <input
            class="input-box"
            type="digit"
            value="{{registration}}"
            placeholder="请输入注销费用（万元）"
            bindinput="inputChange"
            bindblur="inputBlur"
            data-field="registration"
          />
        </view>

        <!-- 检验报告 -->
        <view class="form-item">
          <text class="label">检验报告</text>
          <input
            class="input-box"
            type="digit"
            value="{{inspection}}"
            placeholder="请输入车辆检验报告费用（万元）"
            bindinput="inputChange"
            bindblur="inputBlur"
            data-field="inspection"
          />
        </view>
      </view>
    </block>

    <!-- 步骤4：出口 -->
    <block wx:if="{{currentStep === 4}}">
      <view class="section-header">出口</view>
      <view class="vehicle-card">
        <!-- 港杂费 -->
        <view class="form-item">
          <text class="label">港杂费</text>
          <input
            class="input-box"
            type="digit"
            value="{{portCharges}}"
            placeholder="请输入港杂费用（万元）"
            bindinput="inputChange"
            bindblur="inputBlur"
            data-field="portCharges"
          />
        </view>

        <!-- 打包证明 -->
        <view class="form-item">
          <text class="label">打包证明</text>
          <input
            class="input-box"
            type="digit"
            value="{{packingFee}}"
            placeholder="请输入打包证明费用（万元）"
            bindinput="inputChange"
            bindblur="inputBlur"
            data-field="packingFee"
          />
        </view>

        <!-- 报关费 -->
        <view class="form-item">
          <text class="label">报关费</text>
          <input
            class="input-box"
            type="digit"
            value="{{declarationFee}}"
            placeholder="请输入海关费用（万元）"
            bindinput="inputChange"
            bindblur="inputBlur"
            data-field="declarationFee"
          />
        </view>

        <!-- 出口物流 -->
        <view class="form-item">
          <text class="label">出口物流</text>
          <input
            class="input-box"
            type="digit"
            value="{{exportGoods}}"
            placeholder="请输入出口物流费用（万元）"
            bindinput="inputChange"
            bindblur="inputBlur"
            data-field="exportGoods"
          />
        </view>

        <!-- 出口保险 -->
        <view class="form-item">
          <text class="label">出口保险</text>
          <input
            class="input-box"
            type="digit"
            value="{{exportInsurance}}"
            placeholder="请输入出口保险费用（万元）"
            bindinput="inputChange"
            bindblur="inputBlur"
            data-field="exportInsurance"
          />
        </view>
      </view>
    </block>

    <!-- 步骤5：垫资账税 -->
    <block wx:if="{{currentStep === 5}}">
      <!-- 垫资账税 -->
      <view class="section-header">垫资账税</view>
      <view class="vehicle-card">
        <!-- 垫资比例 -->
        <view class="form-item">
          <text class="label">垫资比例</text>
          <input
            class="input-box"
            type="digit"
            placeholder="请输入占用比例"
            value="{{fundingRatio}}"
            bindinput="inputChange"
            bindblur="inputBlur"
            data-field="fundingRatio"
          />
        </view>

        <!-- 垫资利息 -->
        <view class="form-item">
          <text class="label">垫资利息</text>
          <input
            class="input-box"
            type="digit"
            placeholder="请输入垫资利息"
            value="{{taxRate}}"
            bindinput="inputChange"
            bindblur="inputBlur"
            data-field="taxRate"
          />
        </view>

        <!-- 垫资时间 -->
        <view class="form-item">
          <text class="label">垫资时间</text>
          <input
            class="input-box"
            type="digit"
            placeholder="请输入垫资时间（天）"
            value="{{fundingDays}}"
            bindinput="inputChange"
            bindblur="inputBlur"
            data-field="fundingDays"
          />
        </view>

        <!-- 垫税时间 -->
        <view class="form-item">
          <text class="label">垫税时间</text>
          <input
            class="input-box"
            type="digit"
            placeholder="请输入垫税时间（天）"
            value="{{taxDays}}"
            bindinput="inputChange"
            bindblur="inputBlur"
            data-field="taxDays"
          />
        </view>
      </view>

      <!-- 备注 -->
      <view class="section-header">备注</view>
      <view class="vehicle-card">
        <view class="form-item remark-item">
          <textarea
            class="textarea-box"
            placeholder="请输入备注信息"
            value="{{remarks}}"
            bindinput="inputChange"
            data-field="remarks"
          />
        </view>
      </view>
    </block>
  </view>

  <!-- 底部导航按钮 -->
  <view class="bottom-buttons">
    <view
      wx:if="{{currentStep > 1}}"
      class="prev-btn"
      bindtap="prevStep"
    >上一步</view>
    <view
      wx:else
      class="placeholder-btn"
    ></view>
    <view
      wx:if="{{currentStep < 5}}"
      class="next-btn"
      bindtap="nextStep"
    >下一步</view>
    <view
      wx:else
      class="submit-btn"
      bindtap="submitForm"
    >生成报价单</view>
  </view>
</view>