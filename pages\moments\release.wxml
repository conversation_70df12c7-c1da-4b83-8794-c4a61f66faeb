<!--pages/moments/release.wxml-->
<view class="release-container">
  <!-- 自定义导航栏 -->
  <view
    class="custom-nav"
    style="padding-top: {{statusBarHeight}}px; --status-bar-height: {{statusBarHeight}}px;"
  >
    <view class="status-bar"></view>
    <view class="nav-content">
      <view
        class="back-icon"
        bindtap="navigateBack"
      >
        <image
          src="/icons/moments/back.svg"
          mode="aspectFit"
        ></image>
      </view>
      <view class="nav-title">{{fromRejected ? '编辑采购信息' : '发布采购信息'}}</view>
    </view>
  </view>

  <view
    class="form-container"
    bindtap="onPageClick"
  >
    <view
      class="form-item border-radius"
      catchtap="stopPropagation"
    >
      <text class="label required">标题</text>
      <input
        type="text"
        placeholder="请填写发布采购信息的主标题"
        value="{{title}}"
        bindinput="inputTitle"
        disabled="{{isReviewing && !isWithdrawn}}"
        catchtap="stopPropagation"
      />
    </view>

    <view class="form-item">
      <text class="label required">品牌</text>
      <view
        class="custom-picker"
        catchtap="stopPropagation"
      >
        <view
          class="picker-value {{isReviewing && !isWithdrawn ? 'disabled-picker' : ''}}"
          bindtap="{{isReviewing && !isWithdrawn ? '' : 'showBrandPicker'}}"
          data-picker-id="brand-picker"
        >
          <text class="{{brandSelected ? '' : 'placeholder-text'}}">{{brandSelected || '请选择需要采购车辆的品牌'}}</text>
          <text class="arrow"></text>
        </view>
      </view>
    </view>

    <view class="form-item">
      <text class="label required">车系</text>
      <view
        class="custom-picker"
        catchtap="stopPropagation"
      >
        <view
          class="picker-value {{isReviewing && !isWithdrawn ? 'disabled-picker' : ''}}"
          bindtap="{{isReviewing && !isWithdrawn ? '' : 'showSeriesPicker'}}"
          data-picker-id="series-picker"
        >
          <text class="{{seriesSelected ? '' : 'placeholder-text'}}">{{seriesSelected || '请选择需要采购车辆的品牌'}}</text>
          <text class="arrow"></text>
        </view>
      </view>
    </view>

    <view class="form-item">
      <text class="label required">车型</text>
      <view
        class="custom-picker"
        catchtap="stopPropagation"
      >
        <view
          class="picker-value {{isReviewing && !isWithdrawn ? 'disabled-picker' : ''}}"
          bindtap="{{isReviewing && !isWithdrawn ? '' : 'showModelPicker'}}"
          data-picker-id="model-picker"
        >
          <text class="{{modelSelected ? '' : 'placeholder-text'}}">{{modelSelected || '请选择需要采购车辆的品牌'}}</text>
          <text class="arrow"></text>
        </view>
      </view>
    </view>

    <view class="form-item new-used-type">
      <text class="label required">新旧类型</text>
      <radio-group
        bindchange="onNewTypeChange"
        class="radio-group"
      >
        <label class="radio-item {{isNew ? 'active' : ''}}">
          <radio
            value="new"
            checked="{{isNew}}"
            color="#4080ff"
            disabled="{{isReviewing && !isWithdrawn}}"
          />
          <text>新车</text>
        </label>
        <label class="radio-item {{!isNew ? 'active' : ''}}">
          <radio
            value="used"
            checked="{{!isNew}}"
            color="#4080ff"
            disabled="{{isReviewing && !isWithdrawn}}"
          />
          <text>二手车</text>
        </label>
      </radio-group>
    </view>


    <view class="form-item">
      <text class="label">是否有发票</text>
      <view
        class="custom-picker"
        catchtap="stopPropagation"
      >
        <view
          class="picker-value {{isReviewing && !isWithdrawn ? 'disabled-picker' : ''}}"
          bindtap="{{isReviewing && !isWithdrawn ? '' : 'showInvoicePicker'}}"
          data-picker-id="invoice-picker"
        >
          <text class="{{invoiceType ? '' : 'placeholder-text'}}">{{invoiceName}}</text>
          <text class="arrow"></text>
        </view>
      </view>
    </view>

    <view class="form-item">
      <text class="label required">交付地点</text>
      <view
        class="custom-picker"
        catchtap="stopPropagation"
      >
        <view
          class="picker-value {{isReviewing && !isWithdrawn ? 'disabled-picker' : ''}}"
          bindtap="{{isReviewing && !isWithdrawn ? '' : 'showProvincePicker'}}"
          data-picker-id="province-picker"
        >
          <text class="{{locationSelected ? '' : 'placeholder-text'}}">{{locationSelected || '广东广州'}}</text>
          <text class="arrow"></text>
        </view>
      </view>
    </view>



    <view class="form-item">
      <text class="label">过户次数</text>
      <input
        type="number"
        placeholder="请填写车辆的过户次数"
        value="{{transferCount}}"
        bindinput="inputTransferCount"
        disabled="{{isReviewing && !isWithdrawn}}"
      />
    </view>

    <view class="form-item">
      <text class="label required">目标价格</text>
      <input
        type="digit"
        placeholder="请填写车辆的目标价格 (万元)"
        value="{{targetPrice}}"
        bindinput="inputTargetPrice"
        disabled="{{isReviewing && !isWithdrawn}}"
      />
    </view>

    <view class="form-item">
      <text class="label required">采购数量</text>
      <input
        type="number"
        placeholder="请填写车辆的采购数量"
        value="{{purchaseCount}}"
        bindinput="inputPurchaseCount"
        disabled="{{isReviewing && !isWithdrawn}}"
      />
    </view>

    <view class="form-item insurance">
      <text class="label">是否有保险</text>
      <radio-group
        bindchange="onInsuranceChange"
        class="radio-group"
      >
        <label class="radio-item {{hasInsurance ? 'active' : ''}}">
          <radio
            value="yes"
            checked="{{hasInsurance}}"
            color="#4080ff"
            disabled="{{isReviewing && !isWithdrawn}}"
          />
          <text>是</text>
        </label>
        <label class="radio-item {{!hasInsurance ? 'active' : ''}}">
          <radio
            value="no"
            checked="{{!hasInsurance}}"
            color="#4080ff"
            disabled="{{isReviewing && !isWithdrawn}}"
          />
          <text>否</text>
        </label>
      </radio-group>
    </view>





    <view class="form-item description-item">
      <text class="label">配置说明</text>
      <view class="description-container">
        <textarea
          placeholder="输入配置、车况以及售后服务说明"
          value="{{description}}"
          bindinput="inputDescription"
          maxlength="500"
          disabled="{{isReviewing && !isWithdrawn}}"
        />
        <view class="word-count">{{descriptionLength || 0}}/500</view>
      </view>
    </view>

    <!-- 图片上传区域的整体容器 -->
    <view class="upload-section">
      <!-- 采购证明上传 -->
      <view class="form-item upload-item proof-upload">
        <text class="label required">采购证明</text>
        <view class="upload-content">
          <view class="upload-container">
            <view
              class="upload-btn {{isReviewing && !isWithdrawn ? 'disabled-upload' : ''}}"
              bindtap="{{isReviewing && !isWithdrawn ? '' : 'chooseImage'}}"
              wx:if="{{!proofImages.length}}"
            >
              <image
                class="camera-icon"
                src="/icons/moments/poto.png"
                mode="aspectFit"
              ></image>
            </view>
            <view
              class="image-preview"
              wx:if="{{proofImages.length > 0}}"
            >
              <image
                src="https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/uploads/proof/1751274482277_247.png"
                mode="aspectFill"
                binderror="imageError"
                show-menu-by-longpress
              ></image>
              <view
                class="reupload {{isReviewing && !isWithdrawn ? 'disabled-upload' : ''}}"
                catchtap="{{isReviewing && !isWithdrawn ? '' : 'chooseImage'}}"
              >重新上传</view>
            </view>
          </view>
          <view class="upload-hint">请提供以下证明（海外客户采购聊天截图/打款记录/合同）</view>
        </view>
      </view>


      <!-- 新增：多图上传区域 -->
      <view class="form-item multi-upload-item">
        <text class="label">图片</text>
        <view class="upload-content">
          <view class="multi-upload-container">
            <!-- 已上传的多图 -->
            <view
              class="multi-upload-item"
              wx:for="{{multiImages}}"
              wx:key="index"
            >
              <image
                class="multi-upload-image"
                src="{{item}}"
                mode="aspectFill"
                binderror="multiImageError"
                data-index="{{index}}"
                show-menu-by-longpress
              ></image>
              <view
                class="multi-delete-icon {{isReviewing && !isWithdrawn ? 'disabled-upload' : ''}}"
                catchtap="{{isReviewing && !isWithdrawn ? '' : 'deleteMultiImage'}}"
                data-index="{{index}}"
              >×</view>
            </view>

            <!-- 多图上传按钮 -->
            <view
              class="multi-upload-btn {{isReviewing && !isWithdrawn ? 'disabled-upload' : ''}}"
              bindtap="{{isReviewing && !isWithdrawn ? '' : 'chooseMultiImages'}}"
              wx:if="{{multiImages.length < 3}}"
            >
              <image
                class="multi-upload-icon"
                src="/icons/moments/poto.png"
                mode="aspectFit"
              ></image>
            </view>
          </view>
          <view class="multi-upload-hint">可添加更多图片（最多3张）</view>
        </view>
      </view>
    </view>

    <!-- 驳回原因卡片 -->
    <view
      class="reject-reason-card"
      wx:if="{{(fromRejected && hasRejectReason) || isReviewing}}"
    >
      <view class="reject-title">{{isReviewing ? '审核状态' : '打回原因'}}</view>
      <!-- 驳回原因详情，仅在非审核中状态显示 -->
      <block wx:if="{{!isReviewing}}">
        <view class="reason-item">
          <text class="reason-label">问题类型</text>
          <view class="reason-value">{{rejectType}}</view>
        </view>
        <view class="reason-item">
          <text class="reason-label">详细原因</text>
          <view class="reason-value detail-value">{{rejectDetail}}</view>
        </view>
      </block>
      <!-- 审核中状态显示 -->
      <view
        class="reviewing-message"
        wx:if="{{isReviewing}}"
      >
        正在审核中，请耐心等候...
      </view>
    </view>
  </view>

  <!-- 将下拉菜单放在外部，避免被form-container的z-index限制 -->
  <!-- 品牌选择下拉框 -->
  <view
    class="picker-dropdown global-dropdown"
    wx:if="{{showBrandPicker && (!isReviewing || isWithdrawn)}}"
    style="top: {{brandPickerTop}}px; left: {{brandPickerLeft}}px; width: {{brandPickerWidth}}px; min-width: 300rpx;"
  >
    <view class="picker-header">
      <text class="picker-title">选择品牌</text>
      <text
        class="picker-cancel"
        catchtap="cancelSelection"
      >取消</text>
    </view>
    <scroll-view
      scroll-y
      style="max-height: 400rpx;"
    >
      <view
        wx:if="{{brands.length === 0}}"
        class="empty-tip"
      >暂无品牌数据</view>
      <view
        wx:else
        class="brands-count-debug"
      >共 {{brands.length}} 个品牌</view>
      <view
        class="picker-option {{brandSelected === item.brand_name ? 'active' : ''}}"
        wx:for="{{brands}}"
        wx:key="id"
        data-id="{{item.id}}"
        data-value="{{item.brand_name}}"
        catchtap="selectBrand"
      >
        {{item.brand_name}}
      </view>
    </scroll-view>
  </view>

  <!-- 车系选择下拉框 -->
  <view
    class="picker-dropdown global-dropdown"
    wx:if="{{showSeriesPicker && (!isReviewing || isWithdrawn)}}"
    style="top: {{seriesPickerTop}}px; left: {{seriesPickerLeft}}px; width: {{seriesPickerWidth}}px; min-width: 300rpx;"
  >
    <view class="picker-header">
      <text class="picker-title">选择车系</text>
      <text
        class="picker-cancel"
        catchtap="cancelSelection"
      >取消</text>
    </view>
    <scroll-view
      scroll-y
      style="max-height: 400rpx;"
    >
      <view
        class="picker-option {{seriesSelected === item.series_name ? 'active' : ''}}"
        wx:for="{{series}}"
        wx:key="id"
        data-id="{{item.series_id}}"
        data-value="{{item.series_name}}"
        catchtap="selectSeries"
      >
        {{item.series_name}}
      </view>
    </scroll-view>
  </view>

  <!-- 车型选择下拉框 -->
  <view
    class="picker-dropdown global-dropdown"
    wx:if="{{showModelPicker && (!isReviewing || isWithdrawn)}}"
    style="top: {{modelPickerTop}}px; left: {{modelPickerLeft}}px; width: {{modelPickerWidth}}px; min-width: 300rpx;"
  >
    <view class="picker-header">
      <text class="picker-title">选择车型</text>
      <text
        class="picker-cancel"
        catchtap="cancelSelection"
      >取消</text>
    </view>
    <scroll-view
      scroll-y
      style="max-height: 400rpx;"
    >
      <view
        class="picker-option {{modelSelected === (item.ui_vehicle_name + (item.official_price ? ' ¥' + item.official_price + '万元' : '')) ? 'active' : ''}}"
        wx:for="{{models}}"
        wx:key="id"
        data-id="{{item.id}}"
        data-value="{{item.ui_vehicle_name + (item.official_price ? ' ¥' + item.official_price + '万元' : '' )}}"
        catchtap="selectModel"
      >
        {{item.ui_vehicle_name}}{{item.official_price ? ' ¥' + item.official_price + '万元' : ''}}
      </view>
    </scroll-view>
  </view>

  <!-- 发票类型选择下拉框 -->
  <view
    class="picker-dropdown global-dropdown"
    wx:if="{{showInvoicePicker && (!isReviewing || isWithdrawn)}}"
    style="top: {{invoicePickerTop}}px; left: {{invoicePickerLeft}}px; width: {{invoicePickerWidth}}px; min-width: 300rpx;"
  >
    <view class="picker-header">
      <text class="picker-title">选择发票类型</text>
      <text
        class="picker-cancel"
        catchtap="cancelSelection"
      >取消</text>
    </view>
    <scroll-view
      scroll-y
      style="max-height: 300rpx;"
    >
      <view
        class="picker-option {{invoiceType === '0' ? 'active' : ''}}"
        data-value="0"
        catchtap="selectInvoiceType"
      >
        无需发票
      </view>
      <view
        class="picker-option {{invoiceType === '1' ? 'active' : ''}}"
        data-value="1"
        catchtap="selectInvoiceType"
      >
        普通发票
      </view>
      <view
        class="picker-option {{invoiceType === '2' ? 'active' : ''}}"
        data-value="2"
        catchtap="selectInvoiceType"
      >
        增值税专用发票
      </view>
    </scroll-view>
  </view>

  <!-- 省份选择下拉框 -->
  <view
    class="picker-dropdown global-dropdown"
    wx:if="{{showProvincePicker && (!isReviewing || isWithdrawn)}}"
    style="top: {{provincePickerTop}}px; left: {{provincePickerLeft}}px; width: {{provincePickerWidth}}px; min-width: 300rpx;"
  >
    <view class="picker-header">
      <text class="picker-title">选择省份</text>
      <text
        class="picker-cancel"
        catchtap="cancelSelection"
      >取消</text>
    </view>
    <scroll-view
      scroll-y
      style="max-height: 300rpx;"
    >
      <view
        class="picker-option {{provinceSelected === item.name ? 'active' : ''}}"
        wx:for="{{provinces}}"
        wx:key="code"
        data-value="{{item.name}}"
        data-code="{{item.code}}"
        catchtap="selectProvince"
      >
        {{item.name}}
      </view>
    </scroll-view>
  </view>

  <!-- 城市选择下拉框 -->
  <view
    class="picker-dropdown global-dropdown"
    wx:if="{{showCityPicker}}"
    style="top: {{cityPickerTop}}px; left: {{cityPickerLeft}}px; width: {{cityPickerWidth}}px; min-width: 300rpx;"
  >
    <view class="picker-header">
      <text class="picker-title">选择{{provinceSelected}}的城市</text>
      <text
        class="picker-cancel"
        catchtap="cancelSelection"
      >取消</text>
    </view>
    <scroll-view
      scroll-y
      style="max-height: 300rpx;"
    >
      <view
        class="picker-option"
        wx:for="{{cities[provinceCode]}}"
        wx:key="code"
        data-value="{{item.name}}"
        data-code="{{item.code}}"
        catchtap="selectCity"
      >
        {{item.name}}
      </view>
    </scroll-view>
  </view>

  <!-- 底部占位，确保内容不被底部操作栏遮挡 -->
  <view class="bottom-placeholder"></view>

  <view class="bottom-actions">
    <view
      class="save-draft"
      bindtap="saveDraft"
      wx:if="{{!fromDraft && !fromRejected && !isReviewing}}"
    >存草稿</view>
    <view
      class="publish {{isReviewing && !isWithdrawn ? 'review-btn' : (isWithdrawn ? '' : '')}}"
      bindtap="{{isReviewing && !isWithdrawn ? 'withdrawForEditing' : 'publish'}}"
    >{{isReviewing && !isWithdrawn ? '撤回重新编辑' : (isWithdrawn ? '更新' : (fromRejected ? '更新' : '发布'))}}</view>
  </view>
</view>