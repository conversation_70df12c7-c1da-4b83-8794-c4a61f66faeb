<!--pages/set/phoneget.wxml-->
<view class="container">
  <!-- 自定义导航栏 -->
  <view class="custom-nav" style="padding-top: {{statusBarHeight}}px;">
    <view class="nav-content">
      <view class="nav-back" bindtap="goBack">
        <view class="back-icon"></view>
      </view>
      <view class="nav-title">修改手机号</view>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="main-content" style="margin-top: {{statusBarHeight + 44}}px;">
    <!-- 将所有内容包裹在卡片中 -->
    <view class="phone-card">
      <view class="phone-title">验证手机号</view>
      
      <view class="input-row">
        <view class="label">手机号</view>
        <view class="input-value">+86 {{phoneFormatted}}</view>
      </view>
      
      <view class="input-row">
        <view class="label">验证码</view>
        <view class="input-container">
          <input class="verify-input" type="number" value="{{verifyCode}}" placeholder="请输入验证码" bindinput="inputVerifyCode" />
          <view class="verify-btn" bindtap="getVerifyCode">
            {{countDown > 0 ? countDown + 's后重新获取' : '获取验证码'}}
          </view>
        </view>
      </view>
    </view>
    
    <!-- 提交按钮 -->
    <view class="submit-btn {{verifyCode ? 'active' : ''}}" bindtap="{{verifyCode ? 'submitVerify' : ''}}">提交</view>
  </view>
</view>