Component({
    properties: {
        items: {
            type: Array,
            value: [
                {
                    icon: '/icons/new-car.png',
                    text: '新车上市',
                    url: '/pages/new-car/index'
                },
                {
                    icon: '/icons/new-car.png',
                    text: '二手车',
                    url: '/pages/used-car/index'
                },
                {
                    icon: 'https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/uploads/proof/1751273378903_552.png',
                    text: '新能源',
                    url: '/pages/new-energy/index'
                },
                {
                    icon: '/icons/new-car.png',
                    text: '我要卖车',
                    url: '/pages/sell-car/index'
                }
            ]
        }
    },
    methods: {
        onItemTap(e) {
            const { url } = e.currentTarget.dataset
            wx.navigateTo({ url })
        }
    }
}) 