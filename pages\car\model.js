import api from '../../utils/api'

Page({
    data: {
        seriesId: '', // 车系ID
        seriesName: '', // 车系名称
        brandName: '', // 品牌名称
        modelList: [], // 车型列表
        loading: true
    },

    onLoad(options) {
        const { seriesId, seriesName, brandName } = options

        if (!seriesId) {
            wx.showToast({
                title: '车系参数错误',
                icon: 'none'
            })
            setTimeout(() => {
                wx.navigateBack()
            }, 1500)
            return
        }

        this.setData({
            seriesId,
            seriesName,
            brandName
        })

        // 获取车型列表
        this.getModelList(seriesId)
    },

    // 获取车型列表
    async getModelList(seriesId) {
        try {
            this.setData({ loading: true })
            // console.log('获取车型列表，车系ID:', seriesId)

            const res = await api.car.getVehicleList({ series_id: seriesId })
            // console.log('车型接口返回数据:', res)

            const models = Array.isArray(res) ? res : (res.data || [])
            // console.log('原始车型数据:', models)

            const processedModels = models.map(item => ({
                id: item.id || item.vehicle_id,
                name: item.ui_vehicle_name || `${item.series_name} ${item.vehicle_name}`,
                price: item.official_price ? `${item.official_price}万元` : '',
                letter: (item.ui_vehicle_name || item.vehicle_name || '').charAt(0).toUpperCase()
            }))
            // console.log('处理后的车型数据:', processedModels)

            const groupedModels = this.groupModelsByLetter(processedModels)
            // console.log('分组后的数据:', groupedModels)

            this.setData({
                modelList: groupedModels,
                loading: false
            }, () => {
                console.log('设置到data后的modelList:', this.data.modelList)
            })
        } catch (error) {
            console.error('获取车型列表失败', error)
            wx.showToast({
                title: '获取车型列表失败',
                icon: 'none'
            })
            this.setData({ loading: false })
        }
    },

    // 按首字母分组车型
    groupModelsByLetter(models) {
        // console.log('开始分组处理:', models)
        const groups = {}
        models.forEach(item => {
            const letter = item.letter
            if (!groups[letter]) {
                groups[letter] = []
            }
            groups[letter].push(item)
        })
        // console.log('分组结果:', groups)

        // 转换为数组格式
        const result = Object.keys(groups).sort().map(letter => ({
            letter,
            list: groups[letter]
        }))
        // console.log('最终分组数组:', result)
        return result
    },

    // 选择车型
    onSelectModel(e) {
        const { id, name } = e.currentTarget.dataset

        // 构建完整车辆名称（品牌+车系+车型）
        const fullCarName = `${this.data.brandName} ${this.data.seriesName} ${name}`

        // 获取当前页面栈
        const pages = getCurrentPages()
        // 获取上级页面(quote)
        const quotePage = pages[pages.length - 4]

        if (quotePage) {
            // 更新上级页面的车辆数据
            quotePage.setData({
                'car.id': id,
                'car.name': fullCarName
            })

            // 返回到报价页面
            wx.navigateBack({
                delta: 3
            })
        }
    },

    // 返回上一页
    onBack() {
        wx.navigateBack()
    },

    // 车型选择事件
    onModelSelect(e) {
        const { id, name } = e.currentTarget.dataset;

        // 查找选中的车型以获取价格
        let selectedModel = null;
        for (const group of this.data.modelList) {
            for (const model of group.list) {
                if (model.id == id) {
                    selectedModel = model;
                    break;
                }
            }
            if (selectedModel) break;
        }

        // 构建显示名称，包含价格
        const displayName = selectedModel && selectedModel.price
            ? `${name} ${selectedModel.price}`
            : name;

        // console.log('选择的车型:', { id, name, displayName });

        // 获取当前页面栈
        const pages = getCurrentPages();
        // 获取上级页面(quote)
        const quotePage = pages[pages.length - 4];

        if (quotePage) {
            // 更新上级页面的车辆数据
            quotePage.setData({
                'car.id': id,
                'car.name': displayName
            });

            // 返回到报价页面
            wx.navigateBack({
                delta: 3
            });
        } else {
            wx.showToast({
                title: '选择成功',
                icon: 'success'
            });
        }
    }
}) 