import api from '../../utils/api';

Page({
    data: {
        brandList: []
    },

    onLoad() {
        this.getBrandList();
    },

    // 获取品牌列表
    async getBrandList() {
        try {
            const res = await api.car.getBrandList();
            // console.log('接口返回数据:', res);

            // 直接处理数据，不判断code
            const brands = Array.isArray(res) ? res : (res.data || []);
            // console.log('原始品牌数据:', brands);

            const processedBrands = brands.map(item => ({
                id: item.id,
                name: item.brand_name,
                letter: item.letter // 直接使用接口返回的letter字段
            }));
            // console.log('处理后的品牌数据:', processedBrands);

            const groupedBrands = this.groupBrandsByLetter(processedBrands);
            // console.log('分组后的数据:', groupedBrands);

            this.setData({ brandList: groupedBrands }, () => {
                console.log('设置到data后的brandList:', this.data.brandList);
            });
        } catch (error) {
            console.error('获取品牌列表失败', error);
            wx.showToast({
                title: '获取品牌列表失败',
                icon: 'none'
            });
        }
    },

    // 按首字母分组品牌
    groupBrandsByLetter(brands) {
        // console.log('开始分组处理:', brands);
        const groups = {};
        brands.forEach(brand => {
            const letter = brand.letter;
            if (!groups[letter]) {
                groups[letter] = [];
            }
            groups[letter].push(brand);
        });
        // console.log('分组结果:', groups);

        // 转换为数组格式
        const result = Object.keys(groups).sort().map(letter => ({
            letter,
            list: groups[letter]
        }));
        // console.log('最终分组数组:', result);
        return result;
    },

    // 品牌选择事件
    onBrandSelect(e) {
        const { id, name } = e.currentTarget.dataset;
        // 跳转到车系选择页面，并传递品牌信息
        wx.navigateTo({
            url: `/pages/car/series?brandId=${id}&brandName=${name}`
        });
    }
});
