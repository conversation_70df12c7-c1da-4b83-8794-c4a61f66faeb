const formatTime = date => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  return `${[year, month, day].map(formatNumber).join('/')} ${[hour, minute, second].map(formatNumber).join(':')}`
}

const formatNumber = n => {
  n = n.toString()
  return n[1] ? n : `0${n}`
}

/**
 * 设置带过期时间的缓存
 * @param {string} key - 缓存的键名
 * @param {any} value - 要存储的值
 * @param {number} [expiry=7] - 过期时间数值，默认7天
 * @param {string} [unit='seconds'] - 过期时间单位：'seconds'|'minutes'|'hours'|'days'
 */
const setCacheWithExpiry = (key, value, expiry = 7, unit = 'days') => {
  // 如果没有设置过期时间，直接存储值
  if (expiry === 0) {
    wx.setStorageSync(key, {
      value: value,
      permanent: true  // 标记为永久存储
    });
    return;
  }

  // 计算过期时间（毫秒）
  const multiplier = {
    seconds: 1000,
    minutes: 60 * 1000,
    hours: 60 * 60 * 1000,
    days: 24 * 60 * 60 * 1000
  };

  const now = new Date().getTime();
  const expiryMs = expiry * (multiplier[unit] || multiplier.seconds);

  const item = {
    value: value,
    expiry: now + expiryMs,
    permanent: false
  };

  wx.setStorageSync(key, item);
};

/**
 * 获取缓存，如果过期则返回null
 * @param {string} key - 缓存的键名
 * @returns {any|null} 缓存的值，如果过期或不存在则返回null
 */
const getCacheWithExpiry = (key) => {
  const item = wx.getStorageSync(key);

  // 如果缓存不存在，返回null
  if (!item) return null;

  // 如果不是带过期时间的格式，直接返回原始值
  if (!item.hasOwnProperty('value') || (!item.hasOwnProperty('expiry') && !item.permanent)) {
    return item;
  }

  // 如果是永久存储，直接返回值
  if (item.permanent) return item.value;

  const now = new Date().getTime();

  // 如果已过期，删除缓存并返回null
  if (now > item.expiry) {
    wx.removeStorageSync(key);
    return null;
  }

  return item.value;
};

/**
 * 获取缓存的过期时间
 * @param {string} key - 缓存的键名
 * @returns {number|null} 过期时间戳，如果是永久存储或不存在则返回null
 */
const getCacheExpiry = (key) => {
  const item = wx.getStorageSync(key);
  if (!item || item.permanent) return null;
  return item.expiry;
};

/**
 * 检查缓存是否过期
 * @param {string} key - 缓存的键名
 * @returns {boolean} 是否过期
 */
const isCacheExpired = (key) => {
  const item = wx.getStorageSync(key);
  if (!item) return true;
  if (item.permanent) return false;
  return new Date().getTime() > item.expiry;
};

/**
 * 存储用户信息，默认7天过期
 * @param {Object} userInfo - 用户信息对象
 * @param {number} [days=7] - 过期天数
 */
const setUserInfo = (userInfo, days = 7) => {
  setCacheWithExpiry('userInfo', userInfo, days, 'days');
};

/**
 * 获取用户信息
 * @returns {Object|null} 用户信息对象，如果过期或不存在则返回null
 */
const getUserInfo = () => {
  // return {"phone":"18819435486","is_info":1,"short_name":"我是简称 1","logo":"https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/uploads/my/1742786880564_359.webp","identity":"service","app_id":1063}
  return getCacheWithExpiry('userInfo');
};

const formatDate = (dateString) => {
  if (!dateString) return '';
  // 如果是标准日期格式，只取前10位 (YYYY-MM-DD)
  if (dateString.length >= 10 && dateString.includes('-')) {
    return dateString.substring(0, 10);
  }
  // 如果是时间戳或其他格式，转换为日期对象再格式化
  try {
    const date = new Date(dateString);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  } catch (e) {
    return dateString;
  }
};

const getAppId = () => {
  const userInfo = getUserInfo()
  // exit;
  let app_id = 0
  if (userInfo !== null) {
    app_id = userInfo.app_id
  }
  return app_id
}

const getLevel = () => {
  const userInfo = getUserInfo()
  let level = 0
  if (userInfo !== null) {
    level = userInfo.level
  }
  return level
}
const parseHtml = (html) => {
  return html.replace(/<img([^>]*)>/g, (match, p1) => {
    return `<img${p1} style="max-width:100%;height:auto;display:block;">`;
  });
};

const splitValue = (str, separator, num = -1) => {
  if (!str) return ""
  const arr = str.split(separator)
  if (num == -1) return arr

  return arr[num] || "";
}

/**
 * 显示登录弹窗
 * @param {Function} onSuccess - 登录成功后的回调函数
 * @param {Function} onCancel - 取消登录的回调函数
 * @param {Object} options - 配置选项
 * @returns {Object} 包含show和hide方法的对象，用于控制弹窗显示和隐藏
 */
const showLoginPopup = (onSuccess, onCancel, options = {}) => {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];

  // 检查用户是否已登录
  const userInfo = getUserInfo();
  if (userInfo && userInfo.user_id) {
    // 用户已登录，直接执行成功回调
    if (typeof onSuccess === 'function') {
      onSuccess(userInfo);
    }
    return null;
  }

  // 设置默认参数
  const defaultOptions = {
    title: '点击登录，解锁更多功能',
    buttonText: '登录',
    imageUrl: 'https://zhaochexia-1331160188.cos.ap-guangzhou.myqcloud.com/uploads/proof/1751274411926_900.png'
  };

  const finalOptions = { ...defaultOptions, ...options };

  // 设置页面数据，显示登录弹窗
  currentPage.setData({
    showLoginPopup: true,
    loginPopupOptions: finalOptions
  });

  // 注册页面的处理函数
  currentPage.handleLogin = function () {
    // 隐藏弹窗
    currentPage.setData({
      showLoginPopup: false
    });

    // 跳转到登录页面
    wx.navigateTo({
      url: '/pages/login/index',
      success: () => {
        // 创建一个页面返回监听器，检查用户是否登录成功
        const checkLoginStatus = () => {
          const updatedUserInfo = getUserInfo();
          if (updatedUserInfo && updatedUserInfo.user_id) {
            // 用户登录成功，执行成功回调
            if (typeof onSuccess === 'function') {
              onSuccess(updatedUserInfo);
            }
          }
        };

        // 监听页面显示事件，用户从登录页返回时触发
        const originalOnShow = currentPage.onShow;
        currentPage.onShow = function () {
          // 调用原始的onShow方法
          if (originalOnShow) {
            originalOnShow.call(currentPage);
          }

          // 检查登录状态
          checkLoginStatus();

          // 恢复原始的onShow方法
          currentPage.onShow = originalOnShow;
        };
      }
    });
  };

  // 注册关闭弹窗的方法
  currentPage.closeLoginPopup = function () {
    currentPage.setData({
      showLoginPopup: false
    });

    // 执行取消回调
    if (typeof onCancel === 'function') {
      onCancel();
    }
  };

  // 返回控制对象
  return {
    show: () => {
      currentPage.setData({
        showLoginPopup: true
      });
    },
    hide: () => {
      currentPage.setData({
        showLoginPopup: false
      });
    }
  };
};

/**
 * 检查登录状态，如果未登录则显示登录弹窗
 * @param {Function} callback - 登录成功后的回调函数
 * @param {Object} options - 登录弹窗的配置选项
 * @returns {Boolean} 用户是否已登录
 */
const checkLoginWithPopup = (callback, options = {}) => {
  const userInfo = getUserInfo();
  if (userInfo && userInfo.app_id) {
    // 用户已登录，直接执行回调
    if (typeof callback === 'function') {
      callback(userInfo);
    }
    return true;
  } else {
    // 用户未登录，显示登录弹窗
    showLoginPopup(callback, null, options);
    return false;
  }
};

//随机生成手机号码
const generateRandomPhone = () => {
  // 第一位固定是1
  const first = '1';
  // 第二位：3-9
  const second = Math.floor(Math.random() * 7 + 3);
  // 剩余9位：随机0-9
  const rest = Array.from({ length: 9 }, () => Math.floor(Math.random() * 10)).join('');
  return first + second + rest;
}

// 将日期字符串转换为兼容 iOS 的格式
const formatDateString = (dateStr) => {
  if (!dateStr) return new Date().toISOString();

  // 检查是否已经是标准格式
  if (dateStr.includes('T')) return dateStr;

  // 处理 yyyy-MM-dd HH:mm:ss 格式
  if (dateStr.includes('-') && dateStr.includes(':')) {
    // 将 yyyy-MM-dd HH:mm:ss 转换为 yyyy-MM-ddTHH:mm:ss
    return dateStr.replace(' ', 'T');
  }

  // 处理纯日期 yyyy-MM-dd
  if (dateStr.includes('-') && !dateStr.includes(':')) {
    return dateStr + "T00:00:00";
  }

  return dateStr;
}

//存储登录之后的信息
const storeLoginFlags = (result) => {
  // 构建用户信息对象
  const userInfo = {
    phone: result.purePhoneNumber || result.phoneNumber || '',
    is_info: result.info ? 1 : 0,
    short_name: result.info?.short_name || '',
    logo: result.info?.avatar || '',
    identity: result.info?.identity || '',
    app_id: result.info?.app_id || '',
    is_auth: result.info?.is_auth || 0,
    level: result.info?.level || 1,
    level_time_expire: result.info?.level_time_expire || '',
    account_type: result.info?.account_type || '',
    admin_id: result.info?.admin_id || 0,
  };

  // 保存用户信息和登录状态
  setCacheWithExpiry('userInfo', userInfo);

  return userInfo
}

module.exports = {
  formatTime,
  setCacheWithExpiry,
  getCacheWithExpiry,
  getCacheExpiry,
  isCacheExpired,
  setUserInfo,
  getUserInfo,
  formatDate,
  getAppId,
  parseHtml,
  splitValue,
  showLoginPopup,
  checkLoginWithPopup,
  generateRandomPhone,
  formatDateString,
  storeLoginFlags,
  getLevel
}
