/* 详情页整体容器 */
.detail-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 自定义导航栏 */
.custom-nav {
  width: 100%;
  background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
  position: relative;
}

.status-bar {
  width: 100%;
}

.nav-content {
  display: flex;
  height: 44px;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
}

.nav-back {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
}

.nav-placeholder {
  width: 44px;
}

/* 轮播图容器 */
.swiper-container {
  width: 100%;
  height: 250px;
  position: relative;
  background-color: #ffffff;
}

.image-swiper {
  width: 100%;
  height: 100%;
}

.swiper-image {
  width: 100%;
  height: 100%;
}

.image-counter {
  position: absolute;
  left: 50%;
  bottom: 15px;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.6);
  color: #ffffff;
  font-size: 14px;
  padding: 4px 12px;
  border-radius: 12px;
  z-index: 10;
  font-weight: 500;
}

/* 商业标题区域 */
.commercial-title-section {
  padding: 15px;
  background-color: #ffffff;
  margin-bottom: 10px;
}

.commercial-title {
  font-size: 22px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 5px;
  line-height: 1.4;
}

.commercial-id {
  font-size: 14px;
  color: #666666;
  margin-bottom: 5px;
}

/* 价格区域 */
.price-container {
  margin-top: 15px;
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
}

.current-price {
  display: flex;
  align-items: baseline;
}

.price-label {
  display: none;
}

.price-value {
  font-size: 30px;
  font-weight: bold;
  color: #4080ff;
  line-height: 1;
}

.price-unit {
  font-size: 16px;
  color: #4080ff;
  margin-left: 2px;
  font-weight: bold;
}

.price-comparison {
  font-size: 14px;
  color: #999999;
  margin-bottom: 5px;
}

.strikethrough {
  text-decoration: line-through;
}

/* 可滚动内容区域 */
.scrollable-content {
  flex: 1;
  overflow-y: auto;
  background-color: #f5f5f5;
}

/* 详情部分通用样式 */
.detail-section {
  margin: 10px;
  padding: 15px;
  background-color: #ffffff;
  border-radius: 8px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 15px;
  padding-left: 10px;
  border-left: 3px solid #ff4d4f;
}

/* 参数网格 */
.params-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px 10px;
}

.param-grid-item {
  display: flex;
  flex-direction: column;
}

.grid-label {
  font-size: 12px;
  color: #999999;
  margin-bottom: 3px;
}

.grid-value {
  font-size: 14px;
  color: #333333;
}

/* 参数分组标题 */
.param-group-title {
  grid-column: 1 / -1;
  font-size: 14px;
  font-weight: bold;
  color: #666666;
  margin-top: 10px;
  margin-bottom: 5px;
  padding-bottom: 5px;
  border-bottom: 1px solid #eeeeee;
}

/* 详情内容 */
.detail-content {
  font-size: 14px;
  color: #666666;
  line-height: 1.6;
  padding: 0 5px;
}

/* 底部操作栏 */
.bottom-action-bar {
  display: flex;
  height: 50px;
  background-color: #ffffff;
  border-top: 1px solid #eeeeee;
  padding: 0 10px;
  align-items: center;
  padding-bottom: 16px;
}

/* 收藏按钮样式 */
.collect-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 8px;
}

.collect-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 3px;
}

.collect-text {
  font-size: 12px;
  color: #666666;
}

/* 按钮样式 */
.contact-btn, .view-btn {
  flex: 1;
  height: 36px;
  line-height: 36px;
  text-align: center;
  color: #ffffff;
  font-size: 14px;
  margin: 0 5px;
  border-radius: 8px;
  padding: 0;
}

.contact-btn {
  background-color: #ff4d4f;
}

.view-btn {
  background-color: #1890ff;
}

.info-container {
  display: flex;
  margin-bottom: 5px;
}

.info-container .commercial-id {
  margin-right: 20px;
}
