// pages/supply_chain_services/car_service.js
import config from '../../config'; // 导入配置文件
import api from '../../utils/api'; // 导入API模块
import util from '../../utils/util'; // 导入util工具类

Page({

  /**
   * 页面的初始数据
   */
  data: {
    navBarHeight: 0, // 导航栏高度
    statusBarHeight: 0, // 状态栏高度
    COS_CONFIG: config.COS_CONFIG, // 添加腾讯云配置
    serviceList: [], // 服务列表数据
    pageSize: 10, // 每页数据量
    currentPage: 1, // 当前页码
    hasMoreData: true, // 是否还有更多数据
    searchValue: '', // 搜索值
    loading: false, // 加载状态
    totalCount: 0, // 总数据量
    forceUpdate: 0 // 强制更新视图
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setNavBarInfo();
    this.loadServiceList();
  },

  /**
   * 加载车务服务列表
   */
  loadServiceList(isRefresh = true) {
    if (this.data.loading) return;

    this.setData({ loading: true });

    // 如果是刷新，重置页码
    if (isRefresh) {
      this.setData({ currentPage: 1 });
    }

    // 构建请求参数
    const params = {
      page: this.data.currentPage,
      limit: this.data.pageSize,
      company_name: this.data.searchValue || ''
    };

    console.log('请求参数:', params);

    // 调用API接口获取数据
    api.vehicleService.getList(params).then(res => {
      console.log('车务服务列表数据:', res);

      let list = [];
      let total = 0;

      // 判断不同的数据结构情况
      if (res) {
        // 根据实际数据结构处理
        if (res.code === 200 && res.data) {
          // 标准格式：{code: 200, data: {list: [], total: 0}}
          list = res.data.list || [];
          total = res.data.total || 0;
        } else if (res.list) {
          // 直接返回列表格式：{list: [], total: 0}
          list = res.list || [];
          total = res.total || list.length;
        } else if (Array.isArray(res)) {
          // 直接返回数组格式
          list = res;
          total = res.length;
        }

        console.log('解析后的列表数据:', list);

        // 格式化数据以适应页面展示
        list = list.map(item => ({
          id: item.id,
          companyName: item.company_name || item.company || '',
          province: item.province || '',
          city: item.city || '',
          invoice: item.invoice === 1 ? '开增票' :
            item.invoice === 2 ? '开增票' : '不开票', // 处理invoice为2的情况
          finance: item.finance === 1 ? '可垫资' : '不可垫资',
          efficiency: item.efficiency || '',
          cost: item.cost || '',
          cost_content: item.cost_content || '',
          remark: item.remark || ''
        }));
      }

      console.log('格式化后的数据:', list);

      this.setData({
        serviceList: isRefresh ? list : [...this.data.serviceList, ...list],
        hasMoreData: this.data.currentPage * this.data.pageSize < total,
        totalCount: total,
        loading: false
      });

      // 强制更新视图
      setTimeout(() => {
        this.setData({
          forceUpdate: Date.now()
        });
      }, 100);

    }).catch(err => {
      console.error('获取车务服务列表失败:', err);
      wx.showToast({
        title: '获取列表失败',
        icon: 'none'
      });
      this.setData({ loading: false });
    });
  },

  /**
   * 搜索输入事件
   */
  onSearchInput(e) {
    this.setData({
      searchValue: e.detail.value
    });
  },

  /**
   * 执行搜索
   */
  onSearch() {
    this.loadServiceList();
  },

  /**
   * 卡片点击事件
   */
  onCardTap(e) {
    const id = e.currentTarget.dataset.id;

    // 获取用户信息
    const userInfo = util.getUserInfo();

    // 检查登录状态
    if (userInfo && userInfo.app_id) {
      // 用户已登录，直接跳转到详情页
      wx.navigateTo({
        url: `/pages/supply_chain_services/car_service_detail?id=${id}`
      });
    } else {
      // 用户未登录，直接跳转到登录页面
      wx.navigateTo({
        url: '/pages/login/index'
      });
    }
  },

  /**
   * 设置导航栏信息
   */
  setNavBarInfo() {
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    // 获取状态栏高度
    const statusBarHeight = systemInfo.statusBarHeight;
    // 导航栏高度 = 状态栏高度 + 44(导航内容高度)
    const navBarHeight = statusBarHeight + 44;

    this.setData({
      statusBarHeight: statusBarHeight,
      navBarHeight: navBarHeight,
      searchBarHeight: 90, // 更新搜索栏高度(rpx转px)，用于计算内容区域的margin-top
      windowWidth: systemInfo.windowWidth,
      windowHeight: systemInfo.windowHeight
    });
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 下拉刷新重新加载数据
    this.loadServiceList();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    // 如果没有更多数据，直接返回
    if (!this.data.hasMoreData) return;

    // 页码加1
    this.setData({
      currentPage: this.data.currentPage + 1
    });

    // 加载更多数据
    this.loadServiceList(false);
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '车务服务 - 找车侠',
      path: '/pages/supply_chain_services/car_service'
    };
  }
})