<!--pages/login/forgot_password.wxml-->
<view
    class="container"
    style="--status-bar-height: {{statusBarHeight}}px;"
>
    <!-- 自定义导航栏 -->
    <view
        class="custom-nav"
        style="padding-top: {{statusBarHeight}}px;"
    >
        <view class="nav-content">
            <view
                class="back-icon"
                bindtap="navigateBack"
            >
                <van-icon
                    name="arrow-left"
                    size="20px"
                    color="#333"
                />
            </view>
            <view class="nav-title">{{text.reset_password}}</view>
        </view>
    </view>
    <language-switcher position="top-left" showLangList="{{true}}" size="normal" />
    <!-- 内容区域 -->
    <view
        class="main-content"
        style="padding-top: {{statusBarHeight + 44}}px;"
    >
        <!-- 表单 -->
        <view class="reset-form">
            <!-- 手机号输入框 -->
            <view class="input-item">
                <view class="input-icon">
                    <van-icon
                        name="phone-o"
                        size="20px"
                        color="#999"
                    />
                </view>
                <input
                    class="input-field"
                    type="number"
                    placeholder="{{text.enter_phone_number}}"
                    placeholder-class="placeholder"
                    model:value="{{phone}}"
                />
            </view>

            <!-- 验证码输入框 -->
            <view class="input-item">
                <view class="input-icon">
                    <van-icon
                        name="certificate"
                        size="20px"
                        color="#999"
                    />
                </view>
                <input
                    class="input-field"
                    type="number"
                    placeholder="{{text.please_enter_the_verification_code}}"
                    placeholder-class="placeholder"
                    model:value="{{verifyCode}}"
                />
                <button
                    class="verify-code-btn {{isCountDown ? 'disabled-btn' : ''}}"
                    catchtap="getVerifyCode"
                    disabled="{{isCountDown}}"
                >
                    {{codeBtnText}}
                </button>
            </view>

            <!-- 新密码输入框 -->
            <view class="input-item">
                <view class="input-icon">
                    <van-icon
                        name="lock"
                        size="20px"
                        color="#999"
                    />
                </view>
                <input
                    class="input-field"
                    password="{{!showPassword}}"
                    placeholder="{{text.pass_chars}}"
                    placeholder-class="placeholder"
                    model:value="{{password}}"
                />
                <view
                    class="password-toggle"
                    bindtap="togglePasswordVisibility"
                >
                    <van-icon
                        name="{{showPassword ? 'eye-o' : 'closed-eye'}}"
                        size="20px"
                        color="#999"
                    />
                </view>
            </view>

            <!-- 确认密码输入框 -->
            <view class="input-item">
                <view class="input-icon">
                    <van-icon
                        name="lock"
                        size="20px"
                        color="#999"
                    />
                </view>
                <input
                    class="input-field"
                    password="{{!showPassword}}"
                    placeholder="{{text.confirm_new_password}}"
                    placeholder-class="placeholder"
                    model:value="{{confirmPassword}}"
                    bindinput="onConfirmPasswordInput"
                />
            </view>

            <!-- 错误提示 -->
            <view class="error-container">
                <view class="error-message {{errorInfo.type}} {{errorInfo.show ? 'visible' : 'hidden'}}">
                    <van-icon
                        wx:if="{{errorInfo.show}}"
                        name="{{errorInfo.icon || 'info-o'}}"
                        size="14px"
                    />
                    <text wx:if="{{errorInfo.show}}">{{errorInfo.msg}}</text>
                </view>
            </view>
        </view>

        <!-- 协议区域 -->
        <view class="agreement-container">
            <checkbox
                class="checkbox"
                checked="{{isAgree}}"
                bindtap="toggleAgree"
            ></checkbox>
            <view class="agreement-text">
              {{text.i_agree_to_abide_by}}<text
                    class="link"
                    bindtap="viewAgreement"
                    data-type="service"
                >《{{text.user_service_agreement}}》</text>、<text
                    class="link"
                    bindtap="viewAgreement"
                    data-type="privacy"
                >《{{text.privacy_policy}}》</text>
            </view>
        </view>

        <!-- 提交按钮 -->
        <button
            class="submit-btn {{isAgree ? '' : 'submit-btn-disabled'}}"
            bindtap="handleResetPassword"
            disabled="{{!isAgree}}"
            loading="{{loading}}"
        >
            提交
        </button>
    </view>
</view>