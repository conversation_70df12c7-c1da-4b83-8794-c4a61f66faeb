<!--pages/supply_chain_services/car_service.wxml-->
<view class="container">
    <view class="gradient-bg"></view>

    <!-- 统一头部背景 -->
    <view
        class="unified-header"
        style="height: {{navBarHeight + 70}}px;"
    >
        <!-- 自定义导航栏 -->
        <view
            class="custom-navbar"
            style="height: {{navBarHeight}}px;"
        >
            <view
                class="status-bar"
                style="height: {{statusBarHeight}}px;"
            ></view>
            <view class="navbar-content">
                <view
                    class="nav-back"
                    bindtap="goBack"
                >
                    <van-icon
                        name="arrow-left"
                        size="20px"
                        color="#333"
                    />
                </view>
                <view class="nav-title">车务服务</view>
                <view class="nav-placeholder"></view>
            </view>
        </view>

        <!-- 搜索区域 (固定在导航栏下方) -->
        <view
            class="search-bar"
            style="top: {{navBarHeight-1}}px;"
        >
            <view class="search-input-wrapper">
                <icon
                    type="search"
                    size="14"
                    class="search-icon"
                ></icon>
                <input
                    class="search-input"
                    placeholder="请输入公司名称搜索"
                    placeholder-style="color: #aaa; font-size: 26rpx;"
                    value="{{ searchValue || ''}}"
                    bindinput="onSearchInput"
                    bindconfirm="onSearch"
                />
                <view
                    class="search-btn"
                    bindtap="onSearch"
                >搜索</view>
            </view>
        </view>
    </view>

    <!-- 背景过渡区域 -->
    <view
        class="transition-area"
        style="top: {{navBarHeight + 70}}px;"
    ></view>

    <!-- 页面内容容器 -->
    <view
        class="content"
        style="margin-top: {{navBarHeight + 70}}px; padding-bottom: 30rpx;"
        data-force-update="{{forceUpdate}}"
    >
        <view class="service-list">
            <!-- 车务服务列表内容 -->
            <view
                class="service-card"
                wx:for="{{serviceList}}"
                wx:key="id"
                bindtap="onCardTap"
                data-id="{{item.id}}"
            >
                <view class="service-card-header">
                    <view class="company-name">{{item.companyName}}</view>
                    <view class="card-arrow">
                        <van-icon
                            name="arrow"
                            size="16px"
                            color="#999"
                        />
                    </view>
                </view>
                <view class="service-card-info">
                    <!-- 修改开始：三个项目并排一行，每项标题在上值在下 -->
                    <view class="info-row three-column">
                        <view class="info-column">
                            <view class="info-label">是否开票</view>
                            <view class="fee-value">{{item.invoice}}</view>
                        </view>
                        <view class="info-column">
                            <view class="info-label">垫资能力</view>
                            <view class="fee-value">{{item.finance}}</view>
                        </view>
                        <view class="info-column">
                            <view class="info-label">服务范围</view>
                            <view class="fee-value">{{item.province}}{{item.city}}</view>
                        </view>
                    </view>

                    <!-- 效能单独一行 -->
                    <view class="info-row">
                        <view class="info-item-inline">效能 <text class="fee-value">{{item.efficiency}}</text></view>
                    </view>

                    <!-- 费用单独一行 -->
                    <view class="fee-info">
                        费用 <text class="fee-value">{{item.cost}}元/辆</text>
                    </view>
                    <!-- 修改结束 -->
                </view>
            </view>

            <!-- 加载更多提示 -->
            <view
                class="loading-more"
                wx:if="{{serviceList.length > 0}}"
            >
                <view wx:if="{{loading}}">
                    <van-loading
                        size="24px"
                        type="spinner"
                    />
                    <text style="margin-left: 10rpx;">加载中...</text>
                </view>
                <view wx:elif="{{hasMoreData}}">上拉加载更多</view>
                <view wx:else>没有更多数据了</view>
            </view>

            <!-- 空状态提示 -->
            <view
                class="empty-state"
                wx:if="{{serviceList.length === 0 && !loading}}"
            >
                <view class="empty-text">暂无数据</view>
            </view>

            <!-- 加载中状态 -->
            <view
                class="loading-state"
                wx:if="{{serviceList.length === 0 && loading}}"
            >
                <van-loading
                    size="32px"
                    type="spinner"
                />
                <view class="loading-text">加载中...</view>
            </view>


        </view>
    </view>
</view>