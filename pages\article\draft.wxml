<!--pages/article/draft.wxml-->
<view
  class="container"
  style="padding-top: {{statusBarHeight + 44}}px;"
>

  <!-- 自定义导航栏 -->
  <view class="custom-nav">
    <view
      class="status-bar"
      style="height: {{statusBarHeight}}px;"
    ></view>
    <view class="nav-content">
      <view
        class="back-icon"
        bindtap="navigateBack"
      >
        <image src="/icons/moments/back.svg"></image>
      </view>
      <view class="nav-title">草稿箱</view>
    </view>
  </view>

  <!-- 添加遮罩层，用于点击关闭菜单 -->
  <view
    class="mask"
    wx:if="{{hasOpenMenu}}"
    bindtap="closeAllMenus"
  ></view>

  <!-- 草稿管理标题 -->
  <view class="draft-header">
    <view class="draft-count">{{total}}篇草稿</view>
    <view
      class="manage-btn"
      bindtap="toggleManageMode"
    >{{isManageMode ? '完成' : '管理'}}</view>
  </view>


  <!-- 草稿内容 -->
  <view class="draft-content">
    <view
      class="draft-item"
      wx:for="{{draftList}}"
      wx:key="id"
    >
      <!-- 管理模式下显示选择框 -->
      <view
        class="select-box"
        wx:if="{{isManageMode}}"
        catchtap="toggleSelectItem"
        data-index="{{index}}"
      >
        <view class="checkbox {{item.selected ? 'checked' : ''}}"></view>
      </view>

      <view class="draft-info {{isManageMode ? 'manage-mode' : ''}}">
        <view class="vehicle-name">{{item.ui_vehicle_name}}</view>
        <view class="vehicle-type">类型：{{item.used_type_name}}</view>
        <view class="dots">......</view>
      </view>

      <!-- 普通模式下的操作按钮 -->
      <view
        class="action-buttons"
        wx:if="{{!isManageMode}}"
      >
        <view
          class="edit-btn"
          bindtap="onEditTap"
          data-id="{{item.id}}"
          data-index="{{index}}"
        >编辑</view>
        <view
          class="publish-btn"
          bindtap="onPublishTap"
          data-id="{{item.id}}"
          data-index="{{index}}"
        >发布</view>

      </view>
    </view>

    <!-- 没有草稿时显示空提示 -->
    <view
      class="empty-tips"
      wx:if="{{draftList.length === 0}}"
    >
      暂无草稿
    </view>
  </view>

  <!-- 底部操作栏 - 仅在管理模式下显示 -->
  <view
    class="footer-action-bar"
    wx:if="{{isManageMode}}"
  >
    <view
      class="select-all-box"
      bindtap="toggleSelectAll"
    >
      <view class="checkbox {{isAllSelected ? 'checked' : ''}}"></view>
      <text>全选</text>
    </view>
    <view
      class="delete-btn {{selectedCount > 0 ? 'active' : ''}}"
      bindtap="onBatchDeleteTap"
    >删除{{selectedCount > 0 ? '('+selectedCount+')' : ''}}</view>
  </view>
</view>