/**
 * 网络请求封装
 * 统一处理请求、响应拦截、错误处理等
 */

import config from '/../config';

// 基础URL，可根据环境配置
const BASE_URL = config.BASE_URL;

// 请求中间件 - 请求前拦截
const requestInterceptor = (options) => {
  // 获取token
  const token = wx.getStorageSync('token');

  // 合并请求头
  const header = {
    'content-type': 'application/json',
    ...(token ? { 'Authorization': `Bearer ${token}` } : {}),
    ...options.header
  }; ``

  return {
    ...options,
    header
  };
};

// 响应中间件 - 响应后拦截
const responseInterceptor = (response, options = {}) => {
  const { statusCode, data } = response;

  // 请求成功
  if (statusCode >= 200 && statusCode < 300) {
    // 检查返回的数据结构
    // 如果data直接是有效数据（没有code/msg/data的格式），则直接返回
    if (!data.hasOwnProperty('code') && !data.hasOwnProperty('msg')) {
      return data;
    }

    // 业务状态判断（根据后端接口规范调整）
    if (data && (
      data.code === 0 ||
      data.code === 1 ||
      data.code === 200 ||
      data.code === '0' ||
      data.code === '1' ||
      data.code === '200' ||
      // 检查code是否在allowErrorCodes中
      (options.allowErrorCodes && options.allowErrorCodes.includes(data.code))
    )) {
      return data.data || data;
    } else {
      // 业务错误
      const errorMsg = data.msg || '请求失败';
      // 只有当options.toast不为false时才显示错误提示
      if (options.toast !== false) {
        wx.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 2000
        });
      }
      return Promise.reject(errorMsg);
    }
  } else if (statusCode === 401) {
    // 未授权，清除token并跳转登录
    wx.removeStorageSync('token');
    // 只有当options.toast不为false时才显示错误提示
    if (options.toast !== false) {
      wx.showToast({
        title: '登录已过期，请重新登录',
        icon: 'none',
        duration: 2000
      });
    }

    // 延迟跳转，让用户看到提示
    setTimeout(() => {
      wx.navigateTo({
        url: '/pages/login/index'
      });
    }, 1500);

    return Promise.reject('未授权');
  } else {
    // 其他错误
    const errorMsg = `网络请求错误：${statusCode}`;
    // 只有当options.toast不为false时才显示错误提示
    if (options.toast !== false) {
      wx.showToast({
        title: errorMsg,
        icon: 'none',
        duration: 2000
      });
    }
    return Promise.reject(errorMsg);
  }
};

/**
 * 发起请求的基础方法
 * @param {Object} options - 请求配置
 * @returns {Promise} 请求结果Promise
 */
const request = (options) => {
  // 合并请求参数
  const mergedOptions = requestInterceptor({
    ...options,
    url: /^https?:\/\//.test(options.url) ? options.url : `${BASE_URL}${options.url}`
  });

  // 显示加载提示
  if (options.loading !== false) {
    wx.showLoading({
      title: options.loadingText || '加载中...',
      mask: true
    });
  }



  return new Promise((resolve, reject) => {
    wx.request({
      ...mergedOptions,
      success: (res) => {
        try {
          const result = responseInterceptor(res, options);
          resolve(result);
        } catch (error) {
          reject(error);
        }
      },
      fail: (err) => {
        wx.showToast({
          title: '网络连接失败',
          icon: 'none',
          duration: 2000
        });
        reject(err);
      },
      complete: () => {
        // 隐藏加载提示
        if (options.loading !== false) {
          wx.hideLoading();
        }
      }
    });
  });
};

// 常用请求方法封装
const http = {
  get: (url, data, options = {}) => {
    return request({
      url,
      data,
      method: 'GET',
      ...options
    });
  },

  post: (url, data, options = {}) => {
    return request({
      url,
      data,
      method: 'POST',
      ...options
    });
  },

  put: (url, data, options = {}) => {
    return request({
      url,
      data,
      method: 'PUT',
      ...options
    });
  },

  delete: (url, data, options = {}) => {
    return request({
      url,
      data,
      method: 'DELETE',
      ...options
    });
  },

  // 上传文件
  upload: (url, filePath, formData = {}, name = 'file', options = {}) => {
    const token = wx.getStorageSync('token');
    const header = {
      ...(token ? { 'Authorization': `Bearer ${token}` } : {}),
      ...options.header
    };

    if (options.loading !== false) {
      wx.showLoading({
        title: options.loadingText || '上传中...',
        mask: true
      });
    }

    return new Promise((resolve, reject) => {
      wx.uploadFile({
        url: /^https?:\/\//.test(url) ? url : `${BASE_URL}${url}`,
        filePath,
        name,
        formData,
        header,
        success: (res) => {
          try {
            // 上传接口返回的是字符串，需要转换
            const data = JSON.parse(res.data);
            if (data.code === 0) {
              resolve(data.data);
            } else {
              wx.showToast({
                title: data.msg || '上传失败',
                icon: 'none',
                duration: 2000
              });
              reject(data.msg);
            }
          } catch (error) {
            reject('上传失败');
          }
        },
        fail: (err) => {
          wx.showToast({
            title: '上传失败',
            icon: 'none',
            duration: 2000
          });
          reject(err);
        },
        complete: () => {
          if (options.loading !== false) {
            wx.hideLoading();
          }
        }
      });
    });
  }
};

export default http; 