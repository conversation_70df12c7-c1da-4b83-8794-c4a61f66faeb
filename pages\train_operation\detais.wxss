/* pages/train_operation/detais.wxss */
page {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: fixed;
    -webkit-overflow-scrolling: touch;
    background-color: #F5F5F5;
}

.container {
    min-height: 100vh;
    height: 100%;
    background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    overflow: hidden;
}

/* 自定义导航栏样式 */
.custom-nav {
    width: 100%;
    background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
}

.status-bar {
    width: 100%;
}

.nav-content {
    height: 44px;
    display: flex;
    align-items: center;
    position: relative;
}

.back-icon {
    position: absolute;
    left: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10rpx;
}

.nav-title {
    flex: 1;
    text-align: center;
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
}

/* 主内容区域 */
.main-content {
    position: relative;
    z-index: 10;
    margin-top: 0;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    padding: 20rpx;
    background: linear-gradient(to right, #DCECFF, #EAECF6, #FFECE8, #DFDFDF, #F1F4F9);
}

/* 切换标签卡片样式 */
.tabs-card {
    background-color: #FFFFFF;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    padding: 0 20rpx;
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.tabs-container {
    display: flex;
    align-items: center;
    height: 88rpx;
}

.tab-item {
    flex: 1;
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.tab-text {
    font-size: 28rpx;
    color: #333333;
    font-weight: normal;
}

.tab-item.active .tab-text {
    color: #3B82F6;
    font-weight: 500;
}

.tab-line {
    position: absolute;
    bottom: 6rpx;
    width: 40rpx;
    height: 4rpx;
    border-radius: 2rpx;
    background: transparent;
}

.tab-item.active .tab-line {
    background: linear-gradient(to right, rgba(59, 130, 246, 1), rgba(105, 174, 249, 1), rgba(239, 245, 255, 1));
}

/* 信息卡片样式 */
.info-card {
    background-color: #FFFFFF;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    padding: 30rpx 0;
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.info-section {
    padding: 0 30rpx;
    background-color: #FFFFFF;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.section-title {
    font-size: 32rpx;
    color: #3D3D3D;
    font-weight: bold;
    margin-bottom: 0;
    padding: 30rpx 0;
}

.info-row {
    display: flex;
    justify-content: space-between;
    padding: 30rpx 0;
    font-size: 28rpx;
    border-bottom: 1rpx solid #EEEEEE;
}

.info-row:last-child {
    border-bottom: none;
}

.info-label {
    color: #666666;
}

.info-value {
    color: #333333;
    max-width: 60%;
    text-align: right;
}

/* 空状态提示 */
.empty-tip {
    padding: 60rpx 0;
    text-align: center;
    color: #999999;
    font-size: 28rpx;
}

/* iOS底部安全区域适配 */
@supports (padding-bottom: constant(safe-area-inset-bottom)) {
    .container {
        padding-bottom: constant(safe-area-inset-bottom);
    }
}

@supports (padding-bottom: env(safe-area-inset-bottom)) {
    .container {
        padding-bottom: env(safe-area-inset-bottom);
    }
}

/* 流程时间轴样式 */
.process-timeline {
    background-color: #FFFFFF;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    padding: 20rpx 0;
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

/* 时间轴项目样式 */
.timeline-item {
    display: flex;
    position: relative;
    padding: 65rpx 30rpx;
    padding-bottom: 0;
    margin-bottom: 15rpx;
}

/* 上连接线 - 连接到上一个节点 */
.timeline-content-vertical::before {
    content: "";
    position: absolute;
    width: 3rpx;
    top: -75rpx;
    /* 总向上距离 = 间距(15rpx) + 高度(60rpx) */
    height: 60rpx;
    /* 固定线条长度 */
    left: 50%;
    transform: translateX(-50%);
    background-color: #DDDDDD;
    z-index: 1;
    background-image: linear-gradient(#DDDDDD 50%, transparent 50%);
    background-size: 4rpx 16rpx;
}

/* 下连接线 - 连接到下一个节点 */
.timeline-content-vertical::after {
    content: "";
    position: absolute;
    width: 3rpx;
    top: calc(100% + 15rpx);
    /* 从状态标签下方固定间距(15rpx)开始 */
    height: 60rpx;
    /* 固定线条长度，与上连接线一致 */
    left: 50%;
    transform: translateX(-50%);
    background-color: #DDDDDD;
    z-index: 1;
    background-image: linear-gradient(#DDDDDD 50%, transparent 50%);
    background-size: 4rpx 16rpx;
}

/* 左侧时间轴部分 */
.timeline-left {
    position: relative;
    width: 100px;
    padding-right: 20rpx;
    display: flex;
    justify-content: center;
}

/* 时间轴垂直连接线 - 基础样式 */
.timeline-line {
    display: none;
    /* 隐藏原有连接线 */
}

/* 移除旧的连接线样式 */
.timeline-content-vertical::after {
    display: none;
}

/* 垂直排列的时间轴内容 */
.timeline-content-vertical {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100px;
    text-align: center;
    position: relative;
    z-index: 2;
    min-height: 190rpx;
}

/* 第一个项目不需要上连接线 */
.timeline-item:first-child .timeline-content-vertical::before {
    display: none;
}

/* 最后一个项目不需要下连接线 */
.timeline-item:last-child .timeline-content-vertical::after {
    display: none;
}

/* 已完成状态的上连接线 - 蓝色 */
.timeline-content-vertical.completed::before {
    background-color: #3B82F6;
    background-image: linear-gradient(#3B82F6 50%, transparent 50%);
}

/* 已完成状态的下连接线 - 蓝色 */
.timeline-content-vertical.completed::after {
    background-color: #3B82F6;
    background-image: linear-gradient(#3B82F6 50%, transparent 50%);
}

/* 进行中状态的上连接线 - 黄色 */
.timeline-content-vertical.in-progress::before {
    background-color: #FFB800;
    background-image: linear-gradient(#FFB800 50%, transparent 50%);
}

/* 进行中状态的下连接线 - 灰色（保持默认） */
.timeline-content-vertical.in-progress::after {
    /* 保持默认灰色 */
}

/* 待处理状态的上、下连接线都保持灰色（默认） */

/* 时间轴图标样式 */
.timeline-icon {
    position: relative;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 图标图片样式 */
.status-icon {
    width: 32px;
    height: 32px;
    z-index: 2;
}

/* 已完成状态图标 */
.timeline-icon.completed {
    background-color: transparent;
}

.icon-check {
    width: 15rpx;
    height: 8rpx;
    border: 2rpx solid #FFFFFF;
    border-top: none;
    border-right: none;
    transform: rotate(-45deg);
}

/* 进行中状态图标 */
.timeline-icon.in-progress {
    background-color: transparent;
}

.icon-progress {
    width: 16rpx;
    height: 16rpx;
    border-radius: 50%;
    background-color: #FFFFFF;
}

/* 未开始状态图标 */
.timeline-icon.not-started {
    border: 1px solid #DDDDDD;
    background-color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: center;
}

.icon-dot {
    width: 10rpx;
    height: 10rpx;
    border-radius: 50%;
    background-color: #DDDDDD;
}

/* 待处理状态图标 */
.timeline-icon.pending {
    background-color: transparent;
}

/* 时间轴内容样式 */
.timeline-content {
    margin-left: 40rpx;
}

.timeline-step {
    font-size: 28rpx;
    font-weight: 500;
    color: #333333;
    margin-top: 20rpx;
    margin-bottom: 12rpx;
}

.timeline-info {
    font-size: 24rpx;
    color: #666666;
    margin-bottom: 12rpx;
}

.timeline-date {
    font-size: 22rpx;
    color: #999999;
    margin-bottom: 12rpx;
}

/* 状态文本样式 */
.timeline-status {
    font-size: 22rpx;
    display: inline-block;
    padding: 2rpx 10rpx;
    border-radius: 4rpx;
    margin-bottom: 0;
    position: relative;
    z-index: 2;
    background-color: #FFFFFF;
    box-shadow: 0 0 2rpx rgba(0, 0, 0, 0.1);
}

.timeline-status.completed {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3B82F6;
    box-shadow: 0 0 4rpx rgba(59, 130, 246, 0.2);
}

.timeline-status.in-progress {
    background-color: rgba(255, 184, 0, 0.1);
    color: #FFB800;
    box-shadow: 0 0 4rpx rgba(255, 184, 0, 0.2);
}

.timeline-status.pending {
    background-color: rgba(153, 153, 153, 0.1);
    color: #999999;
    box-shadow: 0 0 4rpx rgba(153, 153, 153, 0.2);
}

.timeline-status.not-started {
    background-color: rgba(153, 153, 153, 0.1);
    color: #999999;
}

/* 右侧操作按钮区域 */
.timeline-right {
    flex: 1;
    margin-left: 20rpx;
}

.action-buttons {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 10rpx;
    justify-content: space-between;
}

/* 流程项目之间的分隔线样式 */
.timeline-divider {
    height: 1rpx;
    background-color: #EEEEEE;
    margin: 20rpx 0;
    width: 100%;
    display: block;
}

.action-btn {
    width: 48%;
    height: 64rpx;
    line-height: 62rpx;
    padding: 0 10rpx;
    background-color: transparent;
    color: #3B82F6;
    font-size: 24rpx;
    border-radius: 12rpx;
    border: 1px solid #3B82F6;
    margin-right: 0;
    display: inline-block;
    text-align: center;
    white-space: nowrap;
    margin-bottom: 15rpx;
    box-sizing: border-box;
}

.action-btn:last-child {
    margin-right: 0;
}

.action-btn::after {
    border: none;
}

/* 按钮禁用状态样式 */
.action-btn.disabled {
    background-color: #F5F5F5 !important;
    color: #909399 !important;
    border: 1px solid #DCDFE6 !important;
    cursor: not-allowed;
}

/* 待处理状态按钮样式 */
.action-btn.pending-btn {
    background-color: transparent;
    color: #AAAAAA;
    border: 1px solid #CCCCCC;
}

/* 已完成状态按钮样式 */
.action-btn.completed-btn {
    background-color: transparent;
    color: #FFB800;
    border: 1px solid #FFB800;
}

/* 备注信息特殊处理 */
.info-value.remarks {
    text-align: left;
    max-width: 100%;
    color: #666666;
    line-height: 1.5;
}

/* 车辆详情项目 */
.vehicle-detail-item {
    margin-bottom: 20rpx;
    padding-bottom: 20rpx;
    border-bottom: 1px dashed #EEEEEE;
}

.vehicle-detail-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}