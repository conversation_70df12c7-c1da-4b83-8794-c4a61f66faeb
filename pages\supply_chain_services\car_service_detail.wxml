<!--pages/supply_chain_services/car_service_detail.wxml-->
<import src="../../templates/loginPopup/loginPopup.wxml" />

<view
    class="container"
    style="{{ pageStyle }}"
>
    <!-- 骨架屏 - 仅在页面加载中且不显示正常加载动画时显示 -->
    <view
        class="skeleton-screen"
        wx:if="{{isLoading && !showLoadingAnimation}}"
    >
        <!-- 骨架自定义导航栏 -->
        <view class="skeleton-custom-nav">
            <view
                class="skeleton-status-bar"
                style="height: {{statusBarHeight}}px;"
            ></view>
            <view class="skeleton-nav-title"></view>
        </view>

        <!-- 骨架车辆列表 -->
        <view class="skeleton-car-list">
            <view
                class="skeleton-car-item"
                wx:for="{{6}}"
                wx:key="index"
            >
                <view class="skeleton-car-image"></view>
                <view class="skeleton-car-info">
                    <view class="skeleton-car-title"></view>
                    <view class="skeleton-car-params"></view>
                    <view class="skeleton-car-price"></view>
                </view>
            </view>
        </view>
    </view>

    <!-- 自定义导航栏 -->
    <view class="custom-nav">
        <view
            class="status-bar"
            style="height: {{statusBarHeight}}px;"
        ></view>
        <view class="nav-title">
            <view
                class="nav-back"
                bindtap="navigateBack"
            >
                <van-icon
                    name="arrow-left"
                    size="20px"
                    color="#333"
                />
            </view>
            <view class="title-text">车务服务</view>
            <view class="nav-placeholder"></view>
        </view>
    </view>

    <!-- 内容区域 - 添加top-margin以避开导航栏 -->
    <view class="content-container">
        <!-- 页面内容 -->
        <view class="service-content">
            <!-- 车务服务卡片头部 -->
            <view class="service-card financial-card">
                <view class="card-image-container">
                    <image
                        class="card-image"
                        src="{{COS_CONFIG.url}}wechat/assets/images/car_service_long_detil.png"
                        mode="scaleToFill"
                    ></image>
                </view>
            </view>

            <!-- 车务服务详细信息 -->
            <view class="info-card">
                <view class="info-inside">
                    <view class="info-inside-row">
                        <view class="info-inside-row-title">省份</view>
                        <view class="info-inside-row-content">{{province}}</view>
                    </view>
                    <view class="info-inside-row">
                        <view class="info-inside-row-title">城市</view>
                        <view class="info-inside-row-content">{{city}}</view>
                    </view>
                    <view class="info-inside-row">
                        <view class="info-inside-row-title">背书公司</view>
                        <view class="info-inside-row-content">{{company_name}}</view>
                    </view>
                    <view class="info-inside-row">
                        <view class="info-inside-row-title">开票情况</view>
                        <view class="info-inside-row-content">{{invoice_status}}</view>
                    </view>
                    <view class="info-inside-row">
                        <view class="info-inside-row-title">垫资能力</view>
                        <view class="info-inside-row-content">{{financing_ability}}</view>
                    </view>
                    <view class="info-inside-row">
                        <view class="info-inside-row-title">服务范围</view>
                        <view class="info-inside-row-content">{{service_scope}}</view>
                    </view>
                    <view class="info-inside-row">
                        <view class="info-inside-row-title">效能</view>
                        <view class="info-inside-row-content">{{capability}}</view>
                    </view>
                    <view class="info-inside-row">
                        <view class="info-inside-row-title">费用</view>
                        <view class="info-inside-row-content">{{cost}}</view>
                    </view>
                    <view class="info-inside-row">
                        <view class="info-inside-row-title">补充说明</view>
                        <view class="info-inside-row-content">{{additional_notes}}</view>
                    </view>
                </view>
            </view>

            <!-- 联系信息标题 -->
            <view class="section-header">
                <text class="section-title">联系信息</text>
                <!-- <view class="section-line"></view>
                <view class="section-stars">
                    <text class="star">★</text>
                    <text class="star">★</text>
                    <text class="star">★</text>
                </view> -->
            </view>

            <!-- 联系信息 -->
            <view class="contact-card">
                <view class="contact-info">
                    <view class="contact-row">
                        <view class="contact-label">公司名称</view>
                        <view class="contact-value">{{contact_company}}</view>
                    </view>
                    <view
                        class="contact-row"
                        wx:if="{{phone}}"
                    >
                        <view class="contact-label">联系方式</view>
                        <view class="contact-value">{{phone}}</view>
                    </view>
                    <view
                        class="contact-row"
                        wx:if="{{email}}"
                    >
                        <view class="contact-label">联系邮箱</view>
                        <view class="contact-value">{{email}}</view>
                    </view>
                </view>
            </view>
        </view>
    </view>

    <!-- 底部咨询按钮 -->
    <view class="bottom-button">
        <button
            class="share-button"
            open-type="share"
        >
            <van-icon
                name="share-o"
                size="24px"
            />
            <text>分享</text>
        </button>
        <button
            class="consult-button"
            bindtap="onConsult"
        >咨询下单</button>
    </view>

    <!-- 引用登录弹窗模板 -->
    <template
        is="loginPopup"
        data="{{ showLoginPopup, loginPopupOptions }}"
    />
</view>