/* pages/member/index.wxss */

/* 页面容器 */
.container {
    min-height: 100vh;
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    position: relative;
}

/* 渐变背景 */
.gradient-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #F1F4F9 75%, #DCECFF 100%);
    background-attachment: fixed;
    /* 固定背景，避免滚动时背景移动 */
    z-index: -1;
}

/* 自定义导航栏 */
.custom-navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 100;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 100%);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.status-bar {
    width: 100%;
}

.navbar-content {
    display: flex;
    height: 44px;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
}

.nav-back {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 44px;
    z-index: 5;
    margin-left: -8rpx;
}

.nav-title {
    flex: 1;
    text-align: center;
    font-size: 34rpx;
    font-weight: 500;
    color: #333;
}

.nav-placeholder {
    width: 40px;
    height: 44px;
}

/* 内容区域 */
.content {
    width: 100%;
    padding: 20rpx;
    box-sizing: border-box;
    position: relative;
}

/* 会员卡片样式 */
.member-card {
    width: 100%;
    padding: 0 20rpx;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: transparent;
    position: relative;
    z-index: 5;
    border-left: 2px solid rgba(0, 0, 0, 0.05);
    border-right: 2px solid rgba(0, 0, 0, 0.05);
    border-bottom: 2px solid rgba(0, 0, 0, 0.05);
    border-radius: 0 0 12rpx 12rpx;
}

/* 会员等级勋章药丸容器 */
.medal-pill-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 70%;
    height: 70rpx;
    background-image: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(245, 247, 250, 1));
    border-radius: 35rpx;
    padding: 0 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.medal-text {
    font-size: 28rpx;
    color: #333;
    font-weight: 400;
}

.medal-action {
    display: flex;
    align-items: center;
}

.medal-action-text {
    font-size: 28rpx;
    color: #4080FF;
    margin-right: 4rpx;
}

.medal-action-arrow {
    font-size: 28rpx;
    color: #4080FF;
    font-weight: 500;
}

/* 会员徽章容器 */
.badge-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 15rpx;
    width: 100%;
    position: relative;
    z-index: 15;
}

/* 会员徽章和箭头的容器 */
.badge-and-arrow-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 620rpx;
    margin-bottom: 10rpx;
}

/* 会员轮播图容器 */
.member-swiper-container {
    position: relative;
    width: 280rpx;
    height: 280rpx;
    border-radius: 50%;
    overflow: hidden;
}

/* 会员轮播图样式 */
.member-swiper {
    width: 280rpx;
    height: 280rpx;
    border-radius: 50%;
    overflow: hidden;
}

.member-swiper-item {
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 会员徽章图片 */
.member-badge {
    width: 250rpx;
    height: 250rpx;
    border-radius: 50%;
}

/* 会员等级文字 */
.member-level {
    font-size: 30rpx;
    color: #333;
    font-weight: 500;
    margin-top: 10rpx;
    text-align: center;
    transition: all 0.3s ease;
}

/* 进度条容器 */
.progress-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 15rpx;
    margin-top: 15rpx;
    position: relative;
    z-index: 10;
}

/* 进度条包装器 */
.progress-wrapper {
    width: 85%;
    position: relative;
    height: 50rpx;
    display: flex;
    align-items: center;
}

/* 进度条 */
.progress-bar {
    width: 100%;
    height: 12rpx;
    background-color: #E5E7EB;
    border-radius: 6rpx;
    overflow: visible;
    position: relative;
}

/* 进度条内部填充 */
.progress-inner {
    height: 100%;
    background: linear-gradient(to right, #4080FF, #77A5FF);
    border-radius: 6rpx;
    transition: width 0.3s ease-in-out;
    position: relative;
    min-width: 80rpx;
    /* 确保即使进度为0，也有足够宽度显示药丸 */
}

/* 药丸形状指示器 */
.progress-pill {
    position: absolute;
    right: -40rpx;
    top: 50%;
    transform: translateY(-50%);
    background-color: #4080FF;
    color: #fff;
    font-size: 24rpx;
    padding: 6rpx 16rpx;
    border-radius: 20rpx;
    white-space: nowrap;
    z-index: 10;
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

/* 进度文字 */
.progress-text {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24rpx;
    color: #fff;
    z-index: 5;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 会员选项 */
.member-options {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    margin-top: 15rpx;
    padding: 15rpx 0;
}

/* 选项项目 */
.option-item {
    display: flex;
    align-items: center;
    padding: 0 20rpx;
    height: 40rpx;
}

.option-item text {
    font-size: 28rpx;
    color: #333;
}

/* 分隔线 */
.divider {
    color: #ddd;
    font-size: 28rpx;
    margin: 0 10rpx;
}

/* 箭头图标 */
.arrow-icon {
    display: flex;
    align-items: center;
    margin-left: 4rpx;
}

/* 会员类型选择按钮 */
.member-type-tabs {
    width: 100%;
    display: flex;
    padding: 0;
    margin: 10rpx 0 20rpx;
    box-sizing: border-box;
}

.member-type-tab {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50%;
    height: 80rpx;
    position: relative;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0), rgba(245, 247, 250, 1));
    box-sizing: border-box;
}

.member-type-icon {
    width: 40rpx;
    height: 40rpx;
    margin-right: 10rpx;
}

.member-type-text {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
}

.active-silver {
    border-top: 2px solid #4080FF;
    border-left: 2px solid #4080FF;
    border-right: 2px solid #4080FF;
    border-bottom: none;
    border-radius: 6rpx 6rpx 0 0;
    z-index: 10;
}

.inactive-silver {
    border-bottom: 2px solid #4080FF;
    border-top: none;
    border-left: none;
    border-right: none;
    z-index: 9;
}

.active-gold {
    border-top: 2px solid #4080FF;
    border-left: 2px solid #4080FF;
    border-right: 2px solid #4080FF;
    border-bottom: none;
    border-radius: 6rpx 6rpx 0 0;
    z-index: 10;
}

.inactive-gold {
    border-bottom: 2px solid #4080FF;
    border-top: none;
    border-left: none;
    border-right: none;
    z-index: 9;
}

/* 箭头按钮 */
.swiper-next-btn {
    position: absolute;
    right: -30rpx;
    top: 50%;
    transform: translateY(-50%);
    width: 50rpx;
    height: 50rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
}

.next-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    background: transparent;
    box-shadow: none;
}

.arrow-image {
    width: 50rpx;
    height: 50rpx;
}

/* 价格卡片容器 */
.price-cards-container {
    width: 100%;
    margin-top: 30rpx;
    margin-bottom: 10rpx;
    position: relative;
}

/* 价格卡片滚动视图 */
.price-scroll-view {
    width: 100%;
    white-space: nowrap;
    padding: 10rpx 0 20rpx;
}

/* 价格卡片样式 */
.price-card {
    display: inline-block;
    width: 248rpx;
    height: 226rpx;
    margin-right: 20rpx;
    margin-top: 5rpx;
    border-radius: 12rpx;
    background-size: cover;
    background-position: center;
    box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
    position: relative;
    padding: 20rpx;
    box-sizing: border-box;
    overflow: hidden;
    transition: all 0.2s ease;
    border: 3rpx solid transparent;
}

/* 背景图层 */
.card-bg-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    z-index: 0;
}

/* 选中状态的价格卡片 */
.price-card.selected {
    border: 3rpx solid #4080FF;
    transform: translateY(-4rpx);
    box-shadow: 0 8rpx 16rpx rgba(64, 128, 255, 0.2);
}

.price-card:first-child {
    margin-left: 30rpx;
}

.price-card:last-child {
    margin-right: 30rpx;
}

/* 价格卡片标题 */
.price-card-title {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
    margin-bottom: 16rpx;
    text-shadow: none;
    position: relative;
    z-index: 1;
}

/* 价格卡片内容区 */
.price-card-content {
    display: flex;
    align-items: flex-end;
    position: relative;
    z-index: 1;
}

/* 价格符号 */
.price-symbol {
    font-size: 24rpx;
    color: #333;
    font-weight: bold;
    margin-right: 4rpx;
    align-self: flex-start;
    margin-top: 6rpx;
}

/* 价格数值 */
.price-value {
    font-size: 44rpx;
    color: #333;
    font-weight: bold;
    text-shadow: none;
}

/* 原价容器 */
.original-price-container {
    position: relative;
    margin-left: 10rpx;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

/* 原价 */
.price-original {
    font-size: 24rpx;
    color: rgba(0, 0, 0, 0.5);
    text-decoration: line-through;
    align-self: flex-end;
}

/* 价格标签 */
.price-tag {
    position: relative;
    margin-bottom: 2rpx;
    background-color: #3B82F6;
    color: #fff;
    font-size: 20rpx;
    padding: 2rpx 8rpx;
    border-radius: 8rpx;
}

/* 首年专属优惠文本样式 */
.annual-promo-text {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    text-align: center;
    font-size: 22rpx;
    color: #333;
    font-weight: 500;
    z-index: 1;
    background-color: #E6F0FF;
    padding: 8rpx 0;
    border-radius: 0 0 12rpx 12rpx;
}

/* 会员说明区域 */
.member-description {
    padding: 10rpx 20rpx;
    margin: 5rpx 0;
    width: 100%;
    box-sizing: border-box;
}

.description-text {
    font-size: 24rpx;
    color: #999;
    line-height: 1.6;
    text-align: left;
}

.price-highlight {
    font-size: 24rpx;
    color: #3B82F6;
    line-height: 1.6;
    font-weight: 500;
    display: inline;
}

/* 确认按钮 */
.confirm-button-container {
    padding: 10rpx 0;
    margin: 5rpx 0 10rpx;
    width: 100%;
}

.confirm-button {
    background: linear-gradient(90deg, #4080FF, #5A9DFF);
    color: #FFFFFF;
    border-radius: 20rpx;
    font-size: 32rpx;
    padding: 0;
    border: none;
    width: 100%;
    font-weight: normal;
    height: 80rpx;
    line-height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.confirm-button::after {
    border: none;
}

/* 协议勾选区域 */
.agreement-container {
    display: flex;
    justify-content: center;
    padding: 5rpx 30rpx 20rpx;
}

.agreement-item {
    display: flex;
    align-items: center;
    justify-content: center;
}

.agreement-text {
    font-size: 24rpx;
    color: #999;
}

.agreement-link {
    font-size: 24rpx;
    color: #4080FF;
}

/* 自定义radio样式 */
radio .wx-radio-input {
    width: 30rpx;
    height: 30rpx;
    margin-right: 10rpx;
}

/* 会员权益对比区域 */
.member-benefits-container {
    width: 100%;
    margin-top: 30rpx;
    padding: 0 20rpx 30rpx;
    box-sizing: border-box;
}

.member-benefits-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 20rpx;
    text-align: left;
    padding-left: 10rpx;
}

.member-benefits-image-container {
    width: 100%;
    display: flex;
    justify-content: center;
}

.member-benefits-image {
    width: 100%;
    border-radius: 12rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 支付订单状态卡片 */
.payment-status-card {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-image: linear-gradient(135deg, #AAD3FE 0%, #DCE5F3 30%, #F8EFED 70%, #EFF3F9 100%);
    border-radius: 12rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0;
    width: 400rpx;
    height: 240rpx;
    z-index: 999;
    animation: fadeIn 0.3s ease-in-out;
    text-align: center;
    box-sizing: border-box;
}

.payment-diamond-icon {
    width: 160rpx;
    height: 160rpx;
    margin: 0 auto 10rpx;
    display: block;
    transform: translateX(-20rpx);
}

.payment-status-text {
    font-size: 28rpx;
    color: #333;
    text-align: center;
    font-weight: 500;
    line-height: 1.5;
    margin: 0;
    padding: 0;
    transform: translateY(5rpx);
    /* 稍微向下移动文本 */
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.9);
    }

    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

/* 会员套餐限制卡片 */
.member-limit-card {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-image: linear-gradient(135deg, rgba(255, 236, 232, 1) 0%, rgba(220, 229, 243, 1) 30%, rgba(248, 239, 237, 1) 70%, rgba(239, 243, 249, 1) 100%);
    border-radius: 16rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    padding: 35rpx 30rpx;
    width: 580rpx;
    height: 540rpx;
    z-index: 999;
    animation: fadeIn 0.3s ease-in-out;
    text-align: center;
    box-sizing: border-box;
}

.member-diamond-icon {
    width: 360rpx;
    height: 360rpx;
    margin: 7rpx auto;
    display: block;
    transform: translateX(-20rpx);
}

.member-limit-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 0rpx;
}

.member-limit-text {
    font-size: 36rpx;
    color: #3D3D3D;
    text-align: center;
    font-weight: 500;
    line-height: 1.5;
    margin: 0;
    padding: 0;
}

.member-limit-subtext {
    font-size: 30rpx;
    color: #3D3D3D;
    text-align: center;
    line-height: 1.5;
    margin: 5rpx 0;
    padding: 0;
}

.member-limit-message {
    font-size: 28rpx;
    color: #AAAAAA;
    text-align: center;
    line-height: 1.6;
    margin: 10rpx 0;
    padding: 0 15rpx;
    width: 100%;
}

.member-limit-close {
    background-color: #8EB9FF;
    color: #3D3D3D;
    font-size: 30rpx;
    font-weight: 500;
    width: 360rpx;
    height: 160rpx;
    line-height: 160rpx;
    border-radius: 8rpx;
    margin-top: 20rpx;
    border: none;
    padding: 0;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 会员勋章轮播图卡片样式 */
.medal-carousel-drawer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background-color: transparent;
    /* 完全透明背景 */
    z-index: 999;
    display: none;
}

.medal-carousel-drawer.show {
    display: block;
    /* 显示时改为block */
}

.medal-carousel-content {
    width: 100%;
    position: absolute;
    /* 使用绝对定位 */
    top: 0;
    /* 从顶部开始 */
    left: 0;
    padding: 30rpx 0;
    background-color: rgba(61, 61, 61, 0.9);
    /* 内容区域背景 */
    border-radius: 0 0 20rpx 20rpx;
}

.medal-carousel-nav {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30rpx;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    pointer-events: none;
    box-sizing: border-box;
}

.medal-carousel-arrow {
    width: 50rpx;
    height: 50rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    pointer-events: auto;
}

.medal-carousel-swiper {
    width: 100%;
    height: 380rpx;
}

.medal-carousel-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.medal-carousel-image {
    width: 280rpx;
    height: 280rpx;
}

.medal-carousel-name {
    margin-top: 20rpx;
    font-size: 30rpx;
    color: #ffffff;
    font-weight: 500;
}

/* 收回按钮样式 */
.medal-carousel-collapse {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: 30rpx;
}